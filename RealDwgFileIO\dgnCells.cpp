/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnCells.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
// This file is #included in rDwgDgnExtension.cpp


/*=================================================================================**//**
// A helper class that converts common data extracted from a shared cell instance, 
// a reference attachment, or a type 2 cell, to a DWG block reference object.
*
* @bsiclass                                                     Don.Fu          01/11
+===============+===============+===============+===============+===============+======*/
class           DwgBlockReferenceFromCommonData
{
private:
    AcDbBlockReference*     m_blockReference;
    AcDbObjectId            m_blockId;
    DgnClipData             m_clipData;
    DPoint3d                m_origin;
    RotMatrix               m_matrix;
    double                  m_currentToDefaultScale;
    ConvertFromDgnContext*  m_fromDgnContext;
    ElementHandleCP         m_inputElement;

public:
    DwgBlockReferenceFromCommonData (AcDbBlockReference* insert, AcDbObjectId blockId, const DgnClipData& clip, DPoint3dCR origin, RotMatrixCR matrix, double scale=1.0)
        {
        m_blockReference = insert;
        m_blockId = blockId;
        m_clipData = clip;
        m_origin = origin;
        m_matrix = matrix;
        m_currentToDefaultScale = scale;
        m_fromDgnContext = NULL;
        m_inputElement = NULL;
        }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/2000
+---------------+---------------+---------------+---------------+---------------+------*/
void                SaveBlockReference (ElementHandleR elemHandle, ConvertFromDgnContextR context)
    {
    m_fromDgnContext = &context;
    m_inputElement = &elemHandle;

    DVec3d          xColumn, yColumn, zColumn;
    m_matrix.GetColumns (xColumn, yColumn, zColumn);

    // if user wants to remove z-coordinate in DWG file, do so now:
    bool            isYNegated = false;
    if (context.GetSettings().IsZeroZCoordinateEnforced())
        {
        m_origin.z = 0.0;

        if (fabs(zColumn.z + 1.0) < TOLERANCE_VectorEqual)
            {
            // the insert is on world's negative xy-plane, flip it up
            zColumn.Negate ();
            yColumn.Negate ();

            isYNegated = true;
            }
        }

    RotMatrix       extrusionMatrix;
    RealDwgUtil::RotMatrixFromArbitraryAxis (extrusionMatrix, zColumn);

    extrusionMatrix.MultiplyTranspose (xColumn);
    extrusionMatrix.MultiplyTranspose (yColumn);

    // Assume a square matrix...(i.e. no shear)
    DPoint3d        scale;
    scale.x = xColumn.magnitude ();
    scale.y = yColumn.magnitude ();
    scale.z = zColumn.magnitude ();

    if (isYNegated)
        scale.y = -scale.y;

    // Check for reflection
    if (m_matrix.Determinant() < 0.0)
        scale.y = -scale.y;

    // ACAD can only uniformly scale annotative objects - TFS 349289:
    bool    uniformScaling = fabs(fabs(scale.x) - fabs(scale.y)) < TOLERANCE_ScaleRatio && fabs(fabs(scale.x) - fabs(scale.z)) < TOLERANCE_ScaleRatio;
    if (!uniformScaling)
        this->EnsureBlockDefinitionNonAnnotative ();

    AcGeScale3d     geScale = RealDwgUtil::GeScale3dFromDPoint3d (scale);
    double          annotationScale = 1.0;
    bool            isAnnotative = uniformScaling && this->DecomposeAnnotationScale(geScale, annotationScale) && context.CanSaveAnnotationScale();

    AcGeMatrix3d    originalTransform = m_blockReference->blockTransform();

    AcDbMInsertBlock*   minsert = AcDbMInsertBlock::cast (m_blockReference);
    if (nullptr != minsert)
        {
        /*------------------------------------------------------------------------------------------------------------
        We never create an minsert, but we may be editing it. If the minsert cell is scaled in addition to the original 
        scale, we need to apply the difference to the minsert's spacings such that it will be spaced correctly in ACAD.
        TFS 172632.
        --------------------------------------------------------------------------------------------------------------*/
        AcGeScale3d     spaceScales = geScale * m_blockReference->scaleFactors().inverse();

        if (fabs(spaceScales.sx) > TOLERANCE_ZeroScale)
            minsert->setColumnSpacing (spaceScales.sx * minsert->columnSpacing());
        if (fabs(spaceScales.sy) > TOLERANCE_ZeroScale)
            minsert->setRowSpacing (spaceScales.sy * minsert->rowSpacing());
        }

    m_blockReference->setBlockTableRecord (m_blockId);
    m_blockReference->setRotation (bsiTrig_getPositiveNormalizedAngle (atan2 (xColumn.y, xColumn.x)));
    m_blockReference->setScaleFactors (geScale);

    // set normal before points being set as otherwise it applies transformation by the new normal.
    m_blockReference->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (zColumn));
    m_blockReference->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (m_origin));

    // Note - AutoCAD does not support spatial filter with no points.
    size_t          nClipPoints;
    if (0 != (nClipPoints = m_clipData.m_pointArray.size()))
        {
        double      defaultToCurrentScale = 0.0 != m_currentToDefaultScale ? 1.0 / m_currentToDefaultScale : 1.0;
        double      scaleFromDGN = context.GetScaleFromDGN() * defaultToCurrentScale;

        for (size_t iPoint = 0; iPoint < nClipPoints; iPoint++)
            m_clipData.m_pointArray[iPoint].Scale (m_clipData.m_pointArray[iPoint], scaleFromDGN);

        context.GetTransformFromDGN().Multiply (m_clipData.m_origin);
        m_clipData.m_origin.Scale (m_clipData.m_origin, defaultToCurrentScale);

        if (!m_clipData.m_pointArray[0].IsEqual(m_clipData.m_pointArray[nClipPoints-1]))
            m_clipData.m_pointArray.push_back (m_clipData.m_pointArray[0]);

        m_clipData.m_frontClipZ *= scaleFromDGN;
        m_clipData.m_backClipZ *= scaleFromDGN;

        SetSpatialFilterFromClipBoundary (m_clipData);
        }
    else
        {
        AcDbIndexFilterManager::removeFilter (m_blockReference, AcDbSpatialFilter::desc());
        }

    MSElementDescrCP    elmdscr = elemHandle.GetElementDescrCP ();
    AcDbObjectId        msInsertLayerId;
    if (NULL != elmdscr && 0 == elmdscr->el.ehdr.level)
        {
        if (!(msInsertLayerId = context.GetMSInsertLayer()).isNull())
            {
            m_blockReference->setLayer (msInsertLayerId);
            }
        else
            {
            // TR# 148227 - When creating a block reference from a normal cell, don't allow the block ref to go to frozen level.
            bool    setLayerZero = false;

            msInsertLayerId = m_blockReference->layerId();

            if (msInsertLayerId.isNull())
                setLayerZero = true;
            else
                {
                AcDbLayerTableRecordPointer pLayer (msInsertLayerId, AcDb::kForRead);
                if (Acad::eOk != pLayer.openStatus())
                    setLayerZero = true;
                else
                    setLayerZero = pLayer->isFrozen();
                }

            if (setLayerZero)
                m_blockReference->setLayer (context.GetDatabase()->layerZero());
            }
        }

    context.SaveBlockTagsToDatabase (m_blockReference, elemHandle);

    // save item type to attributes
    ConvertItemTypeToAttribute      itemtypeToAttributes (m_blockReference, elemHandle, context);
    itemtypeToAttributes.Convert ();

    // set annotative after attributes have been attached, or else their annotation sizes may be wrong, particularly after adding viewport annotation scales in post process.
    if (isAnnotative)
        context.AddAnnotationScaleToObject (m_blockReference, annotationScale, elemHandle.GetElementId());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/16
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   EnsureBlockDefinitionNonAnnotative ()
    {
    /*-----------------------------------------------------------------------------------
    This method is to make sure that if we have a non-uniformly scaled block reference,
    the referenced block is non-annotative.  ACAD cannot scale an annotative block non-
    uniformaly. If the block is annotative, make a copy of existing block and set it as
    a non-uniformly scaled, non-annotative block.
    -----------------------------------------------------------------------------------*/
    AcDbSmartBlockTableRecordPointer    oldBlock(m_blockId, AcDb::kForRead);
    if (Acad::eOk != oldBlock.openStatus())
        return  CantOpenObject;

    if (!RealDwgUtil::IsObjectAnnotative(oldBlock))
        return  RealDwgSuccess;

    // if we have previously made a copy for the same block, re-use that copy:
    T_ObjectIdMap&  nonUniformScalingBlocks = m_fromDgnContext->GetBlockListForNonUniformScaling ();

    auto    found = nonUniformScalingBlocks.find (m_blockId);
    if (found != nonUniformScalingBlocks.end())
        {
        m_blockId = found->second;
        return  RealDwgSuccess;
        }

    AcDbSmartBlockTableRecordPointer    newBlock;
    if (Acad::eOk != newBlock.create())
        return  OutOfMemoryError;

    if (Acad::eOk != newBlock->copyFrom(oldBlock))
        return  CantAddNewBlock;

    AcDbBlockTableRecordIterator*   iter = nullptr;
    if (Acad::eOk != oldBlock->newIterator(iter, true, true))
        return  BlockIteratorFailure;

    for (; !iter->done(); iter->step())
        {
        AcDbEntityP     oldEntity = nullptr;
        if (Acad::eOk == iter->getEntity(oldEntity, AcDb::kForRead))
            {
            AcRxObject* newObject = oldEntity->isA()->create ();
            if (nullptr != newObject)
                {
                AcDbEntityP newEntity = AcDbEntity::cast (newObject);
                if (nullptr != newEntity && Acad::eOk == newEntity->copyFrom(oldEntity) && Acad::eOk == newBlock->appendAcDbEntity(newEntity))
                    newEntity->close ();
                else
                    delete newObject;
                }
            oldEntity->close ();
            }
        }

    // set a unique block name:
    if (!newBlock->isAnonymous())
        {
        AcString    newName;
        if (Acad::eOk == oldBlock->getName(newName))
            m_fromDgnContext->DeduplicateBlockName (newName);
        else
            newName.assign (L"*U");

        newBlock->setName (newName.kwszPtr());
        }

    // set the new block non-uniform scaling & non-annotative:
    newBlock->setBlockScaling (AcDbBlockTableRecord::BlockScaling::kAny);
    RealDwgUtil::SetObjectAnnotative (newBlock, false);

    AcDbObjectId    oldBlockId = m_blockId;

    // add the new block to block table and relace the block ID for the block reference
    AcDbBlockTablePointer   blockTable (m_fromDgnContext->GetDatabase()->blockTableId(), AcDb::kForWrite);
    if (Acad::eOk != blockTable.openStatus())
        {
        BeAssert (false && "Failed creating a new block for a non-unitformly scaled cell!");
        return  CantAddNewBlock;
        }

    m_blockId = m_fromDgnContext->AddBlockToBlockTable (blockTable, newBlock, 0);
    
    if (!m_blockId.isValid())
        {
        BeAssert (false && "Failed creating a new block for a non-unitformly scaled cell!");
        delete newBlock;
        return  CantAddNewBlock;
        }

    newBlock->close ();

    // add the old & new block ID's in our lookup list for next run:
    nonUniformScalingBlocks.insert (bpair<AcDbObjectId,AcDbObjectId>(oldBlockId, m_blockId));

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/01
+---------------+---------------+---------------+---------------+---------------+------*/
static void                     ExtractClipFromCell
(
DgnClipData&                    clipData,
ElementHandleCR                 cellElement,
DPoint3dR                       cellOrigin,
RotMatrixR                      cellMatrix,
ConvertFromDgnContextR          context
)
    {
    ClipVectorPtr               clipper;
    ClipPrimitivePtr            clipPrimitive;
    ClipPolygonCP               clipPolygon;

    if (SUCCESS != CellUtil::ClipFromLinkage(clipper, cellElement, NULL, false) ||
        !clipper.IsValid() ||
        clipper->empty() ||
        !(clipPrimitive = clipper->front()).IsValid() ||
        NULL == (clipPolygon = clipPrimitive->GetPolygon()))
        return;

    clipData.m_backClipZ  = clipPrimitive->GetZLow();
    clipData.m_frontClipZ = clipPrimitive->GetZHigh();

    Transform       clipTransform;
    clipPrimitive->GetTransforms (&clipTransform, NULL);

    clipTransform.GetMatrix (clipData.m_matrix);
    clipTransform.GetTranslation (clipData.m_origin);

    Transform       cellTransform;
    cellTransform.SetMatrix (cellMatrix);
    cellTransform.SetTranslation (cellOrigin);

    cellTransform.Multiply (clipData.m_origin);
    clipData.m_matrix.InitProduct (cellMatrix, clipData.m_matrix);

    clipData.m_frontClipOn   = clipPrimitive->ClipZHigh();
    clipData.m_backClipOn    = clipPrimitive->ClipZLow();
    clipData.m_isOutsideClip = clipPrimitive->IsMask();

    // ACAD only has 1 clip loop: either inner or outer, so in case both exist, we choose to take the 1st inner clip:
    size_t          iFirstInnerAt = 0;
    DPoint2dCP      clipPoints = &clipPolygon->front();
    size_t          nClipPoints = clipPolygon->size();

    if (!clipData.m_isOutsideClip)
        {
        for (; iFirstInnerAt < nClipPoints; iFirstInnerAt++)
            {
            if (clipPoints[iFirstInnerAt].IsDisconnect())
                {
                clipData.m_isOutsideClip = true;
                iFirstInnerAt++;
                break;
                }
            }
        }

    // if there is no inner loop, take all points
    if (iFirstInnerAt >= nClipPoints)
        iFirstInnerAt = 0;

    for (size_t iPoint = iFirstInnerAt; iPoint < nClipPoints; iPoint++)
        {
        // ignore rest of the outside loops
        if (clipPoints[iPoint].IsDisconnect())
            break;

        clipData.m_pointArray.push_back (clipPoints[iPoint]);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/12
+---------------+---------------+---------------+---------------+---------------+------*/
static void     TransformToDwg (RotMatrixR matrix, DPoint3dR origin, ConvertFromDgnContextR context)
    {
    // transform DGN readout to DWG:
    context.GetTransformFromDGN().Multiply (origin);

    RotMatrix       localMatrix;
    context.GetLocalTransform().GetMatrix (localMatrix);
    matrix.InitProduct (localMatrix, matrix);
    }


private:
/*------------------------------------------------------------------*//**
* @param nodeP => first node to inspect
* @param mask => mask to search for.
* @return => first node found with specified mask
* @bsimethod                                    EDL 01/01
+----------------------------------------------------------------------*/
VuP                 Vu_findMaskOnMateAroundFace
(
VuP                 nodeP,
VuMask              mask
) const
    {
    VU_FACE_LOOP (currP, nodeP)
        {
        if (vu_getMask  (vu_edgeMate (currP), mask))
            return currP;
        }
    END_VU_FACE_LOOP (currP, nodeP)
    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
@description
1) Find all criss-crosses among lines
2) Expand to the largest face
@param [out] outerLoop dominant face (ONLY FILLED IF THERE ARE SELFINTERSECTIONS WITHIN THE INPUT LOOP)
@param [in]  inputLoop input face, suspected of having self intersections.
@return false if input has no self intersections.  In this case the outputLoop array is NOT filled.
        true if there are self intersections.  In this case the outputLoop is filled.
* @bsimethod                                                    Earlin.Lutz     05/04
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            Vu_simplifyToPrimaryLoop
(
bvector<DPoint2d>               &outerLoop,
bvector<DPoint2d> const         &inputLoop
) const
    {
    VuMask          mVisited, mPrimaryFace;
    double          currArea, maxArea;
    VuArrayP        pFaceArray;
    VuP             pMaxAreaSeed = NULL, pNewSeed = NULL, pCurr = NULL;
    VuSetP          pGraph;
    bool            boolstat = false;
    outerLoop.clear ();

    size_t          numIn = inputLoop.size ();
    if (numIn < 3)
        return false;

    pGraph = vu_newVuSet (0);
    mVisited        = vu_grabMask (pGraph);
    mPrimaryFace    = vu_grabMask (pGraph);
    pFaceArray      = vu_grabArray (pGraph);

    vu_makeLoopFromArray (pGraph, const_cast <DPoint2d *> (&inputLoop[0]), (int)inputLoop.size (), false, false);
    vu_mergeOrUnionLoops (pGraph, VUUNION_PARITY);
    vu_clearMaskInSet (pGraph, mVisited | VU_EXTERIOR_EDGE | mPrimaryFace);

    int         nLoops = 0, nNodes = 0;
    maxArea = 0.0;
    VU_SET_LOOP (pSeed, pGraph)
        {
        nNodes++;
        if (!vu_getMask (pSeed, mVisited))
            {
            nLoops++;

            vu_setMaskAroundFace (pSeed, mVisited);
            currArea = vu_area (pSeed);
            if (currArea <= 0.0)
                {
                vu_setMaskAroundFace (pSeed, VU_EXTERIOR_EDGE);
                }
            else
                {
                if (currArea > maxArea)
                    {
                    maxArea = currArea;
                    pMaxAreaSeed = pSeed;
                    }
                vu_arrayAdd (pFaceArray, pSeed);
                }
            }
        }

    END_VU_SET_LOOP (pSeed, pGraph)

    if ((nLoops == 2 && nNodes == (numIn-1) * 2) || !pMaxAreaSeed)
        goto wrapup;

    vu_setMaskAroundFace (pMaxAreaSeed, mPrimaryFace);

    for (vu_arrayOpen (pFaceArray); vu_arrayRead (pFaceArray, &pCurr); )
        {
        if (pCurr != pMaxAreaSeed && !Vu_findMaskOnMateAroundFace (pCurr, mPrimaryFace))
            vu_setMaskAroundFace (pCurr, VU_EXTERIOR_EDGE);
        }

    // Make sure the primary face seed is not on an edge about to be deleted ...
    pNewSeed = Vu_findMaskOnMateAroundFace (pMaxAreaSeed, VU_EXTERIOR_EDGE);
    if (NULL != pNewSeed)
        {
        int         numOut, i;

        vu_freeEdgesByMaskCount (pGraph, VU_EXTERIOR_EDGE, true, false, true);
        numOut = vu_countEdgesAroundFace (pNewSeed);

        for (i = 0, pCurr = pNewSeed; i < numOut; i++, pCurr = vu_fsucc (pCurr))
            {
            DPoint2d xy;
            vu_getDPoint2d (&xy, pCurr);
            outerLoop.push_back (xy);
            }

        boolstat = true;
        }

wrapup:
    vu_returnMask  (pGraph, mVisited);
    vu_returnMask  (pGraph, mPrimaryFace);
    vu_returnArray (pGraph, pFaceArray);
    vu_freeVuSet (pGraph);
    return  boolstat;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               SetTransformData
(
FilerDataList&              dataList,
int                         dataIndex,
TransformCR                 transform
) const
    {
    for (int iRow=0; iRow < 3; iRow++)
        {
        for (int iColumn=0; iColumn < 4; iColumn++)
            {
            DoubleFilerData* doubleFilerData;
            if (NULL == (doubleFilerData = dynamic_cast <DoubleFilerData *> (dataList[dataIndex + iRow*4 + iColumn])))
                {
                BeAssert (false && L"Error setting matrix for a spatial filter!");
                return BadDataSequence;
                }
            doubleFilerData->SetValue (transform.form3d[iRow][iColumn]);
            }
        }
    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               SetSpatialFilterTransformsAndFrontBack
(
AcDbSpatialFilter*          pSpatialFilter,
TransformCR                 blockTransform,
TransformCR                 worldToClip,
DgnClipData&                clipData,
AcGePoint2dArray&           geClipPoints
) const
    {
    // Since there is no method to set the spatial filter matrices, we have to set them using our RecordingFiler.
    // Here is a dump from the recordingFiler for an AcDbSpatialFilter:

    //     0: kDwgSoftPointerId, ffe03b78 fe0b3961 AcDbDictionary       // AcDbObject:              This is a pointer to the owner of the BlockTableRecord.
    //     1: kDwgUInt32,    1 (0x1)                                    // AcDbObject:              This is the number of reactors. Is followed by the SoftPointerIds of the reactors. In this case there is one.
    //     2: kDwgSoftPointerId, ffe03b78 fe0b3961 AcDbDictionary       // AcDbObject:              This is a pointer to the AcDbDictionary that is a reactor to this AcDbSpatialFilter.
    //     3: kDwgHardOwnershipId, NULL                                 // AcDbObject:              This is a pointer to the extension dictionary, if there is one.
    //     4: kDwgUInt16,    4 (0x4)                                    // AcDbSpatialFilter:       Number of clip points.
    //     5: AcGePoint2d, -10.000000, -5.000000, 0.000000              // AcDbSpatialFilter:       One point for each clip point.
    //     6: AcGePoint2d, 10.000000, -5.000000, 0.000000
    //     7: AcGePoint2d, 10.000000, 5.000000, 0.000000
    //     8: AcGePoint2d, -10.000000, 5.000000, 0.000000
    //     9: kDwgUInt16,    0 (0x0)                                    // AcDbSpatialFilter:       Not sure what this is. ODA code doesn't show it.
    //    10: AcGeVector3d, 0.000000, 0.000000, 1.000000                // AcDbSpatialFilter:       Normal
    //    11: AcGePoint3d, 0.000000, 0.000000, 0.000000                 // AcDbSpatialFilter:       Origin
    //    12: kDwgUInt16,    1 (0x1)                                    // AcDbSpatialFilter:       Enabled
    //    13: kDwgUInt16,    0 (0x0)                                    // AcDbSpatialFilter:       Clipping Front enabled. If 1, followed by the value as KDwgReal.
    //    14: kDwgUInt16,    0 (0x0)                                    // AcDbSpatialFilter:       Clipping Back enabled. If 1, followed by the value as KDwgReal.
    //    15: kDwgReal 1.000000                                         // AcDbSpatialFilter:       12 doubles representing the OriginalInverseBlockXform.
    //    16: kDwgReal 0.000000
    //    17: kDwgReal 0.000000
    //    18: kDwgReal 0.000000
    //    19: kDwgReal 0.000000
    //    20: kDwgReal 1.000000
    //    21: kDwgReal 0.000000
    //    22: kDwgReal 0.000000
    //    23: kDwgReal 0.000000
    //    24: kDwgReal 0.000000
    //    25: kDwgReal 1.000000
    //    26: kDwgReal 0.000000
    //    27: kDwgReal 1.000000                                         // AcDbSpatialFilter:       12 doubles representing the inverse of what you get calling getClipSpaceToWCSMatrix.
    //    28: kDwgReal 0.000000
    //    29: kDwgReal 0.000000
    //    30: kDwgReal 20.000000
    //    31: kDwgReal 0.000000
    //    32: kDwgReal 1.000000
    //    33: kDwgReal 0.000000
    //    34: kDwgReal -5.000000
    //    35: kDwgReal 0.000000
    //    36: kDwgReal 0.000000
    //    37: kDwgReal 1.000000
    //    38: kDwgReal 0.000000

    RecordingFiler filer (40);
    filer.RecordData (pSpatialFilter);
#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("pSpatialFilter in SetSpatialFilterFromClipBoundary");
#endif

    FilerDataList&      dataList        = filer.GetDataList();
    UInt32FilerData*    reactorCount;

    if (NULL == (reactorCount= dynamic_cast <UInt32FilerData *> (dataList[1])))
        {
        BeAssert (false && L"Error at begining of DWG filer recorder!");
        return BadDataSequence;
        }
    UInt32              sequenceStart    = 1 + reactorCount->GetValue();

    assert (NULL != dynamic_cast <HardOwnershipIdFilerData *> (dataList[sequenceStart + 1]));
    assert (NULL != dynamic_cast <UInt16FilerData *>          (dataList[sequenceStart + 2]));

    // If four rectangular points are passed to AcDbSpatialFilter::setDefinition, it might simplify to the two corner points.
    UInt16FilerData*    numPointData     = dynamic_cast <UInt16FilerData *>(dataList[sequenceStart+2]);
    if (NULL == numPointData)
        {
        BeAssert (false && L"DWG filer error in a spatial filter!");
        return BadDataSequence;
        }

    int     numPointsFound      = numPointData->GetValue();
    int     numPointsExpected   = geClipPoints.length();
    if ( (numPointsFound != numPointsExpected) && ( (numPointsFound != 2) || (numPointsExpected != 4)) )
        {
        BeAssert (false && L"DWG filer error in a spatial filter: number of clipping points!");
        return BadDataSequence;
        }

    UInt32 afterPoints = sequenceStart + 3 + numPointsFound;
    BeAssert (nullptr != dynamic_cast <UInt16FilerData *>          (dataList[afterPoints]));
    BeAssert (nullptr != dynamic_cast <Vector3dFilerData *>        (dataList[afterPoints + 1]));   // Normal
    BeAssert (nullptr != dynamic_cast <Point3dFilerData *>         (dataList[afterPoints + 2]));   // Origin
    BeAssert (nullptr != dynamic_cast <UInt16FilerData *>          (dataList[afterPoints + 3]));   // Enabled
    BeAssert (nullptr != dynamic_cast <UInt16FilerData *>          (dataList[afterPoints + 4]));   // Front Clip enabled

    // in the calling routine, we set the front clip to off.
    UInt16FilerData*    clipFrontEnabledData = dynamic_cast <UInt16FilerData *>(dataList[afterPoints + 4]);
    if ( (NULL == clipFrontEnabledData) || (0 != clipFrontEnabledData->GetValue()) )
        {
        BeAssert (false && L"DWG filer error in a spatial filter: front clip enabled!");
        return BadDataSequence;
        }
    // If we want front clip on, turn it on and insert the value.
    if (clipData.m_frontClipOn)
        {
        clipFrontEnabledData->SetValue (1);
        filer.InsertEntryAt (afterPoints + 5, new DoubleFilerData (clipData.m_frontClipZ));
        afterPoints++;
        }

    // do the same thing for the back clip.
    UInt16FilerData*    clipBackEnabledData = dynamic_cast <UInt16FilerData *>(dataList[afterPoints + 5]);
    if ( (NULL == clipBackEnabledData) || (0 != clipBackEnabledData->GetValue()) )
        {
        BeAssert (false && L"DWG filer error in a spatial filter: back clip enabled!");
        return BadDataSequence;
        }
    // If we want Back Clip on, turn it on and insert the value.
    if (clipData.m_backClipOn)
        {
        clipBackEnabledData->SetValue (1);
        filer.InsertEntryAt (afterPoints + 6, new DoubleFilerData (clipData.m_backClipZ));
        afterPoints++;
        }

    // we've located the start of the two transforms. Set the data for them.
    UInt32      blockXFormStart = afterPoints + 6;
    if (dataList.size() < (blockXFormStart + 24))
        {
        BeAssert (false && L"DWG filer error in a spatial filter: block transforms!");
        return BadDataSequence;
        }

    // now set the transform.
    RealDwgStatus status;
    if (RealDwgSuccess != (status = SetTransformData (dataList, blockXFormStart, blockTransform)))
        return status;

    if (RealDwgSuccess != (status = SetTransformData (dataList, blockXFormStart+12, worldToClip)))
        return status;

    // play back the filer to save the data
    Acad::ErrorStatus readStatus = filer.PlaybackData (pSpatialFilter);
    BeAssert (Acad::eOk == readStatus && L"DWG filer error setting spatial filer data");

#if defined (REALDWG_FILER_DEBUG)
    RecordingFiler confirm (24);
    confirm.RecordData (pSpatialFilter);
    confirm.DumpList ("pSpatialFilter after setting transforms");
#endif

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------*//**
* @bsimethod                                                    RayBentley       08/98
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   SetSpatialFilterFromClipBoundary
(
DgnClipData&                    clipData
) const
    {
    AcDbSpatialFilter*          pSpatialFilter;
    bool                        addFilter = false;

    if (NULL == (pSpatialFilter = ToDgnExtBlockReference::GetSpatialFilter(m_blockReference, AcDb::kForWrite)))
        {
        pSpatialFilter = new AcDbSpatialFilter();
        addFilter = true;
        }

    AcGePoint2dArray            geClipPoints;
    DPoint2dArray               fixedPoints;
    if (Vu_simplifyToPrimaryLoop (fixedPoints, clipData.m_pointArray))
        {
        DIAGNOSTIC_PRINTF ("Self Intersecting Clip boundary detected and simplified to dominant loop\n");

        size_t                  nFixedPoints = fixedPoints.size ();
        if (fixedPoints[0].IsEqual(fixedPoints[nFixedPoints-1]))
            nFixedPoints--;

        for (size_t i = 0; i < nFixedPoints; i++)
            geClipPoints.append (RealDwgUtil::GePoint2dFromDPoint2d (fixedPoints[i]));
        }
    else
        {
        size_t                  nClipPoints = clipData.m_pointArray.size ();
        if (clipData.m_pointArray[0].IsEqual (clipData.m_pointArray[nClipPoints-1]))
            nClipPoints--;

        for (size_t i = 0; i < nClipPoints; i++)
            geClipPoints.append (RealDwgUtil::GePoint2dFromDPoint2d (clipData.m_pointArray[i]));
        }

    DVec3d      column2;
    clipData.m_matrix.GetColumn (column2, 2);
    column2.Normalize ();

    // Note: Whenever I tried to set a front clip value with setDefinition, it returned eInvalidInput. So I changed it to set the front/back clipping in SetSpatialFilterTransformsAndFrontBack
    Acad::ErrorStatus errorStatus;
    if (Acad::eOk != (errorStatus = pSpatialFilter->setDefinition (geClipPoints, RealDwgUtil::GeVector3dFromDPoint3d (column2), 0.0,
                                                    ACDB_INFINITE_XCLIP_DEPTH, ACDB_INFINITE_XCLIP_DEPTH, Adesk::kTrue)))
        {
        BeAssert (false && L"Error setting spatial filter definition");
        if (!addFilter)
            pSpatialFilter->close();
        return CantCreateSpatialFilter;
        }

    Transform   clipToWorld;
    clipToWorld.SetTranslation (clipData.m_origin);
    clipToWorld.SetMatrix (clipData.m_matrix);

    // Fix for TR# 164922 - The block transform stored in the spatial filter does not include the insertion origin (if not xRef).
    // Fix for TR# 298438 - RealDWG seems to do the opposite to the above scenario: block transform includes the insertion point while xref does not!!
    AcGeMatrix3d    blockGeMatrix = m_blockReference->blockTransform();
    AcGeMatrix3d    insertionTranslation;

    AcDbBlockTableRecordPointer pBlockTR (m_blockReference->blockTableRecord(), AcDb::kForRead);
    if (Acad::eOk == pBlockTR.openStatus() && pBlockTR->isFromExternalReference())
        {
        AcGePoint3d     blockOrigin = pBlockTR->origin();

        insertionTranslation.setToTranslation (AcGeVector3d(blockOrigin.x, blockOrigin.y, blockOrigin.z));
        blockGeMatrix.postMultBy (insertionTranslation);
        }

    Transform       blockTransform;
    RealDwgUtil::TransformFromGeMatrix3d (blockTransform, blockGeMatrix);
    Transform       inverseBlockTransform;
    inverseBlockTransform.InverseOf (blockTransform);

    Transform       worldToClip;
    worldToClip.InverseOf (clipToWorld);

    RealDwgStatus   status = SetSpatialFilterTransformsAndFrontBack (pSpatialFilter, inverseBlockTransform, worldToClip, clipData, geClipPoints);

    pSpatialFilter->setInverted (clipData.m_isOutsideClip);

    if (addFilter)
        {
        if (RealDwgSuccess == status)
            {
            // AcDbIndexFilterManager::addFilter prerequisites db residency of the output block reference
            if (!m_blockReference->objectId().isValid())
                m_fromDgnContext->AddEntityToCurrentBlock (m_blockReference, m_inputElement->GetElementId());

            Acad::ErrorStatus es = AcDbIndexFilterManager::addFilter (m_blockReference, pSpatialFilter);

            if (Acad::eOk != es)
                DIAGNOSTIC_PRINTF ("Failed adding spatial filter for xRef! [%ls]\n", acadErrorStatusText(es));

            pSpatialFilter->close();
            }
        else
            {
            delete pSpatialFilter;
            }
        }
    else
        {
        pSpatialFilter->close();
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/10
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    DecomposeAnnotationScale
(
AcGeScale3d&            overallScale,
double&                 annotationScale
)
    {
    /*-------------------------------------------------------------------------------------------------------
    We have got an overall block scale composed of annotation scale and instance scale.  We need to decompose
    the scale and get the instance scale to return.

    We only need to do this for annotative block reference.  Unlike in MicroStation, in ACAD all the instances 
    of an annotative block definition is annotative.  An xRef is not annotative.  So we will ignore instances 
    of an xRef or a non-annotative definition.

    There are two scenarios to be taken in consideration:
        1) the input instance element has an annotation scale - invert the annotation scale
        2) the instance itself is not annotative, but its definition is - invert the model annotation scale
        
    We do not need to explicitly set block references for annotation scales, as they only inherit the setting
    from their definitions.  There is no override flags.
    -------------------------------------------------------------------------------------------------------*/
    annotationScale = 1.0;

    bool                hasAnnotation = false;
    if (REFERENCE_ATTACH_ELM == m_inputElement->GetElementType() || !this->IsBlockDefinitionAnnotative())
        return  hasAnnotation;

    DgnModelP           model = m_inputElement->GetDgnModelP ();
    IAnnotationHandler* annoHandler = dynamic_cast <IAnnotationHandler*> (&m_inputElement->GetHandler());
    if (NULL != annoHandler)
        {
        if (annoHandler->HasAnnotationScale(&annotationScale, *m_inputElement))
            {
            // case 1: the input instance element is annotative, invert the annotation scale
            hasAnnotation = true;
            }
        else if (NULL != model)
            {
            // case 2: the instance element is not annotative, but its definition is, invert the model scale
            annotationScale = model->GetModelInfo().GetAnnotationScaleFactor ();
            hasAnnotation = true;
            }

        this->UpdateAnnotationScaleForPaperspaceBlockRef (annotationScale);
        }

    if (hasAnnotation && annotationScale > TOLERANCE_ZeroScale && fabs(annotationScale - 1.0) > TOLERANCE_ZeroScale)
        {
        // invert the output scale
        overallScale.setToProduct (overallScale, 1.0 / annotationScale);
        return  hasAnnotation;
        }

    /*-----------------------------------------------------------------------------------
    We probably no longer need below logic to handle round tripping annotative blocks as
    we should have handled them in above cases.  However, if there is ever a block which
    is produced from a non-annotation supported element, i.e. in case of NULL==annoHandler,
    we'd still like to catch it and correct the scale.

    AcDbBlockReference::setScaleFactors apparently compounds insert scale with annotation
    scale, thus results in an overall block scale that is not what we intend to have.

    The effective annotation scale may not necessarily be CANNOSCALE because an annotative
    object that does not support CANNOSCALE may still get drawn at a scaled size, depending
    upon the scale list it supports.  A safe way to bypass RealDWG's annotation scale 
    compounding is to attempt to scale the block reference by scale factor of 1, and 
    immediately followed by getting the scale back from the block reference.  If our unit 
    scale gets back as a different scale, we know for sure RealDWG has applied an annotation 
    scale to it.  We need to decompose the overall scale to produce our intended scale factor.
    -----------------------------------------------------------------------------------*/
    AcGeScale3d     unitScale (1., 1., 1.);
    m_blockReference->setScaleFactors (unitScale);

    AcGeScale3d     annoScale = m_blockReference->scaleFactors ();
    if (!annoScale.isEqualTo(unitScale) && !annoScale.isEqualTo(AcGeScale3d(0., 0., 0.)))
        overallScale.setToProduct (overallScale, annoScale.inverse());

    return  hasAnnotation;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsBlockDefinitionAnnotative ()
    {
    AcDbBlockTableRecordPointer     block(m_blockId, AcDb::kForRead);
    if (Acad::eOk == block.openStatus())
        {
        /*-------------------------------------------------------------------------------------------------
        If the block is a proxy of a dynamic block, check the dynamic block for annotative.
        Since AcDbDynBlockReference::isDynamicBlock does not work so we extract the dynamic block ID from
        the proxy block (i.e. this anonymous block "*U").
        ------------------------------------------------------------------------------------------------*/
        AcDbObjectId                dynBlockId;
        if (block->isAnonymous() && this->ExtractDynamicBlockId(dynBlockId, block))
            {
            AcDbDynBlockTableRecord         dynBlock (dynBlockId);
            if (dynBlock.isDynamicBlock())
                {
                AcDbBlockTableRecordPointer     blockRecord (dynBlock.blockTableRecordId(), AcDb::kForRead);
                if (Acad::eOk == blockRecord.openStatus())
                    return  RealDwgUtil::IsObjectAnnotative (blockRecord);
                }
            }
        // check the normal block
        return  RealDwgUtil::IsObjectAnnotative (block);
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            UpdateAnnotationScaleForPaperspaceBlockRef (double& annotationScale)
    {
    /*---------------------------------------------------------------------------------------------------
    When saving a DWG file, RealDWG seems to add CANNOSCALE to all block references, hence we do not need
    to explicitly add the scale in model space.  Annotative objects in layouts are also added with CANNOSCALE.
    Since the layout annotation scale is 1:1, we need to add scale 1:1 to the supported list of the block 
    reference.
    ---------------------------------------------------------------------------------------------------*/
    ModelId     defaultModelId = m_fromDgnContext->GetFile()->GetDefaultModelId ();
    DgnModelP   model = m_fromDgnContext->GetModel ();        
    if (NULL != model && defaultModelId != model->GetModelId())
        {
        AcDbObjectContextInterface* objectContextInterface = ACRX_PE_PTR (m_blockReference, AcDbObjectContextInterface);
        AcDbAnnotationScale const*  annoScale1v1 = m_fromDgnContext->GetAnnotationScale1vs1 ();
        if (NULL == objectContextInterface || NULL == annoScale1v1)
            return;

        if (!objectContextInterface->hasContext(m_blockReference, *annoScale1v1))
            {
            // RealDWG does not allow us to add a new context to a non-database resident object
            if (m_blockReference->objectId().isNull() &&
                (m_fromDgnContext->AddEntityToCurrentBlock(m_blockReference, m_inputElement->GetElementId())).isNull())
                DIAGNOSTIC_PRINTF ("Failed adding a block reference into database ID=%I64d!\n", m_inputElement->GetElementId());

            Acad::ErrorStatus       es = objectContextInterface->addContext (m_blockReference, *annoScale1v1);
            if (Acad::eOk == es)
                {
                // scale 1:1 has been successfully added to the block reference - remove the annotation scale:
                annotationScale = 1.0;
                }
            else
                {
                DIAGNOSTIC_PRINTF ("Error adding annotation scale 1:1 to block reference in paperspace! [%ls]\n", acadErrorStatusText(es));
                // handle error: let the caller invert the overall scale to compensate the annotation scale
                DgnModelP           defaultModel = m_fromDgnContext->GetFile()->FindLoadedModelById (defaultModelId);
                if (NULL != defaultModel)
                    annotationScale = defaultModel->GetModelInfo().GetAnnotationScaleFactor ();
                }
            }
        else
            {
            // scale 1:1 is already supported by the block reference - remove the annotation scale:
            annotationScale = 1.0;
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ExtractDynamicBlockId (AcDbObjectId& dynBlockId, AcDbBlockTableRecord* proxyBlock)
    {
    // don't bother to extract the dynamic block ID from the proxy block if the insert entity has no AcDbBlockRpresentation extension dictionary
    AcDbDictionaryPointer   extDictionary (m_blockReference->extensionDictionary(), AcDb::kForRead);
    if (Acad::eOk != extDictionary.openStatus() || !extDictionary->has(L"AcDbBlockRepresentation"))
        return  false;

    AcDbHandle  blockHandle;
    if (RealDwgXDataUtil::GetDynamicBlockHandleFromProxyBlock(blockHandle, proxyBlock) &&
        Acad::eOk == m_fromDgnContext->GetDatabase()->getAcDbObjectId(dynBlockId, false, blockHandle, 0))
        {
        return  dynBlockId.isValid ();
        }

    return  false;
    }


};  // DwgBlockReferenceFromCommonData


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          12/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtSharedCellInstance : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    SharedCellHandler*  sharedCellHandler = dynamic_cast <SharedCellHandler*> (&elemHandle.GetHandler());
    if (NULL == sharedCellHandler)
        return  BadElementHandler;

    // apply the option to drop nested shared cells
    if (context.GetSettings().DropNestedCells() && !context.IsCurrentBlockLayout())
        {
        DropGeometry    dropCell ((DropGeometry::Options) (DropGeometry::OPTION_SharedCells | DropGeometry::OPTION_Complex));
        dropCell.SetSharedCellOptions (DropGeometry::SHAREDCELL_Geometry);
        return  context.DropElementToDwg (acObject, existingObject, elemHandle, dropCell);
        }

    WChar               cellName[MAX_CELLNAME_LENGTH];
    if (SUCCESS != sharedCellHandler->ExtractName(cellName, _countof(cellName), elemHandle))
        cellName[0] = 0;

    /*-------------------------------------------------------------------------------------------------------------
    Round trip an original DWG point back as a point entity or a block reference:

    1) For a shared cell name POINT_DISPLAY, i.e. a DWG point with no thickness, it is always a point entity.  This
       allows us to round trip an unextruded point as a point entity.
    2) For POINT_DISPLAY_xxx, i.e. an extruded point shared cell, we'd like to keep it as a block reference if we
       are saving a DGN file as DWG, or we are editing a DWG file but the point display shared cell is no longer 
       anonymous. This allows us to round trip an extruded point as an extruded object.
    3) If we are editing a DWG file, we generally would like to round trip an extruded point display cell as a point
       with the exceptions in above 2).
    -------------------------------------------------------------------------------------------------------------*/
    bool                blockWanted = !context.SavingChanges() || !CellUtil::IsAnonymous(elemHandle);
    if (RealDwgUtil::IsPointCellName(cellName, blockWanted))
        {
        PointSharedCellToAcDbPoint  sharedCell2Point(elemHandle, cellName);
        return  sharedCell2Point.ConvertToDwg (acObject, existingObject, context);
        }

    // SharedCellHandler::GetDefinition makes 2 attempts: get ID from linkage and search by name:
    ElementRefP         definitionElemRef = sharedCellHandler->GetDefinition (elemHandle, *context.GetFile());
    if (NULL == definitionElemRef)
        return CantFindSharedCellDefinition;           // Cant create this insert... no definition.

    ElementId           definitionElemId = definitionElemRef->GetElementId ();
    if (0 == definitionElemId || INVALID_ELEMENTID == definitionElemId)
        return CantFindSharedCellDefinition;           // Cant create this insert... no definition.

    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbBlockReference::desc());
    AcDbBlockReference* blockReference = AcDbBlockReference::cast (acObject);
    if (NULL == blockReference)
        return  NullObject;

    AcDbObjectId        blockId = blockReference->blockTableRecord ();
    DPoint3d            origin;
    RotMatrix           matrix;
    double              scale = 1.0;
    RealDwgStatus       status = ExtractBlockFromSharedCell (blockId, blockReference, origin, matrix, scale, elemHandle, definitionElemId, definitionElemRef, sharedCellHandler, context);

    if (RealDwgSuccess == status)
        {
        DgnClipData     clipData;
        DwgBlockReferenceFromCommonData::ExtractClipFromCell (clipData, elemHandle, origin, matrix, context);
        DwgBlockReferenceFromCommonData::TransformToDwg (matrix, origin, context);

        DwgBlockReferenceFromCommonData     blockRefConverter (blockReference, blockId, clipData, origin, matrix, scale);
        blockRefConverter.SaveBlockReference (elemHandle, context);
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/2000
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ExtractBlockFromSharedCell
(
AcDbObjectId&           blockId,
AcDbBlockReference*     blockReference,
DPoint3dR               origin,
RotMatrixR              matrix,
double&                 currentToDefaultScale,
ElementHandleR          elemHandle,
ElementId               definitionElemId,
ElementRefP             definitionElemRef,
SharedCellHandler*      sharedCellHandler,
ConvertFromDgnContextR  context
) const
    {
    bool                newBlockCreated = false;
    if (blockId.isNull() || definitionElemId != context.ElementIdFromObjectId(blockId))
        {
        AcDbObjectId    definitionObjectId = context.ExistingObjectIdFromElementId (definitionElemId);
        if (definitionObjectId.isNull())
            {
            // try to create a new block table record from type 34
            ElementHandle           definitionElemHandle (definitionElemRef);
            if (!definitionElemHandle.IsValid())
               {
               DIAGNOSTIC_PRINTF ("Shared Cell: %I64d with unfound Definition ignored\n", elemHandle.GetElementCP()->ehdr.uniqueId);
               return CantFindSharedCellDefinition;
               }

            AcDbBlockTableRecord*   newBlock = NULL;
            StatusInt   saveToDatabaseStatus = context.CreateObjectFromElement((AcDbObjectP&)newBlock, definitionElemHandle);

            if (BSISUCCESS == saveToDatabaseStatus && NULL != newBlock)
                {
                // set the block units only if it's not yet closed - TFS 348861
                if (context.GetSettings().SaveBlockUnitsFromFileUnits() && newBlock->isNewObject())
                    newBlock->setBlockInsertUnits (RealDwgUtil::AcDbUnitsValueFromDgnUnits(context.GetStandardTargetUnits()));

                blockId = newBlock->objectId ();
                newBlock->close ();
                newBlockCreated = true;
                }

            if (blockId.isNull())
                return CantSaveNestedDefinition;
            else
                definitionObjectId = blockId;
            }

        if (definitionObjectId.isValid())
            blockId = definitionObjectId;
        else
            return DefectiveBlockDefinition;
       }

    sharedCellHandler->GetTransformOrigin (elemHandle, origin);
    CellUtil::ExtractRotation (matrix, elemHandle);

    Transform           inverseCellTransform;
    context.SaveTagsAsAttributeDefinitions (blockId, context.ComputeInverseCellTransform(inverseCellTransform, origin, matrix), elemHandle);

    // Shared cells will be stored using units from defaultmodelRef - If the units for this one are different then
    // we'll need to apply scale appropriately.  (TR# 131310).
    // An exception is when the block definition is newly created above, in which case current model's, as opposed
    // to the default model's, units have applied to the block.  TR 283170 helped to uncover this problem.
    if (!newBlockCreated)
        {
        currentToDefaultScale = context.GetUnitScaleFromCurrentModelToTarget ();
        if (1.0 != currentToDefaultScale)
            matrix.ScaleColumns (matrix, currentToDefaultScale, currentToDefaultScale, currentToDefaultScale);
        }

    SCOverride const*   override = sharedCellHandler->GetSharedCellOverrides (elemHandle);
    if (NULL != override && (override->level || override->color || override->style || override->weight))
        {
        // Schedule post processing to handle the overrides.
        context.PostProcessingRequired (ElementHandle(), blockId);
        }

    return  RealDwgSuccess;
    }

}; // ToDwgExtSharedCellInstance


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          01/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtType2Cell : public ToDwgExtension
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    Type2Handler*       type2Handler = dynamic_cast <Type2Handler*> (&elemHandle.GetHandler());
    if (NULL == type2Handler)
        return  BadElementHandler;

    DropGeometry    dropCell ((DropGeometry::Options) (DropGeometry::OPTION_SharedCells | DropGeometry::OPTION_Complex));
    dropCell.SetSharedCellOptions (DropGeometry::SHAREDCELL_Geometry);

    // Drop Triforma DEM cell to the first visible cell.
    if (context.GetSettings().DropTriformaCells() && context.IsTriformaDrawing() && context.IsTriformaDrawingElement(elemHandle))
        return  this->DropTriformaCellsToVisible (elemHandle, context);
    else if ((context.DropSectionCells() && this->IsSectionCell(elemHandle)) ||
             (context.GetSettings().DropNestedCells() && !context.IsCurrentBlockLayout()))
        return  context.DropElementToDwg (acObject, existingObject, elemHandle, dropCell);
    
    return this->Type2CellToBlockReference (acObject, existingObject, elemHandle, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   Type2CellToBlockReference (AcDbObjectP& acObject, AcDbObjectP existingObject, ElementHandleR inElement, ConvertFromDgnContextR context) const
    {
    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbBlockReference::desc());
    AcDbBlockReference* blockReference = AcDbBlockReference::cast (acObject);
    if (NULL == blockReference)
        return  NullObject;

    AcDbObjectId        blockId = blockReference->blockTableRecord ();
    DPoint3d            origin;
    RotMatrix           matrix;
    RealDwgStatus       status = ExtractBlockFromType2Cell (blockId, blockReference, origin, matrix, inElement, context);

    if (RealDwgSuccess == status)
        {
        // Check visibility which is not checked in UpdateEntityPropertiesFromElement for a type 2 cell header - TR 267618.
        if (inElement.GetElementCP()->hdr.dhdr.props.b.invisible)
            blockReference->setVisibility (AcDb::kInvisible);

        DgnClipData     clipData;
        DwgBlockReferenceFromCommonData::ExtractClipFromCell (clipData, inElement, origin, matrix, context);
        DwgBlockReferenceFromCommonData::TransformToDwg (matrix, origin, context);

        DwgBlockReferenceFromCommonData     blockRefConverter (blockReference, blockId, clipData, origin, matrix);
        blockRefConverter.SaveBlockReference (inElement, context);
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/2000
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ExtractBlockFromType2Cell
(
AcDbObjectId&           blockId,
AcDbBlockReference*     blockReference,
DPoint3dR               origin,
RotMatrixR              matrix,
ElementHandleR          elemHandle,
ConvertFromDgnContextR  context
) const
    {
    // allocate memory to reduce stack overflow in deeply nested cells.
    WCharP              cellName = (WCharP) calloc (MAX_CELLNAME_LENGTH, sizeof(WChar));
    if (NULL == cellName)
        return  OutOfMemoryError;

    // Triforma element handlers are expected to handle their ToDwg extensions, but when absent, we try to save as much of their data as we can.
    bool                triformaForm = context.IsTriformaForm (elemHandle);
    RealDwgStatus       status = RealDwgSuccess;

    CellUtil::GetCellName (cellName, MAX_CELLNAME_LENGTH, *elemHandle.GetElementCP());
    context.ExtractTransformFromCell (origin, matrix, elemHandle);

    bool            maintainCellHeader = NeedToMaintainCellHeader (cellName, elemHandle, context);
    bool            matchFound = false;

    if (blockId.isNull())
        {
        // No matching if maintaining cells, no Matching within a shared cell definition.
        if (!maintainCellHeader && context.IsCurrentBlockLayout())
            {
            matchFound = (0 != wcscmp (cellName, L"DWG Proxy Entity")) && context.FindMatchingCellBlock (&blockId, elemHandle, cellName);
            }

        // Create block if it does not yet exist.
        if (blockId.isNull())
            {
            AcString    name (cellName);
            blockId = context.CreateAndAddBlockHeader (name, elemHandle);
            }

        if (maintainCellHeader)     // Add XData with original cell name.
            RealDwgXDataUtil::SetMicroStationXDataStringByKey (blockReference, StringConstants::XDataKey_CellNameLinkage, cellName, context.GetDatabase());

        bool        saveIgnoreTriformaNodes = context.GetIgnoreTriformaNodes();

        context.SetIgnoreTriformaNodes (saveIgnoreTriformaNodes || (context.IsTriformaNode(elemHandle) && !context.IsStructuralTriformaNode(elemHandle)));
        if (!matchFound)
            {
            if (triformaForm)
                {
                StatusInt   appStatus = context.BlockFromApplicationData (blockId, elemHandle);
                if (SUCCESS != appStatus)
                    status = context.BlockFromTriformaElements (blockId, elemHandle, origin, matrix);
                }
            else
                {
                status = context.BlockFromCellElement (blockId, elemHandle);
                }
            }
        context.SetIgnoreTriformaNodes (saveIgnoreTriformaNodes);
        }
    else if (maintainCellHeader && blockId.isValid())
        {
        status = ReplaceBlockChildren (blockId, elemHandle, context);
        if (RealDwgSuccess == status)
            RealDwgXDataUtil::SetMicroStationXDataStringByKey (blockReference, StringConstants::XDataKey_CellNameLinkage, cellName, context.GetDatabase());
        }

    free (cellName);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    IsSectionCell (ElementHandleCR inElement) const
    {
    MSElementCP     pElement = inElement.GetElementCP ();
    WChar           name[512] = { 0 };

    // Note - this test makes me puke, but I don't want to make a string object to do the compare.
    return  CELL_HEADER_ELM == pElement->ehdr.type &&
            BSISUCCESS == LinkageUtil::ExtractNamedStringLinkageByIndex(name, 512, STRING_LINKAGE_KEY_Name, 0, pElement) &&
            0 == wcscmp (name, L"SMTSCT");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/02
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    IsLightingCell
(
WCharCP               pName
) const
    {
    return 0 == wcscmp (pName, NAME_DistanceLight) ||
           0 == wcscmp (pName, NAME_PointLight) ||
           0 == wcscmp (pName, NAME_SpotLight);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ReplaceBlockChildren
(
AcDbObjectId&           blockId,
ElementHandleR          elemHandle,
ConvertFromDgnContextR  context
) const
    {
    // For now, only allow PowerPID to modify cell definition.
    if (!context.IsBmfCadElement (elemHandle))
        return  RealDwgSuccess;

    MSElementDescrCP    cellDescr = elemHandle.GetElementDescrCP ();
    if (NULL == cellDescr || CellContainsExistingElement(cellDescr, context))
        return  RealDwgSuccess;

    AcDbBlockTableRecordPointer pBlockTR (blockId, AcDb::kForWrite);
    if (Acad::eOk != pBlockTR.openStatus())
        return  RealDwgSuccess;

    bool                            erasedChildren = false;
    bool                            hasChildren = false;
    AcDbBlockTableRecordIterator*   pEntIter = NULL;

    if (Acad::eOk == pBlockTR->newIterator(pEntIter, true, true))
        {
        hasChildren = true;

        AcDbEntityP         entity = NULL;
        for (; !pEntIter->done(); pEntIter->step())
            {
            if (Acad::eOk == pEntIter->getEntity(entity, AcDb::kForWrite))
                {
                entity->erase ();
                entity->close ();
                erasedChildren = true;
                }
            }

        delete pEntIter;
        }

    pBlockTR->close ();

    if (hasChildren && !erasedChildren)
        {
        DIAGNOSTIC_PRINTF ("Block is not changed from cell (ID=%64d).\n", elemHandle.GetElementCP()->ehdr.uniqueId);
        return  RealDwgSuccess;
        }

    /*-----------------------------------------------------------------------------------
    We have removed the first level children of the block, which will be filled will new
    children, including nested blocks.  To create new nested blocks, we need to clear out 
    the input element ID's.  We need a dup cell to do that.
    -----------------------------------------------------------------------------------*/
    EditElementHandle   copyEeh;
    MSElementDescrP     copyDescr = NULL;
    if (BSISUCCESS == cellDescr->Duplicate(&copyDescr, true, false))
        copyEeh.SetElementDescr (copyDescr, true, false);

    if (copyEeh.IsValid())
        ClearCellChildElementIds (copyEeh.GetElementDescrP());

    return context.BlockFromCellElement (blockId, copyEeh.IsValid() ? copyEeh : elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    CellContainsExistingElement
(
MSElementDescrCP        pCellElmdscr,
ConvertFromDgnContextR  context
) const
    {
    /*-----------------------------------------------------------------------------------
    It's troublesome to replace existing entities in a block, so for now we only handle
    the case where an application replaces existing children with new elements. In the
    future when needed we may allow full editing of block definition by resolving problems
    associated with database residency of existing entities.  For now this suffices the
    requirement by PowerPID.
    -----------------------------------------------------------------------------------*/
    for (MSElementDescrCP pChildEd = pCellElmdscr->h.firstElem; NULL != pChildEd; pChildEd = pChildEd->h.next)
        {
        if (context.ExistingObjectIdFromElementId(pChildEd->el.ehdr.uniqueId))
            return  true;

        if (CELL_HEADER_ELM == pChildEd->el.ehdr.type && CellContainsExistingElement(pChildEd, context))
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            ClearCellChildElementIds (MSElementDescrP elmdscr) const
    {
    for (MSElementDescrP thisElem = elmdscr; NULL != thisElem; thisElem = thisElem->h.next)
        {
        if (thisElem->el.ehdr.isComplexHeader)
            return ClearCellChildElementIds (thisElem->h.firstElem);

        thisElem->el.ehdr.uniqueId = 0;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    NeedToMaintainCellHeader
(
WCharP                  wName,
ElementHandleR          elemHandle,
ConvertFromDgnContextR  context
) const
    {
    bool        hasAppData = false;

    if (context.GetIsSavingApplicationData())
        {
        if (SUCCESS == context.ExtractApplicationLinkageFromElement (NULL, NULL, elemHandle.GetElementCP()) ||
            RealDwgXDataUtil::GetXDataFromXAttributes (NULL, elemHandle.GetElementRef(), context.GetDatabase()))
            hasAppData = true;
        }

    if (hasAppData)
        {
        if (0 == wName[0])
            wcscpy (wName, NULLCELLNAME);

        return  true;
        }

    if (context.GetSettings().SaveMicroStationSettings())
        {
        if (IsLightingCell(wName))
            {
            BeAssert (false && L"lighting cells should no longer be saved as blocks!!");
            return  true;
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   DropTriformaCellsToVisible (ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    MSElementDescrP firstChild = inElement.GetElementDescrCP()->h.firstElem;
    if (NULL == firstChild)
        return  MstnElementUnacceptable;

    MSElementDescrP elemChain = NULL;
    if (BSISUCCESS != firstChild->Duplicate(&elemChain, true, false))
        return  OutOfMemoryError;

    this->DropTriformaCellsToVisible (&elemChain);

    EditElementHandle   eeh (elemChain, true, false);
    StatusInt           status = context.SaveElementChainToDatabase (eeh, &inElement, NULL);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/07
+---------------+---------------+---------------+---------------+---------------+------*/
void            DropTriformaCellsToVisible (MSElementDescrH ppElmdscrOut) const
    {
    /*-----------------------------------------------------------------------------------
    This is needed for a case from SSOE: Triforma extracted DEM objects to a plane by
    transforming their visible components to that plane but kept transformation matrices
    of parent cells out of the plane.  Since the visible components always aligned on the
    extraction plane (say xy-plane), everything displays fine except that it can introduce
    a small floating error that throws it child texts out of plane.  Such a small tilting
    made ACAD to display true type font texts slightly heavier than those precisely placed
    on the plan when zooming the drawing at certain factor.

    The right solution is for TF to correct the skewed cell header matrix.  But for now,
    we are handling it here via config var MS_DWG_DROPTFDEMCELLS.  Since a DEM object
    may contain other cells (DEM or not) that also have the skewed matrix, we drop them
    all till we hit the visible cells which are supposed to be always on the extraction plane.
    -----------------------------------------------------------------------------------*/
    WChar     cellName[MAX_CELLNAME_LENGTH];

    for (MSElementDescrP pDescr = *ppElmdscrOut; NULL != pDescr; pDescr = pDescr->h.next)
        {
        bool    isFirst = (pDescr == *ppElmdscrOut);

        if (CELL_HEADER_ELM == pDescr->el.ehdr.type)
            {
            if (SUCCESS == CellUtil::GetCellName(cellName, _countof(cellName), pDescr->el) && 0 != wcscmp(cellName, L"TFVISBL"))
                {
                MSElementDescr  *pDropped = NULL;

                // copy the cell children
                if (BSISUCCESS != pDescr->h.firstElem->Duplicate(&pDropped, true, false))
                    continue;

                // replace the cell with the new one - the old complex element shall all be freed:
                pDescr = pDescr->ReplaceDescr (*pDropped);

                if (isFirst)
                    *ppElmdscrOut = pDescr;
                }
            }
        }
    }


}; // ToDwgExtType2Cell


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          02/12
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtGroupHole : public ToDwgExtType2Cell
{
private:
    mutable bool        m_isFilled;
    mutable bool        m_isPatterned;
    mutable bool        m_isOutlined;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    /*--------------------------------------------------------------------------------------------
    There are three fill status for a group hole: filled without a boundary, filled with a boundary
    shown in a different color, or not filled at all.

    For an unfilled group hole, we simply create a DWG entity based on the nonfilled group hole mapping.

    For a filled group hole, we may create one or two entities depending on the entity mapping for 
    filled group holes.  When we have to create two entities, we have to use a proper saving sequence 
    of the entities to maintain a desired fill effect reflecting the boundary display.

    These are the options for filled group holes (exposed to GUI since Topaz):
        1) Polyface Mesh: a single polyface mesh entity, plus a hatch entity if patterned
        2) Region: a single region entity, plus a hatch entity of patterned
        3) Hatch: a single hatch entity

    ---------------------------------------------------------------------------------------------*/
    bool        hasHatch = this->GetFillInfo (elemHandle, context);

    AcRxClass*  typeNeeded = this->GetTypeNeeded (elemHandle, context);
    if (NULL == typeNeeded)
        return __super::ToObject (elemHandle, acObject, existingObject, context);
        
    // do not create a new region - it shall be created by AcDbBody::acisIn, but preserve existing type
    if (RealDwgUtil::IsObjectAcisType(existingObject) || typeNeeded != AcDbRegion::desc())
        acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);
    else
        acObject = nullptr;

    AcDbRegion* acRegion = AcDbRegion::cast (acObject);
    if (NULL != acRegion || typeNeeded == AcDbRegion::desc())
        {
        AcDbObjectP     newObject = NULL;
        RealDwgStatus   status = context.ConvertSolidElementToAcis (newObject, acObject, elemHandle);
        // Type Region might have been replaced to a different entity type - a valid status
        if (RealDwgSuccess == status || ReplacedObjectType == status)
            {
            if (RealDwgSuccess == status && nullptr == acObject)
                {
                acObject = newObject;
                }
            else if (ReplacedObjectType == status && nullptr != newObject && nullptr != acObject)
                {
                if (acObject->objectId().isValid())
                    {
                    if (Acad::eOk != acObject->handOverTo(newObject))
                        acObject->erase ();
                    }
                else if (acObject->isNewObject())
                    {
                    delete acObject;
                    }
                acObject = newObject;
                status = RealDwgSuccess;
                }
            }

        // create and add a single patterned hacth (no fill, and add the region first)
        if (m_isPatterned)
            context.AddHatchEntity (AcDbEntity::cast(acObject), elemHandle, false, true, false);

        return  status;
        }

    AcDbPolyFaceMesh*   acPolyface = AcDbPolyFaceMesh::cast (acObject);
    if (NULL != acPolyface && RealDwgSuccess == context.SetAcDbPolyfaceMeshFromElement(acPolyface, elemHandle))
        {
        if (acPolyface != acObject)
            {
            // a new polyface has replaced existing polyface - delete the existing object
            delete acObject;
            acObject = acPolyface;
            }
        // create and add a single patterned hacth (no fill, and add the polyfacemesh first)
        if (m_isPatterned)
            context.AddHatchEntity (acPolyface, elemHandle, false, true, false);
        return  RealDwgSuccess;
        }

    AcDbHatch*          acHatch = AcDbHatch::cast (acObject);
    if (NULL != acHatch && RealDwgSuccess == this->SetAcDbHatchFromGroupHole(acHatch, existingObject, elemHandle, context))
        return  RealDwgSuccess;

    // handle failed cases - will drop the cell to components
    if (nullptr != acObject)
        {
        if (acObject->objectId().isValid())
            acObject->erase ();
        else
            delete acObject;
        acObject = NULL;
        }

    DropGeometry    dropCell ((DropGeometry::Options) (DropGeometry::OPTION_SharedCells | DropGeometry::OPTION_Complex));
    dropCell.SetSharedCellOptions (DropGeometry::SHAREDCELL_Geometry);

    return  context.DropElementToDwg (acObject, existingObject, elemHandle, dropCell);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleR elemHandle, ConvertFromDgnContextR context) const
    {
    switch (context.GetSettings().GetGroupedHoleMapping(m_isFilled, context.GetThreeD()))
        {
        case Closed_Region:
            return AcDbRegion::desc();
        case Closed_Hatch:
            return AcDbHatch::desc();
        case Closed_Polyface:
            return AcDbPolyFaceMesh::desc();
        default:
            DIAGNOSTIC_PRINTF ("Unmapped group hole (ID=%I64d) gets dropped to a cell.\n", elemHandle.GetElementId());
        }

    return  NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbHatchFromGroupHole (AcDbHatch* acHatch, AcDbObjectP existingObject, ElementHandleR inElement, ConvertFromDgnContextR context) const
    {
    // create a single filled or patterned hatch
    RealDwgStatus   status = context.HatchFromElement (acHatch, inElement, AcDbEntity::cast(existingObject), false, m_isFilled);
    if (RealDwgSuccess == status)
        {
        // add a patterned hatch if both filled and patterned:
        if (m_isFilled && m_isPatterned)
            {
            // but add the filled hatch first so the patterned hatch can display over the filled
            if (!acHatch->objectId().isValid())
                context.AddEntityToCurrentBlock (AcDbEntity::cast(acHatch), inElement.GetElementId());

            AcDbHatch*  newHatch = new AcDbHatch ();
            if (context.AddEntityToCurrentBlock(AcDbEntity::cast(newHatch), 0).isValid())
                {
                status = context.HatchFromElement (newHatch, inElement, NULL, true, false);
                if (RealDwgSuccess == status)
                    newHatch->close ();
                else
                    newHatch->erase ();
                }
            else
                {
                if (nullptr != newHatch)
                    delete newHatch;
                DIAGNOSTIC_PRINTF ("Failed adding a patterned hatch for group hole ID=%I64d\n", inElement.GetElementId());
                }
            }
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            GetFillInfo (ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    UInt32      fillColor = 0, outlineColor = 0;
    bool        isSolidFilled = false, isGradientFilled = false;

    if (!context.IsFilledOrPatterned(inElement, &isSolidFilled, &isGradientFilled, &m_isOutlined, &m_isPatterned, &fillColor, &outlineColor))
        {
        m_isFilled = false;
        return  false;
        }

    // the outline color should be the color of the first solid element that is not a complex header:
    if (isSolidFilled)
        {
        outlineColor = this->GetSolidBoundaryColor (inElement);
        m_isOutlined = fillColor != outlineColor;
        }

    m_isFilled = isSolidFilled || isGradientFilled;

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32          GetSolidBoundaryColor (ElementHandleCR inElement) const
    {
    for (ChildElemIter child(inElement, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
        {
        MSElementCP childElement = child.GetElementCP ();
        if (!childElement->hdr.dhdr.props.b.h)
            {
            if (childElement->ehdr.isComplexHeader)
                return this->GetSolidBoundaryColor (child);

            return childElement->hdr.dhdr.symb.color;
            }
        }
    return  0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ToObjectPostProcess
(
ElementHandleR              inElement,
AcDbObjectP                 acObject, 
ConvertFromDgnContextR      context
) const override
    {
    // associate hatch with boundary entities
    if (context.IsFilledOrPatterned(inElement))
        {
        ToDwgExtAssocRegion assocRegion;
        return assocRegion.ToObjectPostProcess (inElement, acObject, context);
        }
    return  RealDwgSuccess;
    }

};  // ToDwgExtGroupHole


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          01/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtReferenceAttachment : public ToDwgExtension
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    DgnAttachmentP  dgnAttachment = DgnAttachment::FindByElementId (context.GetModel(), elemHandle.GetElementCP()->ehdr.uniqueId);
    if (NULL == dgnAttachment)
        return  MstnElementUnacceptable;

    // merge extraction model
    if (DgnModelType::Extraction_Deprecated == dgnAttachment->GetModelType())
        {
        // the deprecated extraction model, if present, should have been processed per Non-Default Model merge option, hence should never hit here anymore.
        return  MergeFailed;
        }

    AcRxClass*      typeNeeded = GetTypeNeeded (dgnAttachment, context);
    if (NULL == typeNeeded)
        return  RealDwgCantSelfAttach;

    acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);

    AcDbViewport*       acViewport = AcDbViewport::cast (acObject);
    if (NULL != acViewport)
        return  context.SetAcDbViewportFromSelfReference(acViewport, elemHandle);

    AcDbDgnReference*   acDgnUnderlay = AcDbDgnReference::cast (acObject);
    if (NULL != acDgnUnderlay)
        {
        ConvertReferenceToDgnUnderlay   toUnderlay (&context, &elemHandle);
        return toUnderlay.Convert (acDgnUnderlay, dgnAttachment);
        }

    AcDbBlockReference* blockReference = AcDbBlockReference::cast (acObject);
    if (NULL == blockReference)
        return  MstnElementUnacceptable;

    AcDbObjectId        blockId = blockReference->blockTableRecord ();
    DPoint3d            origin;
    RotMatrix           matrix;
    DgnClipData         clipData;
    RealDwgStatus       status = ExtractXRefFromReferenceElement (&blockId, &origin, &matrix, clipData, blockReference, elemHandle, context);

    if (RealDwgSuccess == status)
        {
        // origin & matrix should have been transformed to DWG
        DwgBlockReferenceFromCommonData     blockRefConverter (blockReference, blockId, clipData, origin, matrix);
        blockRefConverter.SaveBlockReference (elemHandle, context);
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ToDwgExtReferenceAttachment::IsSelfAttachmentOfParent (DgnAttachmentP dgnAttachment)
    {
    DgnModelP   model = dgnAttachment->GetDgnModelP ();
    DgnModelP   parentModel = dgnAttachment->GetParent().GetDgnModelP ();

    return  model == parentModel;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (DgnAttachmentP dgnAttachment, ConvertFromDgnContextR context) const
    {
    if (IsSelfAttachmentOfParent(dgnAttachment))
        {
        DIAGNOSTIC_PRINTF ("Rejecting self-attached reference!\n");
        return  NULL;
        }
    else if (context.IsDgnUnderlay(dgnAttachment))
        {
        return  AcDbDgnReference::desc ();
        }
    else if (context.IsModelRefViewportAttachment(dgnAttachment))
        {
        return AcDbViewport::desc ();
        }
    else
        {
        return AcDbBlockReference::desc ();
        }
    }

/*---------------------------------------------------------------------------------*//**
* @bsimethod                                                    RayBentley       08/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ExtractXRefFromReferenceElement
(
AcDbObjectId*                   pBlockTRObjectId,
DPoint3dP                       pOrigin,
RotMatrixP                      pDMatrix,
DgnClipData&                    clipData,
AcDbBlockReference*             pBlockReference,
ElementHandleR                  elemHandle,
ConvertFromDgnContextR          context
) const
    {
    MSElementCP                 pElement = elemHandle.GetElementCP();
    const ReferenceFileElm*     pReferenceElm = &pElement->referenceFileElm;
    if (pReferenceElm->fb_opts.inactive || pReferenceElm->fb_opts.metadataOnly)
        return RealDwgIgnoreElement;

    WChar                       wName[MAX_CELLNAME_LENGTH];
    LinkageUtil::ExtractNamedStringLinkageByIndex (wName, MAXFILELENGTH, STRING_LINKAGE_KEY_FileName, 0, pElement);

    DgnAttachmentP  pRefFile = DgnAttachment::FindByElementId (context.GetModel(), pElement->ehdr.uniqueId);
    if (NULL == pRefFile)
        return  CantAccessMstnElement;

    if (0 != IsSelfAttachmentOfParent(pRefFile))
       {
        DIAGNOSTIC_PRINTF ("Rejecting Self Attached Reference");
        return RealDwgCantSelfAttach;
        }

    context.ExtractClipFromReference (clipData, pRefFile);

    Transform               refTransform;
    DPoint3d                insertionBase;
    insertionBase.Zero ();

    context.GetTransformsFromReference (refTransform, insertionBase, clipData, pRefFile, pReferenceElm);

    refTransform.GetMatrix (*pDMatrix);
    refTransform.GetTranslation (*pOrigin);

    if (0.0 == pDMatrix->Determinant())
        pDMatrix->InitIdentity ();

    AcDbObjectId            blockTRObjectId = context.GetFileHolder().GetXRefBTRObjectIdByInstanceId (pElement->ehdr.uniqueId);
    if (*pBlockTRObjectId != blockTRObjectId)
        *pBlockTRObjectId = blockTRObjectId;

    if (pBlockTRObjectId->isNull())
        {
        DIAGNOSTIC_PRINTF ("BlockTableRecord not found for ExternalRef - null object ID\n");
        return BlockTableRecordForXRefNotFound;
        }
    AcDbBlockTableRecordPointer   pXRefBlock (*pBlockTRObjectId, AcDb::kForWrite, true);
    if (Acad::eOk != pXRefBlock.openStatus())
        {
        DIAGNOSTIC_PRINTF ("BlockTableRecord not found for ExternalRef - open status %ls\n", acadErrorStatusText(pXRefBlock.openStatus()));
        return BlockTableRecordForXRefNotFound;
        }

    /*-----------------------------------------------------------------------------------
    We could have erased the xref block definition with an instance that is in a different
    model, as in a workflow in ProjectWise, summarized in TR 295793.  In this case we want
    to unerase the block definition and continue on creating the new instance.
    -----------------------------------------------------------------------------------*/
    if (pXRefBlock->isErased() && Acad::eOk != pXRefBlock->erase(false))
        {
        DIAGNOSTIC_PRINTF ("BlockTableRecord for ExternalRef was previously erased and failed to be unerased\n");
        return BlockTableRecordForXRefNotFound;
        }

    if (!pXRefBlock->isFromExternalReference())
        {
        DIAGNOSTIC_PRINTF ("BlockTableRecord not found for ExternalRef - not an XRef block\n");
        return BlockTableRecordForXRefNotFound;
        }

    // Store the insertion base point
    pXRefBlock->setOrigin (RealDwgUtil::GePoint3dFromDPoint3d (insertionBase));

    AcDbObjectId    layerId;
    WString         xRefLayer = context.GetSettings().GetInsertLayerName ();
    LevelHandle     level = context.GetModel()->GetLevelCache().GetLevel (pElement->ehdr.level);
    if (level.IsValid())
        layerId = context.SavingChanges() ? context.GetFileHolder().GetLayerByLevelHandle(level) : context.GetFileHolder().GetLayerByLevelId(level.GetLevelId());

    // if user has specified a layer for xref, put xref to that layer
    if (layerId.isValid())
        {
        pBlockReference->setLayer (layerId);
        }
    else if (!xRefLayer.empty())
        {
        // else if user has specified a layer for all XRefs, use that layer.
        if (!(layerId = context.GetFileHolder().GetLayerByName(xRefLayer.c_str())).isNull())
            pBlockReference->setLayer (layerId);
        }
    else if (0 == pElement->ehdr.level)
        {
        /*-------------------------------------------------------------------------------
        If we arrived here, we have never assigned a layer for this reference attachment.
        In the past, OpenDWG defaulted all newly created entities to layer 0 but now we
        have RealDWG which puts them to the active layer.  Layer 0 vs active layer each
        has its own pros vs cons.  At this time we believe that keeping the old behavior
        is more appropriate such that we do not have to reeducate our users with a new
        behavior.
        -------------------------------------------------------------------------------*/
        pBlockReference->setLayer (context.GetFileHolder().GetDatabase()->layerZero());
        }

    WString     comments;
    if (BSISUCCESS == LinkageUtil::ExtractNamedStringLinkageByIndex(comments, STRING_LINKAGE_KEY_Description, 0, pElement) && !comments.empty())
        pXRefBlock->setComments (comments.c_str());

    // DGN specific data - we used save them only if Save Application Data is turned on, but users on Communities did not seem to like that.
    // save RefAttachMethod enumeration to xdata:
    UInt16          refAttachMethod = (UInt16) pReferenceElm->fb_opts.attachMethod;
    RealDwgResBuf*  pData = RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, refAttachMethod);
    RealDwgXDataUtil::AddMicroStationXDataToObject (pBlockReference, pData, StringConstants::XDataKey_ReferenceAttachMethod, context.GetDatabase());

    // save option treatAsElement to xdata
    UInt16          refAsElement = (UInt16) pReferenceElm->fb_opts.treatAsElement;
    pData = RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, refAsElement);
    RealDwgXDataUtil::AddMicroStationXDataToObject (pBlockReference, pData, StringConstants::XDataKey_ReferenceTreatedAsElement, context.GetDatabase());

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ToObjectPostProcess
(
ElementHandleR              inElement,
AcDbObjectP                 acObject, 
ConvertFromDgnContextR      context
) const override
    {
    AcDbViewport*   acViewport = AcDbViewport::cast(acObject);
    if (NULL != acViewport)
        return  context.SetAcDbViewportPostProcess (acViewport, inElement);

    return  RealDwgSuccess;
    }

}; // ToDwgExtReferenceAttachment


/*---------------------------------------------------------------------------------------
    These are XRef utilities implemented on ConvertFromDgnContext:
---------------------------------------------------------------------------------------*/
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::ExtractClipFromReference (DgnClipData& clipData, DgnAttachmentP pRefFile)
    {
    // This code was split from ToDwgExtReferenceAttachment to be shared with DgnUnderlay
    if (NULL == pRefFile || !pRefFile->IsClipped())
        return;

    // initialize clipping origin to be attachment origin in master file
    pRefFile->GetMasterOrigin (clipData.m_origin);

    DgnAttachmentClips  refClips;
    refClips.Init (*pRefFile, NULL, true);
    if (!refClips.HasClips())
        return;

    ClipVectorPtr       clip = refClips.CalculateClip (NULL);
    if (!clip.IsValid())
        return;

    /*---------------------------------------------------------------------------------------------------------------
    Currently DWG only supports 1 clip, either normal(inside) or inverted(outside).  If both types exist, we choose
    normal clip over ouside clip.  We will create an inverted xclip only if there is no boundary clip applied to the
    ref attachment.  Note the terminology difference used here: DgnClipData::m_isOutsideClip = inverted xclip, whereas
    DgnAttachmentClips::HasOuterClip = normal boundary clip.
    ---------------------------------------------------------------------------------------------------------------*/
    Transform           forward, inverse, clipTransform;

    clipData.m_isOutsideClip = !refClips.HasOuterClip ();

    FOR_EACH (ClipPrimitivePtr const& clipPrimitive, *clip)
        {
        Transform       deltaTransform;

        if (clipPrimitive == clip->front())
            {
            // first clip
            deltaTransform.InitIdentity ();
            clipPrimitive->GetTransforms (&forward, &inverse);
            clipTransform = forward;
            }
        else
            {
            // other clips
            clipPrimitive->GetTransforms (&forward, NULL);
            deltaTransform.InitProduct (inverse, forward);
            }

        // if a normal clipping boundary exists, and this one is a mask clip, move on to the next clip. If all clips are of mask, take the first one.
        if (!clipData.m_isOutsideClip && clipPrimitive->IsMask())
            continue;

        ClipPolygonCP       clipPolygon;
        if (NULL != (clipPolygon = clipPrimitive->GetPolygon()))
            clipData.m_pointArray = *clipPolygon;

        if (clipData.m_pointArray.size() > 0)
            {
            // got our first mask clip or the normal boundary clip - this is it!
            deltaTransform.Multiply (&clipData.m_pointArray.front(), &clipData.m_pointArray.front(), (int)clipData.m_pointArray.size());

            clipData.m_isOutsideClip = clipPrimitive->IsMask ();
            break;
            }
        }

    if (clipData.m_pointArray.empty())
        DIAGNOSTIC_PRINTF ("Did not get clip points for ref attachment ID=%I64d\n", pRefFile->GetElementId());

    clipTransform.GetTranslation (clipData.m_origin);
    clipTransform.GetMatrix (clipData.m_matrix);

    // if we're not clipping with an element, check the rotateClipping flag.
    if ( (0 == pRefFile->GetClipElementId()) && !pRefFile->IsRotateClipping() && !this->GetSettings().DisableV7RefClipRotation())
        {
        RotMatrixCR     viewMatrix = this->GetFileHolder().GetViewRotation ();
        clipData.m_matrix.InverseOf (viewMatrix);
        }

    clipData.m_frontClipOn = pRefFile->HasFrontClip ();
    if (clipData.m_frontClipOn)
        clipData.m_frontClipZ = pRefFile->GetFrontClipDepth ();
    clipData.m_backClipOn = pRefFile->HasBackClip ();
    if (clipData.m_backClipOn)
        clipData.m_backClipZ = pRefFile->GetBackClipDepth ();
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::GetTransformsFromReference
(
TransformR                  refTransform,
DPoint3dR                   insertionBase,
DgnClipData&                clipData,
DgnAttachmentP              pRefFile,
const ReferenceFileElm*     pReferenceElm
)
    {
    // This code was split from ToDwgExtReferenceAttachment to be shared with DgnUnderlay
    Transform               refToDGN;
    ModelInfoCP             pReferenceModelInfo = pRefFile->GetModelInfoCP ();

    if (NULL != pReferenceModelInfo)
        {
        UnitDefinition      refTargetUnits;
        DwgOpenUnitMode     unitMode = DWGOpenUnitMode_NotSetYet;

        // If this is a DWG reference - make sure that we use the same unit mode by which it was attached to infer the transform.
        bool                isDwgRef = mdlModelRef_isTargetFormatDwgOrDxf (pRefFile);
        if (isDwgRef)
            {
            if (!mdlModelRef_isTargetFormatDwgOrDxf (this->GetModel()))
                {
                int         refDwgUnitMode = pRefFile->GetForeignUnitMode ();
                unitMode = refDwgUnitMode < 0 ? (DwgOpenUnitMode)pReferenceElm->foreignUnitMode : (DwgOpenUnitMode)refDwgUnitMode;
                }
            }
        else
            {
            unitMode = this->GetDwgOpenUnitModeFromSaveUnitMode (this->GetSettings().GetDwgSaveUnitMode());
            }

        pRefFile->GetTransformToParent (refTransform, true);

        this->GetTransformToDGNFromModelInfo (&refToDGN, NULL, &refTargetUnits, *pReferenceModelInfo, pRefFile->GetDgnModelP(), unitMode);

        insertionBase = pReferenceModelInfo->GetInsertionBase ();

        Transform           insertionBaseTransform;

        if (isDwgRef && NULL != pReferenceModelInfo)
            insertionBase.DifferenceOf (insertionBase, pReferenceModelInfo->GetGlobalOrigin());

        insertionBaseTransform.InitFrom (insertionBase.x, insertionBase.y, insertionBase.z);
        refTransform.InitProduct (refTransform, insertionBaseTransform);

        // Subtract from the clip origin.
        DPoint3d            parentInsertionBase;

        refTransform.MultiplyMatrixOnly (parentInsertionBase, insertionBase);
        clipData.m_origin.Add (parentInsertionBase);
        }
    else
        {
        refToDGN = this->GetTransformToDGN ();

        refTransform.InitIdentity();
        refTransform.SetMatrix (pReferenceElm->transform);
        refTransform.ScaleMatrixColumns (refTransform, pReferenceElm->scale, pReferenceElm->scale, pReferenceElm->scale);
        refTransform.SetTranslation (pReferenceElm->masterOrigin);
        refTransform.TranslateInLocalCoordinates (refTransform, -pReferenceElm->refOrigin.x, -pReferenceElm->refOrigin.y, -pReferenceElm->refOrigin.z);
        }

    refTransform.InitProduct (refTransform, refToDGN);
    refTransform.InitProduct (this->GetTransformFromDGN(), refTransform);

    Transform               dgnToRef;
    dgnToRef.InverseOf (refToDGN);
    dgnToRef.MultiplyMatrixOnly (insertionBase);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Barry.Bentley   11/01
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     GetOutputXRefFileName (WStringR outputBaseName, WCharCP sourceFileSpec, WCharCP proposedBaseName, DgnModelRefP parentModelRef)
    {
    // This method is largely a copy of mdlFile_getAcadTranslateFileName in an effort of removing the dependency on MstnPlatform.

    // extract the directory from the sourceFileSpec
    WString     sourceDevice, sourceDir, sourceBaseName, sourceExt, fullDirectory;
    BeFileName::ParseName (&sourceDevice, &sourceDir, &sourceBaseName, &sourceExt, sourceFileSpec);

    // if we have no sourceDevice or sourceDir, and we have a parentModelRef, get its directory.
    if (sourceDevice.empty() && sourceDir.empty() && INVALID_MODELREF != parentModelRef)
        {
        DgnFileP    parentDgnFile = parentModelRef->GetDgnFileP();
        if (NULL == parentDgnFile)
            return  false;

        WString     parentFileSpec = parentDgnFile->GetFileName ();

        if (!parentFileSpec.empty())
            BeFileName::ParseName (&sourceDevice, &sourceDir, NULL, NULL, parentFileSpec.GetWCharCP());
        }

    // if it has the dgn extension, we always use the same base name.
    // only have to worry if the output base name is the same as the proposed base name.
    // otherwise the user has chosen a different name, and it's not our problem.
    if (!sourceExt.EqualsI(L"dgn") && !sourceExt.EqualsI(L"dwg") && !sourceExt.EqualsI(L"dxf") && sourceBaseName.EqualsI(proposedBaseName))
        {
        bool        appendExtension = true;

        // Original V8 Versions would only append extension if there was more than one file using the name.
        // This proved unreliable (if a reference file didn't exist it wouldn't be correct etc.)
        if (ConfigurationManager::IsVariableDefined (L"MS_DWGOMITUNIQUEEXTENSION"))
            {
            // see if there are other files with same base name in the directory
            BeFileName::BuildName (fullDirectory, sourceDevice.GetWCharCP(), sourceDir.GetWCharCP(), NULL, NULL);
            fullDirectory += WString(L"\\");

            WString             filter = sourceBaseName + WString(L".*");
            BeFileListIterator  fileFinder (filter.GetWCharCP(), true);

            // walk through existing files that have the same base file name and look for no DWG files
            int                 numMatchingFiles = 0;
            BeFileName          foundFile;
            while (BSISUCCESS == fileFinder.GetNextFileName(foundFile))
                {
                WString         extName = BeFileName::GetExtension (foundFile.GetWCharCP());
                if (!extName.empty())
                    {
                    if (!extName.EqualsI(L"dwg") && !extName.EqualsI(L"dxf") && !extName.EqualsI(L"bak"))
                        numMatchingFiles++;
                    }
                else
                    {
                    numMatchingFiles++;
                    }

                if (numMatchingFiles > 1)
                    break;
                }

            appendExtension = (numMatchingFiles > 1);
            }

        // if more than one file with same base name, we have to make up a name with directory as part of it.
        if (appendExtension)
            {
            outputBaseName = sourceBaseName + WString(L"_") + sourceExt;
            return true;
            }
        }

    if (NULL != proposedBaseName && 0 != proposedBaseName[0])
        outputBaseName.assign (proposedBaseName);
    else if (!sourceBaseName.empty())
        outputBaseName = sourceBaseName;    // unlikely scenario
    else
        outputBaseName.assign (L"???");     // should never happen

    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcString                    ConvertFromDgnContext::XRefPathFromDgnPath
(
WCharCP                     sourceFileSpec,
WCharCP                     pAttachName,
bool                        isDefaultModel,
WCharCP                     pModelName,
DgnModelRefP                parentModelRef,
WCharCP                     currentPath
)
    {
    int         pathMode        = this->GetSettings().GetSaveReferencePathMode();
    bool        omitPath        = m_omitReferencePath || SaveReferencePath_Never == pathMode;
    bool        relativePath    = SaveReferencePath_Relative == pathMode;

    if (NULL != currentPath)
        {
        // existing xRefBlock has a path
        WString currentDevice, currentDirectory;
        BeFileName::ParseName (&currentDevice, &currentDirectory, NULL, NULL, currentPath);
        relativePath  = (currentDevice.empty() && !currentDirectory.empty());
        // if user wants to remove the path, remove the path from existing block def - per request in TFS 152291:
        omitPath      = m_omitReferencePath || (currentDevice.empty() && currentDirectory.empty());
        }

    WString     outBaseName, outPath, proposedName;
    BeFileName::ParseName (NULL, NULL, &proposedName, NULL, sourceFileSpec);
    GetOutputXRefFileName (outBaseName, sourceFileSpec, proposedName.GetWCharCP(), m_model);

    // Validate base file name - TR 315815 shows an embedded ref containing invalid chars:
    this->ValidateFileName (outBaseName);

    if (!isDefaultModel)
        {
        WString baseName(outBaseName);
        WString modelName(pModelName);
        this->ValidateFileName (modelName);
        WString::Sprintf (outBaseName, L"%ls_%ls", baseName.c_str(), modelName.c_str());
        }

    WString     outputExtension (L"dwg");

    // If we have a relative path (directory without device) then use that - else include output path.
    WString     attachDevice, attachDirectory;
    BeFileName::ParseName (&attachDevice, &attachDirectory, NULL, NULL, pAttachName);

    if (attachDevice.empty() && !attachDirectory.empty())
        {
        // It is already a relative path.
        omitPath = false;
        BeFileName::BuildName (outPath, NULL, attachDirectory.GetWCharCP(), outBaseName.GetWCharCP(), outputExtension.GetWCharCP());
        }
    else
        {
        WString outputDevice, outputDir;
        if (relativePath)
            {
            WString     relativeFileSpec;
            BeFileName::FindRelativePath (relativeFileSpec, sourceFileSpec, parentModelRef->GetDgnFileP()->GetFileName().c_str());
            BeFileName::ParseName (&outputDevice, &outputDir, NULL, NULL, relativeFileSpec.GetWCharCP());
            omitPath = false;
            }
        else
            {
            BeFileName::ParseName (&outputDevice, &outputDir, NULL, NULL, sourceFileSpec);
            }
        BeFileName::BuildName (outPath, outputDevice.GetWCharCP(), outputDir.GetWCharCP(), outBaseName.GetWCharCP(), outputExtension.GetWCharCP());
        }

    AcString  xRefPath (outPath.c_str());

    if (omitPath)
        {
        // Strip off existing directory.
        int         lastSlashIndex;

        if (-1 != (lastSlashIndex = xRefPath.findRev ('\\')))
            xRefPath = xRefPath.substr (lastSlashIndex+1, -1);
        }
    return xRefPath;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/01
+---------------+---------------+---------------+---------------+---------------+------*/
static AcString             BlockNameFromXRefPath
(
AcString&                   xRefPath,
WCharCP                     pLogicalName,
ConvertFromDgnContext&      context
)
    {
    int extensionIndex, pathIndex;

    // Ignore leading spaces - this will handle string with all spaces.  ValidateName will handle trailing spaces.
    while (NULL != pLogicalName && *pLogicalName == ' ')
        pLogicalName++;

    if (NULL != pLogicalName &&
        0 != *pLogicalName && 0 != _wcsicmp (L"Ref", pLogicalName) && 0 != _wcsnicmp (L"Ref-", pLogicalName, 4))
        {
        // Needs work - make sure this doesn't conflict with a shared cell name??
        return pLogicalName;
        }

    if (!xRefPath.isEmpty() && (extensionIndex = xRefPath.findRev ('.')) > 0)
        {
        AcString      blockName = xRefPath.substr (extensionIndex);

        if ((pathIndex = blockName.findRev ('\\')) > 0 || (pathIndex = blockName.findRev ('/')) > 0 || (pathIndex = blockName.findRev (':')) > 0)
            blockName = blockName.substr (pathIndex + 1, -1);

        return blockName;
        }
    return xRefPath;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   11/08
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 CantFindAcceptableBlockTableRecord
(
AcDbObjectId&               blockTRObjectId,
WCharCP                   blockNameWChars,
AcString const&             blockName,
ConvertFromDgnContextR      context
)
    {
    if (0 == *blockNameWChars)
        return true;

    if ( (blockTRObjectId = context.GetFileHolder().GetBlockByName (blockName)).isNull() )
        return true;

    // found a blockTableRecord with the name we want.
    AcDbBlockTableRecordPointer pBlockTR (blockTRObjectId, AcDb::kForRead);
    if (Acad::eOk != pBlockTR.openStatus())
        return true;

    // must be marked as an external reference.
    if (!pBlockTR->isFromExternalReference())
        return true;

    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetIsExternalRefAndOverlay
(
AcDbBlockTableRecord*       xRefBTR,
bool                        isOverlay
)
    {
    RecordingFiler filer (24);
    filer.RecordData (xRefBTR);
#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("xRefBTR in SetIsExternalRefAndOverlay");
#endif
    // the dump looks like this
    //
    //  0: kDwgSoftPointerId, ffa04c10 fd8c00b2 AcDbBlockTable      // AcDbObject:              This is a pointer to the owner of the BlockTableRecord.
    //  1: kDwgUInt32,    0                                         // AcDbObject:              This is the number of reactors. Would be followed by the SoftPointerIds of the reactors, if there were any.
    //    <Here we would one SoftPointerId for each reactor>
    //  2: kDwgHardOwnershipId, NULL                                // AcDbObject:              This is a pointer to the extension dictionary, if there is one.
    //  3: kDwgString(ACHAR) polyline2d                             // AcDbSymbolTableRecord:   Name of symbol table record.
    //  4: kDwgInt16,    0                                          // AcDbSymbolTableRecord:   ?? Is Dependent on XRef? 0 if not, 0x100 if is?
    //  5: kDwgBool false                                           // AcDbSymbolTableRecord:   ??
    //  6: kDwgHardPointerId, NULL                                  // AcDbSymbolTableRecord:   XRef BlockId, if dependent on XRef.

    //  7: kDwgBool false                                           // AcDbBlockTableRecord:    anonymous
    //  8: kDwgBool false                                           // AcDbBlockTableRecord:    hasAttributes
    //  9: kDwgBool false                                           // AcDbBlockTableRecord:    isXRef
    // 10: kDwgBool false                                           // AcDbBlockTableRecord:    overlay
    // 11: kDwgBool false                                           // AcDbBlockTableRecord:    unloaded
    // 12: kDwgHardOwnershipId, NULL                                // AcDbBlockTableRecord:    this is the "blockBegin" entity.

    // 13: kDwgUInt32,    0                                         // !!! This is written out when the BTR is newly created, but not after it is in the database. It is not ever read back in. Do not know the reason for this behavior.

    // 14: kDwgHardOwnershipId, NULL,                               // AcDbBlockTableRecord:    this is the "blockEnd" entity.
    // 15: AcGePoint3d, 0.000000, 0.000000, 0.000000                // AcDbBlockTableRecord:    base point.
    // 16: kDwgString(ACHAR) C:\testdgn\dwg\polyline2d.dwg          // AcDbBlockTableRecord:    XRef path.
                                                                    // AcDbBlockTableRecord:    Here a loop of SoftPointerIds is started.
    // 17: kDwgUInt8,    0                                          // AcDbBlockTableRecord:    If 1, a softPointerId To a BlkRef follows. If 2, a softPointerId to a NestedXRefBTR follows. If 0, end of loop. [From looking at ODA code].

    // 18: kDwgHardPointerId, NULL                                  // AcDbBlockTableRecord:    LayoutID.
    // 19: kDwgString(ACHAR)                                        // AcDbBlockTableRecord:    Description.
    // 20: kDwgUInt32,    0                                         // AcDbBlockTableRecord:    size of preview. If nonzero, a byteArray is next.
                                                                    // AcDbBlockTableRecord:    Byte Array of preview, if there.
    // 21: kDwgUInt16,    0                                         // AcDbBlockTableRecord:    insert units.
    // 22: kDwgBool true                                            // AcDbBlockTableRecord:    explodable.
    // 23: kDwgUInt8,    0                                          // AcDbBlockTableRecord:    scaling.
    //
    // The "isXRef" flag is the third bool.
    FilerDataList&      dataList        = filer.GetDataList();
    UInt32FilerData*    reactorCount;

    if (NULL == (reactorCount= dynamic_cast <UInt32FilerData *> (dataList[1])))
        {
        BeAssert (false && L"DWG filer error in xRef block: reactors!");
        return BadDataSequence;
        }
    int                 sequenceStart    = 1 + reactorCount->GetValue();

    // this is here only because I want to step through it if we ever hit this case.
    BeAssert (1 == sequenceStart);

    BeAssert (nullptr != dynamic_cast <HardPointerIdFilerData *>   (dataList[sequenceStart + 5]));
    BeAssert (nullptr != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 6]));
    BeAssert (nullptr != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 7]));
    BeAssert (nullptr != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 8]));
    BeAssert (nullptr != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 9]));
    BeAssert (nullptr != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 10]));
    BeAssert (nullptr != dynamic_cast <HardOwnershipIdFilerData *> (dataList[sequenceStart + 11]));

    BoolFilerData*      isRefData;
    BoolFilerData*      isOverlayData;
    if ( (NULL != dynamic_cast <HardPointerIdFilerData *> (dataList[sequenceStart + 5])) &&
         (NULL != dynamic_cast <BoolFilerData *> (dataList[sequenceStart + 6])) && (NULL != dynamic_cast <BoolFilerData *> (dataList[sequenceStart + 7])) &&
         (NULL != (isRefData = dynamic_cast <BoolFilerData *> (dataList[sequenceStart + 8]))) && (NULL != (isOverlayData = dynamic_cast <BoolFilerData *> (dataList[sequenceStart + 9]))) )
        {
        isRefData->SetValue (true);
        isOverlayData->SetValue (isOverlay);

        // Acad2009: The UInt32 at 13 is written by dwgOutFields for a newly created BTR, but not if the BTR is already in the database. It is never read back on dgnInFields.
        // I don't know the reason for this behavior. I have a feeling it's a bug in their "kBagFiler" type checking.
        if (NULL != dynamic_cast <UInt32FilerData*> (dataList[sequenceStart + 12]))
            filer.RemoveEntryAt (sequenceStart + 12);

        Acad::ErrorStatus readStatus = filer.PlaybackData (xRefBTR);
        BeAssert (Acad::eOk == readStatus);
        BeAssert (xRefBTR->isFromExternalReference());
        BeAssert (isOverlay == xRefBTR->isFromOverlayReference());
        return (Acad::eOk == readStatus) ? RealDwgSuccess : BadDataSequence;
        }
    else
        return BadDataSequence;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus            AddNestedXRefToParentBTR
(
AcDbBlockTableRecord*           parentBTR,
AcDbObjectId&                   nestedBTRObjectId
)
    {
    RecordingFiler filer (24);
    filer.RecordData (parentBTR);
#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("parentBTR in AddNestedXRefToParentBTR");
#endif
    FilerDataList&      dataList        = filer.GetDataList();
    UInt32FilerData*    reactorCount;

    if (NULL == (reactorCount= dynamic_cast <UInt32FilerData *> (dataList[1])))
        {
        BeAssert (false && L"DWG filer error in xRef block: reactors!");
        return BadDataSequence;
        }
    int                 sequenceStart    = 1 + reactorCount->GetValue();

    // this is here only because I want to step through it if we ever hit this case.
    BeAssert (1 == sequenceStart);

    BeAssert (NULL != dynamic_cast <HardPointerIdFilerData *>   (dataList[sequenceStart + 5]));
    BeAssert (NULL != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 6]));
    BeAssert (NULL != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 7]));
    BeAssert (NULL != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 8]));
    BeAssert (NULL != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 9]));
    BeAssert (NULL != dynamic_cast <BoolFilerData *>            (dataList[sequenceStart + 10]));
    BeAssert (NULL != dynamic_cast <HardOwnershipIdFilerData *> (dataList[sequenceStart + 11]));

    // if it has the bogus UInt32 at position 13, (actually, it shouldn't, if my presumption that it is only in non-database resident object) remove it.
    if (NULL != dynamic_cast <UInt32FilerData*> (dataList[sequenceStart + 12]))
        filer.RemoveEntryAt (sequenceStart + 12);

    // see SetIsExternalRefAndOverlay for record information.
    // start at record 16. Should see UInt8
    for (UInt32 iRec=sequenceStart + 15; iRec < dataList.size()-1; iRec+=2)
        {
        UInt8FilerData*             typeData;
        Adesk::UInt8                pointerType;
        if (NULL == (typeData = dynamic_cast <UInt8FilerData *> (dataList[iRec])))
            {
            BeAssert (false && L"DWG filer error in xRef block: type!");
            return BadDataSequence;
            }

        pointerType = typeData->GetValue();
        BeAssert (pointerType < 3 && L"DWG filer error inxRef block: pointer type");

        if (0 == pointerType)
            {
            // insert a UInt8 (2) record and a / SoftPointerId record.
            filer.InsertEntryAt (iRec,      new UInt8FilerData (2));
            filer.InsertEntryAt (iRec+1,    new SoftPointerIdFilerData (nestedBTRObjectId));
            break;
            }

        // if we got here, then pointerType is either 1 or 2, and we should have a SoftPointerIdFilerData following.
        if (NULL == dynamic_cast <SoftPointerIdFilerData *> (dataList[iRec+1]))
            {
            BeAssert (pointerType < 3 && L"DWG filer error inxRef block: pointer type");
            return BadDataSequence;
            }
        }

    Acad::ErrorStatus readStatus = filer.PlaybackData (parentBTR);
#if defined (REALDWG_FILER_DEBUG)
    if (Acad::eOk == readStatus)
        {
        RecordingFiler confirm (24);
        confirm.RecordData (parentBTR);
        confirm.DumpList ("parentBTR after adding nested id");
        }
#endif
    return (Acad::eOk == readStatus) ? RealDwgSuccess : BadDataSequence;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus            AddXRefBTR
(
AcDbObjectId&                   blockTRObjectId,
AcString&                       blockName,
const AcString&                 xRefPath,
bool                            overlay,
DgnAttachmentCP                 attachment,
ConvertFromDgnContext&          context
)
    {
    AcDbBlockTableRecord*   xRefBTR = new AcDbBlockTableRecord();
    context.DeduplicateBlockName (blockName);

    Acad::ErrorStatus       status;
    status = xRefBTR->setName (blockName);
    assert (Acad::eOk == status);
    status = xRefBTR->setPathName (xRefPath);
    assert (Acad::eOk == status);

    AcDbBlockTablePointer   pBlockTable (context.GetDatabase()->blockTableId(), AcDb::kForWrite);
    blockTRObjectId = context.AddBlockToBlockTable (pBlockTable, xRefBTR, 0);
    assert (!blockTRObjectId.isNull());
    if (blockTRObjectId.isNull())
        {
        delete xRefBTR;
        return CantCreateXRefBTR;
        }
    RealDwgStatus rDwgStatus;
    if (RealDwgSuccess != (rDwgStatus = SetIsExternalRefAndOverlay (xRefBTR, TO_BOOL (overlay))))
        {
        assert (false);
        }

    if (context.GetSettings().SaveBlockUnitsFromFileUnits())
        {
        // default block units to be the target file units
        AcDb::UnitsValue    insUnits = RealDwgUtil::AcDbUnitsValueFromDgnUnits (context.GetStandardTargetUnits());
        DgnModelP           attachedModel = nullptr != attachment ? attachment->GetDgnModelP() : nullptr;

        // try setting block units from the source model units per SaveAs Options:
        if (nullptr != attachedModel)
            {
            DwgOpenUnitMode translatedUnitmode = context.GetDwgOpenUnitModeFromSaveUnitMode (context.GetSettings().GetDwgSaveUnitMode());
            
            UnitDefinition  refUnits;
            refUnits.SetInvalid ();

            context.GetTransformToDGNFromModelInfo (nullptr, nullptr, &refUnits, attachedModel->GetModelInfo(), attachedModel, translatedUnitmode);

            if (refUnits.IsValid())
                insUnits = RealDwgUtil::AcDbUnitsValueFromDgnUnits (refUnits.IsStandardUnit());
            }

        xRefBTR->setBlockInsertUnits (insUnits);
        }

    xRefBTR->close();
    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/98
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SaveModelXRefsToDatabase
(
DgnModelP                   rootModelRef,
DgnModelRefP                parentModelRef
)
    {
    bool                    isRoot                  = (parentModelRef == rootModelRef);
    bool                    saveChildrenToDatabase  = (this->SavingChanges() || !this->GetSettings().CreateOverlaysForReferenceAttachments());
    DgnFileP                parentFile              = parentModelRef->GetDgnFileP ();
    if (NULL == parentFile)
        return;
    DgnFileFormatType       originalFormat          = parentFile->GetOriginalFormat ();

    ModelRefIteratorP       mrIterator  = new ModelRefIterator (parentModelRef, MRITERATE_PrimaryChildRefs, 0);
    if (NULL == mrIterator)
        return;

    DgnModelRefP         childModelRef;
    for (childModelRef = mrIterator->GetFirst(); NULL != childModelRef; childModelRef = mrIterator->GetNext())
        {
        DgnAttachmentP  reference = childModelRef->AsDgnAttachmentP();

        if (ToDwgExtReferenceAttachment::IsSelfAttachmentOfParent(reference) || this->IsModelRefViewportAttachment(childModelRef) || DgnModelType::Extraction_Deprecated == childModelRef->GetModelType())
            continue;

        bool            overlay         = reference->GetDoNotDisplayAsNested ();
        bool            displayOn       = reference->IsDisplayed();
        WCharCP         logicalName     = reference->GetLogicalName();
        WCharCP         storedBlockName = reference->GetDwgBlockName();
        WCharCP         modelName       = reference->GetModelNameCP();
        ElementId       attachmentID    = reference->GetElementId();
        WString         pathName        = reference->GetEffectiveFullFileSpec ();
        WString         attachName      = reference->GetAttachName (true);

        if (pathName.empty() && attachName.empty())
            continue;

        // if it is a DGN underlay, save the attachment info for later deletion operation
        if (this->IsDgnUnderlay(reference))
            {
            DgnUnderlayDefInfo  dgndefInfo;
            dgndefInfo.SetSourceFileName (attachName.GetWCharCP());
            dgndefInfo.SetModelName (modelName);
            dgndefInfo.SetElementId (attachmentID);
            m_dgnUnderlayDefInfoArray.push_back (dgndefInfo);
            continue;
            }

        DgnFileP        childFile       = childModelRef->GetDgnFileP ();
        bool            isDefaultModel  = NULL == childFile || childModelRef->GetModelId() == RealDwgUtil::GetDwgModelSpaceId(childFile);
        WCharCP         pDgnPath        = pathName.empty() ? attachName.GetWCharCP() : pathName.GetWCharCP();
        AcString        xRefPath        = this->XRefPathFromDgnPath (pDgnPath, attachName.GetWCharCP(), isDefaultModel, modelName, parentModelRef, NULL);
        AcString        blockName       = (0 == storedBlockName[0]) ? BlockNameFromXRefPath (xRefPath, GetSettings().DisallowLogicalNameFromXRefBlockNames() ? NULL : logicalName, *this) : storedBlockName;

        this->ValidateName (blockName);

        // When checking in from DGN file, honor the createOverlaysForReferenceAttachments flag.
        if (!overlay && (originalFormat == DgnFileFormatType::V7 || originalFormat == DgnFileFormatType::V8))
            overlay = TO_BOOL (this->GetSettings().CreateOverlaysForReferenceAttachments ());

        AcDbObjectId                blockTRObjectId;
        if ((blockTRObjectId = m_pFileHolder->GetXRefBTRObjectIdByModelRef (reference)).isNull())
            {
            // Did not find an acceptable BlockTableRecord (by reference ID path) for this reference. Must create it
            if (isRoot)
                {
                // If its a direct attachment, create an xRef block table record even if this necessitates creating a new block name through deduplication.
                if (CantFindAcceptableBlockTableRecord (blockTRObjectId, storedBlockName, blockName, *this))
                    {
                    if (RealDwgSuccess != AddXRefBTR (blockTRObjectId, blockName, xRefPath, overlay, reference, *this))
                        {
                        DIAGNOSTIC_PRINTF ("Unable to save X-Ref \"%ls\"", blockName.kwszPtr());
                        continue;
                        }
                    }
                }
            else
                {
                // If its a nested attachment, things are a bit trickier.  We'll create a new xRef block table record only if the name is unique.
                // If the name is not unique, we'll look at the existing block table record and use it only if the parent block table Record has this listed
                // as a nested X-Ref.  There may be cases where the nested attachments layers etc. cannot be set because the name is already used.
                AcDbObjectId                    parentBlockTRObjectId;
                AcDbBlockTableRecordPointer     parentBlockTableRecord;

                if (! (parentBlockTRObjectId = m_pFileHolder->GetXRefBTRObjectIdByModelRef (parentModelRef->AsDgnAttachmentP())).isNull() && (Acad::eOk == parentBlockTableRecord.open (parentBlockTRObjectId, AcDb::kForWrite)) )
                    {
                    // we were able to find an XRefBTR for the root parent
                    if ((blockTRObjectId = m_pFileHolder->GetBlockByName (blockName)).isNull())
                        {
                        // no block with that name. Create the block.
                        if (RealDwgSuccess != AddXRefBTR (blockTRObjectId, blockName, xRefPath, overlay, reference, *this))
                            {
                            DIAGNOSTIC_PRINTF ("Unable to save X-Ref \"%ls\"", blockName.kwszPtr());
                            continue;
                            }
                        // now add a reference to it in the parent
                        AddNestedXRefToParentBTR (parentBlockTableRecord, blockTRObjectId);
                        }
                    else
                        {
                        // found block, does it represent a nested XRef?
                        int                     nestedIndex;
                        AcDbObjectIdArray       nestedIds;

                        RealDwgUtil::GetNestedXRefIds (parentBlockTableRecord, nestedIds);
                        if (! nestedIds.find (blockTRObjectId, nestedIndex, 0))
                            {
                            DIAGNOSTIC_PRINTF ("Unable to check in nested X-Ref - Block name (%ls) already in use\n", blockName.kwszPtr());
                            continue;
                            }
                        }
                    }
                }
            }

        if (!blockTRObjectId.isNull())
            {
            AcDbBlockTableRecordPointer xRefBTR (blockTRObjectId, AcDb::kForWrite);
            if (Acad::eOk == xRefBTR.openStatus())
                {
                const ACHAR*        xrefPath = { 0 };
                Acad::ErrorStatus   status;
                status = xRefBTR->pathName (xrefPath);
                BeAssert (Acad::eOk == status && L"Error getting xref path!");
                status = xRefBTR->setPathName (this->XRefPathFromDgnPath (pDgnPath, attachName.GetWCharCP(), isDefaultModel, modelName, parentModelRef, xrefPath));
                BeAssert (Acad::eOk == status);
                status = xRefBTR->setIsUnloaded (!displayOn);
                BeAssert (Acad::eOk == status);

                // Make sure it's set an xref BTR and the overlay bit is set right. To do that, we have to use our RecordingFiler.
                if (RealDwgSuccess != SetIsExternalRefAndOverlay (xRefBTR, TO_BOOL (overlay)))
                    {
                    BeAssert (false && L"Failure setting xref & overlay!");
                    xRefBTR->erase();
                    continue;
                    }
                }
            m_pFileHolder->AddExternalRef (blockTRObjectId, reference);
            this->CreateLayersForModelRef (xRefBTR, childModelRef, rootModelRef);
            }

        // If not in the rootModelRef, recurse into the more deeply nested refs, That means we save all directly attached children first to insure that their block names take precedence over nested ones.
        if (!isRoot && saveChildrenToDatabase)
            this->SaveModelXRefsToDatabase (rootModelRef, childModelRef);
        }

    // If we're in the rootModelRef, this is where we recurse through the nested references. (We do the directly attached references "breadth-first", then the more deeply nested references "depth-first".
    if (isRoot && saveChildrenToDatabase)
        for (childModelRef = mrIterator->GetFirst(); NULL != childModelRef; childModelRef = mrIterator->GetNext())
            this->SaveModelXRefsToDatabase (rootModelRef, childModelRef);

    delete mrIterator;
    }


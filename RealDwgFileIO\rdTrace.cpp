/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdTrace.cpp $
|
|  $Copyright: (c) 2013 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          03/09
+===============+===============+===============+===============+===============+======*/
class   ToDgnExtTrace : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbTrace*          pTrace = AcDbTrace::cast (acObject);
    DPoint3d            points[5];

    for (int i=0; i<4; i++)
        {
        AcGePoint3d     acPoint;

        pTrace->getPointAt (i, acPoint);
        RealDwgUtil::DPoint3dFromGePoint3d (points[i], acPoint);
        }
    RealDwgUtil::SwapPoints (points[2], points[3]);

    context.GetTransformToDGN().Multiply (points, 4);

    points[4] = points[0];              // Stupid closure point

    BentleyStatus       status = ShapeHandler::CreateShapeElement (outElement, NULL, points, 4, context.GetThreeD(), *context.GetModel());
    if (SUCCESS == status)
        {
        context.ApplyThickness (outElement, pTrace->thickness(), pTrace->normal(), false);
        context.ElementHeaderFromEntity (outElement, pTrace);
        // add fill
        IAreaFillPropertiesEdit*    areaFill = dynamic_cast <IAreaFillPropertiesEdit*> (&outElement.GetHandler(MISSING_HANDLER_PERMISSION_ChangeAttrib));
        if (NULL != areaFill)
            {
            UInt32  fillColor = outElement.GetElementCP()->hdr.dhdr.symb.color;
            areaFill->AddSolidFill (outElement, &fillColor);
            }
        }

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

};  // ToDgnExtTrace

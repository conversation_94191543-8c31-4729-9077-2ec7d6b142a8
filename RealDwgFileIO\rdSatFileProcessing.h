/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSatFileProcessing.h $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

#include <BeSQLite/BeSQLite.h>
#include <Bentley/BeFileListIterator.h>
#include <windows.h>
#include <string>
#include <fstream>
#include <sstream>
#include <functional>
USING_NAMESPACE_BENTLEY_SQLITE

static WString const s_mutexName = L"satToPsMutex";
BeFileName RootDir ();

struct ILog : RefCountedBase
    {
private:
    std::wstringstream m_ss;

public:
    virtual void Log (WCharCP) = 0;

    template <typename T, typename... Args>
    void Log (WStringCR first, T appended, Args... args)
        {
        m_ss << first.c_str ();
        Log (appended, args...);
        }

    template <typename T>
    void Log (T t)
        {
        m_ss << t << std::endl;
        Log (m_ss.str ().c_str ());
        }
    };
using ILogPtr = RefCountedPtr<ILog>;

struct EmptyLog final : ILog
    {
    void Log (WCharCP) override {}
    };

struct IGuid : RefCountedBase
    {
    virtual BeGuid Id () = 0;
    WString IdAsWString ()
        {
        return WString (Id ().ToString ().c_str ());
        }
    };
using IGuidPtr = RefCountedPtr<IGuid>;

struct NewCachedGuid final : IGuid
    {
private:
    BeGuid m_guid;

public:
    BeGuid Id () override;
    };

struct WStringAsGuid final : IGuid
    {
private:
    WString const m_str;

public:
    WStringAsGuid (WStringCR str);
    BeGuid Id () override;
    };

/*=================================================================================**//**
* Directory for sat files and other files
* @bsiclass                                                     JP.Wenger       02/2019
+===============+===============+===============+===============+===============+======*/
struct SatDirectory
    {
private:
    //Each sat to PS conversion session is associated a guid, in order to avoid ambiguities if we have multiple MicroStation sessions running at the same time
    //or the same session opening multiple dwg files sequentially
    IGuidPtr m_guid;

public:
    SatDirectory (IGuidPtr guid);
    BeFileName SessionDir ();
    bvector<BeFileName> FilesInSessionDir (std::function<bool (BeFileNameCR)> predicate);
    bool AnySatFile (BeFileNameR satFile);
    bool HasSatFiles ();
    bool IsEndState ();
    };

/*=================================================================================**//**
* Mutex used by the children processes for asynchronous multiprocessing
* @bsiclass                                                     JP.Wenger       02/2019
+===============+===============+===============+===============+===============+======*/
struct SatMutexChild : RefCountedBase
    {
    virtual HANDLE Open ();
    virtual bool IsValid (HANDLE);
    virtual DWORD WaitToAcquire (HANDLE);
    virtual bool ReleaseSatMutex (HANDLE);
    virtual void Close (HANDLE);
    };
using SatMutexChildPtr = RefCountedPtr<SatMutexChild>;

/*=================================================================================**//**
* Sat file processing (called in spawned process)
* @bsiclass                                                     JP.Wenger       02/2019
+===============+===============+===============+===============+===============+======*/
struct SatFileProcessing
    {
private:
    SatMutexChildPtr m_mutex;
    bool m_notifyToProcess = false;

    //Directory where sat files are moved to be processed.
    //Moving the sat files there allows to release the mutex.
    BeFileName ProcessingDir ();
    BeFileName MovedToProcessingDir (BeFileNameCR satFile);
    void FindFileToProcess ();

protected:
    ILogPtr m_log;
    SatDirectory m_satDir;
    BeFileName m_fileToProcess;

public:
    SatFileProcessing (ILogPtr log, WStringCR guid, SatMutexChildPtr mutex = new SatMutexChild);
    virtual bool ProcessSatFile ();
    bool Process ();
    };

/*=================================================================================**//**
* This is the multiprocessing asynchronous sat to parasolid files conversion producer process
* @bsiclass                                                     JP.Wenger       02/2019
+===============+===============+===============+===============+===============+======*/
struct SatFileConversionMP
    {
private:
    IGuidPtr m_guid;
    SatDirectory m_satDir;

    int m_satId = 1;
    HANDLE m_satMutex = nullptr;
    WString m_mutexName = L"satToPsMutex";
    std::function<bool (WCharCP)> m_outputToSat;
    bvector<PROCESS_INFORMATION> m_pis; //children process information

    bool CreateMutex ();
    BeFileName CurrentDir () const;
    BeFileName ExeFileName () const;
    bool SpawnSatToPsChildProcess ();
    BeFileName EndFile ();

public:
    virtual WString CmdLine ();

    SatFileConversionMP ();
    virtual ~SatFileConversionMP ();

    //Notify the children processes that there are no more sat files to convert.
    void SignalEnd ();

    //Return a filename suitable for multiprocessing asynchronous sat -> PS conversion
    BeFileName NewAcisFileName (WCharCP origin);

    //In production: write the content of the acis geometry to a sat file
    //In test: create a dummy sat file
    void SetSatOutputStrategy (std::function<bool (WCharCP)> const&);

    //create and write data to a sat file asynchronously
    bool OutputToSatAsynch ();

    //spawn children processes which will convert the sat files to PS files asynchronously
    bool SpawnSatToPsChildrenProcesses (int nProcesses);

    //Wait until the children processes have finished, then close.
    bool WaitForFinish ();

    void VisitSatDirectory (std::function<void (SatDirectory&)> const& visitor);
    };

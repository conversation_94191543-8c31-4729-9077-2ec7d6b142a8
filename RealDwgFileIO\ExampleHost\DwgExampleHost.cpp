/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/ExampleHost/DwgExampleHost.cpp $
|
|  $Copyright: (c) 2016 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include    <stdarg.h>
#include    <DgnPlatform\DgnPlatformApi.h>
#include    <Mstn\RealDwg\rDwgSettings.h>
#include    <Mstn\RealDwg\rDwgEnums.r.h>

#include    "DwgExampleHost.h"

using namespace Bentley::RealDwg;

static UShort   s_dwgWeights[] = {0, 5, 9, 13, 15, 18, 20, 25, 30, 35, 40, 50, 53, 60, 70, 80, 90, 100, 106, 120, 140, 158, 200, 211 };
static byte     s_dwgColors[256][3] =
        {
        { 255, 255, 255 },      // 255
        { 255, 255, 255 },      // 0
        { 255,   0,   0 },      // 1
        { 255, 255,   0 },      // 2
        {   0, 255,   0 },      // 3
        {   0, 255, 255 },      // 4
        {   0,   0, 255 },      // 5
        { 255,   0, 255 },      // 6
        { 255, 255, 255 },      // 7
        { 128, 128, 128 },      // 8
        { 192, 192, 192 },      // 9
        { 255,   0,   0 },      // 10
        { 255, 127, 127 },      // 11
        { 204,   0,   0 },      // 12
        { 204, 102, 102 },      // 13
        { 153,   0,   0 },      // 14
        { 153,  76,  76 },      // 15
        { 127,   0,   0 },      // 16
        { 127,  63,  63 },      // 17
        {  76,   0,   0 },      // 18
        {  76,  38,  38 },      // 19
        { 255,  63,   0 },      // 20
        { 255, 159, 127 },      // 21
        { 204,  51,   0 },      // 22
        { 204, 127, 102 },      // 23
        { 153,  38,   0 },      // 24
        { 153,  95,  76 },      // 25
        { 127,  31,   0 },      // 26
        { 127,  79,  63 },      // 27
        {  76,  19,   0 },      // 28
        {  76,  49,  38 },      // 29
        { 255, 127,   0 },      // 30
        { 255, 191, 127 },      // 31
        { 204, 102,   0 },      // 32
        { 204, 153, 102 },      // 33
        { 153,  76,   0 },      // 34
        { 153, 114,  76 },      // 35
        { 127,  63,   0 },      // 36
        { 127,  95,  63 },      // 37
        {  76,  38,   0 },      // 38
        {  76,  57,  38 },      // 39
        { 255, 191,   0 },      // 40
        { 255, 223, 127 },      // 41
        { 204, 153,   0 },      // 42
        { 204, 178, 102 },      // 43
        { 153, 114,   0 },      // 44
        { 153, 133,  76 },      // 45
        { 127,  95,   0 },      // 46
        { 127, 111,  63 },      // 47
        {  76,  57,   0 },      // 48
        {  76,  66,  38 },      // 49
        { 255, 255,   0 },      // 50
        { 255, 255, 127 },      // 51
        { 204, 204,   0 },      // 52
        { 204, 204, 102 },      // 53
        { 153, 153,   0 },      // 54
        { 153, 153,  76 },      // 55
        { 127, 127,   0 },      // 56
        { 127, 127,  63 },      // 57
        {  76,  76,   0 },      // 58
        {  76, 76,   38 },      // 59
        { 191, 255,   0 },      // 60
        { 223, 255, 127 },      // 61
        { 153, 204,   0 },      // 62
        { 178, 204, 102 },      // 63
        { 114, 153,   0 },      // 64
        { 133, 153,  76 },      // 65
        {  95, 127,   0 },      // 66
        { 111, 127,  63 },      // 67
        {  51,  76,   0 },      // 68
        {  66,  76,  38 },      // 69
        { 127, 255,   0 },      // 70
        { 191, 255, 127 },      // 71
        { 102, 204,   0 },      // 72
        { 153, 204, 102 },      // 73
        {  76, 153,   0 },      // 74
        { 114, 153,  76 },      // 75
        {  63, 127,   0 },      // 76
        {  95, 127,  63 },      // 77
        {  38,  76,   0 },      // 78
        {  57,  76,  38 },      // 79
        {  63, 255,   0 },      // 80
        { 159, 255, 107 },      // 81
        {  51, 204,   0 },      // 82
        { 127, 204, 102 },      // 83
        {  38, 153,   0 },      // 84
        {  95, 153,  76 },      // 85
        {  31, 127,   0 },      // 86
        {  79, 127,  63 },      // 87
        {  19,  76,   0 },      // 88
        {  47,  76,  38 },      // 89
        {   0, 255,   0 },      // 90
        { 127, 255, 127 },      // 91
        {   0, 204,   0 },      // 92
        { 102, 204, 102 },      // 93
        {   0, 153,   0 },      // 94
        {  76, 153,  76 },      // 95
        {   0, 127,   0 },      // 96
        {  63, 127,  63 },      // 97
        {   0,  76,   0 },      // 98
        {  38,  76,  38 },      // 99
        {   0, 255,  63 },      // 100
        { 127, 255, 159 },      // 101
        {   0, 204,  51 },      // 102
        { 102, 204, 127 },      // 103
        {   0, 153,  38 },      // 104
        {  76, 153,  95 },      // 105
        {   0, 127,  31 },      // 106
        {  63, 127,  31 },      // 107
        {   0,  76,  19 },      // 108
        {  38,  76,  47 },      // 109
        {   0, 255, 127 },      // 110
        { 127, 255, 191 },      // 111
        {   0, 204, 102 },      // 112
        { 102, 204, 153 },      // 113
        {   0, 153,  76 },      // 114
        {  76, 153, 114 },      // 115
        {   0, 127,  63 },      // 116
        {  63, 127,  95 },      // 117
        {   0,  76,  38 },      // 118
        {  38,  76,  57 },      // 119
        {   0, 255, 191 },      // 120
        { 127, 255, 223 },      // 121
        {   0, 204, 153 },      // 122
        { 102, 204, 178 },      // 123
        {   0, 153, 114 },      // 124
        {  76, 153, 133 },      // 125
        {   0, 127,  95 },      // 126
        {  63, 127, 111 },      // 127
        {   0,  76,  57 },      // 128
        {  38,  76,  66 },      // 129
        {   0, 255, 255 },      // 130
        { 127, 255, 255 },      // 131
        {   0, 204, 204 },      // 132
        { 102, 204, 204 },      // 133
        {   0, 153, 153 },      // 134
        {  76, 153, 153 },      // 135
        {   0, 127, 127 },      // 136
        {  63, 127, 127 },      // 137
        {   0,  76,  76 },      // 138
        {  38,  76,  76 },      // 139
        {   0, 191, 255 },      // 140
        { 127, 223, 255 },      // 141
        {   0, 153, 204 },      // 142
        { 102, 178, 204 },      // 143
        {   0, 114, 153 },      // 144
        {  76, 133, 153 },      // 145
        {   0,  95, 127 },      // 146
        {  63, 111, 127 },      // 147
        {   0,  57,  76 },      // 148
        {  38,  66,  76 },      // 149
        {   0, 127, 255 },      // 150
        { 127, 191, 255 },      // 151
        {   0, 102, 204 },      // 152
        { 102, 153, 204 },      // 153
        {   0,  76, 153 },      // 154
        {  76, 114, 153 },      // 155
        {   0,  63, 127 },      // 156
        {  63,  95, 127 },      // 157
        {   0,  38,  76 },      // 158
        {  38,  57,  76 },      // 159
        {   0,  63, 255 },      // 160
        { 127, 159, 255 },      // 161
        {   0,  51, 204 },      // 162
        { 102, 127, 204 },      // 163
        {   0,  38, 153 },      // 164
        {  76,  95, 153 },      // 165
        {   0,  31, 127 },      // 166
        {  63,  79, 127 },      // 167
        {   0,  19,  76 },      // 168
        {  38,  47,  76 },      // 169
        {   0,   0, 255 },      // 170
        { 127, 127, 255 },      // 171
        {   0,   0, 204 },      // 172
        { 102, 102, 204 },      // 173
        {   0,   0, 153 },      // 174
        {  76,  76, 153 },      // 175
        {   0,   0, 127 },      // 176
        {  63,  63, 127 },      // 177
        {   0,   0,  76 },      // 178
        {  38,  38,  76 },      // 179
        {  63,   0, 255 },      // 180
        { 159, 127, 255 },      // 181
        {  51,   0, 204 },      // 182
        { 127, 102, 204 },      // 183
        {  38,   0, 153 },      // 184
        {  95,  76, 153 },      // 185
        {  31,   0, 127 },      // 186
        {  79,  63, 127 },      // 187
        {  19,   0,  76 },      // 188
        {  47,  38,  76 },      // 189
        { 127,   0, 255 },      // 190
        { 191, 127, 255 },      // 191
        { 102,   0, 204 },      // 192
        { 153, 102, 204 },      // 193
        {  76,   0, 153 },      // 194
        { 114,  76, 153 },      // 195
        {  63,   0, 127 },      // 196
        {  95,  63, 127 },      // 197
        {  38,   0,  76 },      // 198
        {  57,  38,  76 },      // 199
        { 191,   0, 255 },      // 200
        { 223, 127, 255 },      // 201
        { 153,   0, 204 },      // 202
        { 178, 102, 204 },      // 203
        { 114,   0, 153 },      // 204
        { 133,  76, 153 },      // 205
        {  95,   0, 127 },      // 206
        { 111,  63, 127 },      // 207
        {  57,   0,  76 },      // 208
        {  66,  38,  76 },      // 209
        { 255,   0, 255 },      // 210
        { 255, 127, 255 },      // 211
        { 204,   0, 204 },      // 212
        { 204, 102, 204 },      // 213
        { 153,   0, 153 },      // 214
        { 153,  76, 153 },      // 215
        { 127,   0, 127 },      // 216
        { 127,  63, 127 },      // 217
        {  76,   0,  76 },      // 218
        {  76,  38,  76 },      // 219
        { 255,   0, 191 },      // 220
        { 255, 127, 223 },      // 221
        { 204,   0, 153 },      // 222
        { 204, 102, 178 },      // 223
        { 204, 102, 114 },      // 224
        { 153,  76, 133 },      // 225
        { 127,   0,  95 },      // 226
        { 127,  63, 111 },      // 227
        {  76,   0,  57 },      // 228
        {  76,  38,  66 },      // 229
        { 255,   0, 127 },      // 230
        { 255, 127, 191 },      // 231
        { 204,   0, 102 },      // 232
        { 204, 102, 153 },      // 233
        { 153,   0,  76 },      // 234
        { 153,  76, 114 },      // 235
        { 127,   0,  63 },      // 236
        { 127,  63,  95 },      // 237
        {  76,   0,  38 },      // 238
        {  76,  38,  57 },      // 239
        { 255,   0,  63 },      // 240
        { 255, 127, 159 },      // 241
        { 204,   0,  51 },      // 242
        { 204, 102, 127 },      // 243
        { 153,   0,  38 },      // 244
        { 153,  76,  95 },      // 245
        { 127,   0,  31 },      // 246
        { 127,  63,  79 },      // 247
        {  76,   0,  19 },      // 248
        {  76,  38,  47 },      // 249
        {  51,  51,  51 },      // 250
        {  91,  91,  91 },      // 251
        { 132, 132, 132 },      // 252
        { 173, 173, 173 },      // 253
        { 214, 214, 214 },      // 254
        };


/*=================================================================================**//**
* @bsimethod                                                    BentleySystems  
+===============+===============+===============+===============+===============+======*/
class           MyDwgConversionSettings : public IDwgConversionSettings
{
public:
    MyDwgConversionSettings () : m_textStyleNameTemplate (L"Style-$s") , m_saveVersion (DwgFileVersion_Max) {}

    // used when opening DWGs
    virtual bool                    GetDgnSeedFile (WStringR seedFile) const override
        {
        // supply a DGN seed file for DWG load
        seedFile.assign (L"C:\\transeed.dgn"); 
        return true;
        }

    virtual int                     GetCustomObjectDisplayView() const override                 { return  0; }
    virtual ObjectDisplayMode       GetCustomObjectDisplayMode() const override                 { return  CustomObjectDisplay_Default; }
    virtual ProxyShowMode           GetProxyShowMode() const override                           { return  ProxyObject_Show; }
    virtual bool                    CreateDGNMaterials() const override                         { return  true;  }
    virtual bool                    CreateDGNLights() const override                            { return  true;  }
    virtual bool                    GraphicGroupAttributes() const override                     { return  false; }
    virtual bool                    SetAxisLockFromOrthoMode() const override                   { return  false; }
    virtual bool                    OpenModelSpaceAs2d() const override                         { return  false; }
    virtual bool                    OpenPaperSpaceAs2d() const override                         { return  false; }
    virtual bool                    DiscardInvalidEntities() const override                     { return  false; }
    virtual bool                    HyperlinkAsEngineeringLink() const override                 { return  false; }
    virtual bool                    AttributesAsTags() const override                           { return  true; }
    virtual bool                    DisallowLogicalNameFromXRefBlockNames() const override      { return  false; }
    virtual bool                    PreserveSeedOrigin () const override                        { return  false;  }
    virtual double                  GetLineCodeScale () const override                          { return  0.0; }
    virtual double                  GetLineWeightScale () const override                        { return  0.0; }
    virtual bool                    AllowPsolidAcisInteropLogging () const override             { return  false; }
    virtual ColumnTextDropMode      GetColumnTextDropMode () const override                     { return  DropColumnText_Auto; }
    virtual bool                    GetDefaultXrefTreatAsElement () const override              { return  false; }
    virtual void                    GetSheetBackgroundColor (int& red, int& green, int& blue) const override    { red = green = blue = 255; }
    virtual void                    GetDesignBackgroundColor (int& red, int& green, int& blue) const override   { red = green = blue = 0; }
    virtual bool                    OpenPDFAsVector() const override                            { return  true; }

    // used for both opening and saving DWG's
    virtual bool                    MapVPortLocateLockToDisplayUnlocked() const override        { return  false; }
    virtual bool                    IgnoreXData() const override                                { return  false; }

    // used when saving to DWG
    virtual bool                    SetViewportLayerFromClipElement() const override            { return  false; }
    virtual ViewportFreezeMode      GetViewportFreezeMode () const override                     { return  VPFreeze_ViewportsAndGlobal; }
    virtual bool                    AllowLeaderHooklineToBeAdded() const override               { return  false; }
    virtual bool                    IsZeroZCoordinateEnforced() const override                  { return  false; }
    virtual bool                    CreateDWGMaterials() const override                         { return  true;  }
    virtual bool                    CreateDWGLights() const override                            { return  true;  }
    virtual bool                    CreateExtrusionsFromProjectedSolids() const override        { return  false; }
    virtual bool                    CreateSingleBlockFromDuplicateCells() const override        { return  true;  }
    virtual bool                    SaveApplicationData() const override                        { return  false; }
    virtual bool                    SaveMicroStationSettings() const override                   { return  false; }
    virtual bool                    AllowScaledBlocksFromCells() const override                 { return  true;  }
    virtual bool                    CreateOverlaysForReferenceAttachments() const override      { return  false; }
    virtual bool                    DropUnsupportedLineStyles() const override                  { return  false; }
    virtual bool                    DropUnsupportedAreaPatterns() const override                { return  true;  }
    virtual bool                    DropUnsupportedDimensions() const override                  { return  false; }
    virtual bool                    SetUCSFromCurrentACS() const override                       { return  true;  }
    virtual SaveReferencePathMode   GetSaveReferencePathMode() const override                   { return  SaveReferencePath_WhenSameDirectory; }
    virtual bool                    SaveFrontBackClip() const override                          { return  false; }
    virtual bool                    SaveSheetsToSeparateFiles() const override                  { return  false; }
    virtual bool                    CreateTrueColorFromDgnIndices() const override              { return  false; }
    virtual bool                    DisallowSaveDimensionSettings() const override              { return  false; }
    virtual bool                    DisallowBlockNameFromTriForma() const override              { return  false; }
    virtual bool                    CreateBlocksFromTriForma () const override                  { return  false; }
    virtual bool                    ForcePositiveExtrusionForClockwiseArcs() const override     { return  false; }
    virtual bool                    CreatePolylinesFromSplines() const override                 { return  false; }
    virtual bool                    CreateBlockDefinitionsOnLayer0() const override             { return  false; }
    virtual bool                    CreateBlockDefinitionsWithByBlockColor() const override     { return  false; }
    virtual bool                    CreateBlockDefinitionsWithByBlockStyle() const override     { return  false; }
    virtual bool                    CreateBlockDefinitionsWithByBlockWeight() const override    { return  false; }
    virtual bool                    ConvertEmptyEDFToSpace() const override                     { return  false; }
    virtual bool                    OverrideExistingLinetypeDefinitions() const override        { return  false; }
    virtual bool                    EnableDimensionRoundoff() const override                    { return  false; }
    virtual bool                    UniformlyScaleBlocks () const override                      { return  false; }
    virtual bool                    DropNestedCells() const override                            { return  false; }
    virtual bool                    DropTriformaCells() const override                          { return  false; }
    virtual bool                    AllowFarRefDependency() const override                      { return  false; }
    virtual bool                    DropLabelLines() const override                             { return  false; }
    virtual bool                    DisableV7RefClipRotation() const override                   { return  false; }
    virtual bool                    SaveRasterToSharedCell() const override                     { return  false; } 
    virtual bool                    CopyRasterToOutputFolder() const override                   { return  false; } 
    virtual bool                    IsImageFileFormatSupported (WCharCP pi_schemeTypeName, DgnPlatform::ImageFileFormat  imageFileType) const override {return true;} 
    virtual bool                    UseLevelSymbologyOverrides () const override                { return  false; } 
    virtual bool                    ConvertReferences () const override                         { return  false; }
    virtual int                     GetLevelDisplayView () const override                       { return  0; }
    virtual UInt32                  GetMaxOrphanTagsPerSet() const override                     { return  100; }
    virtual UInt32                  GetMaxDictionaryItems() const override                      { return  2000; }
    virtual WStringCR               GetDictionaryWildcardFilters() const override               { return  m_dictionaryWildcardFilters; }
    virtual ConstructionMapping     GetConstructionClassMapping() const override                { return Construction_Layer; }
    virtual PatternMapping          GetPatternClassMapping() const override                     { return Pattern_Ignore; }
    virtual LinearPatternedMapping  GetLinearPatternedClassMapping() const override             { return LinearPatterned_Omit; }
    virtual double                  GetPolyfaceAngleTolerance () const override                 { return 0.5; }
    virtual bool                    SaveBlockUnitsFromFileUnits () const override               { return false; }

    virtual LineStringMapping       GetLineStringMapping (bool planar) const override
        {
        if (planar)
            return LineString_LWPolyline;
        else
            return LineString_3DPolyline;
        }

    virtual SolidSurfaceMapping     GetSolidSurfaceMapping (bool isFlatSurfaceOrSolid) const override
        {
        if (isFlatSurfaceOrSolid)
            return SolidSurface_Acis;
        else
            return SolidSurface_Polyface;
        }

    virtual ClosedMapping           GetComplexShapeMapping (bool useFilled, bool threeD) const override
        {
        if (useFilled)
            return Closed_Hatch;
        else
            return (threeD ? Closed_Region : Closed_Polyline);
        }

    virtual ClosedMapping           GetTriOrQuadMapping (bool useFilled, bool threeD) const override
        {
        if (useFilled)
            return Closed_SolidOrFace;
        else
            return (threeD ? Closed_SolidOrFace : Closed_Polyline);
        }
    virtual ClosedMapping           GetPolygonMapping (bool useFilled, bool threeD) const override
        {
        if (useFilled)
            return Closed_Hatch;
        else
            return (threeD ? Closed_Polyline : Closed_Polyline);
        }
    virtual ClosedMapping           GetGroupedHoleMapping (bool useFilled, bool threeD) const override
        {
        if (useFilled)
            return Closed_Hatch;
        else
            return (threeD ? Closed_Region: Closed_Region);
        }

    virtual DwgSaveUnitMode         GetDwgSaveUnitMode () const override    { return DWGSaveUnitMode_SeedFileMasterUnits; }
    virtual int                     GetDxfSavePrecision () const override   { return 6; }

    virtual DwgFileVersion          GetDwgSaveVersion () const override     { return m_saveVersion; }
    virtual void                    SetDwgSaveVersion (DwgFileVersion newVersion) override  { m_saveVersion = newVersion; }

    virtual int                     GetDwgWeightFromDgnWeight (int dgnWeight) const override
        {
        if (dgnWeight < 0)
            return 0;
        else if (dgnWeight > MAX_LINEWEIGHTS)
            return s_dwgWeights[_countof (s_dwgWeights)-1];

        double      dgnWidthInMM = 0.275 * dgnWeight;
        return GetDwgWeightFromWidthInMM (dgnWidthInMM);
        }

    virtual int                     GetDwgWeightFromWidthInMM (double dgnWidthInMM) const override
        {
        dgnWidthInMM += 50;
        return  s_dwgWeights[StandardIndexFromDwgWeight (dgnWidthInMM)];
        }

    virtual bool                    GetDwgSeedFile (WStringR seedFileName) const override       { return false; }
    virtual bool                    GetDwgShapeFilePath (WStringR shapeFilePath) const override { return false; }
    virtual bool                    GetDefaultDwgLineWeight (int& value) const override         { return false; }
    virtual WStringCR               GetTextStyleNameTemplate () const override                  { return m_textStyleNameTemplate; }
    virtual WStringCR               GetInsertLayerName () const override                        { return m_insertLayerName; }

    virtual DwgOpenUnitMode         GetDwgOpenUnitMode (WCharCP fileName, DwgLinearUnits currentLinearUnits, StandardUnit currentDesignCenterUnits, DwgFileVersion version) const override
        {
        if ( (DWGLinearUnit_Engineering == currentLinearUnits) || (DWGLinearUnit_Architectural == currentLinearUnits) )
            {
            return static_cast <DwgOpenUnitMode> (StandardUnit::EnglishInches);
            }
        else
            {
            // see if the design center units are meaningful.
            if (StandardUnit::None!= currentDesignCenterUnits)
                return static_cast <DwgOpenUnitMode> (currentDesignCenterUnits);
            else
                return static_cast <DwgOpenUnitMode> (StandardUnit::MetricMeters);
            }
        }

    virtual int                     GetDgnWeightFromDwgWeight (int dwgWeight) const override
        {
        int         dgnWeight = (int) (0.5 + ((double)dwgWeight / 13.75));
        return dgnWeight >= MAX_LINEWEIGHTS ? MAX_LINEWEIGHTS-1 : dgnWeight;
        }

    virtual int                     GetDwgFileCodePage () const override                        { return 1252; }
    virtual DwgSaveNonDefaultModelMode  GetNonDefaultModelMode () const override                { return NonDefaultModels_SeparateFiles; }

    virtual void                    GetDisplayColorTable (byte colorTable[768], DwgColorTableQuery queryMode) const override
        {
        // default R2004 color table
        memcpy (colorTable, s_dwgColors, 768);
        }

    int                             StandardIndexFromDwgWeight (double dwgWeightInMM) const
        {
        for (int index=1; index < _countof (s_dwgWeights); index++)
            {
            if (s_dwgWeights[index] > dwgWeightInMM)
                {
                double      low = s_dwgWeights[index-1], high = s_dwgWeights[index];

                return  ((dwgWeightInMM - low) < (high - dwgWeightInMM) ? index-1 : index);
                }
            }
        return _countof (s_dwgWeights)-1;
        }

    virtual bool                    GetMergeVisibleEdgeSettings (HLineSettings& mveSettings) const override { return false; }

private:
    WString             m_textStyleNameTemplate;
    WString             m_insertLayerName;
    WString             m_dictionaryWildcardFilters;
    DwgFileVersion      m_saveVersion;
};  // End of MyDwgConversionSettings

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
DwgExampleHost::DwgExampleHost ()
    {
    m_dwgConversionSettings = new MyDwgConversionSettings ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
DwgExampleHost::~DwgExampleHost ()
    {
    if (NULL != m_dwgConversionSettings)
        delete m_dwgConversionSettings;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
WCharCP         DwgExampleHost::_GetRegistryProductRootKey ()
    {
    if (NULL == m_realDwgRegistryRootKey)
        m_realDwgRegistryRootKey = L"SOFTWARE\\Bentley\\ObjectDBX\\DwgExampleHost\\R19.2";

    return m_realDwgRegistryRootKey;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
WCharCP         DwgExampleHost::_Product ()
    {
    return L"DwgExampleHost";
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgExampleHost::_GetPassword (WCharCP dwgName, DwgExampleHost::FilePasswordOption options, WCharP password, const size_t bufSize)
    {
    printf ("Enter password for file %ls:\n", dwgName);

    WString     format;
    format.Sprintf (L"%%dls", bufSize - 1);
    
    return 1 == wscanf(format.c_str(), password);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgExampleHost::_FatalError (WCharCP format, ...)
    {
    printf ("\nRealDWG fatal error occured:\n\n");

    va_list     varArgs;
    va_start (varArgs, format);
    wprintf (format, varArgs);
    va_end (varArgs);

    printf ("\n\nHit Enter to exit...");
    getchar ();
    exit (0);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgExampleHost::_GetDwgConversionSettings (IDwgConversionSettings*& settings)
    {
    settings = m_dwgConversionSettings;
    return  true;
    }


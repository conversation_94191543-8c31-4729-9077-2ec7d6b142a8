/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdModelIndexItem.cpp $
|
|  $Copyright: (c) 2015 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    <PERSON>      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsRangeValid
(
AcGePoint3d                 min,
AcGePoint3d                 max
)
    {
    return (min.x <= max.x) && (min.y <= max.y) && (min.z <= max.z) &&
           (fabs (min.x) < MAXIMUM_ValidRangeValue) && (fabs (min.y) < MAXIMUM_ValidRangeValue) &&
           (fabs (min.z) < MAXIMUM_ValidRangeValue) && (fabs (max.x) < MAXIMUM_ValidRangeValue) &&
           (fabs (max.y) < MAXIMUM_ValidRangeValue) && (fabs (max.z) < MAXIMUM_ValidRangeValue);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ModelUnitsMatchInputUnits (ModelInfoCP modelInfo, UnitDefinitionCR inputUnits)
    {
    // if the input units match the seed model's master units, use them:
    if (inputUnits.IsEqual(modelInfo->m_masterUnit))
        return  true;

    // if the input units are Inches and the seed model's sub-units are also Inches, use them:
    if (StandardUnit::EnglishInches == inputUnits.IsStandardUnit())
        return inputUnits.IsEqual (modelInfo->m_subUnit);

    // all other cases fall through to the default units handling
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/01
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetModelUnits
(
ModelInfoP                  pModelInfo,
StandardUnit                dgnUnits,
ModelInfoCP                 pDefaultModelInfo,
DwgLinearUnits              acadLinearUnits
)
    {
    // set up defaults in case everything fails.
    UnitDefinition  storageUnitDefinition = UnitDefinition::GetStandardUnit (dgnUnits);

    if (!storageUnitDefinition.IsValid())
        storageUnitDefinition = UnitDefinition::GetStandardUnit (StandardUnit::MetricMeters);

    pModelInfo->m_storageUnit = storageUnitDefinition;

    if (ModelUnitsMatchInputUnits(pDefaultModelInfo, storageUnitDefinition))
        {
        pModelInfo->m_masterUnit = pDefaultModelInfo->m_masterUnit;
        pModelInfo->m_subUnit    = pDefaultModelInfo->m_subUnit;
        }
    else
        {
        switch (dgnUnits)
            {
            case StandardUnit::EnglishInches:
                if (DWGLinearUnit_Engineering == acadLinearUnits || DWGLinearUnit_Architectural == acadLinearUnits)
                    {
                    // In Architectural or engineering files, use Feet and Inches...Else use inches as master.
                    pModelInfo->m_subUnit = pModelInfo->m_storageUnit;
                    pModelInfo->m_masterUnit = UnitDefinition::GetStandardUnit (StandardUnit::EnglishFeet);
                    }
                else
                    {
                    pModelInfo->m_masterUnit = pModelInfo->m_subUnit = pModelInfo->m_storageUnit;
                    pModelInfo->m_subUnit = pModelInfo->m_masterUnit.GetNextSmaller ();
                    }
                break;

            case StandardUnit::EnglishSurveyFeet:               // TR: 116423. Survey feet master should have survey feet subunits as well.
                pModelInfo->m_masterUnit = pModelInfo->m_subUnit = pModelInfo->m_storageUnit;
                break;

            case StandardUnit::MetricMeters:
                pModelInfo->m_masterUnit = pModelInfo->m_storageUnit;
                pModelInfo->m_subUnit    = UnitDefinition::GetStandardUnit (StandardUnit::MetricMillimeters);       // People don't seem to like centimeters...
                break;

            default:
                pModelInfo->m_masterUnit = pModelInfo->m_subUnit = pModelInfo->m_storageUnit;
                pModelInfo->m_subUnit = pModelInfo->m_masterUnit.GetNextSmaller ();
                break;
            }
        }

    // remove unit labels for Decimal, Fractional and Scientific units
    if ( (DWGLinearUnit_Engineering != acadLinearUnits) && (DWGLinearUnit_Architectural != acadLinearUnits) )
        {
        pModelInfo->m_masterUnit.SetLabel (NULL);
        pModelInfo->m_subUnit.SetLabel (NULL);
        }

    return BSISUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgModelIndexItem::~RealDwgModelIndexItem ()
    {
    if (NULL != m_annotationScale)
        delete m_annotationScale;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            GetValidModelName (WStringR modelName, const ACHAR* dwgModelName)
    {
    // since Vancouver, ModelInfo::SetName has enforced valid model name with below exclusice tokens:
    static WChar    s_disallowedChars[] = L"\\/:*?<>|\"\t\n&=,";
    WCharP          nameChars = (WCharP) _alloca (sizeof(WChar) * (wcslen(dwgModelName) + 1));

    if (nullptr == nameChars)
        {
        modelName.assign (L"_");
        return;
        }

    wcscpy (nameChars, dwgModelName);

    RealDwgUtil::ReplaceUnicodeStringChars (nameChars, s_disallowedChars, L'_');

    modelName.assign (nameChars);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgModelIndexItem::RealDwgModelIndexItem
(
ModelId                     modelId,
ModelInfoCP                 pDefaultModelInfo,
AcDbObjectId                blockTableRecordId,
FileHolderP                 pFileHolder,
RealDwgModelType            modelType
)
    {
    m_modelInfo = ModelInfo::Create(DgnModelType::Normal, NULL, false);

    AcDbDatabase*           pDatabase = pFileHolder->GetDatabase();

    m_loadInProgress        = false;
    m_modelId               = modelId;
    m_blockTableRecordId    = blockTableRecordId;
    m_layoutId              = AcDbObjectId::kNull;
    m_modelType             = modelType;
    m_annoAllVisible        = true;
    m_annotationScale       = NULL;
    m_overallViewportId     = AcDbObjectId::kNull;

    this->SetModelInfo (pDefaultModelInfo);

    IDwgConversionSettings& settings = MstnInterfaceHelper::Instance().GetSettings();

    AcDbBlockTableRecordPointer pBlockTableRecord (blockTableRecordId, AcDb::kForRead);
    if (Acad::eOk == pBlockTableRecord.openStatus() && RealDwgUtil::IsModelOrPaperSpace(pBlockTableRecord))
        {
        int         red =0, green = 0, blue = 0;

        if (modelType == RDWGMODEL_TYPE_Sheet)
            {
            m_modelInfo->m_type            = DgnModelType::Sheet;
            m_modelInfo->m_flags.modelIs3D = !settings.OpenPaperSpaceAs2d();
            settings.GetSheetBackgroundColor (red, green, blue);
            }
        else
            {
            m_modelInfo->m_type = DgnModelType::Normal;
            m_modelInfo->m_flags.modelIs3D = !settings.OpenModelSpaceAs2d();
            settings.GetDesignBackgroundColor (red, green, blue);
            }

        m_modelInfo->m_backgroundColor.red       = (byte) red;
        m_modelInfo->m_backgroundColor.green     = (byte) green;
        m_modelInfo->m_backgroundColor.blue      = (byte) blue;
        m_modelInfo->m_flags.useBackgroundColor  = true;

        m_layoutId = pBlockTableRecord->getLayoutId ();

        AcDbLayoutPointer   pLayout (m_layoutId, AcDb::kForRead);

        const ACHAR*    modelInfoName;
        if (Acad::eOk == pLayout.openStatus())
            {
            pLayout->getLayoutName (modelInfoName);
            if (modelType == RDWGMODEL_TYPE_DefaultModel)
                {
                // pLayout->annoAllVisible seems to pick up xdata on paperspace layouts.  It does not return correct value for modelspace.
                m_annoAllVisible = pDatabase->annoAllVisible ();
                // modelspace uses CANNOSCALE
                m_annotationScale = pDatabase->cannoscale ();
                }
            else
                {
                m_annoAllVisible = pLayout->annoAllVisible ();
                // paperspace layout uses the annotation scale from the overall viewport entity
                AcDbObjectIdArray   viewports = pLayout->getViewportArray ();
                if (viewports.length() > 0)
                    {
                    m_overallViewportId = viewports[0];

                    AcDbViewportPointer     overallViewport(m_overallViewportId, AcDb::kForRead);
                    if (Acad::eOk == overallViewport.openStatus())
                        m_annotationScale = overallViewport->annotationScale ();
                    }
                }
            }
        else
            {
            pBlockTableRecord->getName(modelInfoName);
            }

        WString         validName;
        GetValidModelName (validName, modelInfoName);

        m_modelInfo->SetName (validName.GetWCharCP());
        }

    DwgLinearUnits      lUnits   = static_cast <DwgLinearUnits> (pDatabase->lunits());
    DwgOpenUnitMode     openMode = pFileHolder->GetUnitMode();

    if (DWGOpenUnitMode_SeedFileMasterUnits == openMode)
        {
        m_modelInfo->m_storageUnit = pDefaultModelInfo->m_masterUnit;
        m_modelInfo->m_subUnit     = pDefaultModelInfo->m_subUnit;
        m_modelInfo->m_masterUnit  = pDefaultModelInfo->m_masterUnit;
        }
    else if (DWGOpenUnitMode_SeedFileSubUnits == openMode)
        {
        m_modelInfo->m_storageUnit = pDefaultModelInfo->m_subUnit;
        m_modelInfo->m_subUnit     = pDefaultModelInfo->m_subUnit;
        m_modelInfo->m_masterUnit  = pDefaultModelInfo->m_masterUnit;
        }
    else if (DWG_UNITMODE_UseDesignCenter == openMode)
        {
        SetModelUnits (&*m_modelInfo, RealDwgUtil::DgnUnitsFromAcDbUnitsValue(pDatabase->insunits()), pDefaultModelInfo, lUnits);
        }
    else
        {
        SetModelUnits (&*m_modelInfo, static_cast <StandardUnit> (openMode), pDefaultModelInfo, lUnits);
        }

    // If this is an old (08.00.02.xx) reference attachment, unitMode is set to 0.
    // In this case, use the seed file storage resolution directly to match the
    // way that version worked and preserve the attachment transform.
    if ( pFileHolder->GetUseUorPerStorageFromSeedDirectly() ||
         (SUCCESS != pDefaultModelInfo->m_storageUnit.ConvertDistanceFrom (m_modelInfo->m_uorPerStorage, pDefaultModelInfo->m_uorPerStorage, m_modelInfo->m_storageUnit)) || 
         (m_modelInfo->m_uorPerStorage < MINIMUM_UorPerStorage) )
        {
        if (m_modelInfo->m_uorPerStorage < MINIMUM_UorPerStorage)
            RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_InsufficientSeedResolution, false, pFileHolder->GetFileName().c_str());

        m_modelInfo->m_uorPerStorage = pDefaultModelInfo->m_uorPerStorage;
        }

    if (IsRangeValid (pDatabase->extmin(), pDatabase->extmax()))
        {
        double          diagonal = pDatabase->extmin().distanceTo (pDatabase->extmax());
        if (diagonal * m_modelInfo->m_uorPerStorage > MAXIMUM_DesignDiagonal)
            {
            while (diagonal * m_modelInfo->m_uorPerStorage > MAXIMUM_DesignDiagonal)
                m_modelInfo->m_uorPerStorage /= 10.0;

            RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_ExcessiveSeedResolution, false, pFileHolder->GetFileName().c_str());
            }
        }

    if (!settings.PreserveSeedOrigin())
        m_modelInfo->m_globalOrigin.x = m_modelInfo->m_globalOrigin.y = m_modelInfo->m_globalOrigin.z = 0.0;

    RealDwgUtil::DPoint3dFromGePoint3d (m_modelInfo->m_insertBase, pDatabase->insbase());

    // if we're opening as 2D, do not allow a z-component in insertionBase.
    if (0 == m_modelInfo->m_flags.modelIs3D)
        m_modelInfo->m_insertBase.z = 0.0;

    // ACAD has ANNOTATIVEDWG since R2008
    if (RDWGMODEL_TYPE_DefaultModel == modelType)
        m_modelInfo->SetIsAnnotationCell (pDatabase->annotativeDwg());

    RotMatrix       rotationToDGN;
    rotationToDGN.InitFromScaleFactors (m_modelInfo->m_uorPerStorage, m_modelInfo->m_uorPerStorage, m_modelInfo->m_uorPerStorage);

    DPoint3d        translationToDGN;
    translationToDGN.Init (m_modelInfo->m_globalOrigin.x, m_modelInfo->m_globalOrigin.y, m_modelInfo->m_flags.modelIs3D ? m_modelInfo->m_globalOrigin.z : 0.0);

    Transform       transformToDGN;
    transformToDGN.InitFrom (rotationToDGN, translationToDGN);

    transformToDGN.Multiply (m_modelInfo->m_insertBase);

    m_modelInfo->m_solidExtent = pDefaultModelInfo->m_solidExtent;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgModelIndexItem::GetFrozenLayers
(
AcDbObjectIdArray&          frozenIds
) const
    {
    AcDbBlockTableRecordPointer     pBlock (m_blockTableRecordId, AcDb::kForRead);
    if (Acad::eOk != pBlock.openStatus())
        return;

    AcDbLayoutPointer               pLayout (pBlock->getLayoutId(), AcDb::kForRead);
    if (Acad::eOk != pLayout.openStatus())
        return;

    AcDbObjectIdArray               viewports = pLayout->getViewportArray();
    if (viewports.length() <= 0)
        {
        // An inactive layout contains no viewports, so we have to get it from paperspace block.
        if (GetRealDwgModelType() == RDWGMODEL_TYPE_Sheet)
            viewports.append (RealDwgUtil::GetFirstViewportFromPaperspace(pBlock));

        if (viewports.length() <= 0 || viewports[0].isNull())
            return;
        }

    AcDbViewportPointer             pOverallVport (viewports[0], AcDb::kForRead);
    if (Acad::eOk != pOverallVport.openStatus())
        return;

    pOverallVport->getFrozenLayerList (frozenIds);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgModelIndexItem::SetFrozenLayers
(
ElementId*                  pFrozenIds,
int                         nIds,
ConvertFromDgnContext&      context
)
    {
    // Find overall viewport.
    AcDbBlockTableRecordPointer     pBlock (m_blockTableRecordId, AcDb::kForRead);
    if (Acad::eOk != pBlock.openStatus())
        return;

    AcDbLayoutPointer               pLayout (pBlock->getLayoutId(), AcDb::kForRead);
    if (Acad::eOk != pLayout.openStatus())
        return;

    AcDbObjectIdArray               viewports = pLayout->getViewportArray();
    if (viewports.length() <= 0)
        {
        // An inactive layout contains no viewports, so we have to get it from paperspace block.
        if (GetRealDwgModelType() == RDWGMODEL_TYPE_Sheet)
            viewports.append (RealDwgUtil::GetFirstViewportFromPaperspace(pBlock));

        if (viewports.length() <= 0 || viewports[0].isNull())
            return;
        }

    AcDbViewportPointer             pOverallVport (viewports[0], AcDb::kForWrite);
    if (Acad::eOk != pOverallVport.openStatus())
        return;

    pOverallVport->thawAllLayersInViewport();

    if (0 == nIds)
        return;

    AcDbObjectIdArray               frozenLayerArray;
    for (int iId = 0; iId < nIds; iId++)
        {
        AcDbObjectId                frozenLayerId;
        if (! (frozenLayerId = context.ExistingObjectIdFromElementId (pFrozenIds[iId])).isNull())
            frozenLayerArray.append (frozenLayerId);
        }
    pOverallVport->freezeLayersInViewport (frozenLayerArray);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        RealDwgModelIndexItem::SetModelInfo (ModelInfoCP modelInfo)
    {
    m_modelInfo = modelInfo->MakeCopy();
    m_modelInfo->SetDescription (NULL);
    m_modelInfo->SetDefaultRefLogical (NULL);

//  m_modelInfo->SetIs3d (!DwgSettings.openModelSpaceAs2D());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            RealDwgModelIndexItem::SetAnnotationScale (AcDbAnnotationScale* newScale)
    {
    if (nullptr != m_annotationScale)
        delete m_annotationScale;

    m_annotationScale = newScale;
    }



/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/filehandler/DwgFileHandlerMod.r $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include <Mstn\MdlApi\rscdefs.r.h>

#define DGNFILE_FORMAT_DWG  3
#define DGNFILE_FORMAT_DXF  4

AuxFileHandlerInfoTable 1 =
{
    // leave filename and filter entries blank so they don't get added to the file list.
    //  (DWG and DXF are already in there, early in the list).
    { DGNFILE_FORMAT_DWG, 1, L"", L"", L"", L"", "", { L"*" }},
    { DGNFILE_FORMAT_DXF, 1, L"", L"", L"", L"", "", { L"dxf" }},
    { DGNFILE_FORMAT_DXF, 1, L"", L"", L"", L"", "", { L"dxb" }},
};

/*----------------------------------------------------------------------+
|   DllMdlApp resource                                                  |
+----------------------------------------------------------------------*/
DllMdlApp   1 =
    {
    L"DwgFileHandler", L"DwgFileHandler"
    };

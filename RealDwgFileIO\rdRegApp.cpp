/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdRegApp.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

// ================================ Conversion From Dwg to Dgn ==========================
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SaveRegisteredApplicationsToDgn ()
    {
    AcDbObjectId                tableId = this->GetDatabase()->regAppTableId ();
    AcDbRegAppTablePointer      acRegappTable (tableId, AcDb::kForRead);
    AcDbRegAppTableIterator*    iter = NULL;

    if (Acad::eOk != acRegappTable.openStatus() || Acad::eOk != acRegappTable->newIterator (iter))
        return  CantOpenObject;

    RegisteredAppCollectionPtr  regappCollection = RegisteredAppCollection::Create (*this->GetFile());
    if (!regappCollection.IsValid())
        return  OutOfMemoryError;

    regappCollection->SetTableElementId (this->ElementIdFromObjectId(tableId));

    // TFS#63374 has shown us such a big regapp table that it took an overnight to iterate through...
    UInt32                      entryLimit = this->GetSettings().GetMaxDictionaryItems ();
    UInt32                      entryCount = 0;

    for (iter->start(); !iter->done(); iter->step())
        {
        AcDbObjectId            recordId;
        if (Acad::eOk != iter->getRecordId(recordId))
            continue;

        AcString                        regappName;
        AcDbRegAppTableRecordPointer    acRegapp (recordId, AcDb::kForRead);

        if (Acad::eOk != acRegapp.openStatus() || Acad::eOk != acRegapp->getName(regappName))
            continue;

        if (entryCount++ >= entryLimit)
            {
            DIAGNOSTIC_PRINTF ("The regapp table has been truncated by %d\n", entryCount);
            break;
            }

        if (0 != regappName.compareNoCase(L"ACAD") && 0 != regappName.compareNoCase(L"ACAD_PSEXT"))
            regappCollection->AddRegisteredApp (regappName.kwszPtr(), this->ElementIdFromObjectId(recordId));
        }
    delete iter;

    regappCollection->SaveFromCacheLoader ();

    return  RealDwgSuccess;
    }



// ================================ Conversion From Dgn to Dwg ==========================
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::SaveRegisteredApplicationsToDatabase ()
    {
    RegisteredAppCollectionPtr  regappCollection = RegisteredAppCollection::Create (*this->GetFile());
    if (!regappCollection.IsValid())
        return;

    // TODO: delete regapps from original DWG list

    for each (RegisteredAppPtr regapp in *regappCollection.get())
       this->SaveRegappToDatabase (regapp);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SaveRegappToDatabase (RegisteredAppPtr regapp)
    {
    AcDbRegAppTablePointer  acRegappTable(this->GetDatabase()->regAppTableId(), AcDb::kForWrite);
    if (Acad::eOk != acRegappTable.openStatus())
        return  CantOpenObject;

    // Do not save default ACAD regapps
    // This should only happen in an old file. (TR: 93057).
    WStringCR               regappName = regapp->GetName ();
    if (0 == regappName.CompareToI(L"ACAD") || regappName.StartsWith(L"ACAD_") || regappName.StartsWith(L"AcadAnno"))
        return  AcadSpecificRegApp;

    AcDbRegAppTableRecordPointer    acRegapp;
    ElementId                       elementId = regapp->GetElementId ();
    AcDbObjectId                    existingObjectId = this->ExistingObjectIdFromElementId (elementId);
    if (existingObjectId.isNull())
        acRegapp.create ();
    else
        acRegapp.open (existingObjectId, AcDb::kForWrite);

    if (Acad::eOk != acRegapp.openStatus())
        return  CantOpenObject;

    AcString                existingName;
    if (Acad::eOk != acRegapp->getName(existingName) || 0 != regappName.compare(existingName.kwszPtr()))
        {
        // do not attempt to add dup regapps
        if (acRegappTable->has(regappName.c_str()))
            return  DuplicatedIds;

        Acad::ErrorStatus   es = acRegapp->setName (regappName.c_str());

        if (Acad::eOk == es && this->AddRecordToSymbolTable(acRegappTable, acRegapp, elementId).isValid())
            return  RealDwgSuccess;
        }

    return CantCreateRegApp;
    }

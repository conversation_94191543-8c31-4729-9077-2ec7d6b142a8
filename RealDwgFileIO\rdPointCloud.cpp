/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdPointCloud.cpp $
|
|  $Copyright: (c) 2016 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          06/16
+===============+===============+===============+===============+===============+======*/
class PointsCreator : public IAcDbPointCloudPointProcessor
{
private:
    MSElementDescrP         m_cellHeader;
    MSElementDescrP         m_lastChild;
    ConvertToDgnContextR    m_toDgnContext;

public:
    explicit PointsCreator (MSElementDescrP& cell, ConvertToDgnContextR context) : m_cellHeader(cell), m_toDgnContext(context)
        {
        m_lastChild = nullptr;
        }
    virtual ~PointsCreator () {}

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/16
+---------------+---------------+---------------+---------------+---------------+------*/
virtual ProcessSate     process (const IAcDbPointCloudDataBuffer* buffer) override
    {
    // get DWG points by color
    const AcGePoint3d*                      points = buffer->points();
    const IAcDbPointCloudDataBuffer::RGBA*  colors = buffer->colors();
    const AcGeMatrix3d&                     toWcs = buffer->transform ();

    // prepare DGN data
    TransformCR         toDgn = m_toDgnContext.GetTransformToDGN ();
    DgnModelR           model = *m_toDgnContext.GetModel ();
    bool                is3D = m_toDgnContext.GetThreeD ();
    DSegment3d          lineSeg;
    EditElementHandle   pointEeh;

    for (int i = 0; i < buffer->numPoints(); i++)
        {
        AcGePoint3d     gePoint = points[i];
        gePoint.transformBy (toWcs);

        RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[0], gePoint);
        toDgn.Multiply (lineSeg.point[0]);

        if (!is3D)
            lineSeg.point[0].z = 0.0;

        lineSeg.point[1] = lineSeg.point[0];

        if (BSISUCCESS == LineHandler::CreateLineElement(pointEeh, nullptr, lineSeg, is3D, model))
            {
            MSElementDescrP pointElmdscr = pointEeh.ExtractElementDescr ();
            if (nullptr == pointElmdscr)
                return  ProcessSate::Abort;

            if (nullptr != colors)
                pointElmdscr->el.hdr.dhdr.symb.color = m_toDgnContext.GetDgnColorIndexFromRGB (colors[i][0], colors[i][1], colors[i][2]);

            m_cellHeader->AppendChild (&m_lastChild, pointElmdscr);
            }
        }

    return ProcessSate::Continue;
    }

};  // PointsCreator

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          06/16
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtPointCloudEx : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/16
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbPointCloudEx*   pointcloudEx = AcDbPointCloudEx::cast (acObject);
    if (nullptr == pointcloudEx)
        return context.WorldDrawToElements(AcDbEntity::cast(acObject), outElement);

    // skip point clouds all together if LOD is set to 0:
    int                 lodLevel = context.GetPointCloudsLevelOfDetails ();
    if (lodLevel < 1 || lodLevel > 100)
        return  RealDwgIgnoreEntity;

    // create a point cloud cell header 
    NormalCellHeaderHandler::CreateOrphanCellElement (outElement, pointcloudEx->isA()->dxfName(), context.GetThreeD(), *context.GetModel());
    context.ElementHeaderFromEntity (outElement, pointcloudEx);

    // extract cell header elmdscr
    MSElementDescrP     cellHeader = outElement.ExtractElementDescr ();
    if (nullptr == cellHeader)
        return  OutOfMemoryError;

    // lock the element until we support editing of it:
    cellHeader->el.hdr.ehdr.locked = true;
    
    PointsCreator*      pointsCreator = new PointsCreator (cellHeader, context);
    if (nullptr == pointsCreator)
        return  OutOfMemoryError;

    // extract points by color
    IAcDbPointCloudDataBuffer::DataType type = IAcDbPointCloudDataBuffer::kColor;

    Acad::ErrorStatus   es = pointcloudEx->traverseAllPointData (pointsCreator, nullptr, type, lodLevel);

    if (Acad::eOk == es && nullptr != cellHeader->h.firstElem)
        outElement.SetElementDescr (cellHeader, true, false);
    else
        cellHeader->Release ();

    delete pointsCreator;

    return  outElement.IsValid() ? RealDwgSuccess : CantExtractPoints;
    }

};  // ToDgnExtPointCloudEx

/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSatFileProcessing.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

#include <PSolid\parasolid_kernel.h>
#include <PSolidAcisInterop\PSolidAcisInterop.h>
#include <DgnPlatform/DgnPlatformLib.h>
#include <DgnPlatform/DgnPlatformApi.h>

#include <Mstn\RealDWG\rSatToPs.h>

#include <Bentley\BeFileListIterator.h>
#include <chrono>
#include <thread>
USING_NAMESPACE_BENTLEY_SQLITE
USING_NAMESPACE_BENTLEY_DGNPLATFORM
using namespace Bentley::RealDwg;

struct SatFakeFileProcessing final : SatFileProcessing
    {
    SatFakeFileProcessing (ILogPtr log, WStringCR guid)
        :SatFileProcessing (log, guid)
        {}
    bool ProcessSatFile ()
        {
        BeFileName psFileName = 
            m_satDir
            .SessionDir()
            .AppendToPath (BeFileName::GetFileNameWithoutExtension (m_fileToProcess.c_str ()).c_str ())
            .AppendExtension (L"x_b");

        BeFile psFile;
        psFile.Create (psFileName.c_str ());

        return BeFileNameStatus::Success == BeFileName::BeDeleteFile (m_fileToProcess.c_str ());
        }
    };

struct UniqueLog final : ILog
    {
private:
    UniqueLogFileName& m_fileName;

    bool m_init = false;
    std::wofstream m_out;

    void Init ()
        {
        if (m_init)
            return;

        BeFileName logFileName = m_fileName.FileName ();

        BeFile logFile;
        logFile.Create (logFileName.c_str ());
        logFile.Close ();
        
        m_out.open (logFileName.c_str (), std::ios::out);
        m_init = true;
        }

public:
    UniqueLog (UniqueLogFileName& fileName)
        :m_fileName (fileName)
        {}
    ~UniqueLog ()
        {
        if (m_out.is_open ())
            m_out.close ();
        }

    void Log (WCharCP message) override
        {
        Init ();
        m_out << message << std::endl;
        }
    };

struct MyDgnHost : DgnPlatformLib::Host
    {
public:
    MyDgnHost ()
        {
        DgnPlatformLib::Initialize (*this, true, true);
        setlocale (LC_CTYPE, "");
        }
    };

int wmain (int argc, wchar_t* argv[])
    {
    if (argc < 2)
        return -1;

    MyDgnHost host;

    UniqueLogFileName logFileName (argv[1]);
    ILogPtr log = new UniqueLog (logFileName);

    if (argc >= 3)
        {
        if (WString (argv[2]) == L"TESTING")
            return SatFakeFileProcessing (log, argv[1]).Process () ? 0 : 1;
        if (WString (argv[2]) == L"LOGTESTING")
            {
            log->Log (L"Log testing");
            return 0;
            }
        }

    int ret = SatFileProcessing (log, argv[1]).Process () ? 0 : 1;
    return ret;
    }

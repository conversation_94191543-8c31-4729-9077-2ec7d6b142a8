/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/filehandler/DwgFileHandler.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include    <DgnPlatform/DgnPlatform.h>
#include    <Mstn/ISessionMgr.h>
#include    <Mstn/MdlApi/msrmgr.h>
#include    <Mstn/MdlApi/mdl.h>         // DLLEXPORT MdlMain
#include    <MStn/MdlApi/dloadlib.h>    // extern struct tcb
#include    "DwgMstnHost.h"

USING_NAMESPACE_BENTLEY

DwgFileHandler::DwgMstnHost*    g_dwgMstnHost;

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          08/14
+===============+===============+===============+===============+===============+======*/
struct MasterDwgSessionMonitor : public Bentley::MstnPlatform::SessionMonitor
    {
    virtual void _OnMasterFileStart (DgnFileR dgnFile) override
        {
        DgnFileFormatType   format = DgnFileFormatType::Unknown;

        // MasterFileStartCaller calls us at the end of master file open after all xRef's have been created.
        if (BSISUCCESS == dgnFile.GetVersion(&format, nullptr, nullptr) && (DgnFileFormatType::DWG == format || DgnFileFormatType::DXF == format))
            {
            // get the DWG default model
            DgnModelP           defaultModel = dgnFile.FindLoadedModelById (dgnFile.GetDefaultModelId());
            DgnAttachmentArrayP refList;

            if (nullptr != defaultModel && nullptr != (refList = defaultModel->GetDgnAttachmentsP()))
                {
                // fix each DgnAttachment that has UseAnnotationScale set due to out of sync INSUNITS stored in an xRef block:
                for (auto* ref : *refList)
                    {
                    if (nullptr != ref && ref->IsDWGFormat() && ref->UseAnnotationScale())
                        {
                        double  geomScale = AnnotationScale::GetUserScaleFromRefToRoot (*ref);
                        if (fabs(geomScale - 1.0) > 0.01)
                            ref->SetUseAnnotationScale (false);
                        }
                    }
                }
            }
        }

    virtual void _OnMasterFileStop (DgnFileR dgnFile, bool changingFiles) override
        {
        if (nullptr != g_dwgMstnHost)
            {
            // clear up the DWG master file pointer held in our host prior to closing the file
            bool            clearMaster = true;
            AcDbDatabaseP   masterDwg = nullptr;
            DgnFileP        masterDgn = nullptr;
            if (changingFiles && g_dwgMstnHost->_GetMasterDwgFile(masterDwg, &masterDgn) && masterDgn != &dgnFile)
                clearMaster = false;

            if (clearMaster)
                g_dwgMstnHost->_SetMasterDwgFile (nullptr, &dgnFile);

            // save tcb->activeViewGroup to the host if it is different than what is stored in the file header:
            ElementId   activeViewgroupInFile = dgnFile.GetActiveViewGroupId ();
            if (INVALID_ELEMENTID != tcb->activeViewGroup && 0 != tcb->activeViewGroup && activeViewgroupInFile != tcb->activeViewGroup)
                g_dwgMstnHost->_SetActiveViewGroup (tcb->activeViewGroup);
            else
                g_dwgMstnHost->_SetActiveViewGroup (0);
            }
        }
    };
static MasterDwgSessionMonitor  s_sessionMonitor;


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
extern "C" void MdlMain (int, WCharCP[])
    {
    RscFileHandle       rscHandle = NULLRSC;
    mdlResource_openFile (&rscHandle, NULL, RSC_READONLY);

    // create a MicroStation host app for DwgPlatform
    g_dwgMstnHost = new Bentley::DwgFileHandler::DwgMstnHost (rscHandle);

    RealDwg::DwgPlatformHost::Initialize (*g_dwgMstnHost, false, true);

    ISessionMgr::AddSessionMonitor (s_sessionMonitor);
    }

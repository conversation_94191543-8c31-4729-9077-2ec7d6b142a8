/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgWorldDraw.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Ray.Bentley      07/02
+===============+===============+===============+===============+===============+======*/
class                       MstnGiContext : public AcGiContext
    {
    AcDbDatabase*           m_pDb;

public:
    MstnGiContext() {}

    // methods of AcGiContext that are overridden.
    virtual AcDbDatabase*   database() const override { return m_pDb; }
    virtual Adesk::Boolean  isPsOut() const override { return false; }
    virtual Adesk::Boolean  isPlotGeneration() const override { return false; }
    virtual bool            isBoundaryClipping() const override { return false; }

    void                    Initialize (AcDbDatabase * pDb) { m_pDb = pDb; }
    virtual AcGiDrawable*   OpenDrawable (AcDbStub* id) { return (AcGiDrawable*) 0; }
    };

/*=================================================================================**//**
* @bsiclass                                                     Ray.Bentley      07/02
+===============+===============+===============+===============+===============+======*/
class                       MstnViewport : public AcGiViewport
    {
    AcGeVector3d            m_viewDirection;
    AcGePoint3d             m_cameraLocation;
    AcGePoint3d             m_cameraTarget;
    AcGeVector3d            m_cameraUpDirection;

public:
    MstnViewport()      { m_viewDirection = AcGeVector3d (0.0, 0.0, 1.0); }

    // these methods override those in AcGiViewport.
    virtual void            getModelToEyeTransform (AcGeMatrix3d& transform) const override   { transform = AcGeMatrix3d::kIdentity; }
    virtual void            getEyeToModelTransform (AcGeMatrix3d& transform) const override   { transform = AcGeMatrix3d::kIdentity; }
    virtual void            getWorldToEyeTransform (AcGeMatrix3d& transform) const override   { transform = AcGeMatrix3d::kIdentity; }
    virtual void            getEyeToWorldTransform (AcGeMatrix3d& transform) const override   { transform = AcGeMatrix3d::kIdentity; }

    virtual Adesk::Boolean  isPerspective() const override                              { return false; }
    virtual Adesk::Boolean  doPerspective (AcGePoint3d&) const override                 { return false; }
    virtual Adesk::Boolean  doInversePerspective (AcGePoint3d&) const override          { return false; }

    virtual void            getNumPixelsInUnitSquare(const AcGePoint3d& givenWorldpt, AcGePoint2d& pixelArea, bool includePerspective = true) const override {};

    void                    getCameraLocation(AcGePoint3d& location)    const override { location   = m_cameraLocation; }
    void                    getCameraTarget(AcGePoint3d& target)        const override { target     = m_cameraTarget; }
    void                    getCameraUpVector(AcGeVector3d& upDir)      const override { upDir      = m_cameraUpDirection; }

    virtual Adesk::ULongPtr viewportId()   const override                               { return 0; }
    virtual Adesk::Int16    acadWindowId() const override                               { return 0; }
    virtual void            getViewportDcCorners (AcGePoint2d& lower_left, AcGePoint2d& upper_right) const override { }

    virtual Adesk::Boolean  getFrontAndBackClipValues (Adesk::Boolean& clip_front, Adesk::Boolean& clip_back, double& front, double& back) const override {clip_front = false, clip_back = false; return false; }

    virtual double          linetypeScaleMultiplier() const override                    { return 1.0; }
    virtual double          linetypeGenerationCriteria() const override                 { return 1.0; }
    virtual Adesk::Boolean  layerVisible (const AcDbObjectId& idLayer) const            { return true; }

    virtual AcGeVector3d    viewDir () const override                                   { return m_viewDirection; }


    // our method
    void                    SetViewDirection (AcGeVector3d& viewDirection, AcGeVector3d& cameraUp)
        {
        m_viewDirection     = viewDirection;
        if (viewDirection == AcGeVector3d::kZAxis)
            {
            SetTopView (cameraUp);
            return;
            }

        m_cameraTarget.setToSum (m_cameraLocation, viewDirection);

        if (viewDirection == AcGeVector3d::kYAxis)
            m_cameraUpDirection = m_viewDirection.crossProduct (AcGeVector3d::kZAxis);
        else
            m_cameraUpDirection = cameraUp;
        m_cameraUpDirection.normalize ();
        }

    void                    SetTopView (AcGeVector3d& cameraUp)
        {
        m_viewDirection     = AcGeVector3d (0.0, 0.0, 1.0);
        m_cameraLocation    = AcGePoint3d (0.0, 0.0, 0.0);
        m_cameraTarget      = AcGePoint3d (0.0, 0.0, -1.0);
        m_cameraUpDirection = cameraUp;
        }

    };      // MSTNViewport;

/*=================================================================================**//**
* @bsiclass                                                     Ray.Bentley      07/02
+===============+===============+===============+===============+===============+======*/
class MstnSubEntityTraits : public AcGiSubEntityTraits
    {
private:
    AcCmEntityColor             m_cmColor;
    AcDbObjectId                m_layerId;
    AcDbObjectId                m_lineTypeId;
    Adesk::LongPtr              m_markerId;
    AcGiFillType                m_fillType;
    AcDb::LineWeight            m_lineWeight;
    double                      m_lineTypeScale;
    double                      m_thickness;
    AcDb::PlotStyleNameType     m_plotStyleNameType;
    AcDbObjectId                m_plotStyleNameId;
    AcDbObjectId                m_materialId;
    AcDbObjectId                m_visualStyleId;
    AcCmTransparency            m_transparency;
    AcDbEntityCP                m_sourceEntity;

public:
    MstnSubEntityTraits  ()                                             { };
    ~MstnSubEntityTraits ()                                             { };

    // Set properties of drawn objects.
    //
    virtual void      setColor              (const Adesk::UInt16 color)         override    { m_cmColor.setColorIndex (color); }
    virtual void      setTrueColor          (const AcCmEntityColor& color)      override    { m_cmColor     = color; }
    virtual void      setLayer              (const AcDbObjectId layerId)        override    { m_layerId     = layerId; }
    virtual void      setLineType           (const AcDbObjectId linetypeId)     override    { m_lineTypeId  = linetypeId; }
    virtual void      setSelectionMarker    (const Adesk::LongPtr markerId)     override    { m_markerId    = markerId; }
    virtual void      setFillType           (const AcGiFillType filltype)       override    { m_fillType    = filltype; }
    virtual void      setLineWeight         (const AcDb::LineWeight lw)         override    { m_lineWeight  = lw; }
    virtual void      setLineTypeScale      (double dScale = 1.0)               override    { m_lineTypeScale = dScale; }
    virtual void      setThickness          (double dThickness)                 override    { m_thickness  = dThickness; }
    virtual void      setVisualStyle        (const AcDbObjectId visualStyleId)  override    { m_visualStyleId = visualStyleId; }
    virtual void      setPlotStyleName      (AcDb::PlotStyleNameType type, const AcDbObjectId & id = AcDbObjectId::kNull) override {}
    virtual void      setTransparency       (const AcCmTransparency& transparency) override { m_transparency = transparency; }
    virtual void      setMaterial           (const AcDbObjectId materialId)     override    { m_materialId = materialId; }

    // Return current settings.
    //
    virtual Adesk::UInt16       color               () const override { return m_cmColor.colorIndex(); }
    virtual AcCmEntityColor     trueColor           () const override { return m_cmColor; }
    virtual AcDbObjectId        layerId             () const override { return m_layerId; }
    virtual AcDbObjectId        lineTypeId          () const override { return m_lineTypeId; }
    virtual AcGiFillType        fillType            () const override { return m_fillType; }
    virtual AcDb::LineWeight    lineWeight          () const override { return m_lineWeight; }
    virtual double              lineTypeScale       () const override { return m_lineTypeScale; }
    virtual double              thickness           () const override { return m_thickness; }
    virtual AcDbObjectId        visualStyle         () const override { return m_visualStyleId; }
    virtual AcDbObjectId        materialId          () const override { return m_materialId; }
    virtual AcCmTransparency    transparency        () const override { return m_transparency; }

    // Our methods
    AcDbEntityCP        GetSourceEntity     () {return m_sourceEntity; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                    Initialize
(
AcDbEntityCP            pEntity,
const AcDbObjectId&     visualStyleId
)
    {
    m_cmColor           = pEntity->entityColor();
    m_layerId           = pEntity->layerId();
    m_lineTypeId        = pEntity->linetypeId();
    m_lineWeight        = pEntity->lineWeight();
    m_markerId          = 0;
    m_fillType          = kAcGiFillNever;
    m_lineTypeScale     = 1.0;
    m_thickness         = 0.0;
    m_plotStyleNameType = AcDb::kPlotStyleNameByLayer;
    m_visualStyleId     = visualStyleId;
    m_materialId        = pEntity->materialId ();
    m_transparency      = pEntity->transparency ();
    m_sourceEntity      = pEntity;
    }

    };  // MstnSubEntityTraits

/*=================================================================================**//**
* @bsiclass                                                     Ray.Bentley      07/02
+===============+===============+===============+===============+===============+======*/
class MstnGeometry : public AcGiWorldGeometry, public AcGiViewportGeometry
    {
private:
    std::stack<AcGeMatrix3d>    m_xForm;
    AcGiWorldDraw*              m_pWorldDraw;
    AcGiViewportDraw*           m_pViewportDraw;
    ConvertToDgnContext*        m_toDgnContext;
    mutable bool                m_isInBlockRecord;

    mutable Transform           m_currentTransformToDGN;
    EditElementHandleR          m_elementHandle;
    mutable MSElementDescrP     m_pLastChild;
    MstnSubEntityTraits*        m_pSubEntityTraits;

public:
    MstnGeometry  (EditElementHandleR elementHandle) : m_elementHandle (elementHandle) { }
    ~MstnGeometry () { }

    // from AcGiWorldGeometry:
    virtual void            setExtents (AcGePoint3d *pNewExtents) const override { }
    virtual void            startAttributesSegment () override {}

    // From AcGiViewportGeometry
    virtual Adesk::Boolean  polylineEye (const Adesk::UInt32 nbPoints, const AcGePoint3d* pPoints) const override {return Adesk::kFalse;}
    virtual Adesk::Boolean  polygonEye(const Adesk::UInt32 nbPoints, const AcGePoint3d* pPoints) const override {return Adesk::kFalse;}

    virtual Adesk::Boolean  polylineDc(const Adesk::UInt32 nbPoints, const AcGePoint3d* pPoints) const override {return Adesk::kFalse;}
    virtual Adesk::Boolean  polygonDc(const Adesk::UInt32 nbPoints, const AcGePoint3d* pPoints) const override {return Adesk::kFalse;}

    virtual Adesk::Boolean  rasterImageDc (const AcGePoint3d& origin, const AcGeVector3d& u, const AcGeVector3d& v, const AcGeMatrix2d& pixelToDc,
                            AcDbObjectId entityId, AcGiImageOrg imageOrg, Adesk::UInt32 imageWidth, Adesk::UInt32 imageHeight, Adesk::Int16 imageColorDepth,
                            Adesk::Boolean transparency, ImageSource source, const AcGeVector3d& unrotatedU, const AcGiImageOrg origionalImageOrg,
                            const AcGeMatrix2d& unrotatedPixelToDc, const Adesk::UInt32 unrotatedImageWidth, const Adesk::UInt32 unrotatedImageHeight) const override {return Adesk::kFalse;}
    virtual Adesk::Boolean  ownerDrawDc (Adesk::Int32 vpnumber, Adesk::Int32 left, Adesk::Int32 top, Adesk::Int32 right, Adesk::Int32 bottom, const OwnerDraw* pOwnerDraw) const override {return Adesk::kFalse;}

    // our method.
    void                    SetViewportDraw (AcGiViewportDraw* pViewportDraw) { m_pViewportDraw = pViewportDraw; }
    EditElementHandleR      GetElementHandle () { return m_elementHandle; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        Initialize
(
ConvertToDgnContext&        context,
MstnSubEntityTraits*        pSubEntityTraits,
AcGiWorldDraw*              pWorldDraw,
AcGiViewportDraw*           pViewportDraw
)
    {
    m_toDgnContext      = &context;
    m_pSubEntityTraits  = pSubEntityTraits;
    m_pWorldDraw        = pWorldDraw;
    m_pViewportDraw     = pViewportDraw;
    m_pLastChild        = NULL;
    m_isInBlockRecord   = false;

    m_currentTransformToDGN = context.GetTransformToDGN();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/03
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                getWorldToModelTransform
(
AcGeMatrix3d&               matrix
) const override
    {
    if (!m_xForm.empty())
        {
        matrix = m_xForm.top();
        matrix.invert();
        }
    else
        {
        matrix.setToIdentity();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/03
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                getModelToWorldTransform
(
AcGeMatrix3d&               matrix
) const override
    {
    if (!m_xForm.empty())
        matrix = m_xForm.top();
    else
        matrix.setToIdentity();
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/03
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      pushModelTransform
(
const AcGeMatrix3d&         matrix
) override
    {
    if (!m_xForm.empty())
        m_xForm.push (m_xForm.top() * matrix);
    else
        m_xForm.push (matrix);

    Transform           topTransform;
    m_currentTransformToDGN.InitProduct (m_toDgnContext->GetTransformToDGN(), RealDwgUtil::TransformFromGeMatrix3d (topTransform, m_xForm.top()));

    return Adesk::kTrue;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/03
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      pushModelTransform
(
const AcGeVector3d&         vector
) override
    {
    AcGeMatrix3d      matrix;

    matrix.setToPlaneToWorld (vector);
    this->pushModelTransform (matrix);
    return Adesk::kTrue;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/03
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      popModelTransform () override
    {
    BeAssert (!m_xForm.empty() && L"Popping model transform from an empty stack!");

    m_xForm.pop();

    Transform           topTransform;

    if (m_xForm.empty())
        m_currentTransformToDGN = m_toDgnContext->GetTransformToDGN();
    else
        m_currentTransformToDGN.InitProduct (m_toDgnContext->GetTransformToDGN(), RealDwgUtil::TransformFromGeMatrix3d (topTransform, m_xForm.top()));
    return Adesk::kTrue;
    }



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      circle
(
const AcGePoint3d&          p1,
const AcGePoint3d&          p2,
const AcGePoint3d&          p3
) const override
    {
    DPoint3d                point1, point2, point3;
    DEllipse3d              dEllipse;

    if (dEllipse.InitFromPointsOnArc (RealDwgUtil::DPoint3dFromGePoint3d (point1, p1),
                                      RealDwgUtil::DPoint3dFromGePoint3d (point2, p2),
                                      RealDwgUtil::DPoint3dFromGePoint3d (point3, p3)))
        {
        dEllipse.makeFullSweep ();
        this->AppendDEllipse (dEllipse);
        }
    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      circle
(
const AcGePoint3d&          center,
const double                radius,
const AcGeVector3d&         normal
) const override
    {
    DEllipse3d              dEllipse;
    DPoint3d                point1;
    DVec3d vector2;

    dEllipse.InitFromCenterNormalRadius (RealDwgUtil::DPoint3dFromGePoint3d (point1, center), RealDwgUtil::DVec3dFromGeVector3d (vector2, normal), radius);
    this->AppendDEllipse (dEllipse, &normal);

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      circularArc
(
const AcGePoint3d&          center,
const double                radius,
const AcGeVector3d&         normal,
const AcGeVector3d&         startVector,
const double                sweepAngle,
const AcGiArcType           arcType
) const override

    {
    AcGeVector3d            xVector = startVector;
    AcGeVector3d            yVector;

    xVector.normalize ();
    yVector = normal.crossProduct (xVector);
    xVector *= radius;
    yVector *= radius;

    DEllipse3d      dEllipse;
    bsiDEllipse3d_initFrom3dVectors (&dEllipse, (DPoint3d *) &center, (DVec3d *) &xVector, (DVec3d *) &yVector, 0.0, sweepAngle);
    this->AppendDEllipse (dEllipse, &normal);

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      circularArc
(
const AcGePoint3d&          start,
const AcGePoint3d&          point,
const AcGePoint3d&          end,
const AcGiArcType           arcType
) const override
    {
    DPoint3d                point1, point2, point3;
    DEllipse3d              dEllipse;

    if (dEllipse.initFromPointsOnArc (&RealDwgUtil::DPoint3dFromGePoint3d (point1, start),
                                      &RealDwgUtil::DPoint3dFromGePoint3d (point2, point),
                                      &RealDwgUtil::DPoint3dFromGePoint3d (point3, end)))
        {
        this->AppendDEllipse (dEllipse);
        }
    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      polyline
(
const Adesk::UInt32         pointCount,
const AcGePoint3d*          vertices,
const AcGeVector3d*         normal,
Adesk::LongPtr              subEntityMarker=-1
) const override
    {
    DPoint3dArray       pointArray;

    for (Adesk::UInt32 iPoint=0; iPoint < pointCount; iPoint++)
        {
        DPoint3d        point;
        RealDwgUtil::DPoint3dFromGePoint3d (point, vertices[iPoint]);
        pointArray.push_back (point);
        }

    /*-----------------------------------------------------------------------------------
    Do not use normal here.  Using it causes some mutileaders in 2008's sample file
    "Architectural - Annotation Scaling and Multileaders.dwg" to be incorrectly located.
    So does it to some section objects(TR225623).  ODA's vectorizer does not use normal.
    -----------------------------------------------------------------------------------*/
    EditElementHandle   eeh;
    m_toDgnContext->CreateElementFromVertices (eeh, &pointArray.front(), pointArray.size(), false, m_currentTransformToDGN);

    if (!eeh.IsValid())
        return Adesk::kFalse;

    AcGeVector3d    norm     = (NULL == normal) ? AcGeVector3d(0,0,1) : *(const_cast<AcGeVector3d*>(normal));

    m_toDgnContext->ApplyThickness (eeh, m_pSubEntityTraits->thickness(), norm, false);
    this->AppendElement (eeh);

    return Adesk::kFalse;
    }

#if RealDwgVersion > 2009
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   07/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      polyline
(
const AcGiPolyline&         polyline
) const override
    {
    const Adesk::LongPtr*   subEntMarkerList = polyline.subEntMarkerList ();
    Adesk::LongPtr          subEntityMarker = -1;

    // use the first entry for now:
    if (nullptr != subEntMarkerList)
        subEntityMarker = subEntMarkerList[0];
    
    return  this->polyline (polyline.points(), polyline.vertexList(), polyline.normal(), subEntityMarker);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   07/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      polyPolyline
(
Adesk::UInt32               nPolylines,
const AcGiPolyline*         pPolylines
) const override
    {
    if (nullptr == pPolylines)
        return Adesk::kFalse;

    for (Adesk::UInt32 i = 0; i < nPolylines; i++)
        this->polyline (pPolylines[i]);

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   07/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      image
(
const AcGiImageBGRA32&      imageSource,
const AcGePoint3d&          position,
const AcGeVector3d&         u,      // orientation and magnitude of width
const AcGeVector3d&         v,      // orientation and magnitude of height
TransparencyMode            transparencyMode = kTransparency8Bit
) const override
    {
    // NEEDS_TESTCASE
    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   07/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      ellipticalArc
(
const AcGePoint3d&          center,
const AcGeVector3d&         normal,
double                      majorAxisLength,
double                      minorAxisLength,
double                      startDegreeInRads,
double                      endDegreeInRads,
double                      tiltDegreeInRads,
AcGiArcType                 arcType = kAcGiArcSimple
) const override
    {
    DVec3d                  xAxis, yAxis, zAxis;
    RealDwgUtil::DVec3dFromGeVector3d (zAxis, normal);

    RotMatrix               matrix;
    RealDwgUtil::RotMatrixFromArbitraryGeAxis (matrix, normal);
    matrix.GetColumns (xAxis, yAxis, zAxis);

    DVec3d  majorAxis = DVec3d::FromSumOf (xAxis,  cos(tiltDegreeInRads), yAxis, sin (tiltDegreeInRads));
    DVec3d  minorAxis = DVec3d::FromSumOf (xAxis, -sin(tiltDegreeInRads), yAxis, cos (tiltDegreeInRads));

    DPoint3d                origin;
    RealDwgUtil::DPoint3dFromGePoint3d (origin, center);

    double                  sweptAngle = endDegreeInRads - startDegreeInRads;
    if (!Angle::IsFullCircle(sweptAngle))
        sweptAngle = Angle::AdjustToSweep (sweptAngle, 0, msGeomConst_2pi);
    
    DEllipse3d              dEllipse;
    dEllipse.InitFromScaledVectors (origin, majorAxis, minorAxis, majorAxisLength, minorAxisLength, startDegreeInRads, sweptAngle);

    if (kAcGiArcChord != arcType && kAcGiArcSector != arcType)
        {
        // create a simple elliptic arc element
        this->AppendDEllipse (dEllipse, &normal);
        return Adesk::kFalse;
        }
        
    EditElementHandle       complexChain, ellipseEeh;
    if (RealDwgSuccess != m_toDgnContext->CreateElementFromDEllipse(ellipseEeh, dEllipse, m_currentTransformToDGN))
        return Adesk::kFalse;

    // create a complex chain to represent a filled chord or a filled pie shape enclosed by the elliptic arc:
    ChainHeaderHandler::CreateChainHeaderElement (complexChain, nullptr, true, m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel());

    if (BSISUCCESS != ChainHeaderHandler::AddComponentElement(complexChain, ellipseEeh) )
        return Adesk::kFalse;

    DSegment3d              throughLine;
    dEllipse.Evaluate (throughLine.point[0], startDegreeInRads);
    dEllipse.Evaluate (throughLine.point[1], endDegreeInRads);
    m_currentTransformToDGN.Multiply (&throughLine.point[0], 2);

    EditElementHandle       lineEeh;
    if (kAcGiArcChord == arcType)
        {
        // add a through line across from start to end points
        if (BSISUCCESS == LineHandler::CreateLineElement(lineEeh, nullptr, throughLine, m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel()))
            ChainHeaderHandler::AddComponentElement (complexChain, lineEeh);
        }
    else if (kAcGiArcSector == arcType)
        {
        // add a line from the end point to the center point and another from center point to the start point:
        DSegment3d          end2Center = DSegment3d::From (throughLine.point[1], origin);
        DSegment3d          center2End = DSegment3d::From (origin, throughLine.point[0]);

        if (BSISUCCESS == LineHandler::CreateLineElement(lineEeh, nullptr, end2Center, m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel()))
            ChainHeaderHandler::AddComponentElement (complexChain, lineEeh);
        if (BSISUCCESS == LineHandler::CreateLineElement(lineEeh, nullptr, center2End, m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel()))
            ChainHeaderHandler::AddComponentElement (complexChain, lineEeh);
        }
    else
        {
        BeAssert (false);
        }

    // both types of the elliptical arc are supposed to be filled:
    if (BSISUCCESS == ChainHeaderHandler::AddComponentComplete(complexChain))
        this->AppendElement (complexChain, true);

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   07/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      polyPolygon
(
const Adesk::UInt32         numPolygonIndices,
const Adesk::UInt32*        numPolygonPositions,
const AcGePoint3d*          polygonPositions,
const Adesk::UInt32*        numPolygonPoints,
const AcGePoint3d*          polygonPoints,
const AcCmEntityColor*      outlineColors = NULL,
const AcGiLineType*         outlineTypes = NULL,
const AcCmEntityColor*      fillColors = NULL,
const AcCmTransparency*     fillOpacities = NULL
) const  override
    {
    if (nullptr == numPolygonPositions || nullptr == polygonPositions || nullptr == numPolygonPoints || nullptr == polygonPoints)
        return Adesk::kFalse;

    MstnSubEntityTraits     savedSubEntityTraits = *m_pSubEntityTraits;

    if (nullptr != fillColors)
        m_pSubEntityTraits->setFillType (kAcGiFillAlways);

    for (Adesk::UInt32 i = 0; i < numPolygonIndices; i++)
        {
        if (nullptr != fillColors)
            m_pSubEntityTraits->setTrueColor (fillColors[i]);
        else if (nullptr != outlineColors)
            m_pSubEntityTraits->setTrueColor (outlineColors[i]);

        // NEEDSWORK - map AcGiLineType to existing linetype or DGN line code?

        if (nullptr != fillOpacities)
            m_pSubEntityTraits->setTransparency (fillOpacities[i]);

        // create numPolygonPositions[i] identical polygons:
        for (Adesk::UInt32 j = 0; j < numPolygonPositions[i]; j++)
            this->polygon (numPolygonPoints[i], &polygonPoints[i]);
        }

    *m_pSubEntityTraits = savedSubEntityTraits;

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   07/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      rowOfDots
(
int                         count,
const AcGePoint3d&          start,
const AcGeVector3d&         step
) const override
    {
    bool        is3D = m_toDgnContext->GetThreeD ();
    DgnModelP   model = m_toDgnContext->GetModel ();

    for (int i = 0; i < count; i++)
        {
        // create a line segment of zero length:
        DSegment3d          dot;
        RealDwgUtil::DPoint3dFromGePoint3d (dot.point[0], start);
        m_currentTransformToDGN.Multiply (dot.point[0]);

        dot.point[1] = dot.point[0];

        EditElementHandle   eeh;
        if (BSISUCCESS == LineHandler::CreateLineElement(eeh, nullptr, dot, is3D, *model))
            this->AppendElement (eeh);
        }

    return Adesk::kFalse;
    }

#endif  // R2009

#if RealDwgVersion > 2014
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean edge (const AcArray<AcGeCurve2d*>& edges) const override
    {
    // NEEDS_TESTCASE
    return Adesk::kFalse;
    }
#endif  // R2015

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      polygon
(
const Adesk::UInt32         pointCount,
const AcGePoint3d*          vertices
) const override
    {
    DPoint3dArray   pointArray;
    for (Adesk::UInt32 iPoint=0; iPoint < pointCount; iPoint++)
        {
        DPoint3d        point;
        RealDwgUtil::DPoint3dFromGePoint3d (point, vertices[iPoint]);
        pointArray.push_back (point);
        }

    bool    filled = kAcGiFillAlways == m_pSubEntityTraits->fillType();

    EditElementHandle   eeh;
    m_toDgnContext->CreateElementFromVertices (eeh, &pointArray.front(), pointArray.size(), true, m_currentTransformToDGN);
    this->AppendElement (eeh, filled);

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      mesh
(
const Adesk::UInt32         rows,
const Adesk::UInt32         columns,
const AcGePoint3d*          vertices,
const AcGiEdgeData*         edgeData = NULL,
const AcGiFaceData*         faceData = NULL,
const AcGiVertexData*       vertexData = NULL,
const bool                  bAutoGenerateNormals = true
) const override
    {
    Adesk::UInt32           nTotal;
    BentleyStatus           status = BSIERROR;
    EditElementHandle       eeh;

    if (0 != (nTotal = (rows * columns)))
        {
        DPoint3dArray   pointArray;
        DPoint3d        point;

        for (Adesk::UInt32 iCount = 0; iCount < nTotal; iCount++)
            pointArray.push_back (RealDwgUtil::DPoint3dFromGePoint3d (point, vertices[iCount]));

        DPoint3dP       pVertices = &pointArray.front();
        if (2 == rows && 2 == columns)
            {
            // Seems to be used commonly... just make a shape
            DPoint3d        shapePoints[5];

            shapePoints[0] = shapePoints[4] = pVertices[0];
            shapePoints[1] = pVertices[1];
            shapePoints[2] = pVertices[3];
            shapePoints[3] = pVertices[2];

            if (RealDwgSuccess == m_toDgnContext->CreateElementFromVertices(eeh, shapePoints, 5, true, m_currentTransformToDGN))
                status = BSISUCCESS;
            }
        else
#ifdef CREATE_BSPLINESURFACE_FROM_MESH
            {
            MSBsplineSurface        surface;

            memset (&surface, 0, sizeof(surface));
            surface.uParams.order = surface.vParams.order = 2;

            surface.display.curveDisplay  = true;
            surface.vParams.numPoles = rows;
            surface.uParams.numPoles = columns;

            surface.uParams.numRules = surface.vParams.numPoles;
            surface.vParams.numRules = surface.uParams.numPoles;

            if (SUCCESS == bspsurf_allocateSurface (&surface))
                {
                bspknot_computeKnotVector (surface.uKnots, &surface.uParams, NULL);
                bspknot_computeKnotVector (surface.vKnots, &surface.vParams, NULL);
                m_currentTransformToDGN.multiply (surface.poles, pVertices, nTotal);

                status = (BentleyStatus)BSplineSurfaceHandler::CreateBSplineSurfaceElement (eeh, NULL, surface, *m_toDgnContext->GetModel());

                bspsurf_freeSurface (&surface);
                }
            }
#else   // create mesh element
            {
            PolyfaceHeaderPtr       dgnPolyface = PolyfaceHeader::CreateQuadGrid (columns);
            BlockedVectorDPoint3dR  meshPoints = dgnPolyface->Point ();

            for (Adesk::UInt32 i = 0; i < nTotal; i++)
                meshPoints.push_back (RealDwgUtil::DPoint3dFromGePoint3d(point, vertices[i]));

            dgnPolyface->Transform (m_toDgnContext->GetTransformToDGN());

            status = MeshHeaderHandler::CreateMeshElement (eeh, NULL, *dgnPolyface.get(), m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel());
            }
#endif  // create bspline surface or mesh?
        }

    if (BSISUCCESS == status)
        this->AppendElement (eeh);

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      shell
(
const Adesk::UInt32         nVertex,
const AcGePoint3d*          pVertexList,
const Adesk::UInt32         nFaceIndices,
const Adesk::Int32*         pFaceList,
const AcGiEdgeData*         pEdgeData = NULL,
const AcGiFaceData*         pFaceData = NULL,
const AcGiVertexData*       pVertexData = NULL,
const struct resbuf*        pResBuf = NULL,
const bool                  bAutoGenerateNormals = true
) const override
    {
    if (kAcGiFillAlways == m_pSubEntityTraits->fillType())
        {
        Adesk::UInt32       nVertices=0;
        Adesk::UInt32       maxFaceVertices = 0;

        for (Adesk::UInt32 jFace = 0; jFace < nFaceIndices; jFace+= nVertices)
            if ((nVertices = abs (pFaceList[jFace++])) > maxFaceVertices)
                maxFaceVertices = nVertices;

        DPoint3d        *pFaceVertices = (DPoint3d *) _alloca (maxFaceVertices * sizeof(DPoint3d));
        for (Adesk::UInt32 jFace = 0; jFace<nFaceIndices;)
            {
            nVertices = abs (pFaceList[jFace++]);

            for (Adesk::UInt32 kVertex = 0; kVertex < nVertices; kVertex++)
                RealDwgUtil::DPoint3dFromGePoint3d (pFaceVertices[kVertex], pVertexList[pFaceList[jFace++]]);

            EditElementHandle eeh;
            m_toDgnContext->CreateElementFromVertices (eeh, pFaceVertices, nVertices, true, m_currentTransformToDGN);
            this->AppendElement (eeh, true);
            }
        return Adesk::kTrue;
        }

    // Create our polyface header
    PolyfaceHeaderPtr       dgnPolyface = PolyfaceHeader::CreateVariableSizeIndexed ();
    BlockedVectorDPoint3dR  pointArray = dgnPolyface->Point ();
    DPoint3d                point;

    // Build polyface point array directly from DWG vertex list:
    for (Adesk::UInt32 iVertex = 0; iVertex < nVertex; iVertex++)
        pointArray.push_back (RealDwgUtil::DPoint3dFromGePoint3d(point, pVertexList[iVertex]));

    // Add polyface face one at a time from DWG face list:
    const Adesk::UInt8*     pEdgeVisibilities = (NULL == pEdgeData) ? NULL : pEdgeData->visibility();
    bvector<int>            pointIndices;
    for (Adesk::UInt32 iFace = 0; iFace < nFaceIndices;)
        {
        Adesk::Int32        nFaceVertices = abs (pFaceList[iFace++]);

        pointIndices.clear ();

        for (Adesk::Int32 iFaceVertex = 0; iFaceVertex < nFaceVertices; iFaceVertex++)
            {
            Adesk::Int32    pointIndex = abs(pFaceList[iFace++]) + 1;

            // turn off invisible edges
            if (NULL != pEdgeVisibilities && kAcGiInvisible == *pEdgeVisibilities++)
                pointIndex = -pointIndex;
                
            pointIndices.push_back ((int)pointIndex);
            }
        dgnPolyface->AddIndexedFacet (pointIndices);
        }

    // Transform polyface header to UORs
    dgnPolyface->Transform (m_currentTransformToDGN, false);

    // create the mesh element from the polyface header
    EditElementHandle   eeh;
    BentleyStatus       status = MeshHeaderHandler::CreateMeshElement (eeh, NULL, *dgnPolyface, m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel());

    if (BSISUCCESS == status)
        this->AppendElement (eeh);
    else
        DIAGNOSTIC_PRINTF ("Error creating mesh element from worldDraw!\n");

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      text
(
const AcGePoint3d&          position,
const AcGeVector3d&         normal,
const AcGeVector3d&         direction,
const ACHAR*                msg,
const Adesk::Int32          length,
const Adesk::Boolean        raw,
const AcGiTextStyle&        textstyle
) const override
    {
    // length = -1 indicates that msg is null terminated, length > 0 indicates the length in bytes of msg.
    if (length == 0 || nullptr == msg || 0 == msg[0])
        {
        DIAGNOSTIC_PRINTF ("Discarding a world drawn empty text...\n");
        return Adesk::kFalse;
        }

    DPoint3d                origin;
    RotMatrix               rMatrix;
    TextSizeParam           textSize;

    m_toDgnContext->GetDgnTextSizeAndTransform (&textSize, &origin, &rMatrix, position, direction, normal, textstyle.xScale() * textstyle.textSize(), textstyle.textSize(), &m_currentTransformToDGN);
    if (!m_toDgnContext->ArePointsValid(&origin, 1))
        {
        DIAGNOSTIC_PRINTF ("Discarding a world drawn text <%ls> that is out of design plane...\n", msg);
        return Adesk::kFalse;
        }

    if (textstyle.isBackward())
        textSize.size.width  = - textSize.size.width;

    if (textstyle.isUpsideDown())
        textSize.size.height = - textSize.size.height;


    TextParamWide   textParams;
    memset (&textParams, 0, sizeof(textParams));

    textParams.just = TextElementJustification::LeftBaseline;
    textParams.overridesFromStyle.just  = true;
    textParams.overridesFromStyle.nodeJust = true;
    textParams.exFlags.styleOverrides = true;
    textParams.slant = textstyle.obliquingAngle();
    textParams.flags.slant = (0.0 != textParams.slant);

    FileHolder&     fileHolder  = m_toDgnContext->GetFileHolder();
    DgnModelP       modelRef    = m_toDgnContext->GetModel();
    Adesk::Boolean  bold = false, italic = false;
    Charset         charset = Charset::kUndefinedCharset;
    FontPitch       pitch = FontPitch::kDefault;
    FontFamily      family = FontFamily::kDoNotCare;
    ACHAR*          typeface = NULL;
    DgnFontP        font = NULL, bigfont = NULL;

    if (Acad::eOk != textstyle.font(typeface, bold, italic, charset, pitch, family) || nullptr == typeface || 0 == typeface[0])
        {
        // SHX font
        textParams.font       = fileHolder.GetShapeFontId (textstyle.fileName(), modelRef);
        textParams.shxBigFont = fileHolder.GetShapeFontId (textstyle.bigFontFileName(), modelRef);
        textParams.flags.shxBigFont = 0 != textParams.shxBigFont;
        // these are also needed for runproperties constructor:
        font = DgnFontManager::GetDgnFontMapP(modelRef)->GetFontP (textParams.font);
        if (textParams.flags.shxBigFont)
            bigfont = DgnFontManager::GetDgnFontMapP(modelRef)->GetFontP (textParams.shxBigFont);
        }
    else
        {
        // TrueType font
        textParams.font = fileHolder.GetTrueTypeFontId (typeface, modelRef);
        textParams.exFlags.bold = bold;
        if (!textParams.flags.slant && italic)
            textParams.flags.slant = italic;
        if (NULL != typeface)
            acutDelString (typeface);
        }

    textParams.SetCodePage (m_toDgnContext->GetAnsiCodePage());

    if (textstyle.isUnderlined ())
        {
        textParams.flags.underline = true;
        textParams.underlineSpacing = IS_TRUETYPE_FONTNUMBER (textParams.font) ?
                    TRUETYPE_UNDERLINEOFFSET * fabs (textSize.size.height) : SHX_UNDERLINEOFFSET * fabs (textSize.size.height);
        textParams.overridesFromStyle.underline = true;
        textParams.overridesFromStyle.underlineOffset = true;
        }
    if (textstyle.isOverlined ())
        {
        textParams.exFlags.overline = true;
        textParams.overlineSpacing = IS_TRUETYPE_FONTNUMBER (textParams.font) ?
                    TRUETYPE_OVERLINEOFFSET * fabs (textSize.size.height) : SHX_OVERLINEOFFSET * fabs (textSize.size.height);
        textParams.overridesFromStyle.overline = true;
        textParams.overridesFromStyle.overlineOffset = true;
        }
    if (textstyle.trackingPercent () != 1.0)
        {
        textParams.exFlags.acadInterCharSpacing = true;
        textParams.flags.interCharSpacing = true;
        textParams.characterSpacing = textstyle.trackingPercent ();
        textParams.overridesFromStyle.interCharSpacing = true;
        textParams.overridesFromStyle.acadInterCharSpacing = true;
        }

    TextBlock       textBlock (*modelRef->GetDgnModelP());
    // remove annotation scale set by above constructor
    TextBlockPropertiesPtr  textBlockProperties = textBlock.GetProperties().Clone ();
    if (textBlockProperties.IsValid())
        {
        textBlockProperties->ClearAnnotationScale ();
        textBlock.SetProperties (*textBlockProperties.get());
        }

    textBlock.SetTextElementOrigin (origin);
    textBlock.SetOrientation (rMatrix);

    RunProperties   runProperties (textParams, (DPoint2dCR)textSize.size, *modelRef);
    textBlock.SetRunPropertiesForAdd (runProperties);

    WString                 str(msg, length > 0 ? length : wcslen(msg));
    DwgContextForTextBlock  convertForTextBlock (m_toDgnContext, NULL, m_pSubEntityTraits->GetSourceEntity());
    textBlock.FromMText (str.GetWCharCP(), convertForTextBlock, m_toDgnContext->GetScaleToDGN());

    EditElementHandle  elemHandle;
    if (TEXTBLOCK_TO_ELEMENT_RESULT_Success == TextHandlerBase::CreateElement(elemHandle, NULL, textBlock))
        this->AppendElement (elemHandle);

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      text
(
const AcGePoint3d&          position,
const AcGeVector3d&         normal,
const AcGeVector3d&         direction,
const double                height,
const double                widthFactor,
const double                oblique,
const ACHAR*                msg
) const override
    {
    DPoint3d                origin;
    RotMatrix               rMatrix;
    TextSizeParam           textSize;

    m_toDgnContext->GetDgnTextSizeAndTransform (&textSize, &origin, &rMatrix, position, direction, normal, widthFactor * height, height, &m_currentTransformToDGN);

    if (!m_toDgnContext->ArePointsValid(&origin, 1))
        {
        DIAGNOSTIC_PRINTF ("Discarding a world drawn text <%ls> that is out of design plane...\n", msg);
        return Adesk::kFalse;
        }

    TextParamWide   textParams;
    memset (&textParams, 0, sizeof(textParams));

    textParams.just = TextElementJustification::LeftBaseline;
    textParams.overridesFromStyle.just  = true;
    textParams.overridesFromStyle.nodeJust = true;
    textParams.exFlags.styleOverrides = true;
    textParams.slant = oblique;
    textParams.flags.slant = (0.0 != textParams.slant);

    FileHolder&     fileHolder  = m_toDgnContext->GetFileHolder();
    DgnModelP       modelRef    = m_toDgnContext->GetModel();

    textParams.font = fileHolder.GetDefaultFontID ();
    textParams.SetCodePage (m_toDgnContext->GetAnsiCodePage());

    TextBlock       textBlock (*modelRef->GetDgnModelP());
    // remove annotation scale set by above constructor
    TextBlockPropertiesPtr  textBlockProperties = textBlock.GetProperties().Clone ();
    if (textBlockProperties.IsValid())
        {
        textBlockProperties->ClearAnnotationScale ();
        textBlock.SetProperties (*textBlockProperties.get());
        }

    textBlock.SetTextOrigin (origin);
    textBlock.SetOrientation (rMatrix);

    RunProperties   runProperties (textParams, (DPoint2dCR)textSize.size, *modelRef);
    textBlock.SetRunPropertiesForAdd (runProperties);

    WCharCP                 str = msg;
    DwgContextForTextBlock  convertForTextBlock (m_toDgnContext, NULL, m_pSubEntityTraits->GetSourceEntity());
    textBlock.FromMText (str, convertForTextBlock, m_toDgnContext->GetScaleToDGN());

    EditElementHandle  elemHandle;
    if (TEXTBLOCK_TO_ELEMENT_RESULT_Success == TextHandlerBase::CreateElement(elemHandle, NULL, textBlock))
        this->AppendElement (elemHandle);

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      xline
(
const  AcGePoint3d&         p1,
const  AcGePoint3d&         p2
) const override
    {
    DSegment3d              lineSeg;
    DPoint3d                direction;
    RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[0], p1);
    RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[1], p2);

    direction.normalizedDifference (&lineSeg.point[1], &lineSeg.point[0]);

    m_currentTransformToDGN.Multiply (lineSeg.point[0], lineSeg.point[0]);
    lineSeg.point[1].SumOf (lineSeg.point[0], direction, 1.0E10);

    EditElementHandle   outElement;
    if (BSISUCCESS == LineHandler::CreateLineElement (outElement, NULL, lineSeg, m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel()))
        {
        this->SetInfiniteLine (outElement, true, true);
        this->AppendElement (outElement);
        }

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      ray
(
const  AcGePoint3d&         start,
const  AcGePoint3d&         point
) const override
    {
    DSegment3d              lineSeg;
    DPoint3d                direction;
    RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[0], start);
    RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[1], point);

    direction.normalizedDifference (&lineSeg.point[1], &lineSeg.point[0]);

    m_currentTransformToDGN.Multiply (lineSeg.point[0], lineSeg.point[0]);
    lineSeg.point[1].SumOf (lineSeg.point[0], direction, 1.0E10);

    EditElementHandle   outElement;
    if (BSISUCCESS == LineHandler::CreateLineElement (outElement, NULL, lineSeg, m_toDgnContext->GetThreeD(), *m_toDgnContext->GetModel()))\
        {
        this->SetInfiniteLine (outElement, false, true);
        this->AppendElement (outElement);
        }

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      pline
(
const AcDbPolyline&         input,
Adesk::UInt32               fromIndex = 0,
Adesk::UInt32               numSegs = 0
) const override
    {
    /*-----------------------------------------------------------------------------------
    A closed polyline in ACAD is not rendered (i.e. appears hollow in a render mode) but
    a closed shape is rendered in MicroStation.  For the purpose of worlDraw/viewportDraw
    there is no need to make a closed polyline as a shape.  Instead, a linestring is much
    better representing a polyline's display in all modes.
    -----------------------------------------------------------------------------------*/
    bool    makeLineString = kAcGiHideOrShadeCommand == m_toDgnContext->GetWorldDrawRegenType() || kAcGiShadedDisplay == m_toDgnContext->GetWorldDrawRegenType();

    EditElementHandle eeh;
    m_toDgnContext->CreateElementFromPolylineEntity (eeh, &input, m_currentTransformToDGN, makeLineString);
    this->AppendElement (eeh);

    return Adesk::kFalse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      draw
(
AcGiDrawable*               acGiDrawable
) const override
    {
    AcDbEntity* pEntity = AcDbEntity::cast(acGiDrawable);
    if (NULL != pEntity)
        {
        // do not draw attribute definition:
        if (m_isInBlockRecord && NULL != AcDbAttributeDefinition::cast(acGiDrawable))
            return  Adesk::kTrue;

        m_pSubEntityTraits->setLayer (pEntity->layerId());
        }
    else if (NULL != AcDbBlockTableRecord::cast(acGiDrawable))
        {
        // we are in a block - worldDraw will draw its children.
        m_isInBlockRecord = true;

        if (nullptr != m_pWorldDraw)
            {
            // apply the top transform of "this" geometry to that in the worldDraw, i.e. don't want it to compound with "this" top:
            AcGeMatrix3d    effectiveTop;
            m_pWorldDraw->geometry().getModelToWorldTransform (effectiveTop);

            effectiveTop.invert ();
            effectiveTop *= m_xForm.top ();

            m_pWorldDraw->geometry().pushModelTransform (effectiveTop);

            if (!acGiDrawable->worldDraw(m_pWorldDraw))
                DIAGNOSTIC_PRINTF ("Failed worldDraw on a block children!");

            m_pWorldDraw->geometry().popModelTransform ();

            return  Adesk::kTrue;
            }
        }

    AcDbLine*   pLine;
    AcDbArc*    pArc;
    AcDbCircle* pCircle;

    if (NULL != (pLine = AcDbLine::cast (acGiDrawable)))
        m_pSubEntityTraits->setThickness (pLine->thickness());
    else if (NULL != (pArc = AcDbArc::cast (acGiDrawable)))
        m_pSubEntityTraits->setThickness (pArc->thickness());
    else if (NULL != (pCircle = AcDbCircle::cast (acGiDrawable)))
        m_pSubEntityTraits->setThickness (pCircle->thickness());

    Adesk::Boolean  worldDrawn = false;
    if (NULL != m_pWorldDraw)
        worldDrawn = acGiDrawable->worldDraw (m_pWorldDraw);
    if (false == worldDrawn && NULL != m_pViewportDraw)
        acGiDrawable->viewportDraw (m_pViewportDraw);

    return Adesk::kTrue;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Adesk::Boolean      pushClipBoundary
(
AcGiClipBoundary* acGiClipBoundary
) override
    {
    DIAGNOSTIC_PRINTF("MstnGeometry::pushClipBoundary\n");
    return Adesk::kTrue;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void popClipBoundary () override
    {
    DIAGNOSTIC_PRINTF("MstnGeometry::popClipBoundary\n");
    }

#if RealDwgVersion > 2009
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   07/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual AcGeMatrix3d pushPositionTransform (AcGiPositionTransformBehavior behavior, const AcGePoint3d& offset) override {return m_xForm.top();}
virtual AcGeMatrix3d pushPositionTransform (AcGiPositionTransformBehavior behavior, const AcGePoint2d& offset) override {return m_xForm.top();}
virtual AcGeMatrix3d pushScaleTransform (AcGiScaleTransformBehavior behavior, const AcGePoint3d& extents) override {return m_xForm.top();}
virtual AcGeMatrix3d pushScaleTransform (AcGiScaleTransformBehavior behavior, const AcGePoint2d& extents) override {return m_xForm.top();}
virtual AcGeMatrix3d pushOrientationTransform (AcGiOrientationTransformBehavior behavior) override {return m_xForm.top();}

#endif



private:

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       SetInfiniteLine (EditElementHandleR outElement, bool infiniteStart, bool infiniteEnd) const
    {
    InfiniteLineLinkage     infiniteLineLinkage;
    memset (&infiniteLineLinkage, 0, sizeof infiniteLineLinkage);

    infiniteLineLinkage.hdr.user = 1;
    infiniteLineLinkage.hdr.primaryID = LINKAGEID_InfiniteLine;

    UInt32                  linkageSize = (sizeof(InfiniteLineLinkage) + 7) & ~7;
    LinkageUtil::SetWords (&infiniteLineLinkage.hdr, linkageSize/sizeof(short));

    infiniteLineLinkage.infiniteStart = infiniteStart;
    infiniteLineLinkage.infiniteEnd = infiniteEnd;

    return outElement.AppendElementLinkage (NULL, infiniteLineLinkage.hdr, &infiniteLineLinkage.hdr + sizeof infiniteLineLinkage.hdr);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        AppendDEllipse
(
DEllipse3dR                 dEllipse,
const AcGeVector3d*         normal = NULL
) const
    {
    EditElementHandle   eeh;
    m_toDgnContext->CreateElementFromDEllipse (eeh, dEllipse, m_currentTransformToDGN);
    if (!eeh.IsValid())
        return;

    if (NULL != normal)
        m_toDgnContext->ApplyThickness (eeh, m_pSubEntityTraits->thickness(), *normal, false);

    AppendElement (eeh);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        AppendElement
(
EditElementHandleR          elementToAppend,
bool                        filled = false
) const
    {
    if (!elementToAppend.IsValid())
        return;

    /*-------------------------------------------------------------------------------
    Need to handle color BYBLOCK.  RealDWG Help says that m_pSubEntityTraits->color()
    returns 0 for a block reference parent, 7 for other entities, but TR 269828 has
    proven otherwise: we always get 0 for color BYBLOCK.  Our display code does not
    seem to handle color BYCELL correctly when element is inside a type 2 cell, so
    we force the use of default color here.
    -------------------------------------------------------------------------------*/
    UInt32      dgnColorIndex;
    UInt16      dwgColor = m_pSubEntityTraits->color ();
    if ( (DWG_COLOR_ByBlock == dwgColor) && (SHARED_CELL_ELM != elementToAppend.GetElementType ())  )
        dgnColorIndex  = m_toDgnContext->GetDgnColor  (DWG_COLOR_Default);
    else
        dgnColorIndex  = m_toDgnContext->GetDgnColor  (m_pSubEntityTraits->trueColor());

    AcDbObjectId    layerId = m_pSubEntityTraits->layerId ();

    UInt32      dgnWeightIndex = m_toDgnContext->GetDgnWeight (m_pSubEntityTraits->lineWeight());
    UInt32      levelId        = m_toDgnContext->GetDgnLevel  (layerId);
    Int32       dgnStyleIndex  = m_toDgnContext->GetDgnStyle  (m_pSubEntityTraits->lineTypeId());
    double      transparency   = m_toDgnContext->GetDgnTransparency (m_pSubEntityTraits->transparency(), &layerId);

    ElementPropertiesSetter propSetter;

    propSetter.SetLevel (levelId);
    propSetter.SetWeight (dgnWeightIndex);
    propSetter.SetColor (dgnColorIndex);
    propSetter.SetTransparency (transparency);

    propSetter.Apply (elementToAppend);

    // add fill
    IAreaFillPropertiesEdit*    areaFill;
    if (filled && nullptr != (areaFill = dynamic_cast <IAreaFillPropertiesEdit*> (&elementToAppend.GetHandler(MISSING_HANDLER_PERMISSION_ChangeAttrib))))
        areaFill->AddSolidFill (elementToAppend, &dgnColorIndex);

    // attach material
    AcDbObjectId        materialId = m_pSubEntityTraits->materialId ();
    if (materialId.isValid())
        MaterialManager::GetManagerR().SetMaterialAttachmentId (elementToAppend, m_toDgnContext->ElementIdFromObjectId(materialId));

    // We have to put the style on directly because putting it on by calling setSymbology will clear the params, which might be set to get varying width polylines.
    elementToAppend.GetElementP()->hdr.dhdr.symb.style = dgnStyleIndex;

    MSElementDescrP     appendTo = m_elementHandle.ExtractElementDescr();
    if (NULL != appendTo)
        {
        appendTo->AppendChild (&m_pLastChild, elementToAppend.ExtractElementDescr());
        m_elementHandle.SetElementDescr (appendTo, true, false);
        }
    }

};  // MstnGeometry


/*=================================================================================**//**
*
* MstnWorldDraw
*
* @bsiclass                                                     RayBentley      11/02
+===============+===============+===============+===============+===============+======*/
class MstnWorldDraw : public AcGiWorldDraw
{
private:
    mutable MstnGeometry            m_worldGeometry;
    AcGiRegenType                   m_regenType;
    mutable MstnSubEntityTraits     m_subEntityTraits;
    MstnGiContext                   m_context;
    Adesk::UInt32                   m_numIsolines;

public:
    MstnWorldDraw (EditElementHandleR elementHandle) : m_worldGeometry (elementHandle) {}
    ~MstnWorldDraw () {}

    //from AcGiWorldDraw
    AcGiWorldGeometry&              geometry () const override                      { return m_worldGeometry; }

    //from AcGiCommonDraw
    virtual AcGiRegenType           regenType () const override                     { return m_regenType; }
    virtual Adesk::Boolean          regenAbort () const override                    { return false; };
    virtual AcGiSubEntityTraits&    subEntityTraits () const override               { return m_subEntityTraits; }

    virtual AcGiGeometry*           rawGeometry () const override                   { return static_cast <AcGiWorldGeometry*>(&m_worldGeometry); }
    virtual Adesk::Boolean          isDragging () const override                    { return false; }
    virtual double                  deviation (const AcGiDeviationType deviationType, const AcGePoint3d& point) const override { return 0.01; }
    virtual Adesk::UInt32           numberOfIsolines () const override              { return m_numIsolines; }
    virtual AcGiContext*            context () override                             { return &m_context; }

    // our methods:
    void                            SetViewportDraw (AcGiViewportDraw* viewportDraw) { m_worldGeometry.SetViewportDraw (viewportDraw); }
    MstnGeometry                    GetMstnGeometry () { return m_worldGeometry; }
    MstnSubEntityTraits&            GetMstnSubEntityTraits () { return m_subEntityTraits; }
    void                            SetDatabase (AcDbDatabase* dwg) { m_context.Initialize(dwg); }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        Initialize
(
ConvertToDgnContext&        context,
AcDbEntityCP                pEntity
)
    {
    m_regenType         = context.GetWorldDrawRegenType ();
    m_numIsolines       = context.GetCustomObjectIsolines ();

    m_context.Initialize (context.GetDatabase());
    m_subEntityTraits.Initialize (pEntity, context.GetCustomObjectVisualStyle());
    m_worldGeometry.Initialize (context, &m_subEntityTraits, this, NULL);
    }

};

/*=================================================================================**//**
*
* MstnViewportDraw
*
* @bsiclass                                                     RayBentley      11/02
+===============+===============+===============+===============+===============+======*/
class MstnViewportDraw : public AcGiViewportDraw
{
private:
    AcDbObjectId                    m_viewportId;
    MstnViewport                    m_viewport;
    AcGiRegenType                   m_regenType;
    MstnWorldDraw*                  m_pWorldDraw;
    mutable MstnGeometry            m_viewportGeometry;
    Adesk::UInt32                   m_numIsolines;

public:
    //from AcGiViewportDraw
    virtual AcGiViewportGeometry&   geometry () const override                          { return (AcGiViewportGeometry&)m_viewportGeometry; }
    virtual AcGiViewport&           viewport() const override                           { return (AcGiViewport&) m_viewport; }
    virtual Adesk::UInt32           sequenceNumber() const override                     { return 0; }
    virtual Adesk::Boolean          isValidId(const Adesk::ULongPtr acgiId) const override{ return true; }
    virtual AcDbObjectId            viewportObjectId() const override                   { return m_viewportId; }

    //from AcGiCommonDraw
    virtual AcGiRegenType           regenType () const override                         { return m_regenType; }
    virtual Adesk::Boolean          regenAbort () const override                        { return false; };
    virtual AcGiSubEntityTraits&    subEntityTraits () const override                   { return m_pWorldDraw->subEntityTraits(); }
    virtual AcGiGeometry*           rawGeometry () const override                       { return m_pWorldDraw->rawGeometry(); }
    virtual Adesk::Boolean          isDragging () const override                        { return false; }
    virtual double                  deviation (const AcGiDeviationType deviationType, const AcGePoint3d& point) const override { return 0.01; }
    virtual Adesk::UInt32           numberOfIsolines () const override                  { return m_numIsolines; }
    virtual AcGiContext*            context () override                                 { return m_pWorldDraw->context(); }

    void        SetViewDirection (AcGeVector3d& viewDir, AcGeVector3d& up)              { m_viewport.SetViewDirection (viewDir, up); }

    MstnViewportDraw (MstnWorldDraw* pWorldDraw) : m_pWorldDraw (pWorldDraw), m_viewportGeometry (pWorldDraw->GetMstnGeometry ().GetElementHandle()) {}

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void            Initialize (ConvertToDgnContext& context, const AcDbObjectId& viewportId = AcDbObjectId::kNull)
    {
    m_regenType         = context.GetWorldDrawRegenType ();
    m_numIsolines       = context.GetCustomObjectIsolines ();
    // re-use all overlapped data from worldDraw:
    m_viewportId        = viewportId;
    // reset the DWG in MstnContext from the DWG of the input viewport
    if (m_viewportId.isValid() && m_viewportId.database() != m_pWorldDraw->context()->database())
        m_pWorldDraw->SetDatabase (m_viewportId.database());
    m_pWorldDraw->SetViewportDraw (this);
    /*-----------------------------------------------------------------------------------
    MstnGeometry is multi-inherited from both AcGiWorldGeometry and AcGiViewportGeometry.
    We can type cast their common interfaces but we can not do that for uncommon ones.
    Using m_pWorldDraw->geometry() in place of AcGiViewportDraw::geometry() causes cross
    class interface casting.  A case in point: AcDbWipeout would call rasterImageDc()
    during viewportDraw process.  Because AcDbWorldGeometry does not have rasterImageDc()
    it would raise an unhandled exception(TR 288231).  We want to keep a local copy of
    our MstnGeometry and use it for AcGiViewportDraw::geometry().
    -----------------------------------------------------------------------------------*/
    m_viewportGeometry.Initialize (context, &m_pWorldDraw->GetMstnSubEntityTraits(), m_pWorldDraw, this);
    }

};

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ValidatePropertyFormat (EditElementHandleR outElement, WStringR currString, WStringR previousString)
    {
    /*-------------------------------------------------------------------------------------------------------------
    Attempt to parse the strings we collected from a printf callback via RealDWG hostApp to have them make sense in
    the context of properties is a loser's game.  These ASCII strings can be anyting and in any format.  However, a
    string with too ill format can prompt Properties schema to fail with an error of "invalid memeber name" and then
    nothing at all will display in the Properties.  For example, string ":" sent from Plant3d OE causes such an error.
    -------------------------------------------------------------------------------------------------------------*/
    if (WString::npos == currString.find(':'))
        {
        // current string has no property separator ":" -don't guess, just flush previous string as is:
        if (!previousString.empty())
            RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DwgEntityPropertyList, previousString.GetWCharCP());

        // save current string and move on to the next string
        previousString = currString;
        return  false;
        }
    else if (currString.at(0) == L':')
        {
        // current string starts by ":", combine it with previous string.
        if (!previousString.empty())
            {
            previousString += currString;
            return  false;
            }
        }
    else
        {
        // current string appears like a well formated string by itself
        if (!previousString.empty())
            {
            // attach previous string as is:
            RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DwgEntityPropertyList, previousString.GetWCharCP());

            previousString.clear ();
            return  false;
            }
        }

    // always empty the string we are tracking with if we have a valid string to add:
    previousString.clear ();

    // go ahead and attach validated string to the element
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
static void     AppendPropertyStringToElement (EditElementHandleR outElement, WStringR message, WStringR previousString)
    {
    // remove white spaces in front of the message:
    size_t          numChars = 0;
    for (WString::const_iterator iter = message.begin(); iter != message.end() && *iter <= 0x20; iter++)
        numChars++;
    if (numChars > 0)
        message.erase (0, numChars);

    if (message.empty())
        return;

    // remove white spaces and control chars at the end of the message:
    numChars = 0;
    for (WString::const_reverse_iterator rIter = message.rbegin(); rIter != message.rend() && *rIter <= 0x20; rIter++)
        numChars++;
    if (numChars > 0)
        message.erase (message.length() - numChars, numChars);

    // replace = with :
    numChars = message.find ('=');
    if (WString::npos != numChars)
        message.replace (numChars, 1, L":");

    // ElementInfo schema expects a format of "property name :/= <property value>".
    if (!ValidatePropertyFormat(outElement, message, previousString))
        return;

    RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DwgEntityPropertyList, message.GetWCharCP());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
static void     AddCustomPropertyToElement (EditElementHandleR outElement, AcDbEntityP entity, ConvertToDgnContext& context)
    {
    if (!entity->isAProxy())
        {
        // do not add properties for AutoCAD, AutoPlant or ProSteel objects:
        AcRxClass*      rxClass = entity->isA ();
        const ACHAR*    rxName = rxClass->name();
        if (NULL == rxName || 0 == _wcsnicmp(rxName, L"AcDb", 4) || 0 == _wcsnicmp(rxName, L"At_", 3) || 0 == _wcsnicmp(rxName, L"Ks_", 3))
            return;

        T_MessageList   messageList;
        RealDwgHostAppR hostApp = RealDwgHostApp::Instance ();
        bool            caughtError = false;

        hostApp.StartMessageListCollection (&messageList);
        try
            {
            entity->list ();
            }
        catch (...)
            {
            DIAGNOSTIC_PRINTF ("Exception caught from AcDbEntity::list()\n");
            caughtError = true;
            }
        hostApp.EndMessageListCollection ();
        if (caughtError)
            return;

        WString         previousString;

        for each (WString message in messageList)
            {
            if (message.empty())
                continue;

            // break down text body to multiple lines of texts separately by a LINEFEED:
            size_t      lineFeedAt = WString::npos;
            while (WString::npos != (lineFeedAt = message.find(0x0A)))
                {
                if (lineFeedAt+1 == message.length())
                    {
                    lineFeedAt = WString::npos;
                    break;
                    }

                WString     subString(message.GetWCharCP(), lineFeedAt);
                AppendPropertyStringToElement (outElement, subString, previousString);
                message.erase (0, lineFeedAt + 1);
                }

            // add last line of text:
            if (WString::npos == lineFeedAt)
                AppendPropertyStringToElement (outElement, message, previousString);
            }

        // add last string that happens to be "property name : <no property value>"
        if (!previousString.empty())
            {
            WString     emptyString;
            AppendPropertyStringToElement (outElement, previousString, emptyString);
            }
        }
    else
        {
        // add proxy info
        AcDbProxyEntity*    proxy = AcDbProxyEntity::cast (entity);
        if (NULL == proxy)
            return;

        WString    rscString, wString;
        if (SUCCESS == RmgrResource::LoadWString(rscString, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_PROXY_ClassName))
            {
            wString = rscString + WString(L" : ") + WString(proxy->originalClassName());
            RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DwgEntityPropertyList, wString.GetWCharCP());
            }
        if (SUCCESS == RmgrResource::LoadWString(rscString, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_PROXY_DXFName))
            {
            wString = rscString + WString(L" : ") + WString(proxy->originalDxfName());
            RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DwgEntityPropertyList, wString.GetWCharCP());
            }
        if (SUCCESS == RmgrResource::LoadWString(rscString, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_PROXY_ApplicationName))
            {
            WCharP      appName = NULL, vendorInfo = NULL;
            WCharCP     appDescription = proxy->applicationDescription();
            context.ParseApplicationDescription (appName, vendorInfo, appDescription);

            WString     vendorString(vendorInfo);

            wString = rscString + WString(L" : ") + WString(appName);
            RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DwgEntityPropertyList, wString.GetWCharCP());

            if (!vendorString.empty())
                {
                size_t  foundAt = 0;
                while (WString::npos != (foundAt = vendorString.find(L'|')))
                    {
                    wString = vendorString.substr (0, foundAt);
                    if (!wString.empty())
                        RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DwgEntityPropertyList, wString.GetWCharCP());
                    vendorString = vendorString.erase (0, foundAt + 1);
                    }
                RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DwgEntityPropertyList, vendorString.GetWCharCP());
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 CreateProxyHeader
(
EditElementHandleR          eeh,
AcDbEntityP                 pEntity,
bool                        isAnnotative,
WCharCP                     cellName,
ConvertToDgnContext&        context
)
    {
    WCharCP name = cellName == nullptr ? pEntity->isA()->dxfName() : cellName;
    NormalCellHeaderHandler::CreateOrphanCellElement (eeh, name, context.GetThreeD(), *context.GetModel());

    if (isAnnotative)
        AnnotationCellHeaderHandler::CreateFromNormalCell (eeh);

    MSElementP  element = eeh.GetElementP ();
    
    element->hdr.ehdr.locked = true;

    AddCustomPropertyToElement (eeh, pEntity, context);

    context.ElementHeaderFromEntity (eeh, pEntity);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::WorldDrawToElements
(
AcDbEntityP                 pEntity,
EditElementHandleR          eeh,
const AcDbObjectId&         viewportId,
WCharCP                     cellName
)
    {
    // if an annotative object is in modelspace, will create an annotative cell
    double      annotationScale = 1.0;
    bool        isAnnotative = this->GetDisplayedAnnotationScale (annotationScale, pEntity);

    try
        {
        CreateProxyHeader (eeh, pEntity, isAnnotative, cellName, *this);
        MstnWorldDraw worldDraw (eeh);
        worldDraw.Initialize (*this, pEntity);

        if (!pEntity->worldDraw (&worldDraw))
            {
            MstnViewportDraw viewportDraw (&worldDraw);
            viewportDraw.Initialize (*this, viewportId);

            viewportDraw.SetViewDirection (m_customObjectViewDirection, m_customObjectCameraUp);

            pEntity->viewportDraw (&viewportDraw);
            if (NULL == eeh.GetElementDescrCP()->h.firstElem)
                {
                AcGeVector3d    zAxis(0.0, 0.0, 1.0);
                viewportDraw.SetViewDirection (zAxis, m_customObjectCameraUp);
                pEntity->viewportDraw (&viewportDraw);
                }
            }
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception thrown in DgnExportContext::worldDrawToElements. %ls, ID=%I64d\n", pEntity->isA()->name(), this->ElementIdFromObject(pEntity));
        }

    if (NULL == eeh.GetElementDescrCP()->h.firstElem)
        return NoConversionMethod;

    // set annotation scale but do not transform the cell as the components are already drawn in displayed scale.
    if (isAnnotative)
        AnnotationScale::SetAsXAttribute (eeh, &annotationScale);

    NormalCellHeaderHandler::SetCellOriginAndRange (eeh);
    return RealDwgSuccess;
    }


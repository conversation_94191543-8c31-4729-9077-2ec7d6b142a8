# Symbology Management

## Overview

The RealDwgFileIO framework implements a sophisticated symbology management system that handles the mapping and conversion of visual properties between DWG/DXF and DGN formats. This includes colors, line styles, line weights, layers, and various style definitions.

## Symbology Architecture

### Core Symbology Classes

```
BaseSymbologyData (Abstract)
    ├─ DwgSymbologyData (DWG-specific)
    └─ DgnSymbologyData (DGN-specific)

Table Index System
    ├─ SignedTableIndex (General ID mapping)
    └─ LayerTableIndex (Layer-specific mapping)

Style Converters
    ├─ TextStyleConverter
    ├─ LineStyleConverter
    ├─ DimensionStyleConverter
    ├─ MaterialConverter
    └─ MultilineStyleConverter
```

## Color Management

### Color Mapping System

**File**: `rDwgSymbologyData.cpp`

The framework provides bidirectional color mapping between different color systems:

```cpp
class BaseSymbologyData
{
protected:
    byte m_colorTable[768];  // RGB color table (256 colors × 3 bytes)
    UInt32 m_lastColorIndex;
    ColorTreeNode* m_pColorTree;  // For fast color lookup

public:
    // Core color mapping methods
    UInt32 GetColorIndex(const RgbColorDef* pColor, 
                        BaseConvertContext* context);
    UInt32 GetColorIndex(UInt32 acadColorIndex, 
                        BaseConvertContext* context);
    void SetColorTable(const byte* colorTable);
};
```

### Color Conversion Process

#### DWG to DGN Color Mapping

```cpp
UInt32 DwgSymbologyData::GetColorIndex(
    const RgbColorDef* pColor,
    BaseConvertContext* context)
{
    if (!pColor)
        return 7;  // Default to white

    // Check for exact white or black
    if ((pColor->red == 255 && pColor->green == 255 && pColor->blue == 255) ||
        (pColor->red == 0 && pColor->green == 0 && pColor->blue == 0))
        return 7;  // White background assumption

    // Search color tree for closest match
    return FindClosestColorMatch(pColor);
}
```

#### Color Tree Search

```cpp
struct ColorTreeNode
{
    byte rgb[4];     // RGB + padding for alignment
    UInt32 index;    // Color index
};

UInt32 BaseSymbologyData::FindClosestColorMatch(const RgbColorDef* pColor)
{
    if (!m_pColorTree)
        BuildColorTree();

    UInt32 targetRgb = (pColor->red << 16) | (pColor->green << 8) | pColor->blue;
    UInt32 bestMatch = 7;  // Default white
    UInt32 minDistance = UINT32_MAX;

    // Search tree for closest color
    for (ColorTreeNode* node : m_colorTree) {
        UInt32 nodeRgb = (node->rgb[0] << 16) | (node->rgb[1] << 8) | node->rgb[2];
        UInt32 distance = CalculateColorDistance(targetRgb, nodeRgb);
        
        if (distance < minDistance) {
            minDistance = distance;
            bestMatch = node->index;
        }
    }

    return bestMatch;
}
```

### Color Table Management

```cpp
void BaseSymbologyData::SetColorTable(const byte* colorTable)
{
    memcpy(m_colorTable, colorTable, sizeof(m_colorTable));
    
    // Rebuild color tree for fast lookup
    if (m_pColorTree) {
        delete[] m_pColorTree;
        m_pColorTree = nullptr;
    }
    
    BuildColorTree();
}

void DgnSymbologyData::ReadColorTable(DgnModelP modelRef)
{
    DgnColorMapP colorMap = DgnColorMap::GetForDisplay(modelRef);
    if (!colorMap)
        return;

    byte dgnCtbl[800];
    colorMap->GetRgbColors((RgbColorDef*)dgnCtbl);

    // Adjust for DGN color table format
    memcpy(&dgnCtbl[768], dgnCtbl, 3);
    SetColorTable(dgnCtbl + 3);
}
```

## Line Style Management

### Line Style Conversion

**File**: `rdLineStyleConvert.cpp`

Line styles require complex mapping between AutoCAD linetypes and DGN line styles:

```cpp
class LineStyleConverter
{
public:
    StatusInt ConvertDgnLineStyleToDwg(
        DgnLineStyleP dgnLineStyle,
        AcDbLinetypeTableRecord*& pLinetype,
        ConvertFromDgnContext* context);
        
    StatusInt ConvertDwgLinetypeToDgn(
        AcDbLinetypeTableRecord* pLinetype,
        DgnLineStyleP& dgnLineStyle,
        ConvertToDgnContext* context);

private:
    StatusInt CreateComplexLineStyle(
        const LineStyleParams& params,
        DgnLineStyleP& lineStyle);
};
```

### Line Style Parameters

```cpp
struct LineStyleParams
{
    WString name;
    double scale;
    bvector<double> dashPattern;
    bool hasSymbols;
    bvector<SymbolInfo> symbols;
    
    // DGN-specific properties
    UInt32 modifiers;
    double width;
    ColorInfo color;
};
```

### Line Style Mapping Process

```cpp
StatusInt ConvertDwgLinetypeToDgn(
    AcDbLinetypeTableRecord* pLinetype,
    DgnLineStyleP& dgnLineStyle,
    ConvertToDgnContext* context)
{
    // Get linetype properties
    WString linetypeName;
    pLinetype->getName(linetypeName);
    
    double patternLength = pLinetype->patternLength();
    int numDashes = pLinetype->numDashes();
    
    // Build dash pattern
    bvector<double> dashPattern;
    for (int i = 0; i < numDashes; ++i) {
        double dashLength = pLinetype->dashLengthAt(i);
        dashPattern.push_back(dashLength);
    }
    
    // Check for complex linetype (with shapes/text)
    bool hasShapes = false;
    for (int i = 0; i < numDashes; ++i) {
        if (pLinetype->shapeStyleAt(i) != 0) {
            hasShapes = true;
            break;
        }
    }
    
    if (hasShapes) {
        return CreateComplexLineStyle(pLinetype, dgnLineStyle, context);
    } else {
        return CreateSimpleLineStyle(dashPattern, dgnLineStyle, context);
    }
}
```

## Layer Management

### Layer Table Index

**File**: `rDwgTableIndex.cpp`

```cpp
class LayerTableIndex
{
private:
    struct DgnIdToLayerHandleNode {
        UInt32 dgnId;
        AcDbHandle dbHandle;
    };
    
    struct LayerHandleToDgnIdNode {
        AcDbHandle dbHandle;
        UInt32 dgnId;
    };
    
    AvlTree m_dgnIdToHandleTree;
    AvlTree m_handleToDgnIdTree;

public:
    AcDbHandle GetDBHandle(UInt32 dgnId);
    UInt32 GetDgnId(AcDbHandle dbHandle);
    StatusInt AddEntry(UInt32 dgnId, AcDbHandle dbHandle);
    void RemoveEntry(UInt32 dgnId);
};
```

### Layer Conversion

**File**: `rdLayerConvert.cpp`

```cpp
StatusInt ConvertDgnLevelToDwgLayer(
    DgnLevelP dgnLevel,
    AcDbLayerTableRecord*& pLayer,
    ConvertFromDgnContext* context)
{
    // Create AutoCAD layer
    pLayer = new AcDbLayerTableRecord();
    
    // Set layer name
    WString levelName = dgnLevel->GetName();
    pLayer->setName(levelName.c_str());
    
    // Convert color
    UInt32 dgnColor = dgnLevel->GetColorIndex();
    UInt32 dwgColor = context->GetDwgSymbologyData()->
        GetColorIndex(dgnColor, context);
    
    AcCmColor acadColor;
    acadColor.setColorIndex(dwgColor);
    pLayer->setColor(acadColor);
    
    // Convert linetype
    UInt32 dgnLineStyle = dgnLevel->GetLineStyleId();
    AcDbObjectId linetypeId = MapLineStyleToDwg(dgnLineStyle, context);
    pLayer->setLinetypeObjectId(linetypeId);
    
    // Convert line weight
    UInt32 dgnWeight = dgnLevel->GetWeight();
    AcDb::LineWeight dwgWeight = ConvertWeight(dgnWeight);
    pLayer->setLineWeight(dwgWeight);
    
    // Set layer state
    pLayer->setIsFrozen(dgnLevel->GetFrozenFlag());
    pLayer->setIsOff(!dgnLevel->GetDisplayFlag());
    pLayer->setIsLocked(dgnLevel->GetReadOnlyFlag());
    
    return SUCCESS;
}
```

## Style Management

### Text Style Conversion

**File**: `rdTextStyleConvert.cpp`

```cpp
StatusInt ConvertDgnTextStyleToDwg(
    DgnTextStyleP dgnTextStyle,
    AcDbTextStyleTableRecord*& pTextStyle,
    ConvertFromDgnContext* context)
{
    pTextStyle = new AcDbTextStyleTableRecord();
    
    // Set style name
    WString styleName = dgnTextStyle->GetName();
    pTextStyle->setName(styleName.c_str());
    
    // Convert font
    FontInfo fontInfo = dgnTextStyle->GetFont();
    if (fontInfo.isTrueType) {
        pTextStyle->setFont(fontInfo.name.c_str(), false, false, 0, 0);
    } else {
        // Map SHX font
        WString shxFont = MapDgnFontToShx(fontInfo.name);
        pTextStyle->setFileName(shxFont.c_str());
    }
    
    // Set text properties
    pTextStyle->setTextSize(dgnTextStyle->GetHeight());
    pTextStyle->setXScale(dgnTextStyle->GetWidthFactor());
    pTextStyle->setObliquingAngle(dgnTextStyle->GetSlantAngle());
    
    return SUCCESS;
}
```

### Dimension Style Conversion

**File**: `rdDimStyleConvert.cpp`

Dimension styles are complex and require mapping many properties:

```cpp
StatusInt ConvertDgnDimensionStyleToDwg(
    DgnDimensionStyleP dgnDimStyle,
    AcDbDimStyleTableRecord*& pDimStyle,
    ConvertFromDgnContext* context)
{
    pDimStyle = new AcDbDimStyleTableRecord();
    
    // Basic properties
    pDimStyle->setName(dgnDimStyle->GetName().c_str());
    
    // Text properties
    pDimStyle->setDimtxsty(MapTextStyle(dgnDimStyle->GetTextStyleId()));
    pDimStyle->setDimtxt(dgnDimStyle->GetTextHeight());
    pDimStyle->setDimgap(dgnDimStyle->GetTextMargin());
    
    // Arrow properties
    pDimStyle->setDimblk(MapArrowhead(dgnDimStyle->GetArrowheadType()));
    pDimStyle->setDimasz(dgnDimStyle->GetArrowheadSize());
    
    // Line properties
    pDimStyle->setDimclrd(MapColor(dgnDimStyle->GetDimensionColor()));
    pDimStyle->setDimclre(MapColor(dgnDimStyle->GetExtensionColor()));
    
    // Extension line properties
    pDimStyle->setDimexe(dgnDimStyle->GetExtensionAbove());
    pDimStyle->setDimexo(dgnDimStyle->GetExtensionOrigin());
    
    // Tolerance properties
    if (dgnDimStyle->HasTolerances()) {
        ConvertToleranceSettings(dgnDimStyle, pDimStyle);
    }
    
    return SUCCESS;
}
```

## Weight Conversion

### Line Weight Mapping

```cpp
AcDb::LineWeight ConvertDgnWeightToDwg(UInt32 dgnWeight)
{
    // DGN weights are in 1/1000 inch units
    // AutoCAD weights are predefined values
    
    static const struct {
        UInt32 dgnWeight;
        AcDb::LineWeight dwgWeight;
    } weightMap[] = {
        { 0,   AcDb::kLnWt000 },
        { 5,   AcDb::kLnWt005 },
        { 9,   AcDb::kLnWt009 },
        { 13,  AcDb::kLnWt013 },
        { 15,  AcDb::kLnWt015 },
        { 18,  AcDb::kLnWt018 },
        { 20,  AcDb::kLnWt020 },
        { 25,  AcDb::kLnWt025 },
        { 30,  AcDb::kLnWt030 },
        { 35,  AcDb::kLnWt035 },
        { 40,  AcDb::kLnWt040 },
        { 50,  AcDb::kLnWt050 },
        { 53,  AcDb::kLnWt053 },
        { 60,  AcDb::kLnWt060 },
        { 70,  AcDb::kLnWt070 },
        { 80,  AcDb::kLnWt080 },
        { 90,  AcDb::kLnWt090 },
        { 100, AcDb::kLnWt100 },
        { 106, AcDb::kLnWt106 },
        { 120, AcDb::kLnWt120 },
        { 140, AcDb::kLnWt140 },
        { 158, AcDb::kLnWt158 },
        { 200, AcDb::kLnWt200 },
        { 211, AcDb::kLnWt211 }
    };
    
    // Find closest match
    AcDb::LineWeight bestMatch = AcDb::kLnWt000;
    UInt32 minDiff = UINT32_MAX;
    
    for (const auto& entry : weightMap) {
        UInt32 diff = abs((int)dgnWeight - (int)entry.dgnWeight);
        if (diff < minDiff) {
            minDiff = diff;
            bestMatch = entry.dwgWeight;
        }
    }
    
    return bestMatch;
}
```

## Symbology Caching

### Cache Management

```cpp
class SymbologyCache
{
private:
    std::map<UInt32, UInt32> m_colorCache;
    std::map<UInt32, UInt32> m_lineStyleCache;
    std::map<AcDbObjectId, UInt32> m_textStyleCache;

public:
    UInt32 GetCachedColor(UInt32 sourceColor);
    void CacheColorMapping(UInt32 sourceColor, UInt32 targetColor);
    
    UInt32 GetCachedLineStyle(UInt32 sourceStyle);
    void CacheLineStyleMapping(UInt32 sourceStyle, UInt32 targetStyle);
    
    void ClearCache();
};
```

### Performance Optimization

```cpp
UInt32 BaseSymbologyData::GetColorIndexOptimized(
    const RgbColorDef* pColor,
    BaseConvertContext* context)
{
    // Check cache first
    UInt32 colorKey = MakeColorKey(pColor);
    UInt32 cachedIndex = context->GetCachedColor(colorKey);
    if (cachedIndex != INVALID_COLOR_INDEX)
        return cachedIndex;
    
    // Compute color index
    UInt32 colorIndex = GetColorIndex(pColor, context);
    
    // Cache result
    context->CacheColorMapping(colorKey, colorIndex);
    
    return colorIndex;
}
```

## Error Handling

### Symbology Fallbacks

```cpp
UInt32 GetSafeColorIndex(
    const RgbColorDef* pColor,
    BaseConvertContext* context)
{
    try {
        return GetColorIndex(pColor, context);
    }
    catch (...) {
        // Fallback to default color
        context->LogWarning(L"Color conversion failed, using default");
        return 7;  // White
    }
}
```

### Validation

```cpp
bool ValidateSymbologyData(const SymbologyData& data)
{
    // Validate color table
    if (!data.HasValidColorTable()) {
        return false;
    }
    
    // Validate line style definitions
    if (!data.HasValidLineStyles()) {
        return false;
    }
    
    // Validate weight ranges
    if (!data.HasValidWeights()) {
        return false;
    }
    
    return true;
}
```

This symbology management system ensures high-fidelity preservation of visual properties during conversion between DWG/DXF and DGN formats, while providing efficient caching and fallback mechanisms for robust operation.

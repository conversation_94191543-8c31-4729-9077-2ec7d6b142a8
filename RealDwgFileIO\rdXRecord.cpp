/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdXRecord.cpp $
|
|  $Copyright: (c) 2016 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtXrecord : public ToDgnExtension
{
    mutable ConvertToDgnContext*    m_toDgnContext;
    mutable AcDbDatabase*           m_database;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/10
+---------------+---------------+---------------+---------------+---------------+------*/
static int          ExtractOriginalElementSize (const AcString& entryName, bool isModelApplication)
    {
    int         modelId = 0, attrOffset = 0;
    ElementId   elemId = 0;

    // if dictionary name has attrOffset which is added since 8.11.9.94, restore it so an app can retrieve its linkage attached to type 66:
    if (isModelApplication)
        {
        size_t  nChars = wcslen (StringConstants::UstnDictionaryItem_ModelApplication);
        if (3 == swscanf(&entryName.kwszPtr()[nChars], L"%d %I64d %d", &modelId, &elemId, &attrOffset))
            return  attrOffset;
        }
    else
        {
        size_t  nChars = wcslen (StringConstants::UstnDictionaryItem_Application);
        if (2 == swscanf(&entryName.kwszPtr()[nChars], L"%I64d %d", &elemId, &attrOffset))
            return  attrOffset;
        }

    return  0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus       ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbXrecord*    acXRecord = AcDbXrecord::cast (acObject);
    if (nullptr == acXRecord)
        return  NullObject;

    AcDbObjectId    xRecordOwnerId;
    if ( (xRecordOwnerId = acObject->ownerId()).isNull() )
        {
        DIAGNOSTIC_PRINTF ("Ignoring XRecord with NULL owner\n");
        return RealDwgIgnoreEntity;
        }

    AcDbDictionaryPointer   xRecordOwnerDictionary (xRecordOwnerId, AcDb::kForRead);
    if (Acad::eOk != xRecordOwnerDictionary.openStatus())
        return  CantOpenObject;

    /*----------------------------------------------------------------------------------------------------
    First make sure that this is owned by the MicroStation Dictionary or the DgnModel Dictionary.

    This method gets called from all xRecords, so do not recursive checking all ancestors as that incurs a
    performance penalty.  At this point only two levels of hierarchy are needed.  Should deeper hierarchy is 
    needed in the future we shall handle that per case by case.
    ----------------------------------------------------------------------------------------------------*/
    AcDbObjectId            microStationId = context.GetMicroStationDictionaryId (false);
    if (!microStationId.isValid())
        return RealDwgIgnoreEntity;
    
    AcDbObjectId            grandParentId = xRecordOwnerDictionary->ownerId ();
    if (xRecordOwnerId != microStationId && grandParentId != microStationId)
        return RealDwgIgnoreEntity;

    m_toDgnContext = &context;
    m_database = context.GetDatabase ();

    return  this->ToElement (outElement, acXRecord, xRecordOwnerDictionary);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToElement (EditElementHandleR outElement, AcDbXrecord* pXRecord, AcDbDictionary* ownerDictionary) const
    {
    AcString            entryName;
    if (Acad::eOk != ownerDictionary->nameAt (pXRecord->objectId(), entryName))
        return RealDwgIgnoreEntity;

    RealDwgBinaryData   binaryData;
    RealDwgResBuf*      resBuf;
    if (Acad::eOk != pXRecord->rbChain ((struct resbuf**)&resBuf, m_database))
        return RealDwgIgnoreEntity;

    RealDwgStatus   resBufStatus = RealDwgResBuf::BinaryDataFromRbChain (binaryData, static_cast <RealDwgResBuf*>(resBuf));
    RealDwgResBuf::Free (resBuf);

    if (RealDwgSuccess != resBufStatus)
        return RealDwgIgnoreEntity;

    bool            isNonModelApplication = (entryName.match (StringConstants::UstnDictionaryItem_Application) == wcslen (StringConstants::UstnDictionaryItem_Application));
    bool            isModelApplication    = (entryName.match (StringConstants::UstnDictionaryItem_ModelApplication) == wcslen (StringConstants::UstnDictionaryItem_ModelApplication));

    MSElement       element;
    size_t          headerSize = sizeof (element.ehdr);
    memset (&element.ehdr, 0, headerSize);
    if (isModelApplication || isNonModelApplication)
        {
        element.ehdr.type = MICROSTATION_ELM;
        element.ehdr.level = MSAPPINFO_LEVEL;
        element.ehdr.uniqueId = m_toDgnContext->ElementIdFromObject (pXRecord);
        element.ehdr.nonModel = isNonModelApplication;

        element.ehdr.elementSize = (unsigned int) (headerSize + binaryData.size())/2;
        element.ehdr.attrOffset = ExtractOriginalElementSize (entryName, isModelApplication);
        // we only saved element buffer from end of buffer to end of element, so anything else must be bogus, TR#317048.
        if (element.ehdr.attrOffset < headerSize/2 || element.ehdr.attrOffset > element.ehdr.elementSize)
            element.ehdr.attrOffset = element.ehdr.elementSize;
        }
    else if (entryName.match(StringConstants::UstnDictionaryItem_ExtendedNonGraphics) == wcslen(StringConstants::UstnDictionaryItem_ExtendedNonGraphics))
        {
        ElementId   elemId = 0;
        Int32       modelId = -2;
        UInt32      attrOffset = 0;
        size_t      nChars = wcslen (StringConstants::UstnDictionaryItem_ExtendedNonGraphics);
        if (3 != swscanf(&entryName.kwszPtr()[nChars], L"%d %I64d %d", &modelId, &elemId, &attrOffset))
            return  CantCreateNonGraphicsElement;

        element.ehdr.type = EXTENDED_NONGRAPHIC_ELM;
        element.ehdr.uniqueId = m_toDgnContext->ElementIdFromObject (pXRecord);
        element.ehdr.nonModel = modelId < 0;
        
        element.ehdr.elementSize = (unsigned int) (headerSize + binaryData.size())/2;
        element.ehdr.attrOffset = attrOffset > 0 ? attrOffset : element.ehdr.elementSize;
        element.ehdr.attrOffset = (attrOffset < headerSize/2 || attrOffset > element.ehdr.elementSize) ? element.ehdr.elementSize : attrOffset;
        }
    else if (entryName.match(StringConstants::UstnDictionaryItem_DgnStore) == wcslen(StringConstants::UstnDictionaryItem_DgnStore))
        {
        int         type = 0;
        ElementId   elemId = 0;
        UInt32      attrOffset = 0;
        size_t      nChars = wcslen (StringConstants::UstnDictionaryItem_DgnStore);
        if (3 != swscanf(&entryName.kwszPtr()[nChars], L"%d %I64d %d", &type, &elemId, &attrOffset) || (DGNSTORE_HDR != type && DGNSTORE_COMP != type))
            return  CantCreateDgnStore;

        element.ehdr.type = type;
        element.ehdr.isComplexHeader = (DGNSTORE_HDR == type);
        element.ehdr.uniqueId = m_toDgnContext->ElementIdFromObject (pXRecord);
        element.ehdr.nonModel = true;
        
        element.ehdr.elementSize = (unsigned int) (headerSize + binaryData.size())/2;
        element.ehdr.attrOffset = attrOffset > 0 ? attrOffset : element.ehdr.elementSize;
        // we only saved element buffer from end of buffer to end of element, so anything else must be bogus, TR#317048.
        element.ehdr.attrOffset = (attrOffset < headerSize/2 || attrOffset > element.ehdr.elementSize) ? element.ehdr.elementSize : attrOffset;
        }
    else if (entryName == StringConstants::UstnDictionaryItem_Header && binaryData.size() == offsetof (Tcb, firstNonPersistentMember))
        {
        element.ehdr.type = DGNFIL_HEADER_ELM;
        element.ehdr.level = 8;
        element.ehdr.uniqueId = m_toDgnContext->ElementIdFromObject (pXRecord);
        element.ehdr.nonModel = true;
        element.ehdr.elementSize = element.ehdr.attrOffset = (unsigned int) (sizeof (element.ehdr) + binaryData.size())/2;
        }
    else
        {
        // should never reach here!
        BeAssert (false && L"Unsupported XRecord!");
        return RealDwgIgnoreEntity;
        }

    // set data and create a new element handle:
    memcpy (&element.hdr.dhdr, &binaryData[0], binaryData.size());

    outElement.SetElementDescr (MSElementDescr::Allocate(element, m_toDgnContext->GetModel()), true, false);
    if (!outElement.IsValid())
        return  CantCreateDgnStore;

    // extract xattributes from xdata and add it to type 9, 66, 38 or 39 element.
    m_toDgnContext->ExtractXAttributesFromExtensionDictionary (outElement, pXRecord);

    // recurse into child xRecord objects for a complex element
    if (element.ehdr.isComplexHeader)
        this->ExtractChildXRecords (outElement, pXRecord);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ExtractChildXRecords (EditElementHandleR parentElement, AcDbObjectP parentObject) const
    {
    if (!parentElement.IsValid())
        return  MstnElementUnacceptable;
        
    // open the extension dictionary
    AcDbDictionaryPointer   parentDictionary (parentObject->extensionDictionary(), AcDb::kForRead);
    if (Acad::eOk != parentDictionary.openStatus())
        return  CantOpenObject;

    // iterate through all child objects of the extension dictionary
    MSElementDescrP         children = nullptr, lastChild = nullptr;
    AcDbDictionaryIterator* iterator = parentDictionary->newIterator ();
    for (; !iterator->done(); iterator->next())
        {
        // skip the xRecord of the XAttributes
        if (0 == wcscmp (iterator->name(), StringConstants::XDataKey_XAttribute))
            continue;

        // open the xrecord
        AcDbXrecordPointer      childXrecord (iterator->objectId(), AcDb::kForRead);
        if (Acad::eOk != childXrecord.openStatus() || !childXrecord->isKindOf(AcDbXrecord::desc()))
            return  CantOpenObject;

        // convert this child xrecord to a child element:
        EditElementHandle       childElement;
        if (RealDwgSuccess == this->ToElement(childElement, childXrecord, parentDictionary))
            {
            // add the child element in the chain
            if (nullptr == children)
                children = childElement.ExtractElementDescr ();
            else
                MSElementDescr::InitOrAddToChainWithTail (&children, &lastChild, childElement.ExtractElementDescr());
            }
        }

    // append all converted children to the parent element
    if (nullptr != children)
        parentElement.GetElementDescrP()->AppendDescr (children);

    return  RealDwgSuccess;
    
    }

};  // ToDgnExtXrecord

/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdLeader.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp


/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtLeader : public ToDgnExtension
{
public:
/*---------------------------------------------------------------------------------------
Interfaces required by dim handler to create a dimension element
---------------------------------------------------------------------------------------*/
struct NoteCreateData : public IDimCreateData
    {
    public:
        DimensionStyleR             m_dimStyle;
        DgnTextStyleR               m_textStyle;
        Symbology                   m_symbology;
        LevelId                     m_levelId;
        RotMatrix                   m_dimMatrix;
        RotMatrix                   m_viewMatrix;

    NoteCreateData (DimensionStyleR dimstyle, DgnTextStyleR txtstyle)
        : m_dimStyle(dimstyle), m_textStyle(txtstyle)
        {
        m_symbology.color = m_symbology.style = m_symbology.weight = 0;
        m_levelId = 0;
        m_dimMatrix.InitIdentity ();
        m_viewMatrix.InitIdentity ();
        }

    virtual DimensionStyleCR    _GetDimStyle() const    { return m_dimStyle; }
    virtual DgnTextStyleCR      _GetTextStyle() const   { return m_textStyle; }
    virtual Symbology           _GetSymbology() const   { return m_symbology; }
    virtual LevelId             _GetLevelID() const     { return m_levelId; }
    virtual int                 _GetViewNumber() const  { return 0; }
    virtual RotMatrixCR         _GetDimRMatrix() const  { return m_dimMatrix; }
    virtual RotMatrixCR         _GetViewRMatrix() const { return m_viewMatrix; }
    };  // NoteCreateData

private:
/*---------------------------------------------------------------------------------------
Workaround the lack of access to RealDWG's leader object for DXF group codes 74 and 211.
---------------------------------------------------------------------------------------*/
struct MissingLeaderFields
    {
    bool                        m_isHooklineOnXDir;     // DXF group code 74
    DVec3d                      m_xDirection;           // DXF group code 211

    MissingLeaderFields (const AcDbLeader*  pLeader)
        {
        m_isHooklineOnXDir = true;

        AcGeMatrix3d    ecs;
        pLeader->getEcs (ecs);

        AcGePoint3d     origin;
        AcGeVector3d    x, y, z;

        ecs.getCoordSystem (origin, x, y, z);

        RealDwgUtil::DVec3dFromGeVector3d (m_xDirection, x);
        }
    };

/*---------------------------------------------------------------------------------------
Local variables used within this extension, mostly for filer extraction.
---------------------------------------------------------------------------------------*/
    mutable bool                m_hasHookline;          // DXF group code 75
    mutable bool                m_isHooklineOnXDir;     // DXF group code 74
    mutable DVec3d              m_xDirection;           // DXF group code 211
    mutable bool                m_isAnnotative;
    mutable double              m_annotationScale;

public:
    ToDgnExtLeader()
        {
        m_hasHookline = false;
        m_isHooklineOnXDir = true;
        m_xDirection.Init (1, 0, 0);
        }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus       ToElement
(
AcDbObjectP                 acObject,
EditElementHandleR          outElement,
ConvertToDgnContextR        context
) const override
    {
    AcDbLeader*     pLeader = AcDbLeader::cast (acObject);

    m_annotationScale = 1.0;
    m_isAnnotative = context.GetDisplayedAnnotationScale(m_annotationScale, acObject);

    RealDwgStatus   status;
    if (RealDwgSuccess != (status = CreateDgnLeader(outElement, pLeader, context)))
        return status; 
    
    if (m_isAnnotative)
        {
        DimensionHandler*   dimHandler = dynamic_cast <DimensionHandler*> (&outElement.GetHandler());

        double oldAnnotationScale;
        bool wasAnnotative = dimHandler->GetAnnotationScale(&oldAnnotationScale, outElement);

        if (wasAnnotative && (m_annotationScale != oldAnnotationScale))
            dimHandler->UpdateSpecifiedAnnotationScale(outElement, m_annotationScale);
        else
            RealDwgUtil::SetElementAnnotative(outElement, context.GetModel(), &m_annotationScale);
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               CreateDgnLeader
(
EditElementHandleR          outElement,
const AcDbLeader*           pLeader,
ConvertToDgnContextR        context
) const
    {
    DimensionStylePtr       dgnStyleFromDim;
    DimStylePropMaskPtr     dimStyleCompareMask = DimStylePropMask::CreatePropMask();

    // obtain the dimstyle on dimension, compare it with the dimstyle record in table,
    // and get the mask for style overrides based on the comparison
    context.GetDgnDimensionStyleFromAcEntity (dgnStyleFromDim, dimStyleCompareMask, pLeader);

    // set leader specific variables - remove this once we support it in MS.
    UpdateDimstyleForLeader (dgnStyleFromDim, pLeader, dimStyleCompareMask, context);

    // convert dwg leader to dgn
    RealDwgStatus   status = ConvertDwgLeader (outElement, pLeader, dgnStyleFromDim, context);
    // set the comparision mask to element
    if (RealDwgSuccess == status)
        DimensionHandler::GetInstance().SaveShieldsDirect (outElement, *dimStyleCompareMask);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
virtual   RealDwgStatus   FromElementDeleteObject (AcDbObject* acObject, AcDbObjectIdArray& idsToPurge, ConvertFromDgnContext& context) const override
    {
    AcDbLeader*     pLeader = AcDbLeader::cast (acObject);
    if (NULL == pLeader)
        return  NullObject;

    // remove the persistent reactor on dimension style
    AcDbObjectId    dimstyleId = pLeader->dimensionStyle();

    AcDbObjectPointer<AcDbObject> pDimStyle (dimstyleId, AcDb::kForWrite);
    if (Acad::eOk == pDimStyle.openStatus())
        pDimStyle->removePersistentReactor (acObject->objectId());

    acObject->erase();

    return RealDwgSuccess;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
RotMatrixR                  GetEcsMatrixFromLeader
(
RotMatrixR                  defMatrix,
const AcDbLeader*           pLeader
) const
    {
    AcGeVector3d            normal = pLeader->normal ();

    // try to heal leader on a plane that is opposite to the mtext's
    if (!pLeader->annotationObjId().isNull())
        {
        AcDbEntityPointer   pEntity (pLeader->annotationObjId(), AcDb::kForRead, false);
        AcDbMText*          pMText;
        if (NULL != (pMText = AcDbMText::cast (pEntity)))
            {
            // get mtext's normal, for other objects just use leader's normal.
            if (!normal.isEqualTo (pMText->normal()) && normal.isParallelTo (pMText->normal()))
                normal = pMText->normal();
            }
        }

    DVec3d          yDir, zDir;
    RealDwgUtil::DVec3dFromGeVector3d (zDir, normal);
    yDir.CrossProduct (zDir, m_xDirection);

    defMatrix.InitFromColumnVectors (m_xDirection, yDir, zDir);

    return defMatrix;
    }

/*---------------------------------------------------------------------------------**//**
* Get size of arrowhead, applied by dimscale
*
* @bsimethod                                                    DonFu           03/01
+---------------+---------------+---------------+---------------+---------------+------*/
double           GetArrowheadSize (const AcDbLeader* pLeader, ConvertToDgnContextR context) const
    {
    double  arrowSize = pLeader->dimasz ();
    double  dimScale  = this->GetEffectiveDimscale (pLeader, context);

    arrowSize *= dimScale;

    return  arrowSize;
    }

/*---------------------------------------------------------------------------------**//**
* check arrow head on/off status
*
* @return       true - arrow head display is on; false - it's off
* @bsimethod                                                    DonFu           01/01
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsArrowheadVisible (const AcDbLeader* pLeader, ConvertToDgnContextR context) const
    {
    if (!pLeader->hasArrowHead() || pLeader->numVertices() < 2)
        return  false;

    // Acad automatically turns off arrowhead if arrowhead size is greater than 1/2 of the first segment length.
    DPoint3d    point1, point2;
    RealDwgUtil::DPoint3dFromGePoint3d (point1, pLeader->vertexAt(0));
    RealDwgUtil::DPoint3dFromGePoint3d (point2, pLeader->vertexAt(1));

    // is the arrow size > the first segment length?
    // the magic criteria 0.485 comes from reverse engineering noarrow1.dwg and TR 116110.
    // An exception is when no hookline attached which uses 1/2.
    double      nearHalf = AcDbLeader::kNoAnno == pLeader->annoType() ? 0.5 : 0.485;
    return this->GetArrowheadSize(pLeader, context) <= nearHalf*point1.distance(&point2);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
---------------+---------------+---------------+---------------+---------------+------*/
static bool                 GetTerminatorName
(
WCharP                    pBlockName,
int                         length,
const AcDbLeader*           pLeader,
ConvertToDgnContextR        context
)
    {
    pBlockName[0] = 0;

    if (pLeader->dimldrblk().isNull())
        return false;

    AcDbBlockTableRecordPointer pBlock (pLeader->dimldrblk(), AcDb::kForRead);
    if (Acad::eOk != pBlock.openStatus())
        return  false;

    const ACHAR* terminatorName = NULL;
    if (Acad::eOk == pBlock->getName(terminatorName) && 0 != *terminatorName)
        RealDwgUtil::TerminatedStringCopy (pBlockName, terminatorName, length);

    return  0 != pBlockName[0];
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           10/02
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SetLeaderElbowLength
(
EditElementHandleR          outElement,
DimensionStylePtr           dgnDimstyle,
const AcDbLeader*           pLeader,
ConvertToDgnContextR        context
)
    {
    /*-----------------------------------------------------------------------------------
    ACAD's elbow length=terminator length
    -----------------------------------------------------------------------------------*/
    double elbowLength = pLeader->dimasz() * context.GetScaleToDGN();

    double textHeight = 1.0;
    dgnDimstyle->GetDistanceProp (textHeight, DIMSTYLE_PROP_Text_Height_DISTANCE, context.GetModel());
    if (textHeight < TOLERANCE_TextHeight)
        textHeight = 1.0;

    /*-----------------------------------------------------------------------------------
    DGN's elbow length is actually a scale factor to text height
    -----------------------------------------------------------------------------------*/
    elbowLength /= textHeight;

    // turn off actual elbow display bby setting it to 0 when leader has underline but no hookline
    if (!pLeader->hasHookLine() && pLeader->dimtad() >= 1)
        elbowLength = 0.0;

    mdlDim_setNoteElbowLength (outElement, &elbowLength);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetSplineEndTangents
(
EditElementHandleR          outElement,
const AcDbLeader*           pLeader
) const
    {
    int         numPoints = pLeader->numVertices ();
    if (numPoints < 2)
        return;

    // get first 2 points on leader
    DPoint3d    points[2];
    RealDwgUtil::DPoint3dFromGePoint3d (points[0], pLeader->firstVertex());
    RealDwgUtil::DPoint3dFromGePoint3d (points[1], pLeader->vertexAt(1));

    bool        isAttached = AcDbLeader::kNoAnno != pLeader->annoType();
    return ToDgnExtLeader::SetSplineEndTangents (outElement, points, m_xDirection, m_isHooklineOnXDir, isAttached, numPoints > 2);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SetSplineEndTangents (EditElementHandleR outElement, DPoint3d points[], DVec3dR xDirection, bool isHooklineOnXDir, bool isAttached, bool setStartTangent)
    {
    // Start tangential vector: make it to point inward
    if (setStartTangent)
        {
        // start tangent is a fixed vector, hence editing the note may results in bad terminator orientation
        DPoint3d    tangent;
        tangent.DifferenceOf (points[1], points[0]);
        tangent.Normalize ();

        mdlDim_segmentSetCurveStartTangent (outElement, 0, &tangent);
        }

    // End tangential vector: make it to point inward
    if (isAttached)
        {
        /*-------------------------------------------------------------------------------
        End tangent vector is along x-axis of the leader when annotation is present.
        The direction of the tangential vector is either on the same direction of leader
        x-axis or the opposite of it.
        -------------------------------------------------------------------------------*/
        DPoint3d    tangent = xDirection;

        // apply direction to the vector
        if (!isHooklineOnXDir)
            tangent.Negate ();

        mdlDim_segmentSetCurveEndTangent (outElement, 0, &tangent);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/04
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsAttachedAtMiddleLinePoint
(
const AcDbLeader*           pLeader,
const AcDbMText*            pMtext,
const double                deltaY
)
    {
    // build mtext's ECS matrix from mtext's x and z vectors
    DVec3d          xAxis, yAxis, zAxis;
    RealDwgUtil::DVec3dFromGeVector3d (xAxis, pMtext->direction());
    RealDwgUtil::DVec3dFromGeVector3d (zAxis, pMtext->normal());
    yAxis.crossProduct (&zAxis, &xAxis);

    RotMatrix       ecsMatrix;
    ecsMatrix.initFromColumnVectors (&xAxis, &yAxis, &zAxis);

    // get the mtext's attachment point, and transform it to mtext's ECS
    DPoint3d    attachPoint;
    RealDwgUtil::DPoint3dFromGePoint3d (attachPoint, pMtext->location());
    ecsMatrix.multiply (&attachPoint);

    // move the attachment point by deltaY to point at the middle of line
    attachPoint.y += deltaY;

    // also transform leader point to mtext's ECS
    DPoint3d        hookPoint;
    RealDwgUtil::DPoint3dFromGePoint3d (hookPoint, pLeader->vertexAt(pLeader->numVertices() - 1));
    ecsMatrix.multiply (&hookPoint);

    /*-----------------------------------------------------------------------------------
    Check the y coordinates: if the hook point is at mtext's attachment point (or near it
    by half text height range), treat it as a middle line point:
    -----------------------------------------------------------------------------------*/
    if (fabs(hookPoint.y - attachPoint.y) < 0.9*fabs(deltaY))
        return  true;

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* create a DGN dimension leader from a DWG leader
*
* @bsimethod                                                    DonFu           01/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               CreateDimensionLeader
(
EditElementHandleR          outElement,
DimensionStylePtr           dgnDimstyle,
const AcDbLeader*           pLeader,
ConvertToDgnContextR        context
) const
    {
    // setup DimCreateData so we can create a stub note dimension element.
    DgnTextStylePtr     dgnTextStyle;
    if (SUCCESS != dgnDimstyle->GetTextStyleProp(dgnTextStyle, DIMSTYLE_PROP_Text_TextStyle_TEXTSTYLE) || !dgnTextStyle.IsValid())
        return  CantCreateLeader;

    // set leader type: 0= line, 1= BSpline
    dgnDimstyle->SetBooleanProp (Adesk::kTrue==pLeader->isSplined(), DIMSTYLE_PROP_MLNote_LeaderType_BOOLINT);

    // reset arrow terminator cell name in DGN dimstyle
    WChar               cellName[MAX_CELLNAME_LENGTH];
    GetTerminatorName (cellName, _countof (cellName), pLeader, context);
    if (0 != cellName[0])
        {
        dgnDimstyle->SetStringProp (cellName, DIMSTYLE_PROP_Terminator_ArrowCellName_MSWCHAR);
        context.ResolveTerminatorBlocks (pLeader);
        }

    NoteCreateData       noteCreate(*dgnDimstyle.get(), *dgnTextStyle.get());
    noteCreate.m_levelId = context.GetDgnLevel (pLeader->layerId());
    noteCreate.m_symbology.color = context.GetDgnColor (pLeader->entityColor());
    noteCreate.m_symbology.weight = context.GetDgnWeight (pLeader->lineWeight());
    noteCreate.m_symbology.style = context.GetDgnStyle (pLeader->linetypeId());

    GetEcsMatrixFromLeader (noteCreate.m_dimMatrix, pLeader);
    
    BentleyStatus   status = DimensionHandler::CreateDimensionElement (outElement, noteCreate, DimensionType::Note, context.GetThreeD(), *context.GetModel());
    if (SUCCESS != status)
        return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);

    DimensionHandler*   dimHandler = dynamic_cast <DimensionHandler*> (&outElement.GetHandler());
    if (NULL == dimHandler)
        return  BadElementHandler;

    DimText             dimText;
    memset (&dimText, 0, sizeof dimText);
    mdlDim_initDimTextFromStyle (&dimText, dgnDimstyle.get());

    // Insert leader points:
    DPoint3d            point;
    int                 numPoints = pLeader->hasHookLine() ? pLeader->numVertices() - 1 : pLeader->numVertices();
    for (int i = 0; i < numPoints; i++)
        {
        RealDwgUtil::DPoint3dFromGePoint3d (point, pLeader->vertexAt(i));

        context.GetTransformToDGN().Multiply (point);

        if (SUCCESS != dimHandler->InsertPointDirect(outElement, &point, NULL, dimText, i))
            return  CantInsertDimensionPoint;
        }

    // insert a hook point as the 2nd last def point
    if (pLeader->hasHookLine())
        {
        DPoint3d    newPoint;

        GetHookPoint (newPoint, pLeader, context);

        context.GetTransformToDGN().Multiply (newPoint);

        // the hook point is a vector on x-axis from the last leader point
        int insertAt = pLeader->numVertices() - 1;

        if (SUCCESS != dimHandler->InsertPointDirect(outElement, &newPoint, NULL, dimText, insertAt))
            return  CantInsertDimensionPoint;
        }

    /*-----------------------------------------------------------------------------------
    At this point we may not have cell created and thus mdlAssoc_getPoint may fail, so we
    can't call mdlDim_insertPointDirect to set the assoc point.  We work this around by
    setting the assoc point as the attachment point, either the last or the hook point:
    -----------------------------------------------------------------------------------*/
    if (!pLeader->annotationObjId().isNull())
        mdlNote_setNoteAssocPoint (outElement, context.ElementIdFromObjectId(pLeader->annotationObjId()), pLeader->numVertices()-1);

    if (pLeader->isSplined())
        SetSplineEndTangents (outElement, pLeader);

    // set text vertical placement mode
    UInt16  elbowJointMode = pLeader->dimtad() >= 1 ? DIMSTYLE_VALUE_MLNote_VerAttachment_Underline : DIMSTYLE_VALUE_MLNote_VerAttachment_Middle;
    // set vertical text justification
    UInt16  verticalJust = DIMSTYLE_VALUE_MLNote_VerticalJustification_Center;
    // set horizontal text justification
    DimStyleProp_MLNote_Justification  horizontalJust = DIMSTYLE_VALUE_MLNote_Justification_Left;

    if (!pLeader->annotationObjId().isNull())
        {
        AcDbEntityPointer   pObject (pLeader->annotationObjId(), AcDb::kForRead, false);
        AcDbMText*          pMText;
        if (NULL != (pMText = AcDbMText::cast (pObject)))
            {
            // check vertical attachment point as well as vertical justification from mtext
            switch (pMText->attachment())
                {
                case AcDbMText::kTopLeft:
                case AcDbMText::kTopCenter:
                case AcDbMText::kTopRight:
                case AcDbMText::kTopAlign:
                case AcDbMText::kTopFit:
                case AcDbMText::kTopMid:
                    if (DIMSTYLE_VALUE_MLNote_VerAttachment_Underline != elbowJointMode)
                        {
                        elbowJointMode = DIMSTYLE_VALUE_MLNote_VerAttachment_Top;
                        if (IsAttachedAtMiddleLinePoint(pLeader, pMText, -pMText->textHeight()/2.0))
                            elbowJointMode = DIMSTYLE_VALUE_MLNote_VerAttachment_TopLine;
                        }

                    verticalJust = DIMSTYLE_VALUE_MLNote_VerticalJustification_Top;
                    break;
                case AcDbMText::kBottomLeft:
                case AcDbMText::kBottomCenter:
                case AcDbMText::kBottomRight:
                case AcDbMText::kBottomAlign:
                case AcDbMText::kBottomFit:
                case AcDbMText::kBottomMid:
                    if (DIMSTYLE_VALUE_MLNote_VerAttachment_Underline != elbowJointMode)
                        {
                        elbowJointMode = DIMSTYLE_VALUE_MLNote_VerAttachment_Bottom;
                        if (IsAttachedAtMiddleLinePoint(pLeader, pMText, pMText->textHeight()/2.0))
                            elbowJointMode = DIMSTYLE_VALUE_MLNote_VerAttachment_BottomLine;
                        }

                    verticalJust = DIMSTYLE_VALUE_MLNote_VerticalJustification_Bottom;
                    break;
                }

            // check mtext's horizontal justification
            switch (pMText->attachment())
                {
                case AcDbMText::kTopCenter:
                case AcDbMText::kMiddleCenter:
                case AcDbMText::kBottomCenter:
                case AcDbMText::kBaseCenter:
                    horizontalJust = DIMSTYLE_VALUE_MLNote_Justification_Center;
                    break;
                case AcDbMText::kTopRight:
                case AcDbMText::kMiddleRight:
                case AcDbMText::kBottomRight:
                case AcDbMText::kBaseRight:
                    horizontalJust = DIMSTYLE_VALUE_MLNote_Justification_Right;
                    break;
                }
            }
        }
    mdlDim_setNoteVerLeftAttachment (outElement, &elbowJointMode);
    mdlDim_setNoteVerRightAttachment (outElement, &elbowJointMode);
    mdlDim_setNoteHorizontalJustification (outElement, horizontalJust);
    mdlDim_setMultiJustVertical (outElement, &verticalJust);
    // allow horizontal attachment point to be automatically determined based on text location:
    mdlDim_setNoteAllowAutoMode (outElement, true);

    // display hookline or underline
    mdlDim_setNoteLeaderDisplay (outElement, pLeader->hasHookLine() || pLeader->dimtad() >= 1);

    // set note frame
    mdlDim_setNoteFrameType (outElement, pLeader->dimgap() < 0.0 ? DIMSTYLE_VALUE_MLNote_FrameType_Box : DIMSTYLE_VALUE_MLNote_FrameType_None);

    // set leader elbow size
    SetLeaderElbowLength (outElement, dgnDimstyle, pLeader, context);

    // set symbology for dimension element - need this since we do not have true dimline symb override
    context.SetDimensionComponentSymbology (outElement, pLeader);

    status = outElement.GetDisplayHandler()->ValidateElementRange (outElement, true);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ReadLeaderFieldsCallback
(
AcDb::DxfCode               dxfGroupCode,
const void*                 dataIn,
void*                       dataOut
)
    {
    MissingLeaderFields*    missingData = (MissingLeaderFields*)dataOut;
    if (74 == dxfGroupCode)
        {
        Int16*              intVal = (Int16*) dataIn;
        missingData->m_isHooklineOnXDir = 0 != (*intVal);
        }
    else if (211 == dxfGroupCode)
        {
        AcGePoint3d*        point = (AcGePoint3d*) dataIn;
        RealDwgUtil::DPoint3dFromGePoint3d (missingData->m_xDirection, *point);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ExtractMissingFieldsFromLeader
(
const AcDbLeader*           pLeader
) const
    {
    // workaround the lack of ARX interfaces to get hookline flag and leader running direction.
    MissingLeaderFields     missingData(pLeader);
    ExtractionFiler         filer (ReadLeaderFieldsCallback, pLeader->database(), &missingData);
    filer.ExtractFrom (pLeader);

    m_isHooklineOnXDir = missingData.m_isHooklineOnXDir;
    m_xDirection = missingData.m_xDirection;
    }

/*---------------------------------------------------------------------------------**//**
* convert DWG leader to DGN dimension element
*
* @bsimethod                                                    DonFu           01/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertDwgLeader
(
EditElementHandleR          outElement,
const AcDbLeader*           pLeader,
DimensionStylePtr           dgnDimstyle,
ConvertToDgnContextR        context
) const
    {
    ExtractMissingFieldsFromLeader (pLeader);

    // create dimension element from the straight/spline leader object
    RealDwgStatus   status = CreateDimensionLeader (outElement, dgnDimstyle, pLeader, context);
    if (RealDwgSuccess != status)
        return  status;
    
    if (SUCCESS != outElement.GetDisplayHandler()->ValidateElementRange(outElement, true))
        return  CantSetRange;

    /* set everything on the elements except the colour and weight */
    int     mask = HEADERRESTOREMASK_All & ~HEADERRESTOREMASK_Color & ~HEADERRESTOREMASK_Weight & ~HEADERRESTOREMASK_CONSTRUCTIONCLASS;
    context.ElementHeaderFromEntity (outElement, pLeader, mask);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        UpdateDimstyleForLeader
(
DimensionStylePtr           dgnDimstyle,
const AcDbLeader*           pLeader,
DimStylePropMaskPtr         dimStyleCompareMask,
ConvertToDgnContextR        context
) const
    {
    // note element uses the right terminator
    dgnDimstyle->SetTemplateFlagProp (true, DimensionType::SizeArrow, DIMSTYLE_PROP_Terminator_Right_TEMPLATEFLAG);

    if (pLeader->dimldrblk().isNull())
        {
        // filled arrow head
        dgnDimstyle->SetIntegerProp (0, DIMSTYLE_PROP_Terminator_ArrowType_INTEGER);

        double  termWidth = 0.0;
        if (SUCCESS == dgnDimstyle->GetDoubleProp(termWidth, DIMSTYLE_PROP_Terminator_Width_DOUBLE))
            dgnDimstyle->SetDoubleProp (termWidth / 3.0, DIMSTYLE_PROP_Terminator_Height_DOUBLE);
        }
    else
        {
        // shared cell terminator
        dgnDimstyle->SetIntegerProp (2, DIMSTYLE_PROP_Terminator_ArrowType_INTEGER);

        AcDbEntityPointer   pObject (pLeader->dimldrblk(), AcDb::kForRead, false);

        AcDbBlockTableRecord* pBlock;
        if (NULL != (pBlock = AcDbBlockTableRecord::cast (pObject)))
            {
            const ACHAR* blockName;
            if (Acad::eOk == pBlock->getName(blockName))
                dgnDimstyle->SetStringProp (blockName, DIMSTYLE_PROP_Terminator_ArrowCellName_MSWCHAR);
            }
        }

    if (!this->IsArrowheadVisible(pLeader, context))
        {
        dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Terminator_Type_None, DIMSTYLE_PROP_Terminator_Note_INTEGER);
        dgnDimstyle->SetIntegerProp (DIMSTYLE_VALUE_Symbol_TermType_Default, DIMSTYLE_PROP_Terminator_NoteType_INTEGER);

        if (pLeader->hasArrowHead())
            {
            // enforce AutoCAD rule: turn off terminator when its size > 1/2 first segment length
            dimStyleCompareMask->SetPropertyBit (DIMSTYLE_PROP_Terminator_Note_INTEGER, true);
            dimStyleCompareMask->SetPropertyBit (DIMSTYLE_PROP_Terminator_NoteType_INTEGER, true);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
DPoint3dP                   GetXAxis
(
DPoint3dR                   xAxisOut,
const AcDbLeader*           pLeader
) const
    {
    /*-----------------------------------------------------------------------------------
    Hope RealDWG to eventually provide pLeader->annotationXDir() to replace this call.
    -----------------------------------------------------------------------------------*/
    xAxisOut.Init (m_xDirection);
    return  &xAxisOut;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
double                      GetHookLineDirection
(
const AcDbLeader*           pLeader
) const
    {
    /*-----------------------------------------------------------------------------------
    Hope RealDWG to eventually provide pLeader->isHooklineOnXDir() to replace this call.
    -----------------------------------------------------------------------------------*/
    return  m_isHooklineOnXDir ? 1.0 : -1.0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
DPoint3dP       GetHookPoint (DPoint3dR hookPoint, const AcDbLeader* pLeader, ConvertToDgnContextR context) const
    {
    // an annotative leader may have a dimscale=0
    double      dimScale = this->GetEffectiveDimscale (pLeader, context);
    
    /*-------------------------------------------------------------------------------
    The hook line size is the same as arrowhead height, and it's direction is
    either in the same direction as x-axis or the opposite of x-axis:
    -------------------------------------------------------------------------------*/
    double  hookLength = dimScale * pLeader->dimasz() * GetHookLineDirection (pLeader);

    // create hook line on x-axis
    DPoint3d    newPoint;
    GetXAxis (newPoint, pLeader)->scale (hookLength);

    // get last point
    RealDwgUtil::DPoint3dFromGePoint3d (hookPoint, pLeader->lastVertex());

    // get the new point location by shooting the vector from the last point
    newPoint.add (&hookPoint);

    hookPoint = newPoint;

    return  &hookPoint;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/13
+---------------+---------------+---------------+---------------+---------------+------*/
double          GetEffectiveDimscale (const AcDbLeader* acLeader, ConvertToDgnContextR context) const
    {
    double      dimscale = acLeader->dimscale ();

    // when annotation scale is applied to the leader, dimscale <= 0:
    if (dimscale > TOLERANCE_ZeroScale)
        return  dimscale;

    if (m_isAnnotative && (m_annotationScale > TOLERANCE_ZeroScale))
        return  m_annotationScale;
    else
        dimscale = 1.0;

    return  dimscale;
    }

/*---------------------------------------------------------------------------------------
 Each of below methods searches for a missing field via dxfOut, which is expensive.
 They should only be needed for mtext.  Hope to replace them when RealDWG will provide
 access to hookline direction flag and leader run direction.
---------------------------------------------------------------------------------------*/
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static double               ExtractHookLineDirection
(
const AcDbLeader*           pLeader
)
    {
    MissingLeaderFields     missingData(pLeader);
    ExtractionFiler         filer (ReadLeaderFieldsCallback, pLeader->database(), &missingData);
    filer.ExtractFrom (pLeader);

    return  missingData.m_isHooklineOnXDir ? 1.0 : -1.0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static DPoint3dP            ExtractHookPoint
(
DPoint3d&                   hookPoint,
double                      modelAnnoScale,
const AcDbLeader*           pLeader
)
    {
    MissingLeaderFields     missingData(pLeader);
    ExtractionFiler         filer (ReadLeaderFieldsCallback, pLeader->database(), &missingData);
    filer.ExtractFrom (pLeader);

    // an annotative leader may have a dimscale=0
    double  dimScale = pLeader->dimscale ();
    if (dimScale < TOLERANCE_ZeroScale && RealDwgUtil::IsConstObjectAnnotative(pLeader) && pLeader->ownerId() == acdbSymUtil()->blockModelSpaceId(pLeader->database()))
        dimScale = modelAnnoScale;

    if (dimScale < TOLERANCE_ZeroScale)
        dimScale = 1.0;

    /*-------------------------------------------------------------------------------
    The hook line size is the same as arrowhead height, and it's direction is
    either in the same direction as x-axis or the opposite of x-axis:
    -------------------------------------------------------------------------------*/
    double  hookLength = dimScale * pLeader->dimasz() * (missingData.m_isHooklineOnXDir ? 1.0 : -1.0);

    // create hook line on x-axis
    DPoint3d    newPoint;
    newPoint.init (&missingData.m_xDirection);
    newPoint.scale (hookLength);

    // get last point
    RealDwgUtil::DPoint3dFromGePoint3d (hookPoint, pLeader->lastVertex());

    // get the new point location by shooting the vector from the last point
    newPoint.add (&hookPoint);

    hookPoint = newPoint;

    return  &hookPoint;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static DPoint3dP            ExtractXAxis
(
DPoint3d&                   xAxisOut,
const AcDbLeader*           pLeader
)
    {
    MissingLeaderFields     missingData(pLeader);
    ExtractionFiler         filer (ReadLeaderFieldsCallback, pLeader->database(), &missingData);

    pLeader->dxfOut(&filer, Adesk::kTrue, NULL);

    xAxisOut.init (&missingData.m_xDirection);

    return  &xAxisOut;
    }

};  // ToDgnExtLeader



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          02/11
+===============+===============+===============+===============+===============+======*/
class           ConvertLeaderFromDgnDimension
{
private:
    DPoint3d                m_attachmentPoint;
    bool                    m_hasHookline;          // DXF group code 75
    bool                    m_isHooklineOnXDir;     // DXF group code 74
    DVec3d                  m_xDirection;           // DXF group code 211
    ElementHandleCP         m_dimensionElement;
    ElementHandleCP         m_noteElement;
    ConvertFromDgnContextP  m_fromDgnContext;
    double                  m_scaleFromDgn;

public:
    ConvertLeaderFromDgnDimension (ElementHandleCR dimElement, ElementHandleCP noteElement, ConvertFromDgnContextR context)
        {
        m_attachmentPoint.Zero ();
        m_hasHookline = false;
        m_isHooklineOnXDir = true;
        m_xDirection.Init (1, 0, 0);
        m_dimensionElement = &dimElement;   // => the dimension component of the note, required
        m_noteElement = noteElement;        // => the note cell component of the note, optional
        m_fromDgnContext = &context;
        m_scaleFromDgn = context.GetScaleFromDGN ();
        }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetLeaderFromNoteDimension
(
AcDbLeader*             pLeader
)
    {
    if (NULL == m_dimensionElement || !m_dimensionElement->IsValid())
        return  MstnElementUnacceptable;

    if (NULL == m_fromDgnContext || m_fromDgnContext->GetTargetVersion() < DwgFileVersion_13)
        return  UnsupportedInDwgVersion;

    DimensionElmCP  dgnDim = (DimensionElmCP) m_dimensionElement->GetElementCP();
    if (NULL == dgnDim)
        return  MstnElementUnacceptable;

    // convert the dgn dimstyle to DWG
    AcDbDimStyleTableRecord*  pDimstyle = new AcDbDimStyleTableRecord();

    m_fromDgnContext->InitializeDimstyleFromDimension (pDimstyle, pLeader);

    RealDwgStatus       status = m_fromDgnContext->CreateDwgDimstyleFromElement (pDimstyle, *m_dimensionElement);
    if (RealDwgSuccess != status)
        {
        delete pDimstyle;
        return  status;
        }

    // set dgn dimstyle element id if there is one attached:
    m_fromDgnContext->SetDimstyleRecordId (pLeader, *m_dimensionElement);

    /*--------------------------------------------------------------------------------------------------------
    Set annotation scale if the leader is in the default model.

    We have to do it after style ID is set and before leader parameters are set from calculated style data,
    because an annotative style, one with the ID, zeroes out our leader params like dimscale, dimtxt, and 
    dimasz etc.  Worse yet, when this happens we can no longer set the dimscale anymore and end up with an
    incorrect leader!  To workaround that, by setting the leader's annotation scale after style ID and before 
    style parameters update calls, we can control setting leader params in two ways:
        a) for an annotative leader, we will not call setDimscale but will explicitly set dimasz and dimtxt with
            the model annotation scale (adding annotation scale does not appear to automatically update them).
        b) for a non-annotative leader, we will call setDimscale which should succeed in this case.
    --------------------------------------------------------------------------------------------------------*/
    this->SetLeaderAnnotationScale (pLeader, pDimstyle);

    // set converted dimstyle data to dimension
    m_fromDgnContext->SetDimstyleData (pLeader, pDimstyle);

    delete pDimstyle;

    // empty old vertex list
    int oldVertices = pLeader->numVertices ();
    for (int iVert = 0; iVert < oldVertices; iVert++)
        pLeader->removeLastVertex ();

    RotMatrix   defMatrix;

    // add new vertices: must set the points here as only fromElement carries the local transformation.
    DPoint3dArray   pointArray;
    for (int iPoint = 0; iPoint < dgnDim->nPoints; iPoint++)
        {
        DPoint3d    point = dgnDim->GetPoint (iPoint);
        m_fromDgnContext->GetTransformFromDGN().Multiply (point);

        if (0 == iPoint)
            {
            /*-----------------------------------------------------------------------------------
            set the leader plane from note definition matrix. this must be done before setting
            the leader vertices as DD recompute them using the plane in setVertex
            -----------------------------------------------------------------------------------*/
            SetLeaderPlane (pLeader, defMatrix, point);
            }

        // if user wants to remove z-coordinate in DWG file, do so now:
        if (m_fromDgnContext->GetSettings().IsZeroZCoordinateEnforced())
            point.z = 0.0;

        pLeader->appendVertex (RealDwgUtil::GePoint3dFromDPoint3d(point));
        pointArray.push_back (point);
        }

    // set hook/elbow line, attachment point, etc - local variables for next process:
    SetHookLineInfo (pointArray, defMatrix);

    /*-----------------------------------------------------------------------------------
    For single line note, create leader now as its text is created by this process.
    For multiline note, we have to postpone the process to ensure associated text to be
    processed before hand.  An assumption is made to post process multiline note that
    the associated text is always in the same parent cell as the dimension is; otherwise
    the local transformation can be different.
    -----------------------------------------------------------------------------------*/
    if (SUCCESS != mdlNote_getRootNoteCellId(NULL, *m_dimensionElement))
        status = ConvertSinglelineNoteToDwg (pLeader, pointArray, defMatrix);
    else
        status = ConvertMultilineNoteToDwg (pLeader, pointArray, defMatrix);

    // only need to update an associative leader - an orphaned leader shall be updated by dimension's ToObject process
    if (NULL != m_noteElement)
        m_fromDgnContext->UpdateEntityPropertiesFromElement (pLeader, *m_dimensionElement);

    pLeader->recordGraphicsModified (false);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            SetLeaderAnnotationScale (AcDbLeader* acLeader, AcDbDimStyleTableRecord* acDimstyle)
    {
    double      annoScale = 1.0;
    bool        scaledByModel = mdlDim_overallGetModelAnnotationScale (&annoScale, *m_dimensionElement);

    if (scaledByModel && m_fromDgnContext->CanSaveAnnotationScale())
        {
        m_fromDgnContext->AddAnnotationScaleToObject (acLeader, annoScale, m_dimensionElement->GetElementId());

        /*-----------------------------------------------------------------------------------------------
        Adding annotation scale above may have changed arrowhead and text sizes, in which case we need to 
        update our calculated style by explicitly scaling these params.
        acDimstyle->setDimasz (annoScale * acDimstyle->dimasz());
        acDimstyle->setDimtxt (annoScale * acDimstyle->dimtxt());
        -----------------------------------------------------------------------------------------------*/

        return  true;
        }

    RealDwgUtil::SetObjectAnnotative (acLeader, false);
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbMText::AttachmentPoint  JustifySingleLineTextOrigin
(
DPoint3dR                   origin,
RotMatrixCR                 textMatrix,
bool                        isUnderline,
double                      boxHeight,
double                      boxWidth
)
    {
    // work on text plane:
    textMatrix.MultiplyTranspose (origin);

    // get hook definition point
    DPoint3d    hookPoint = m_attachmentPoint;
    textMatrix.MultiplyTranspose (hookPoint);

    // get center-left or center-right point, assuming all texts have same height
    AcDbMText::AttachmentPoint  attachment = AcDbMText::kMiddleLeft;
    if (!isUnderline && origin.x - hookPoint.x < 0.0)
        {
        attachment = AcDbMText::kMiddleRight;
        origin.x += boxWidth;
        }
    // check for vertical adjustment for underline case
    if (isUnderline)
        {
        if (AcDbMText::kMiddleLeft == attachment)
            attachment = AcDbMText::kBottomLeft;
        else if (AcDbMText::kMiddleRight == attachment)
            attachment = AcDbMText::kBottomRight;

        origin.y -= 0.5 * boxHeight;
        }

    // restore origin to the WCS
    textMatrix.Multiply (origin);

    return  attachment;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                CreateAnnotationText
(
double&                     boxHeight,
double&                     boxWidth,
AcDbLeader*                 pLeader,
RotMatrixCR                 defMatrix
)
    {
    AcDbObjectId        mtextObjId;
    DimensionElmCP      dgnDim = (DimensionElmCP) m_dimensionElement->GetElementCP();
    if (NULL == dgnDim)
        return  mtextObjId;

    bool                isUnderline = 0==dgnDim->flag.horizontal && 0==dgnDim->flag.embed;

    // find text style
    AcDbHandle          dbHandle;
    AcDbObjectId        textStyleId;

    if (!(dbHandle = m_fromDgnContext->GetFileHolder().GetTextStyleIndex()->GetDBHandle (dgnDim->textStyleId)).isNull())
        textStyleId = m_fromDgnContext->ExistingObjectIdFromDBHandle (dbHandle);
    else
        textStyleId = m_fromDgnContext->AddTextStyleFromFontNumber (dgnDim->text.font, 0, false, false, false, 0.0, m_fromDgnContext->GetModel());

    // create a new mtext
    AcDbMText*      pMText = new AcDbMText();
    RotMatrix       textMatrix;
    // need the size of the text frame
    DPoint2d        textSize;

    // get the contents of the mtext from the dimension element
    if (SUCCESS != m_fromDgnContext->CreateMTextFromDimElement (pMText, &textMatrix, &textSize, NULL, *m_dimensionElement, L"\\P"))
        {
        if (pMText->isNewObject())
            delete pMText;
        else
            pMText->erase ();
        return  mtextObjId;
        }

    // add the new mtext to database to get an object id
    mtextObjId = pMText->objectId ();
    if (!mtextObjId.isValid())
        {
        AcDbObjectId    ownerId = pLeader->ownerId().isValid() ? pLeader->ownerId() : m_fromDgnContext->GetCurrentBlockId();
        mtextObjId = m_fromDgnContext->AddEntityToBlockId (ownerId, pMText, 0);
        }

    // set text frame size
    boxHeight = textSize.y * m_scaleFromDgn;
    boxWidth  = textSize.x * m_scaleFromDgn;

    DPoint3d    origin;
    DVec3d      xDirection, zDirection;
    RealDwgUtil::DPoint3dFromGePoint3d (origin, pMText->location());

    textMatrix.GetColumn (xDirection, 0);
    textMatrix.GetColumn (zDirection, 2);

    // find letf/right justification
    AcDbMText::AttachmentPoint attachment = JustifySingleLineTextOrigin (origin, textMatrix, isUnderline, boxHeight, boxWidth);

    // if user wants to remove z-coordinate in DWG file, do so now:
    if (m_fromDgnContext->GetSettings().IsZeroZCoordinateEnforced())
        origin.z = 0.0;

    pMText->setLocation (RealDwgUtil::GePoint3dFromDPoint3d(origin));
    pMText->setDirection (RealDwgUtil::GeVector3dFromDPoint3d(xDirection));
    pMText->setAttachment (attachment);
    pMText->setTextStyle (textStyleId);
    pMText->setFlowDirection (AcDbMText::kLtoR);
    // set 0 box width to avoid ACAD's mtext wrapping
    pMText->setWidth (0.0);

    if (dgnDim->text.b.useColor)
        pMText->setColor (m_fromDgnContext->GetColorFromDgn (dgnDim->text.color, pLeader->colorIndex()));

    // set leader as the reactor for the mtext
    pMText->addPersistentReactor (pLeader->objectId());

    // set leader's annotation offset:
    if (!isUnderline)
        {
        DPoint3d    annoOffset = origin;
        annoOffset.Subtract (m_attachmentPoint);
        pLeader->setAnnotationOffset (RealDwgUtil::GeVector3dFromDPoint3d(annoOffset));
        }

    pMText->close ();

    return  mtextObjId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           08/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               AddTerminatorGeometry
(
AcDbLeader*                 pLeader,
DPoint3dArray&              pointArray
)
    {
    DimensionElmCP          dgnDim = (DimensionElmCP) m_dimensionElement->GetElementCP();
    if (NULL == dgnDim)
        return  MstnElementUnacceptable;

    // an annotative leader may have a dimscale=0
    double      dimScale = pLeader->dimscale ();
    if (dimScale < TOLERANCE_ZeroScale && RealDwgUtil::IsConstObjectAnnotative(pLeader) && pLeader->ownerId() == acdbSymUtil()->blockModelSpaceId(pLeader->database()))
        dimScale = m_fromDgnContext->GetModel()->GetModelInfo().GetAnnotationScaleFactor ();

    if (dimScale < TOLERANCE_ZeroSize)
        dimScale = 1.0;

    // replace attachment point with the hook point (elbow point):
    if (pLeader->hasHookLine())
        ToDgnExtLeader::ExtractHookPoint (pointArray[1], dimScale, pLeader);

    // calculate rotation angle to align the arrowhead on dimension plane
    DPoint3d    arrowDir;
    arrowDir.DifferenceOf (pointArray[0], pointArray[1]);
    double  angle = atan2 (arrowDir.y, arrowDir.x);
    // normalize angle
    while (angle > msGeomConst_2pi)
        angle -= msGeomConst_2pi;

    AcDbEntity* pNewEntity;

    if (!pLeader->dimldrblk().isNull())
        {
        // leader terminator block exists, create an insert entity.
        AcDbBlockReference* pInsert = new AcDbBlockReference();

        pInsert->setBlockTableRecord (pLeader->dimldrblk());
        pInsert->setPosition (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[0]));
        pInsert->setRotation (angle);

        AcGeScale3d scale;
        scale.sx = scale.sy = scale.sz = pLeader->dimasz() * dimScale;
        pInsert->setScaleFactors (scale);

        pNewEntity = pInsert;
        }
    else
        {
        // no leader terminator block, create a solid triangle.
        RotMatrix   defMatrix;
        defMatrix.InitFromAxisAndRotationAngle (2, angle);

        double      width  = dgnDim->geom.termWidth  * m_scaleFromDgn;
        double      height = dgnDim->geom.termHeight * m_scaleFromDgn;
        DPoint3d    triangle[4];
        RealDwgUtil::CalculateArrowheadPoints (triangle, &pointArray.front(), width, height, defMatrix);

        // duplicate last point for solid entity
        triangle[3] = triangle[2];

        AcDbSolid* pSolid = new AcDbSolid ();
        for (int iPoint = 0; iPoint < 4; iPoint++)
            {
            // if user wants to remove z-coordinate in DWG file, do so now:
            if (m_fromDgnContext->GetSettings().IsZeroZCoordinateEnforced())
                triangle[iPoint].z = 0.0;

            pSolid->setPointAt (iPoint, RealDwgUtil::GePoint3dFromDPoint3d(triangle[iPoint]));
            }

        pNewEntity = pSolid;
        }

    // set terminator properties
    pNewEntity->setPropertiesFrom (pLeader);
    m_fromDgnContext->UpdateEntitySymbologyAndLevelFromElement (pNewEntity, m_dimensionElement->GetElementDescrCP());

    DimTermSymbBlock    *pTermSymb = (DimTermSymbBlock*) mdlDim_getOptionBlock (*m_dimensionElement, ADBLK_TERMSYMB, NULL);
    if (NULL != pTermSymb)
        {
        // apply symbology overrides
        if (pTermSymb->termSymb.useColor)
            pNewEntity->setColorIndex (m_fromDgnContext->GetColorIndexFromDgn(pTermSymb->termSymb.color, pLeader->colorIndex()));
        if (pTermSymb->termSymb.useWeight)
            pNewEntity->setLineWeight (m_fromDgnContext->GetLineWeightFromDgn(pTermSymb->termSymb.weight, pLeader->lineWeight()));
        if (pTermSymb->termSymb.useStyle)
            pNewEntity->setLinetype (m_fromDgnContext->GetLineTypeFromDgn(pTermSymb->termSymb.style));
        }

    // add the new entity to database
    m_fromDgnContext->AddEntityToBlockId (pLeader->ownerId(), pNewEntity, 0);

    pNewEntity->close ();

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/03
+---------------+---------------+---------------+---------------+---------------+------*/
DPoint3d                    GetAttachmentPoint
(
DPoint3dCR                  fromPoint,
DPoint3dCR                  toPoint
)
    {
    DPoint3d                attachmentPoint = DPoint3d::From(0, 0, 0);
    if (DIMSTYLE_VALUE_MLNote_VerAttachment_Underline == RealDwgUtil::GetVerAttachmentFromNote(*m_dimensionElement))
        {
        DimensionElmCP      dgnDim = (DimensionElmCP) m_dimensionElement->GetElementCP ();
        if (NULL == dgnDim)
            return  attachmentPoint;

        // for underline case we have to calculate attachment point
        DVec3d              direction = DVec3d::FromStartEndNormalize (fromPoint, toPoint);

        // the hook length in AutoCAD is about character width:
        attachmentPoint.SumOf (fromPoint, direction, dgnDim->geom.termWidth * m_scaleFromDgn);
        }
    else
        {
        // for other joint location cases, the last point is the text attachment point
        attachmentPoint = toPoint;
        }

    return  attachmentPoint;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/04
+---------------+---------------+---------------+---------------+---------------+------*/
void                        GetAttachmentOffset
(
DPoint3dR                   offset,
DPoint3dCR                  textOrigin,
DPoint3dArray&              pointArray,
RotMatrixCR                 defMatrix,
DimensionElmCP              dgnDim
)
    {
    if (dgnDim->nPoints > 0)
        {
        DPoint3d    lastPoint = pointArray.at(dgnDim->nPoints-1);
        DPoint3d    origin = textOrigin;

        m_fromDgnContext->GetTransformFromDGN().Multiply (origin);

        defMatrix.MultiplyTranspose (lastPoint);
        defMatrix.MultiplyTranspose (origin);

        offset.y = lastPoint.y - origin.y;

        defMatrix.Multiply (offset);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           06/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetHookLineInfo
(
DPoint3dArray&              pointArray,
RotMatrixCR                 defMatrix
)
    {
    DimensionElmCP          dgnDim = (DimensionElmCP) m_dimensionElement->GetElementCP ();
    if (NULL == dgnDim || dgnDim->nPoints < 2)
        return;

    /* check direction of hook line */
    DPoint3d        last2Points[2], hookPoints[2];

    last2Points[0] = pointArray.at (dgnDim->nPoints-1);
    last2Points[1] = pointArray.at (dgnDim->nPoints-2);

    defMatrix.MultiplyTranspose (last2Points[0]);
    defMatrix.MultiplyTranspose (last2Points[1]);

    if (SUCCESS == mdlNote_getHookLineFromNoteCell(&hookPoints[0], *m_dimensionElement))
        {
        // case of note cell with a hook line in the cell
        m_fromDgnContext->GetTransformFromDGN().Multiply (hookPoints[0]);
        m_fromDgnContext->GetTransformFromDGN().Multiply (hookPoints[1]);

        DPoint3d    hookSaved[2];
        hookSaved[0] = hookPoints[0];
        hookSaved[1] = hookPoints[1];

        /* find the direction from the hook line stored in note cell: */
        defMatrix.MultiplyTranspose (hookPoints[0]);
        defMatrix.MultiplyTranspose (hookPoints[1]);

        if (last2Points[0].IsEqual(hookPoints[0], TOLERANCE_PointEqual) || last2Points[1].IsEqual(hookPoints[0], TOLERANCE_PointEqual))
            {
            // hookPoint[0] is the attachment point to text, if not a underline
            m_attachmentPoint = GetAttachmentPoint (hookSaved[0], hookSaved[1]);

            m_isHooklineOnXDir = hookPoints[0].x > hookPoints[1].x;
            }
        else
            {
            // hookPoint[1] is the attachment point to text, if not a underline
            m_attachmentPoint = GetAttachmentPoint (hookSaved[1], hookSaved[0]);

            m_isHooklineOnXDir = hookPoints[1].x > hookPoints[0].x;
            }

        m_hasHookline = true;
        }
    else if (SUCCESS == mdlNote_getRootNoteCellId(NULL, *m_dimensionElement))
        {
        // case of note cell without a hookline
        m_isHooklineOnXDir = false;
        m_hasHookline = false;

        m_attachmentPoint = pointArray.at(dgnDim->nPoints-1);
        }
    else if (dgnDim->flag.horizontal)
        {
        // case of single line dimension with embedded text and a hook line in dimension.
        // apply the same logic in the stroke code (dimrad.c) to generate the hookline:
        DVec3d      hooklineDir = DVec3d::FromStartEnd (last2Points[1], last2Points[0]);

        // the hook vector is on leader's plane as the last2Points are already transformed to the plane.

        hooklineDir.y = hooklineDir.z = 0.0;
        if (hooklineDir.x > 0.0)
            {
            hooklineDir.x = 1.0;
            m_isHooklineOnXDir = false;
            }
        else
            {
            hooklineDir.x =-1.0;
            m_isHooklineOnXDir = true;
            }

        // put the vector back to the world:
        defMatrix.Multiply (hooklineDir);

        // project the last point to the hook vector, and add a fixed 2 * char width:
        double  length = 2 * dgnDim->text.width * m_scaleFromDgn;
        m_attachmentPoint.SumOf (pointArray.at(dgnDim->nPoints-1), hooklineDir, length);

        m_hasHookline = true;
        }
    else
        {
        // case of single line dimension with embedded text which is along the dimension line.
        m_isHooklineOnXDir = false;

        m_attachmentPoint = pointArray.at (dgnDim->nPoints-1);

        m_hasHookline = false;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   SetMissingFieldsToLeader
(
AcDbLeader*                     pLeader,
const AcDbLeader::AnnoType      annoType,
double                          boxWidth,
double                          boxHeight
)
    {
    RecordingFiler              filer (60);
    filer.RecordData (pLeader);

#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("AcDbLeader data fields from DWGOUT filing:");
    /*-----------------------------------------------------------------------------------
    Workaround RealDWG to set following missing data fields:
    1) text box width                               DXF group code 41 - missing since R2010
    2) text box height                              DXF group code 40 - missing since R2010
    3) leader x-direction                           DXF group code 211
    4) hookline is on or opposite to x-direction    DXF group code 74
    5) leader has a hookline                        DXF group code 75 - can't find one in DWG!
    6) annotation type - mtext, block, etc.         DXF group code 73
    
    AcDbLeader data fields from DWGOUT filing (R2013):
      0: kDwgSoftPointerId, ff802cf8 fc81c2b2 1F, AcDbBlockTableRecord
      1: kDwgUInt32,    0 (0x0)
      2: kDwgHardOwnershipId, NULL
      3: kDwgHardPointerId, ff802c80 fc81c2ca 10, AcDbLayerTableRecord
      4: kDwgHardPointerId, ff802ca8 fc81c2e2 15, AcDbLinetypeTableRecord
      5: kDwgHardPointerId, ff802ef0 fc81c0ba 96, AcDbMaterial
      6: kDwgUInt8,    0 (0x0)
      7: kDwgBool false
      8: kDwgBool false
      9: kDwgBool false
     10: kDwgBool false
     11: kDwgBool false
     12: kDwgUInt16,  256 (0x100)
     13: kDwgReal 1.000000
     14: kDwgUInt16,    0 (0x0)
     15: kDwgUInt8,   29 (0x1d)
     16: kDwgBool true
     17: kDwgHardPointerId, NULL
     18: kDwgInt32,    1 (0x1)
     19: kDwgString(ACHAR) ACDB_ANNOTATIONSCALES
     20: kDwgHardPointerId, NULL
     21: kDwgBool false
     22: kDwgBool true
     23: kDwgInt32,    0 (0x0)
     24: kDwgInt16,    4 (0x4)
     25: kDwgBool true
     26: kDwgBool true
     27: kDwgHardPointerId, NULL
     28: kDwgUInt32,    2 (0x2)                             Num. of vertices
     29: AcGePoint3d, 8.948394, 5.281669, 0.000000
     30: AcGePoint3d, 10.652986, 7.017012, 0.000000
     31: AcGeVector3d, 1.000000, 0.000000, 0.000000         X-direction 1 (211)
     32: kDwgBool false                                     Is on x-direction 1 (74)
     33: AcGeVector3d, 0.000000, 0.000000, 0.000000
     34: AcGeVector3d, 0.000000, -0.000000, 0.000000
     35: kDwgBool true
     36: kDwgSoftPointerId, NULL
     37: kDwgUInt32,    0 (0x0)
     38: kDwgHardOwnershipId, NULL
     39: kDwgString(ACHAR)
     40: kDwgInt16,    0 (0x0)
     41: kDwgBool false
     42: kDwgHardPointerId, NULL
     43: kDwgString(ACHAR)
     44: kDwgString(ACHAR)
     45: kDwgReal 1.000000
     46: kDwgReal 0.180000
     47: kDwgReal 0.062500
     48: kDwgReal 0.380000
     49: kDwgReal 0.180000
     50: kDwgReal 0.000000
     51: kDwgReal 0.000000
     52: kDwgReal 0.000000
     53: kDwgReal 0.000000
     54: kDwgReal 1.000000
     55: kDwgReal 0.785398
     56: kDwgInt16,    0 (0x0)
     57: kDwgUInt16,    0 (0x0)
     58: kDwgUInt32, 3238002688 (0xc1000000)
     59: kDwgUInt8,    0 (0x0)
     60: kDwgBool false
     61: kDwgBool false
     62: kDwgBool true
     63: kDwgBool true
     64: kDwgBool false
     65: kDwgBool false
     66: kDwgInt16,    0 (0x0)
     67: kDwgInt16,    0 (0x0)
     68: kDwgInt16,    0 (0x0)
     69: kDwgInt16,    0 (0x0)
     70: kDwgReal 0.180000
     71: kDwgReal 0.090000
     72: kDwgReal 0.000000
     73: kDwgReal 25.400000
     74: kDwgReal 1.000000
     75: kDwgReal 0.000000
     76: kDwgReal 1.000000
     77: kDwgReal 0.090000
     78: kDwgReal 0.000000
     79: kDwgBool false
     80: kDwgInt16,    2 (0x2)
     81: kDwgBool false
     82: kDwgBool false
     83: kDwgBool false
     84: kDwgBool false
     85: kDwgUInt16,    0 (0x0)
     86: kDwgUInt32, 3238002688 (0xc1000000)
     87: kDwgUInt8,    0 (0x0)
     88: kDwgUInt16,    0 (0x0)
     89: kDwgUInt32, 3238002688 (0xc1000000)
     90: kDwgUInt8,    0 (0x0)
     91: kDwgUInt16,    0 (0x0)
     92: kDwgUInt32, 3238002688 (0xc1000000)
     93: kDwgUInt8,    0 (0x0)
     94: kDwgInt16,    0 (0x0)
     95: kDwgInt16,    4 (0x4)
     96: kDwgInt16,    4 (0x4)
     97: kDwgInt16,    2 (0x2)
     98: kDwgInt16,    2 (0x2)
     99: kDwgInt16,    0 (0x0)
    100: kDwgInt16,    0 (0x0)
    101: kDwgInt16,    2 (0x2)
    102: kDwgInt16,   46 (0x2e)
    103: kDwgInt16,    0 (0x0)
    104: kDwgInt16,    0 (0x0)
    105: kDwgBool false
    106: kDwgBool false
    107: kDwgInt16,    1 (0x1)
    108: kDwgInt16,    0 (0x0)
    109: kDwgInt16,    0 (0x0)
    110: kDwgInt16,    0 (0x0)
    111: kDwgBool false
    112: kDwgInt16,    3 (0x3)
    113: kDwgBool false
    114: kDwgBool false
    115: kDwgReal 100.000000
    116: kDwgString(ACHAR)
    117: kDwgReal 100.000000
    118: kDwgString(ACHAR)
    119: kDwgHardPointerId, ff802c88 fc81c2c2 11, AcDbTextStyleTableRecord
    120: kDwgHardPointerId, NULL
    121: kDwgHardPointerId, NULL
    122: kDwgHardPointerId, NULL
    123: kDwgHardPointerId, NULL
    124: kDwgHardPointerId, NULL
    125: kDwgHardPointerId, NULL
    126: kDwgHardPointerId, NULL
    127: kDwgInt16,   -2 (0xfffffffe)
    128: kDwgInt16,   -2 (0xfffffffe)
    129: kDwgBool false
    130: kDwgHardPointerId, ff807450 fc819a1a 172, AcDbMText                Annotation ID
    131: kDwgBool false
    132: kDwgUInt16,    0 (0x0)                                             Annotation type (73)
    133: kDwgHardPointerId, ff802d38 fc81c372 27, AcDbDimStyleTableRecord
    134: kDwgUInt16,    0 (0x0)                                             Straight/Spline
    135: kDwgUInt32,    2 (0x2)                                             Num. of vertices
    136: AcGePoint3d, 8.948394, 5.281669, 0.000000
    137: AcGePoint3d, 10.652986, 7.017012, 0.000000
    138: AcGePoint3d, 8.948394, 5.281669, 0.000000
    139: AcGeVector3d, 0.000000, 0.000000, 1.000000
    140: AcGeVector3d, 1.000000, 0.000000, 0.000000                         X-direction 2 (211)
    141: AcGeVector3d, 0.000000, 0.000000, 0.000000
    142: AcGeVector3d, 0.000000, -0.000000, 0.000000
    143: kDwgBool false                                                     Is on x-direction 2 (74)
    144: kDwgBool true
    145: kDwgUInt16,    0 (0x0)
    146: kDwgBool false
    147: kDwgBool false
    -----------------------------------------------------------------------------------*/
#endif
    FilerDataList&      dataList = filer.GetDataList ();

    // Set x-direction:
#if RealDwgVersion == 2009
    UInt32              index = 25 + pLeader->numVertices() + 1;
#else
    UInt32              index = 28 + pLeader->numVertices() + 1;
#endif
    Vector3dFilerData*  vectorData = dynamic_cast <Vector3dFilerData *> (dataList[index++]);
    if (NULL == vectorData)
        return BadDataSequence;

    vectorData->SetValue (m_xDirection);

    // Set the 1st flag indicating on or against leader's x-direction:
    BoolFilerData*   boolData = dynamic_cast <BoolFilerData *> (dataList[index]);
    if (NULL == boolData)
        return BadDataSequence;

    boolData->SetValue (m_isHooklineOnXDir);

    // Set annotation creation type:
#if RealDwgVersion <= 2012
    index += 5;
#else
    index += 100;
#endif
    UInt16FilerData*    int16Data = dynamic_cast <UInt16FilerData *> (dataList[index]);
    if (NULL == int16Data)
        return BadDataSequence;

    int16Data->SetValue ((UInt16)annoType);

    // Set x-direction once again at the second place:
    index += 3 + pLeader->numVertices() + 3;
    vectorData = dynamic_cast <Vector3dFilerData *> (dataList[index]);
    if (NULL == vectorData)
        return BadDataSequence;

    vectorData->SetValue (m_xDirection);

    index += 3;
#if RealDwgVersion == 2009
    // Set box height:
    DoubleFilerData*    doubleData = dynamic_cast <DoubleFilerData *> (dataList[index++]);
    if (NULL == doubleData)
        return BadDataSequence;

    doubleData->SetValue (boxHeight);

    // Set box width:
    doubleData = dynamic_cast <DoubleFilerData *> (dataList[index++]);
    if (NULL == doubleData)
        return BadDataSequence;

    doubleData->SetValue (boxWidth);
#endif

    // Set the 1st flag indicating on or against leader's x-direction:
    boolData = dynamic_cast <BoolFilerData *> (dataList[index]);
    if (NULL == boolData)
        return BadDataSequence;

    boolData->SetValue (m_isHooklineOnXDir);

    Acad::ErrorStatus   es = filer.PlaybackData (pLeader);
#if defined (REALDWG_FILER_DEBUG)
    if (Acad::eOk == es)
        {
        RecordingFiler check (40);
        check.RecordData (pLeader);
        check.DumpList ("AcDbLeader after DWGIN filing:");
        }
#endif

    return (Acad::eOk == es) ? RealDwgSuccess : BadDataSequence;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetTextBox
(
double*                     pPlnoteGap,
AcDbLeader*                 pLeader,
double                      textWidth
)
    {
    // get dimgap set before
    double  dimgap = pLeader->dimgap ();
    // get DGN box frame gap set by the code in plnote.mc
    double  plnoteGap = -0.5 * textWidth * m_scaleFromDgn;

    // For a new note, which has a none-zero margin, dimgap has already been set in the style process.
    // For an old note, apply the hard coded 1/2 char width logic now.
    if (fabs(dimgap) <= TOLERANCE_ZeroSize)
        dimgap = plnoteGap;

    // check if dimgap is already set for text box and no xdata override
    if (dimgap != plnoteGap)
        dimgap = -fabs(dimgap);
    else if (dimgap == 0.0)
        dimgap = -1.e-6;

    pLeader->setDimgap (dimgap);

    if (NULL != pPlnoteGap)
        *pPlnoteGap = plnoteGap;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetTextBoxAndSize
(
double&                     boxHeight,
double&                     boxWidth,
DPoint3dCR                  textSize,
bool&                       isAnnoRequired,
bool                        hasUnderline,
AcDbLeader*                 pLeader
)
    {
    double  plnoteGap = 0;

    SetTextBox (&plnoteGap, pLeader, textSize.x);

    // remove the frame addition to the box size if not already removed for underline case.
    if (!isAnnoRequired)
        {
        plnoteGap *= 2;
        boxHeight += plnoteGap;
        boxWidth  += plnoteGap;
        }
    else if (hasUnderline)
        {
        // a case of text frame + underline
        boxHeight += 2 * plnoteGap;
        }

    // text frame requires mtext attachment
    isAnnoRequired = true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
bool                    GetAnnotationInfo
(
double&                 boxHeight,
double&                 boxWidth,
DPoint3dR               offset,
AcDbLeader*             pLeader,
DPoint3dArray&          pointArray
)
    {
    bool                isAnnoRequired = false;

    /*-----------------------------------------------------------------------------------
    Since dimension note does not have a flag telling us whether a text frame is needed,
    setting text frame flag has to depend on the presence of the shape element in the
    note cell.
    -----------------------------------------------------------------------------------*/
    if (NULL == m_noteElement)
        return  false;

    NoteCellHeaderHandler*  noteHandler = dynamic_cast <NoteCellHeaderHandler*> (&m_noteElement->GetHandler());
    if (NULL == noteHandler)
        return  isAnnoRequired;

    DimensionElmCP          dgnDim = (DimensionElmCP) m_dimensionElement->GetElementCP ();
    if (NULL == dgnDim)
        return  isAnnoRequired;

    UInt16  verAttachment = RealDwgUtil::GetVerAttachmentFromNote (*m_dimensionElement);

    // check underline flag
    bool    hasUnderline = DIMSTYLE_VALUE_MLNote_VerAttachment_Underline == verAttachment;

    // set dimtad for underline, only if necessary
    int     dimtad = hasUnderline ? 1 : 0;
    if (dimtad != pLeader->dimtad())
        pLeader->setDimtad (dimtad);

    RotMatrix           textMatrix;
    textMatrix.initIdentity ();

    DPoint3d            textSize;
    textSize.Zero ();

    for (ChildElemIter childElm(*m_noteElement, ExposeChildrenReason::Count); childElm.IsValid(); childElm=childElm.ToNext())
        {
        ITextQueryCP    textQuery = childElm.GetITextQuery ();
        if (NULL != textQuery && textQuery->IsTextElement(childElm))
            {
            TextBlock       textBlock(childElm);
            textMatrix = textBlock.GetOrientation ();

            DRange3d        textRange = textBlock.GetExactRange ();
            textSize.DifferenceOf (textRange.high, textRange.low);

            // default leader's annotation size to the node size:
            boxHeight = textSize.x * m_scaleFromDgn;
            boxWidth  = textSize.y * m_scaleFromDgn;

            // add 1/2 character width on each side of the text body to match our underline size
            double  width = textBlock.End().GetCurrentRunCP()->GetProperties().GetFontSize().x;
            boxHeight += width;
            boxWidth  += width;

            if (!hasUnderline)
                GetAttachmentOffset (offset, textBlock.GetUserOrigin(), pointArray, textMatrix, dgnDim);
            }
        else if (hasUnderline && !isAnnoRequired && LINE_ELM == childElm.GetElementType())
            {
            // check for underline
            DPoint3d        points[5];
            CurveVectorPtr  pathCurve = ICurvePathQuery::ElementToCurveVector (childElm);
            if (pathCurve.IsValid() & pathCurve->GetStartEnd(points[0], points[1]))
                {
                double      length = points[0].Distance(points[1]) * m_scaleFromDgn;

                DVec3d      textXAxis;
                textMatrix.GetColumn (textXAxis, 0);

                DPoint3d    lineDir;
                lineDir.NormalizedDifference (points[1], points[0]);

                // exclude vertical line
                if (fabs(1.0 - fabs(lineDir.DotProduct(textXAxis))) < TOLERANCE_VectorEqual)
                    {
                    /*-------------------------------------------------------------------
                    ACAD's underline is made up by an elbow length(=terminator size),
                    a dimgap(one side only) and a text width. However, if the angle of
                    the last seg with underline is below 15 degrees, there is no elbow
                    to be added.
                    -------------------------------------------------------------------*/
                    DPoint3d    leaderDir;
                    leaderDir.NormalizedDifference (pointArray.at(dgnDim->nPoints-2), pointArray.at(dgnDim->nPoints-1));

                    double      dimgap = pLeader->dimgap ();
                    if (fabs(leaderDir.DotProduct(lineDir)) < 0.9659258)
                        {
                        boxWidth = length - dimgap - dgnDim->geom.termWidth * m_scaleFromDgn;
                        m_hasHookline = true;
                        }
                    else
                        {
                        boxWidth = length - dimgap;
                        m_hasHookline = false;
                        }

                    // underline requires mtext attachment
                    isAnnoRequired = true;
                    }
                }
            }
        else if (SHAPE_ELM == childElm.GetElementType())
            {
            SetTextBoxAndSize (boxHeight, boxWidth, textSize, isAnnoRequired, hasUnderline, pLeader);
            }
        }

    /*-----------------------------------------------------------------------------------
    New types of text frames (all but box) can be safely determined by multi-note frame
    flag stored on dimension element.  They all should retain as framed, although they
    would lose original shape of the frames.
    -----------------------------------------------------------------------------------*/
    DimStyleProp_MLNote_FrameType   frameType;
    if (SUCCESS == mdlDim_getNoteFrameType(&frameType, *m_dimensionElement) && frameType > DIMSTYLE_VALUE_MLNote_FrameType_Box)
        SetTextBoxAndSize (boxHeight, boxWidth, textSize, isAnnoRequired, hasUnderline, pLeader);

    return  isAnnoRequired;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               SetLeaderParams
(
AcDbLeader*                 pLeader,
const AcDbObjectId          annotationId,
const AcDbLeader::AnnoType  annoType,
DPoint3dArray&              pointArray,
bool                        bResetLastVertex,
const double                boxHeight,
const double                boxWidth
)
    {
    DimensionElmCP          dgnDim = (DimensionElm*) m_dimensionElement->GetElementCP();
    if (NULL == dgnDim)
        return  MstnElementUnacceptable;

    // set leader type, straight or splined
    UInt16                  isCurve = 0;
    if (mdlDim_getNoteLeaderType(&isCurve, *m_dimensionElement) && isCurve)
        pLeader->setToSplineLeader ();

    /*-----------------------------------------------------------------------------------
    To prevent DD from calling evaluateLeader method which changes all leader data we
    have built here, we have to re-open the mtext object and keep it in this same scope
    such that the smart pointer does not get released, which results in calling the
    evaluateLeader method in DD's method attachAnnotation.
    -----------------------------------------------------------------------------------*/
    AcDbMText*    pMText = NULL;
    if (!annotationId.isNull())
        {
        /*-------------------------------------------------------------------------------
        A workaround a case where note cell failed conversion to mtext due to dependency
        check failure in which case the note cell is kept as a block, not an mtext.
        -------------------------------------------------------------------------------*/
        AcDbEntityPointer   pObject (annotationId, AcDb::kForRead, false);
        if (Acad::eOk == pObject.openStatus() && NULL != (pMText = AcDbMText::cast (pObject)))
            {
            if (!pMText->normal().isParallelTo(pLeader->normal()))
                {
                DIAGNOSTIC_PRINTF ("Ignoring Leader due to MText/Leader Normal MisMatch on Leader with AcDbHandle %I64d\n", RealDwgUtil::CastDBHandle (pLeader->objectId().handle()));
                return  RealDwgIgnoreElement;
                }
            // set mtext object's modified flag to false to prevent the eveluateMethod being called from now on
            pMText->recordGraphicsModified (false);
            }
        }

    Acad::ErrorStatus   errorStatus = pLeader->attachAnnotation (annotationId);
    if (Acad::eOk != errorStatus && !annotationId.isNull())
        DIAGNOSTIC_PRINTF ("Leader %I64d failed to be attached by annotationID %I64d. %ls\n", m_fromDgnContext->ElementIdFromObject(pLeader), m_fromDgnContext->ElementIdFromObjectId(annotationId), acadErrorStatusText(errorStatus));

    SetMissingFieldsToLeader (pLeader, annoType, boxWidth, boxHeight);

    // calculate hookline length - by now dimScale and dimasz should have been updated:
    double      dimScale = pLeader->dimscale ();
    if (dimScale < TOLERANCE_ZeroSize)
        dimScale = 1.0;

    double      hooklineLength = pLeader->dimasz() * dimScale;
    double      leaderLength = 0.0;

    /*-----------------------------------------------------------------------------------
    ACAD's last definition point is not at the hook point, it's at the text attachment
    point. So if there is a hook line, the last definition point is better to be moved
    to the text attachment point.
    There is still a problem: ACAD has a fixed hook line length = arrow head length.
    In case the hook line length is not the same as terminator length, the hook point
    will not be the same anymore. There is not much we can do about this.
    -----------------------------------------------------------------------------------*/
    if (!annotationId.isNull())
        {
        // replace the last definition point with the attachment point:
        pointArray[dgnDim->nPoints-1] = m_attachmentPoint;
        bResetLastVertex = true;

        // calculate leader length - from the first point to the start of hooline/dogleg point:
        DVec3d  from1stToAttachment = DVec3d::FromStartEnd (pointArray[0], m_attachmentPoint);
        DVec3d  fromAttachmentToHook = DVec3d::FromScale (m_xDirection, -hooklineLength);
        DVec3d  from1stToHook = DVec3d::FromSumOf (from1stToAttachment, fromAttachmentToHook);

        leaderLength = from1stToHook.Normalize ();
        }
    else
        {
        // no hookline - leader length is the natural length from first point to the last point:
        leaderLength = pointArray[0].Distance (pointArray[1]);
        }

    // update the last vertex if modified
    if (bResetLastVertex && pLeader->numVertices() >= dgnDim->nPoints)
        pLeader->setVertexAt (dgnDim->nPoints-1, RealDwgUtil::GePoint3dFromDPoint3d(pointArray[dgnDim->nPoints-1]));

    pLeader->recordGraphicsModified (false);

    // DWG terminator width == hookline length:
    double      doubleWidth = 2.0 * hooklineLength;

    // When terminator height > 1/2 length of the first segment, acad does not display termnator.
    // So we create an extra terminator geometry:
    if (leaderLength < doubleWidth)
        AddTerminatorGeometry (pLeader, pointArray);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetLeaderPlane
(
AcDbLeader*                 pLeader,
RotMatrixR                  defMatrix,
DPoint3dCR                  planePoint
)
    {
    DimensionHandler*   dimHandler = dynamic_cast <DimensionHandler*> (&m_dimensionElement->GetHandler());
    if (NULL == dimHandler || SUCCESS != dimHandler->GetRotationMatrix(*m_dimensionElement, defMatrix))
        return;

    DVec3d      zAxis;
    defMatrix.GetColumn (m_xDirection, 0);
    defMatrix.GetColumn (zAxis, 2);

    AcGePlane   leaderPlane;
    leaderPlane.set (RealDwgUtil::GePoint3dFromDPoint3d(planePoint), RealDwgUtil::GeVector3dFromDPoint3d(zAxis));

    pLeader->setPlane (leaderPlane);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertSinglelineNoteToDwg
(
AcDbLeader*                 pLeader,
DPoint3dArray&              pointArray,
RotMatrixR                  defMatrix
)
    {
    DimensionElmCP          dgnDim = (DimensionElmCP) m_dimensionElement->GetElementCP();
    if (NULL == dgnDim)
        return  MstnElementUnacceptable;

    double  boxHeight = 0.0, boxWidth = 0.0;

    AcDbLeader::AnnoType   annoType = AcDbLeader::kNoAnno;

    bool    bResetLastVertex = false;

    // create the single line text
    AcDbObjectId annotationObjId = CreateAnnotationText (boxHeight, boxWidth, pLeader, defMatrix);

    if (annotationObjId.isValid())
        {
        // if there is an elbow (horizontal text), force to set it.
        // Note that this is to trade audit error with correct appearance
        if (dgnDim->flag.horizontal)                   // horizontal
            annoType = AcDbLeader::kMText;
        else if (0 == dgnDim->flag.embed)              // along & above text
            {
            /*-----------------------------------------------------------------------
            In this case, the single line text is along (not horizontal), as well as
            above, dimension line.  To avoid an extra underline to appear in ACAD,
            reset the DGN attachment point to the nearest side of the mtext.  In
            addition, set the x-direction of the leader to align with that line
            segment.  The result will be that ACAD draws a underline to make up the
            replaced portion of the dimension line.
            -----------------------------------------------------------------------*/
            if (1 != pLeader->dimtad())
                pLeader->setDimtad (1);

            DSegment3d  lastSeg;
            lastSeg.Init (pointArray[dgnDim->nPoints-1], pointArray[dgnDim->nPoints-2]);

            double      length = lastSeg.length();
            if (length > 0.0)
                {
                // the new attachment point is away from the last point by text width,
                // which should be cut by the wrapping space added in adjustOrigin:
                length = (boxWidth + 0.5 * dgnDim->text.width*m_scaleFromDgn) / length;
                lastSeg.FractionParameterToPoint (m_attachmentPoint, length);
                // update the def point
                pointArray[dgnDim->nPoints-1] = m_attachmentPoint;
                bResetLastVertex = true;

                // reset x-direction of the leader
                DPoint3d    start, end;
                lastSeg.GetEndPoints (start, end);
                m_xDirection.NormalizedDifference (start, end);

                // enable text annotation flag
                annoType = AcDbLeader::kMText;
                }
            }
        else                                            // along & inline text
            {
            // set the leader direction such that the hook line will point along leader line
            m_xDirection.NormalizedDifference (m_attachmentPoint, pointArray[dgnDim->nPoints-2]);

            // ACAD requires annotation flag on to display text box
            if (dgnDim->flag.boxText || dgnDim->flag.capsuleText)
                annoType = AcDbLeader::kMText;
            }

        if (dgnDim->flag.boxText || dgnDim->flag.capsuleText)
            SetTextBox (NULL, pLeader, dgnDim->geom.termWidth);
        }

    SetLeaderParams (pLeader, annotationObjId, annoType, pointArray, bResetLastVertex, boxHeight, boxWidth);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/04
+---------------+---------------+---------------+---------------+---------------+------*/
void                        CheckInlineText
(
DPoint3dArray&              pointArray,
AcDbObjectId                annotationId,
const AcDbLeader*           pLeader
)
    {
    DPoint3d    hookPoints[2];
    if (SUCCESS != mdlNote_getHookLineFromNoteCell(&hookPoints[0], *m_dimensionElement))
        return;

    /*-----------------------------------------------------------------------------------
    check if the last segment of the note dimension is inline with the hook line
    -----------------------------------------------------------------------------------*/
    // hook vector
    DVec3d      hookLine;
    hookLine.NormalizedDifference (hookPoints[1], hookPoints[0]);

    // check text rotation flag
    UInt16      textRotation = 0;
    if (mdlDim_getNoteTextRotation(&textRotation, *m_dimensionElement) && DIMSTYLE_VALUE_MLNote_TextRotation_Vertical == textRotation)
        {
        // text is vertical to dimension x-direction, reset x-direction
        hookLine.Negate ();

        m_xDirection = hookLine;

        /*---------------------------------------------------------------------------
        The whole thing about vertical or inline text in this method shall be reviewed
        in the future.  We shall simply use either text's x-axis or hookline direction
        as leader's x-direction for all cases.  That can even cover text being rotated
        after placement etc.  But the new algorithm will need text matrix in the method
        SetHooklineInfo to check for isHooklineOnXDir and thus involves code restructure.
        ---------------------------------------------------------------------------*/
        return;
        }

    // last segment vector
    DVec3d      lastSegment;
    lastSegment.NormalizedDifference (pointArray[1], pointArray[0]);

    if (!hookLine.isParallelTo(&lastSegment))
        return;

    // inline case, we may have to reset the x-direction in order to suppress the hookline
    AcGePlane       plane;
    AcDb::Planarity planarity = AcDb::kNonPlanar;

    if (Acad::eOk != pLeader->getPlane(plane, planarity) && AcDb::kPlanar == planarity)
        return;

    /*-----------------------------------------------------------------------
    A workaround a case where note cell failed conversion to mtext due to
    dependency check failure in which case the note cell is kept as a block,
    not an mtext.
    -----------------------------------------------------------------------*/
    AcDbEntityPointer   pObject (annotationId, AcDb::kForRead, false);
    AcDbMText*          pMText;
    if (NULL == (pMText = AcDbMText::cast (pObject)))
        return;

    // get note z-axis
    DVec3d              zAxis, annotationDir;
    RealDwgUtil::DVec3dFromGeVector3d (zAxis, plane.normal());
    RealDwgUtil::DVec3dFromGeVector3d (annotationDir, pMText->direction());

    /*-----------------------------------------------------------------------
    If text's direction is in plane of note but does not run to the same
    direction as that of leader's, reset leader's x-axis with text's:
    -----------------------------------------------------------------------*/
    if (!annotationDir.IsEqual(m_xDirection, TOLERANCE_PointEqual) && annotationDir.IsPerpendicularTo(zAxis))
        m_xDirection = annotationDir;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertMultilineNoteToDwg
(
AcDbLeader*                 pLeader,
DPoint3dArray&              pointArray,
RotMatrixR                  defMatrix
)
    {
    DimensionElmCP  dgnDim = (DimensionElmCP) m_dimensionElement->GetElementCP ();
    if (NULL == dgnDim)
        return  MstnElementUnacceptable;

    // get x-axis of the leader
    defMatrix.GetColumn (m_xDirection, 0);

    double                  boxHeight = 0.0, boxWidth = 0.0;
    AcDbLeader::AnnoType    annoType = AcDbLeader::kNoAnno;
    bool                    bResetLastVertex = false;
    AcDbObjectId            annotationObjId = AcDbObjectId::kNull;
    ElementId               annotationElmId = 0;

    if (NULL != m_noteElement && m_noteElement->IsValid())
        annotationElmId = m_noteElement->GetElementCP()->ehdr.uniqueId;

    if (0 != annotationElmId)
        {
        annotationObjId = m_fromDgnContext->ExistingObjectIdFromElementId (annotationElmId);

        if (!annotationObjId.isNull() && annotationObjId.isErased())
            annotationObjId.setNull();

        // get the text node to calculate for box height and width (I wish these could be saved in mtext object!)
        DPoint3d    annoOffset = DPoint3d::From (0., 0., 0.);

        // if note cell contain a text frame, set related params
        bool        isAnnoRequired = GetAnnotationInfo (boxHeight, boxWidth, annoOffset, pLeader, pointArray);

        if (!annotationObjId.isNull())
            {
            AcDbLeader::AnnoType    attachedType;

            AcDbEntityPointer       pAnnoObject (annotationObjId, AcDb::kForRead, false);
            if (Acad::eOk != pAnnoObject.openStatus())
                {
                // a bad object - do not attempt to set annotation
                annotationObjId.setNull ();
                attachedType = AcDbLeader::kNoAnno;
                }
            else if (pAnnoObject->isKindOf(AcDbMText::desc()))
                {
                attachedType = AcDbLeader::kMText;
                }
            else
                {
                DIAGNOSTIC_PRINTF ("Leader annotation id=%I64d is not of an mtext\n", annotationElmId);

                /*-----------------------------------------------------------------------
                A workaround a case where note cell failed conversion to mtext due to
                dependency check failure in which case the note cell is kept as a block,
                not an mtext.
                -----------------------------------------------------------------------*/
                if (pAnnoObject->isKindOf(AcDbBlockTableRecord::desc()))
                    attachedType = AcDbLeader::kBlockRef;
                else
                    attachedType = AcDbLeader::kNoAnno;

                if (m_hasHookline)
                    m_attachmentPoint = pointArray[dgnDim->nPoints-1];
                }

            /*---------------------------------------------------------------------------
            AutoCAD always adds a hook line for annotation, regardless what hasHookLine
            is set.  Thus setting annoType=0 means to link to mtext but also to have a
            hook line.  For leaders that link to mtexts but do not have hook lines cause
            ACAD's audit error.  But in order to keep appearance correct we have to
            sacrifice with the audit error.
            ---------------------------------------------------------------------------*/
            if (m_hasHookline || m_fromDgnContext->GetSettings().AllowLeaderHooklineToBeAdded())
                annoType = attachedType;

            /*---------------------------------------------------------------------------
            Here is another workaround a seemingly ACAD's bug: when we set annoType=3
            (no annotation) and there is no hook line, but there is a text box or underline,
            ACAD does not display text box.  In such special case, we still have to set the
            annotation on.
            ---------------------------------------------------------------------------*/
            if (isAnnoRequired)
                annoType = attachedType;

            /*-----------------------------------------------------------------------------------
            If there is an mtext but no hook line to attach, it is an inline note, in which case
            we need to set the x-direction to align with the mtext such that, when edited in
            AutoCAD the hook line will come back whenever needed.
            -----------------------------------------------------------------------------------*/
            CheckInlineText (pointArray, annotationObjId, pLeader);

            pLeader->setAnnotationOffset (RealDwgUtil::GeVector3dFromDPoint3d(annoOffset));
            }
        }

    SetLeaderParams (pLeader, annotationObjId, annoType, pointArray, bResetLastVertex, boxHeight, boxWidth);

    return  RealDwgSuccess;
    }

};  // ConvertLeaderFromDgnDimension



/*---------------------------------------------------------------------------------**//**
* Get the attachment point to mtext - it can be the last point or the hook point
* @return       attachment point
* @bsimethod                                                    DonFu           10/02
+---------------+---------------+---------------+---------------+---------------+------*/
DPoint3dP                   ConvertToDgnContext::GetNoteCellOrigin
(
DPoint3dR                   origin,
AcDbLeader*                 pLeader
)
    {
    if (pLeader->hasHookLine())
        ToDgnExtLeader::ExtractHookPoint (origin, this->GetModelspaceAnnotationScale(), pLeader);
    else
        RealDwgUtil::DPoint3dFromGePoint3d (origin, pLeader->lastVertex());

    this->GetTransformToDGN().Multiply (origin);

    return  &origin;
    }

/*---------------------------------------------------------------------------------**//**
* Get the two points of the hook line, if there is one
*
* @bsimethod                                                    DonFu           10/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::GetLeaderUnderLine
(
DPoint3dP                   pHookPoints,
double                      textWidth,
AcDbLeader*                 pLeader
)
    {
    RealDwgUtil::DPoint3dFromGePoint3d (pHookPoints[0], pLeader->lastVertex());
    this->GetTransformToDGN().Multiply (pHookPoints[0]);

    DPoint3d    direction;
    ToDgnExtLeader::ExtractXAxis (direction, pLeader);

    if (ToDgnExtLeader::ExtractHookLineDirection(pLeader) > 0.0)
        direction.Negate ();

    // an annotative leader may have a dimscale=0
    double      dimScale = pLeader->dimscale ();
    if (dimScale < TOLERANCE_ZeroScale && RealDwgUtil::IsConstObjectAnnotative(pLeader) && pLeader->ownerId() == acdbSymUtil()->blockModelSpaceId(pLeader->database()))
        dimScale = this->GetModelspaceAnnotationScale ();

    if (dimScale < TOLERANCE_ZeroScale)
        dimScale = 1.0;

    textWidth += pLeader->dimgap() * dimScale * this->GetScaleToDGN();

    pHookPoints[1].SumOf (pHookPoints[0], direction, textWidth);
    }

/*---------------------------------------------------------------------------------**//**
* Get the two points of the hook line, if there is one
*
* @bsimethod                                                    DonFu           10/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::GetLeaderHookLine
(
DPoint3dP                   pHookPoints,
double                      textWidth,
AcDbLeader*                 pLeader
)
    {
    // don't bother with unhooked leader - TFS 371590
    if (!pLeader->annotationObjId().isValid())
        return  BSIERROR;

    /*-----------------------------------------------------------------------------------
    When text is placed above dimension line (dimtad=1), either to extend the hook line
    to become a underline, or create a underline.
    -----------------------------------------------------------------------------------*/
    if (pLeader->hasHookLine())
        {

        double      dimScale = pLeader->dimscale();
        double dAnnotationScale = this->GetModelspaceAnnotationScale();
        if (dimScale <= 0.0)
            {
            double dLeaderAnnoScale = dAnnotationScale;
            bool isAnnotative = this->GetDisplayedAnnotationScale(dLeaderAnnoScale, pLeader);

            if (isAnnotative && (dLeaderAnnoScale > TOLERANCE_ZeroScale))
                dimScale = dAnnotationScale = dLeaderAnnoScale;
            }

        RealDwgUtil::DPoint3dFromGePoint3d (pHookPoints[0], pLeader->lastVertex());
        this->GetTransformToDGN().Multiply (pHookPoints[0]);

        ToDgnExtLeader::ExtractHookPoint (pHookPoints[1], dAnnotationScale, pLeader);
        this->GetTransformToDGN().Multiply (pHookPoints[1]);

        // if text is above dimension line, hookline becomes underline
        if (pLeader->dimtad() >= 1)
            {

            if (dimScale < TOLERANCE_ZeroScale && RealDwgUtil::IsConstObjectAnnotative(pLeader) && pLeader->ownerId() == acdbSymUtil()->blockModelSpaceId(pLeader->database()))
                dimScale = this->GetModelspaceAnnotationScale ();

            if (dimScale < TOLERANCE_ZeroScale)
                dimScale = 1.0;

            // add a text margin to total text width
            textWidth += pLeader->dimgap() * this->GetScaleToDGN() * dimScale;

            DPoint3d    direction;
            direction.NormalizedDifference (pHookPoints[0], pHookPoints[1]);
            pHookPoints[0].SumOf (pHookPoints[0], direction, textWidth);
            }

        return  SUCCESS;
        }
    else if (pLeader->dimtad() >= 1)
        {
        // no hook line, but still need a underline of text:
        GetLeaderUnderLine (pHookPoints, textWidth, pLeader);

        return  SUCCESS;
        }

    return  BSIERROR;
    }


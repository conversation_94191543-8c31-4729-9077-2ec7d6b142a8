/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgConvertEvents.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include <Mstn/RealDWG/rDwgConvertEvents.h>

// From DgnPlatform & PowerPlatform
#include    <DgnPlatform\DgnHandlersAPI.h>
// From RealDWG
#include    <realdwg\base\dbents.h>
#include    <realdwg\base\dbobjptr2.h>
// Optional classes used in our RealDWG interface
class AcDbSun;
class AcDbLayoutManager;
class AcDbLight;
class AcDbMaterial;
class AcGiMaterialMap;
class AcDbMInsertBlock;
class AcDbPolyFaceMesh;
class AcDbPolygonMesh;
class AcDbRasterImageDef;
class AcDbWipeout;
class AcDbXrecord;

// From our RealDWG interface.
#include    <Mstn\RealDwg\rDwgAPI.h>
#include    <Mstn\RealDwg\rDwgConvertContext.h>

using namespace Bentley::RealDwg;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Michael.Butvinnik               05/2019
+---------------+---------------+---------------+---------------+---------------+------*/
DwgConvertEvents::DwgConvertEvents()
    {}

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Michael.Butvinnik               05/2019
+---------------+---------------+---------------+---------------+---------------+------*/
DwgConvertEvents& DwgConvertEvents::GetInstance()
    {
    static DwgConvertEvents instance;
    return instance;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Michael.Butvinnik               05/2019
+---------------+---------------+---------------+---------------+---------------+------*/
void DwgConvertEvents::AddListener(IDwgConvertEventsListener& handler)
    {
    m_handlers.AddHandler(&handler);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Michael.Butvinnik               05/2019
+---------------+---------------+---------------+---------------+---------------+------*/
void DwgConvertEvents::DropListener(IDwgConvertEventsListener& handler)
    {
    m_handlers.DropHandler(&handler);
    }

// internal methods for raising events

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Michael.Butvinnik               05/2019
+---------------+---------------+---------------+---------------+---------------+------*/
void DwgConvertEvents::FireBeforeElementToObject(ElementHandleR eh, ConvertFromDgnContextR context) 
    {
    struct Caller
        {
        ElementHandleR eh;
        ConvertFromDgnContextR context;

        void CallHandler (IDwgConvertEventsListener& handler) const { handler.BeforeElementToObject(eh, context); }
        };
    m_handlers.CallAllHandlers (Caller {eh, context});
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Michael.Butvinnik               05/2019
+---------------+---------------+---------------+---------------+---------------+------*/
void DwgConvertEvents::FireAfterElementFromObject(EditElementHandleR eeh, ConvertToDgnContextR context)
    {
    struct Caller
        {
        EditElementHandleR eeh;
        ConvertToDgnContextR context;

        void CallHandler (IDwgConvertEventsListener& handler) const { handler.AfterElementFromObject(eeh, context); }
        };
    m_handlers.CallAllHandlers (Caller {eeh, context});
    }

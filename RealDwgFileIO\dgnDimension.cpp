/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnDimension.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
// This file is #included in rDwgDgnExtension.cpp


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          01/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtDimension : public ToDwgExtension
{
private:
// set these members locally so we don't have to keep retrieving them.
mutable DgnModelP               m_modelRef;
mutable ConvertFromDgnContextP  m_fromDgnContext;
mutable TransformCP             m_transformFromDgn;
mutable ElementHandleP          m_sourceElementHandle;
mutable DimensionHandler*       m_sourceDimHandler;
mutable bool                    m_isAnnotative;

public:
ToDwgExtDimension()
    {
    m_modelRef              = NULL;
    m_fromDgnContext        = NULL;
    m_transformFromDgn      = NULL;
    m_sourceElementHandle   = NULL;
    m_sourceDimHandler      = NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleCR elemHandle, DimensionType dimType, ConvertFromDgnContextR context) const
    {
    switch (dimType)
        {
        case DimensionType::SizeArrow:
        case DimensionType::SizeStroke:
        case DimensionType::CustomLinear:
        case DimensionType::LocateSingle:
        case DimensionType::LocateStacked:
        case DimensionType::DiameterParallel:
        case DimensionType::DiameterPerpendicular:
            return AcDbRotatedDimension::desc();

        case DimensionType::Diameter:
        case DimensionType::DiameterExtended:
            return AcDbDiametricDimension::desc();

        case DimensionType::Radius:
        case DimensionType::RadiusExtended:
            return AcDbRadialDimension::desc();

        case DimensionType::AngleLines:
        case DimensionType::AngleAxis:
        case DimensionType::AngleAxisX:
        case DimensionType::AngleAxisY:
        case DimensionType::AngleSize:
        case DimensionType::AngleLocation:
        case DimensionType::ArcSize:
        case DimensionType::ArcLocation:
            {
            UInt16              quadrant = 0;
            mdlDim_overallGetAngleQuadrant(&quadrant, elemHandle);
            if (quadrant > 0)
                return AcDb2LineAngularDimension::desc();
            else if (elemHandle.GetElementCP()->dim.frmt.angleMeasure)
                return AcDb3PointAngularDimension::desc();
            else
                return AcDbArcDimension::desc();
            }
        case DimensionType::Ordinate:
            return AcDbOrdinateDimension::desc();

        case DimensionType::Center:
            return AcDbBlockReference::desc();

        case DimensionType::Note:
            return AcDbLeader::desc();

        case DimensionType::LabelLine:
            if (context.GetSettings().DropLabelLines())
                return AcDbBlockReference::desc();
            return AcDbMText::desc();
        }

    return  NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsReallyNote (ElementHandleR elemHandle) const
    {
    // get the root cell if the input dimension is either a note or label leader:
    ElementRefP cellRef = mdlNote_getRootNoteCellElmRef (elemHandle);
    if (nullptr == cellRef)
        return  false;

    // make sure the cell is not of LabelCellHeader:
    EditElementHandle   cellEeh(cellRef);
    if (cellEeh.IsValid() && nullptr != dynamic_cast<LabelCellHeaderHandler*>(&cellEeh.GetHandler()))
        return  false;

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    DimensionHandler*   dimHandler = dynamic_cast <DimensionHandler*> (&elemHandle.GetHandler());
    if (NULL == dimHandler)
        return  BadElementHandler;

    DimensionType      dimType = dimHandler->GetDimensionType (elemHandle);

    // Since note handler's ToDwg extension will save paired note elements, this method only saves widowed leader components:
    if (DIM_NOTE(dimType) && this->IsReallyNote(elemHandle))
        return  RealDwgIgnoreElement;

    acObject = context.InstantiateOrUseExistingObject (existingObject, GetTypeNeeded(elemHandle, dimType, context));

    m_fromDgnContext        = &context;
    m_modelRef              = context.GetModel();
    m_transformFromDgn      = &context.GetTransformFromDGN();
    m_sourceElementHandle   = &elemHandle;
    m_sourceDimHandler      = dimHandler;
    m_isAnnotative          = mdlDim_overallGetModelAnnotationScale (NULL, *m_sourceElementHandle);

    // currently only support annotation scale in modelspace
    if (m_isAnnotative && !context.CanSaveAnnotationScale())
        m_isAnnotative = false;

    AcDbDimension*      dimension;
    AcDbLeader*         leader;
    AcDbBlockReference* blockReference;
    AcDbMText*          mtext;
    if (NULL != (dimension = AcDbDimension::cast(acObject)))
        return  SetAcDbDimensionFromDimensionElement (acObject);
    else if (NULL != (leader = AcDbLeader::cast(acObject)))
        return  SetAcDbLeaderFromDimensionElement (leader);
    else if (NULL != (blockReference = AcDbBlockReference::cast(acObject)))
        return  DropUnsupportedDimensionToBlock (blockReference);
    else if (NULL != (mtext = AcDbMText::cast(acObject)))
        return  SetAcDbMTextFromLabelLine (mtext);

    return  DwgObjectUnsupported;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbDimensionFromDimensionElement
(
AcDbObjectP&            acObject
) const
    {
    // convert the dgn dimstyle to DWG
    AcDbDimStyleTableRecord*  acDimstyle = new AcDbDimStyleTableRecord ();
    if (NULL == acDimstyle)
        return  OutOfMemoryError;

    AcDbDimension*      acDimension = AcDbDimension::cast (acObject);

    m_fromDgnContext->InitializeDimstyleFromDimension (acDimstyle, AcDbEntity::cast(acDimension));

    RealDwgStatus       status = m_fromDgnContext->CreateDwgDimstyleFromElement (acDimstyle, *m_sourceElementHandle);
    if (RealDwgSuccess != status)
        {
        delete acDimstyle;
        return  status;
        }

    // get dgn dimstyle element id if there is one attached:
    AcDbObjectId    tableRecordId = m_fromDgnContext->SetDimstyleRecordId (acDimension, *m_sourceElementHandle);

    /*----------------------------------------------------------------------------------------------------------
    Attaching an annotative style in above call could have incorrectly changed a non-annotative dimension as 
    annotative, effectively changing dimscale=0.  This causes the attempt of overriding dimscale to fail, 
    resulting in an incrrect dimscale.  To prevent that we choose to turn off annotative state of the dimension 
    so we can override dimscale correctly.  We do not want to hanle a true annotative dimension here because we 
    do not want to unncessarily add the annoative dictionary to database if the dimension is to be dropped.  
    Setting dimscale on an annotative dimension has no impact to outcome anyway.  Leader entity has no impact
    either because it sets annotative state prior to adding style overrides.
    ----------------------------------------------------------------------------------------------------------*/
    if (!m_isAnnotative)
        RealDwgUtil::SetObjectAnnotative (acDimension, false);

    // drop dimension to multiple segments
    ElementAgenda   dimArray;
    RealDwgStatus   returnStatus = RealDwgSuccess;

    if (m_sourceDimHandler->GetNumSegments(*m_sourceElementHandle) < 2 || 
        SUCCESS != m_sourceDimHandler->DropDimensionToSegments(dimArray, *m_sourceElementHandle))
        {
        // set converted dimstyle data to dimension which may be used in next step converting the dimension
        m_fromDgnContext->SetDimstyleData ((AcDbEntity*)acDimension, acDimstyle);

        if (RealDwgSuccess == (status = ConvertSingleDimension (acDimension, *m_sourceElementHandle)))
            ScheduleAssociativeDimensionsForPostprocessing (acDimension, *m_sourceElementHandle);
        else if (ReplacedObjectType == status)
            returnStatus = status;
        }
    else
        {
        int     segmentNo = 0;
        bool    isSegment1Dropped = false;

        // convert each segment to DWG
        for each (ElemAgendaEntry dimSegment in dimArray)
            {
            // update local source element handle with the new segment:
            m_sourceElementHandle = &dimSegment;
            
            /*---------------------------------------------------------------------------
            when drop a multi-segment dimension, the segment specific data, such as
            template remains unchanged.  Now we need to update dimension with segment data:
            ---------------------------------------------------------------------------*/
            DimensionType  dimType = m_sourceDimHandler->GetDimensionType (dimSegment);
            if (DIM_LINEAR(dimType) || DIM_ANGULAR(dimType))
                m_fromDgnContext->UpdateDimstyleFromDimSegment (acDimstyle, segmentNo, dimSegment);

            // convert the first segment to the input dimension entity
            if (0 == segmentNo++)
                {
                // set converted dimstyle data to dimension
                m_fromDgnContext->SetDimstyleData ((AcDbEntity*)acDimension, acDimstyle);

                if (RealDwgSuccess == (status = ConvertSingleDimension (acDimension, dimSegment)))
                    {
                    ScheduleAssociativeDimensionsForPostprocessing (acDimension, dimSegment);
                    // check in the first dimension to reserve the object ID such that an insert from dropped segment will not take up this ID, TFS 36959.
                    if (!acDimension->objectId().isValid())
                        m_fromDgnContext->AddEntityToCurrentBlock (acDimension, this->GetSourceOrNewElementId());
                    }
                else if (ReplacedObjectType == status)
                    {
                    // if dimension is replaced or dropped, mark it as such so we can clean it up before return
                    isSegment1Dropped = true;
                    returnStatus = status;
                    }
                }
            else
                {
                // create new dimension entities for the rest of segments
                AcDbDimension*    newDimension = NULL;

                if (DIM_LINEAR(dimType))
                    {
                    newDimension = new AcDbRotatedDimension();
                    }
                else if (DIM_ANGULAR(dimType))
                    {
                    if (acDimension->isKindOf(AcDbArcDimension::desc()))
                        newDimension = new AcDbArcDimension();
                    else
                        newDimension = new AcDb3PointAngularDimension();
                    }
                else if (DIM_ORDINATE(dimType))
                    {
                    newDimension = new AcDbOrdinateDimension ();
                    }
                else
                    {
                    assert (false && L"Unhandled dimension element");
                    break;          // ?? should never happen.
                    }

                // default entity properties to the first dimension
                newDimension->setPropertiesFrom (acDimension);

                // set style table id
                if (!tableRecordId.isNull())
                    newDimension->setDimensionStyle (tableRecordId);

                // populate dimstyle data to dimension
                m_fromDgnContext->SetDimstyleData ((AcDbEntity*)newDimension, acDimstyle);

                // add the new dimension to DWG database
                if (RealDwgSuccess == (status = ConvertSingleDimension (newDimension, dimSegment, segmentNo)))
                    {
                    // set the modified flag to avoid recomputation in DD
                    newDimension->recordGraphicsModified (false);
                    // add the new dimension to database prior to scheduling it for post process
                    if (!newDimension->objectId().isValid())
                        m_fromDgnContext->AddEntityToCurrentBlock (newDimension, 0);

                    ScheduleAssociativeDimensionsForPostprocessing (newDimension, dimSegment);

                    m_fromDgnContext->UpdateEntityPropertiesFromElement (newDimension, dimSegment);

                    newDimension->close();
                    }
                else if (newDimension->objectId().isValid())
                    {
                    newDimension->erase();
                    }
                else if (newDimension->isNewObject())
                    {
                    delete newDimension;
                    }
                }
            if (ReplacedObjectType == status)
                DIAGNOSTIC_PRINTF ("Dimension w/ID=%I64d[seg=%d] is dropped to geometry\n", m_sourceElementHandle->GetElementCP()->ehdr.uniqueId, segmentNo);
            else if (RealDwgSuccess != status)
                DIAGNOSTIC_PRINTF ("Error dropping dimension %I64d, status = %d\n", m_sourceElementHandle->GetElementCP()->ehdr.uniqueId, status);
            }

        if (isSegment1Dropped)
            {
            if (acObject->objectId().isValid())
                {
                acObject->erase ();
                }
            else if (acObject->isNewObject())
                {
                delete acObject;
                acObject = NULL;
                }
            }
        }

    delete acDimstyle;

    return  returnStatus;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbLeaderFromDimensionElement
(
AcDbLeader*             pLeader
) const
    {
    // if new, save it to database or else AcDbLeader::attachAnnotation will fail
    if (!pLeader->objectId().isResident())
        m_fromDgnContext->AddEntityToCurrentBlock (pLeader, m_sourceElementHandle->GetElementId());
    
    // set leader data from dimension part of the note element:
    ConvertLeaderFromDgnDimension   leaderFromDgn(*m_sourceElementHandle, NULL, *m_fromDgnContext);
    return leaderFromDgn.SetLeaderFromNoteDimension (pLeader);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbMTextFromLabelLine
(
AcDbMText*              pMText
) const
    {
    RotMatrix       textMatrix;
    bool            isUnionText = false;

    RealDwgStatus   status = m_fromDgnContext->CreateMTextFromDimElement (pMText, &textMatrix, NULL, &isUnionText, *m_sourceElementHandle, L"\\P\\P");
    if (SUCCESS != status)
        return status;

    // if the mtext is not a union of multiple pieces of texts, no further process needed
    if (!isUnionText)
        return  RealDwgSuccess;

    /*-------------------------------------------------------------------------------
    Need to update text origin for the new mtext: change top/bottom just to middle
    -------------------------------------------------------------------------------*/
    // get the def point of label line
    DPoint3d    defPoint;
    if (BSISUCCESS != m_sourceDimHandler->ExtractPoint(*m_sourceElementHandle, defPoint, 0))
        return  CantCreateText;

    m_transformFromDgn->Multiply (defPoint);
    // tranform it to text plane
    textMatrix.MultiplyTranspose (defPoint);

    // get origin of text
    DPoint3d    origin;
    RealDwgUtil::DPoint3dFromGePoint3d (origin, pMText->location());
    textMatrix.MultiplyTranspose (origin);

    // reset text origin y to be on the line
    origin.y = defPoint.y;

    textMatrix.Multiply (origin);

    // reset origin
    pMText->setLocation (RealDwgUtil::GePoint3dFromDPoint3d(origin));

    // change attachment point to middle
    if (AcDbMText::kBottomLeft == pMText->attachment() || AcDbMText::kTopLeft == pMText->attachment())
        pMText->setAttachment (AcDbMText::kMiddleLeft);
    else if (AcDbMText::kBottomRight == pMText->attachment() || AcDbMText::kTopRight == pMText->attachment())
        pMText->setAttachment (AcDbMText::kMiddleRight);
    else
        pMText->setAttachment (AcDbMText::kMiddleCenter);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               DropUnsupportedDimensionToBlock (AcDbBlockReference* insert, AcDbObjectId blockIdIn=AcDbObjectId::kNull, AcDbDimension* dwgDimIn=NULL) const
    {
    DropGeometry    dropDimOptions (DropGeometry::OPTION_Dimensions);
    dropDimOptions.SetDimensionOptions (DropGeometry::DIMENSION_Geometry);

    AcDbObjectId    blockId;
    if (blockIdIn.isValid())
        blockId = blockIdIn;
    else if (RealDwgSuccess != m_fromDgnContext->DropElementToBlock(blockId, *m_sourceElementHandle, dropDimOptions))
        return  DropFailed;

    if (Acad::eOk != insert->setBlockTableRecord(blockId))
        return  DropFailed;

    // try to set the first dim def point as the block def point - every dim type should have at least one point:
    DPoint3d        point0;
    if (SUCCESS != m_sourceDimHandler->ExtractPoint(*m_sourceElementHandle, point0, 0))
        return  CantExtractPoints;

    m_fromDgnContext->GetTransformFromDGN().Multiply (point0);

    AcGePoint3d     origin = RealDwgUtil::GePoint3dFromDPoint3d (point0);

    AcDbBlockTableRecordPointer     blockRecord(blockId, AcDb::kForWrite);
    if (Acad::eOk == blockRecord.openStatus())
        {
        blockRecord->setOrigin (origin);
        insert->setPosition (origin);
        }

    // save new block reference to database if created by this method, or use the input dimension object ID:
    AcDbObjectId    insertId;
    if (NULL != dwgDimIn && dwgDimIn->objectId().isValid() && Acad::eObjectToBeDeleted == dwgDimIn->handOverTo(insert, false, false))
        insertId = insert->objectId ();
    else
        insertId = m_fromDgnContext->AddEntityToCurrentBlock (insert, this->GetSourceOrNewElementId());

    return insertId.isNull() ? DropFailed : RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId       GetSourceOrNewElementId () const
    {
    ElementId   elementId = m_sourceElementHandle->GetElementId ();

    if (m_fromDgnContext->ExistingObjectIdFromElementId(elementId).isValid())
        elementId = 0;

    return  elementId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               DropUnsupportedDimensionToBlock (AcDbDimension* dwgDimIn, AcDbObjectId blockIdIn = AcDbObjectId::kNull) const
    {
    if (NULL == dwgDimIn)
        return  NotApplicable;

    AcDbBlockReference* insert = new AcDbBlockReference ();

    insert->setPropertiesFrom (dwgDimIn);
    // Set normal prior to setting insertion origin - TR 330999.
    // Since Vanvcouver we started dropping elements using DisplayHandler::Drop which creates dropped components
    // in the world coordinate system, hence there is no more planar info.
    // insert->setNormal (dwgDimIn->normal());

    RealDwgStatus       status = DropUnsupportedDimensionToBlock (insert, blockIdIn, dwgDimIn);

    if (RealDwgSuccess == status)
        insert->close ();
    else
        delete insert;

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateAnonymousDimensionBlock (AcDbObjectId& blockId) const
    {
    DropGeometry    dropDimOptions (DropGeometry::OPTION_Dimensions);
    dropDimOptions.SetDimensionOptions (DropGeometry::DIMENSION_Geometry);

    return m_fromDgnContext->DropElementToBlock (blockId, *m_sourceElementHandle, dropDimOptions);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ToObjectPostProcess
(
ElementHandleR              inElement,
AcDbObjectP                 acObject, 
ConvertFromDgnContextR      context
) const override
    {
    m_fromDgnContext        = &context;
    m_modelRef              = context.GetModel();
    m_transformFromDgn      = &context.GetTransformFromDGN();

    m_sourceElementHandle   = &inElement;

    m_sourceDimHandler      = dynamic_cast <DimensionHandler*> (&inElement.GetHandler());
    if (NULL == m_sourceDimHandler)
        return  BadElementHandler;

    DimensionType      dimType = m_sourceDimHandler->GetDimensionType (inElement);

    for (int iPoint=0; iPoint<4; iPoint++)
        {
        int         dwgPointIndex;
        AssocPoint  assocPoint;

        if ((dwgPointIndex = DwgDimensionPointIndexFromDgn (iPoint, dimType)) >= 0 &&
            SUCCESS == AssociativePoint::ExtractPoint (assocPoint, inElement, iPoint, 2))
            {
            // Create XDictionary if doesn't already exist.
            AcDbDatabase*   pDatabase   = context.GetDatabase();
            AcDbHandle      currentSeed = pDatabase->handseed();
            BeAssert (context.ExistingObjectIdFromDBHandle (currentSeed).isNull());

            Acad::ErrorStatus   status = acObject->createExtensionDictionary();
            BeAssert ( (Acad::eOk == status) || (Acad::eAlreadyInDb == status) );

            AcDbDictionaryPointer   pExtensionDictionary (acObject->extensionDictionary(), AcDb::kForWrite);
            if (Acad::eOk != pExtensionDictionary.openStatus())
                {
                BeAssert (false && L"Failure opening extension dictionary!");
                continue;
                }

            AcDbDimAssoc*           pDimAssoc = NULL;
            if (Acad::eOk != pExtensionDictionary->getAt (StringConstants::XDictionaryItem_AcadDimAssoc, (AcDbObject*&)pDimAssoc, AcDb::kForWrite))
                {
                AcDbObjectId        dimAssocObjectId;
                pExtensionDictionary->setAt (StringConstants::XDictionaryItem_AcadDimAssoc, pDimAssoc = new AcDbDimAssoc(), dimAssocObjectId);
                pDimAssoc->setDimObjId (acObject->objectId());
                acObject->addPersistentReactor (dimAssocObjectId);
                }

            if (NULL != pDimAssoc)
                {
                this->ExtractSnapPointRefFromAssocPoint (assocPoint, pDimAssoc, dwgPointIndex, inElement);
                pDimAssoc->close();
                }
            }
        }

    // set modified=false to avoid recomputeDimBlock in DD (above changes caused modified=true)
    ((AcDbEntity*) acObject)->recordGraphicsModified (false);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           10/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                            GetAxisFromView
(
DPoint3dR                       viewXAxis,
int                             whichAxis,
int                             dimView
) const
    {
    RotMatrix   viewMatrix = RotMatrix::FromIdentity ();

    ViewInfoP   viewInfo = m_modelRef->GetViewInfo (dimView);
    if (NULL != viewInfo)
        viewMatrix = viewInfo->GetRotation ();

    viewMatrix.GetRow ((DVec3dR)viewXAxis, whichAxis);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                    GetOrCreateDefpointsLayer () const
    {
    // get layer table
    AcDbLayerTable*   pLayerTable;
    m_fromDgnContext->GetDatabase()->getLayerTable (pLayerTable, AcDb::kForRead);

    // try to get layer "Defpoints"
    AcDbObjectId        layerId;
    if (Acad::eOk != pLayerTable->getAt (NAME_DefPointsLayer, layerId))
        {
        if (Acad::eOk != pLayerTable->upgradeOpen())
            {
            BeAssert (false && L"Error upgrading layertable open status!");
            pLayerTable->close();
            return NULL;
            }

        // create a new Defpoints layer
        AcDbLayerTableRecord* pLayer = new AcDbLayerTableRecord();

        // Fixed name "Defpoints"
        pLayer->setName(AcString(NAME_DefPointsLayer));
        // set it not plottable
        pLayer->setIsPlottable (false);

        // add it to layer table
        pLayerTable->add (pLayer);

        layerId = pLayer->objectId ();

        pLayer->close ();
        }

    pLayerTable->close();
    return  layerId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                       AddDefpointToBlock
(
AcDbObjectId&                   blockId,
DPoint3dCR                      point
) const
    {
    if (blockId.isNull())
        return  BSIERROR;

    AcDbPoint*    pPoint = new AcDbPoint ();

    // set the position
    pPoint->setPosition (RealDwgUtil::GePoint3dFromDPoint3d(point));

    // set the layer to Defpoints
    pPoint->setLayer (GetOrCreateDefpointsLayer ());

    // append the point entity to block
    AcDbBlockTableRecordPointer pBlockRecord (blockId, AcDb::kForWrite);

    if (Acad::eOk == pBlockRecord.openStatus())
        pBlockRecord->appendAcDbEntity (pPoint);
    else
        {
        BeAssert (false && L"Failed to open defpoint block!");
        delete pPoint;
        }

    pPoint->close ();

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                            AddLindimDefpointsToBlock
(
AcDbObjectId&                   blockId,
const AcDbDimension*            pLinDim,
double                          angle,
double                          zDepth,
bool                            hasEcs,
RotMatrixCR                     defMatrix
) const
    {
    AcDbRotatedDimension* pRotatedDim = NULL;
    AcDbAlignedDimension* pAlignedDim = NULL;

    if (NULL == (pRotatedDim = (AcDbRotatedDimension::cast (pLinDim))))
        {
        if (NULL == (pAlignedDim = AcDbAlignedDimension::cast (pLinDim)))
            {
            BeAssert (false && L"Unexpected dimension type: expected either rotated or aligned dimension!");
            return;
            }
        }

    // make up the compond matrix to transform def points like we did for other geometry
    RotMatrix   compMatrix;
    if (hasEcs && fabs(angle) > MIN_AngleRadians)
        {
        RotMatrix   rotation;
        rotation.initFromAxisAndRotationAngle (2, -angle);
        compMatrix.productOf (&defMatrix, &rotation);
        }
    else
        compMatrix = defMatrix;

    zDepth *= m_fromDgnContext->GetScaleFromDGN ();

    // add group code 10
    DPoint3d    point = RealDwgUtil::DPoint3dFromGePoint3d (point, (NULL == pRotatedDim) ? pAlignedDim->dimLinePoint() : pRotatedDim->dimLinePoint());
    if (hasEcs)
        compMatrix.multiplyTranspose (&point);
    point.z -= zDepth;
    AddDefpointToBlock (blockId, point);

    // add group code 13
    point = RealDwgUtil::DPoint3dFromGePoint3d (point, (NULL == pRotatedDim) ? pAlignedDim->xLine1Point() : pRotatedDim->xLine1Point());
    if (hasEcs)
        compMatrix.multiplyTranspose (&point);
    point.z -= zDepth;
    AddDefpointToBlock (blockId, point);

    // add group code 14
    point = RealDwgUtil::DPoint3dFromGePoint3d (point, (NULL == pRotatedDim) ? pAlignedDim->xLine2Point() : pRotatedDim->xLine2Point());
    if (hasEcs)
        compMatrix.multiplyTranspose (&point);
    point.z -= zDepth;
    AddDefpointToBlock (blockId, point);

    // add group code 11 - text middle point
    point = RealDwgUtil::DPoint3dFromGePoint3d (point, (NULL == pRotatedDim) ? pAlignedDim->textPosition() : pRotatedDim->textPosition());
    if (hasEcs)
        compMatrix.multiplyTranspose (&point);
    point.z -= zDepth;
    AddDefpointToBlock (blockId, point);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           10/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                            RotateTextPointByEcsAngle
(
DPoint3dR                       textPoint,
double                          angle
) const
    {
    // rotate text point by ECS angle to compensate the difference caused by the arbitrary axis algrithm
    if (fabs(angle) > MIN_AngleRadians)
        {
        RotMatrix   rotation;

        rotation.initFromAxisAndRotationAngle (2, angle);
        rotation.multiply (&textPoint);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                            SetTextLocation
(
AcDbDimension*                  pDwgDim,
DimensionElmCP                  pDgnDim,
DPoint3dCR                      textCenter,
double                          angle,
bool                            hasEcs,
RotMatrixCR                     defMatrix
) const
    {
    DPoint3d    point = textCenter;

    // if cirdim's text is above dimension line, set the text def point right on dimension line
    if ((pDwgDim->isKindOf(AcDbRadialDimension::desc()) || pDwgDim->isKindOf(AcDbDiametricDimension::desc())) && pDwgDim->dimtad() > 0)
        {
        double  shiftY = (0.5 * pDwgDim->dimtxt() + pDwgDim->dimgap()) * pDwgDim->dimscale();

        if (pDgnDim->flag.horizontal)
            {
            // for horizontal text, simply shift y-coordinate to dim line
            point.y -= shiftY;
            }
        else
            {
            // for aligned text, find shifting direction to project the point to dim line
            DVec3d  dimDir, shiftVec;

            dimDir.NormalizedDifference (pDgnDim->GetPoint(1), pDgnDim->GetPoint(0));

            // check dimline direction on dimension plane
            defMatrix.MultiplyTranspose (dimDir);

            // will make the shifting direction always points from text center to dim. line
            if (dimDir.x < 0.0)
                dimDir.Negate ();

            shiftVec.UnitPerpendicularXY (dimDir);
            shiftVec.ScaleToLength (-shiftY);

            defMatrix.Multiply (shiftVec);

            point.Add (shiftVec);
            }
        }

    pDwgDim->setTextPosition (RealDwgUtil::GePoint3dFromDPoint3d(point));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/04
+---------------+---------------+---------------+---------------+---------------+------*/
void                            SetOrdinateOrigin
(
AcDbOrdinateDimension*          pDwgOrddim,
DPoint3dArray                   defpoints,
const int                       pointNo,
RotMatrixCR                     defmatrix
) const
    {
    DPoint3d                origin = defpoints.at(0);
    
    DimensionHandler const* hdlr = dynamic_cast<DimensionHandler const*>(&m_sourceElementHandle->GetHandler());
    if (NULL == hdlr)
        {
        pDwgOrddim->setOrigin (RealDwgUtil::GePoint3dFromDPoint3d(origin));
        return;
        }
    
    DimensionStylePtr       style = hdlr->GetDimensionStyle (*m_sourceElementHandle);
    if (style.IsNull())
        {
        pDwgOrddim->setOrigin (RealDwgUtil::GePoint3dFromDPoint3d(origin));
        return;
        }
    
    bool                    uOrdUseDatumValue = false;
    style->GetBooleanProp (uOrdUseDatumValue, DIMSTYLE_PROP_Value_OrdUseDatumValue_BOOLINT);
    double                  dOrdinateDatumValue = 0;
    style->GetDistanceProp (dOrdinateDatumValue, DIMSTYLE_PROP_Value_OrdDatumValue_DISTANCE, m_sourceElementHandle->GetDgnModelP());

    DimOrdinateOverrides    overrides;
    if (BSISUCCESS == mdlDim_getOrdinateOverride(&overrides, *m_sourceElementHandle) && (overrides.modifiers & ORDINATE_Override_StartValueX))
        {
        /*-------------------------------------------------------------------------------
        TerrainModel overrides dimension value to show z-datum on XY plane - TFS 560364.
        We responds that with two options: a user can drop unsupported dimensions.  If not 
        dropped, we treat it as a user datum on X-Axis, by following the same logic as in
        adim_generateOrdinate1.  Note that it adds startValueX + datumValue at the end.
    
        Obviously this workaround, while creating a correct dimension value readout, will 
        result in a dim defpoint that does not exist in DGN, hence to potentially cause 
        fit-view to go off of the model range.
        -------------------------------------------------------------------------------*/
        if (!uOrdUseDatumValue)
            uOrdUseDatumValue = true;
        dOrdinateDatumValue += overrides.startValueX;
        }

    if (uOrdUseDatumValue && fabs(dOrdinateDatumValue) > TOLERANCE_PointEqual)
        {
        // transform base point to dimension's plane
        defmatrix.MultiplyTranspose (origin);

        double              side = 1.0, dimScale = 1.0;
        bool                scaleApplied = false;
        DPoint3d            featurePoint = origin;

        // for the feature point, make sure we move the origin on the right direction
        if (pointNo > 0)
            {
            featurePoint = defpoints.at(1);
            defmatrix.MultiplyTranspose (featurePoint);

            if (featurePoint.y > origin.y)
                side = -1.0;

            // when decreaseOnReverse is on, dimension subtracts datum value on reserse side of the feature point - TFS 496950.
            bool            decreaseOnReverse = false;
            if (side > 0.0 && BSISUCCESS == style->GetBooleanProp(decreaseOnReverse, DIMSTYLE_PROP_Value_OrdDecrementReverse_BOOLINT) && decreaseOnReverse)
                side = -1.0;
            }

        /*--------------------------------------------------------------------------------------------------------------------
        Our stroke code applies dim scale factor only on measured length, not on user datum, i.e.,

        Measured value = Measured length * Dimension Scale + User Datum

        To make this right, we have to first apply above logic to get the mixed result(i.e. scaled length + non-scaled datum),
        then we will have to un-apply the same logic on the final value, because DWG dimension also has the dimension scale
        that will apply to the final value.  Here we first scale the length:
        ---------------------------------------------------------------------------------------------------------------------*/
        if (SUCCESS == style->GetDoubleProp(dimScale, DIMSTYLE_PROP_General_DimensionScale_DOUBLE) && fabs(dimScale) > TOLERANCE_ZeroScale)
            {
            origin.y = featurePoint.y - (featurePoint.y - origin.y) * dimScale;
            scaleApplied = true;
            }

        // set the origin with the user datum value, on dimension's plane:
        origin.y += side * dOrdinateDatumValue * m_fromDgnContext->GetScaleFromDGN();
        // then we unscale the final value to allow dimScale to work:
        if (scaleApplied)
            origin.y = featurePoint.y - (featurePoint.y - origin.y) / dimScale;
        // transform the origin to the world
        defmatrix.Multiply (origin);
        }

    pDwgOrddim->setOrigin (RealDwgUtil::GePoint3dFromDPoint3d(origin));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/04
+---------------+---------------+---------------+---------------+---------------+------*/
int                             GetOrdinatePointNumber
(
DimensionElmCP                  pDgnDim
) const
    {
    /*-----------------------------------------------------------------------------------
    Each datum dimension should have 2 points whereas the base dimension only has one.
    -----------------------------------------------------------------------------------*/
    return  pDgnDim->nPoints < 2 ? 0 : 1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/05
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            IsDimlineOnTextDirection
(
RotMatrixCR                     textmatrix
) const
    {
    DVec3d      textDir, dimlineDir;
    RotMatrix   rotMatrix;

    mdlDim_getDimRawMatrix (&rotMatrix, *m_sourceElementHandle);

    rotMatrix.GetColumn (dimlineDir, 0);
    textmatrix.GetColumn (textDir, 0);

    return  dimlineDir.DotProduct(textDir) > -TOLERANCE_VectorEqual;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ConvertOrdinateDimension
(
AcDbDimension*                  pDwgDim,
DPoint3dArray                   pointArray,
double                          angle,
bool                            hasEcs,
RotMatrixCR                     defMatrix,
int                             axisUpdated,
DPoint3dCR                      textCenter,
RotMatrixCR                     textMatrix
) const
    {
    AcDbOrdinateDimension*    pDwgOrddim;

    if (NULL == (pDwgOrddim = AcDbOrdinateDimension::cast (pDwgDim)))
        return UnexpectedDimensionType;

    // DGN only has y ordinate type - dimension line is always on its local x-axis
    pDwgOrddim->useYAxis ();

    DimensionElmCP  pDgnDim = (DimensionElmCP) m_sourceElementHandle->GetElementCP();
    if (NULL == pDgnDim)
        return UnexpectedDimensionType;

    int datumPoint = GetOrdinatePointNumber (pDgnDim);

    // Set group point 10: base point
    SetOrdinateOrigin (pDwgOrddim, pointArray, datumPoint, defMatrix);

    // Set group code 13: begin of leader line
    pDwgOrddim->setDefiningPoint (RealDwgUtil::GePoint3dFromDPoint3d(pointArray.at(datumPoint)));

    // Set group code 14: end of leader line
    DPoint3d        point, leaderDelta;
    // get delta distance from leader start point to end point, in WCS:
    leaderDelta.x = pDgnDim->GetDimTextCP(datumPoint)->offset * m_fromDgnContext->GetScaleFromDGN();
    leaderDelta.y = pDgnDim->GetDimTextCP(datumPoint)->offsetY * m_fromDgnContext->GetScaleFromDGN();
    leaderDelta.z = 0.0;

    // subtract a text margin when the inline leader points to front side of text, as opposed to the reversed side:
    if (IsDimlineOnTextDirection(textMatrix))
        leaderDelta.x -= pDgnDim->geom.textMargin * m_fromDgnContext->GetScaleFromDGN();

    if (axisUpdated & AXIS_X)
        leaderDelta.x = -leaderDelta.x;
    if (axisUpdated & AXIS_Y)
        leaderDelta.y = -leaderDelta.y;
    defMatrix.multiply (&leaderDelta);

    // add the leader line offset which can extend on both x and y directions
    point.SumOf (pointArray.at(datumPoint), leaderDelta, 1.0);

    pDwgOrddim->setLeaderEndPoint (RealDwgUtil::GePoint3dFromDPoint3d(point));

    // Set text middle point, group code 11
    SetTextLocation (pDwgDim, pDgnDim, textCenter, angle, hasEcs, defMatrix);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* 3-point angular dimension needs arc edge point (group code 10) to tell which side
*   to measure (inner/outer). Assume coordinates and sizes have already been scaled.
*
* @bsimethod                                                    DonFu           06/01
+---------------+---------------+---------------+---------------+---------------+------*/
double                          GetArcEdgePoint
(
DPoint3dR                       edgePoint,
DPoint3dArray                   pointArray,
double                          height,
DimensionElmCP                  pDgnDim,
RotMatrixCR                     defMatrix
) const
    {
    DVec3d  witness1Vec, witness2Vec, yAxis, zAxis;
    witness1Vec.DifferenceOf (pointArray[1], pointArray[0]);
    witness2Vec.DifferenceOf (pointArray[2], pointArray[0]);

    UInt16  quadrant = 0;
    mdlDim_overallGetAngleQuadrant (&quadrant, *m_sourceElementHandle);
    if (2 == quadrant || (!pDgnDim->extFlag.uAngClockwiseSweep && 1 == quadrant) || (pDgnDim->extFlag.uAngClockwiseSweep && 3 == quadrant))
        witness1Vec.Negate ();
    if (2 == quadrant || (!pDgnDim->extFlag.uAngClockwiseSweep && 3 == quadrant) || (pDgnDim->extFlag.uAngClockwiseSweep && 1 == quadrant))
        witness2Vec.Negate ();

    // get distance between def 1 and def 2 points:
    double  def12 = witness1Vec.Magnitude ();

    // get total witness height (from center to tip of arrow head):
    double  witness = height;
    double  gama = def12 > 1.e-8 ? (witness / def12) : 0.0;

    witness1Vec.Scale (gama);

    edgePoint.SumOf (pointArray.at(0), witness1Vec);

    // get center of arc
    DPoint3d    center = pointArray.at (0);

    // build angular arc's definition matrix:
    RotMatrix   arcMatrix, deltaMatrix;
    witness1Vec.Normalize ();
    witness2Vec.Normalize ();
    if (witness1Vec.IsParallelTo(witness2Vec))
        defMatrix.GetColumn (zAxis, 2);
    else
        zAxis.CrossProduct (witness1Vec, witness2Vec);
    zAxis.Normalize ();
    yAxis.CrossProduct (zAxis, witness1Vec);
    yAxis.Normalize ();
    arcMatrix.InitFromColumnVectors (witness1Vec, yAxis, zAxis);

    DVec3d    zAxis1, zAxis2;
    defMatrix.GetColumn (zAxis1, 2);
    arcMatrix.GetColumn (zAxis2, 2);
    double  dotp = zAxis1.DotProduct (zAxis2);

    // flip the arc plane if a CCW 0-quadrant angle on reversed side of arc's
    if (dotp < 0.0 && !pDgnDim->extFlag.uAngClockwiseSweep && 0 == quadrant)
        {
        yAxis.Scale (-1);
        zAxis.Scale (-1);
        arcMatrix.SetColumn (yAxis, 1);
        arcMatrix.SetColumn (zAxis, 2);
        }

    // get sweeping angle and apply gama
    arcMatrix.MultiplyTranspose (witness2Vec);

    double  angle = atan2 (witness2Vec.y, witness2Vec.x);
    /*-----------------------------------------------------------------------------------
    make a positive sweeping angle: quadrant angles are always within 180 degrees whereas
    0-quadrant angle can have smaller or larger sweeping.
    -----------------------------------------------------------------------------------*/
    if (angle < 0.0)
        {
        if (0 == quadrant)
            angle += 2 * PI;
        else
            angle = -angle;
        }

    // sweep backwards if a CW 0-quadrant angle originally aligns with arc plane
    if (dotp > 0.0 && pDgnDim->extFlag.uAngClockwiseSweep && 0 == quadrant)
        angle -= 2 * PI;

    gama = 0.3 * angle;

    /*-------------------------------------------------------------------
    Transform point to arc plane, translate arc center to arc origin,
    rotate delta angle about origin, and then transform the point
    backwards to the world:
    -------------------------------------------------------------------*/
    arcMatrix.MultiplyTranspose (center);
    arcMatrix.MultiplyTranspose (edgePoint);
    edgePoint.Subtract (center);
    deltaMatrix.InitFromAxisAndRotationAngle (2, gama);
    deltaMatrix.Multiply (edgePoint);
    edgePoint.Add (center);
    arcMatrix.Multiply (edgePoint);

    return  angle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetStartEndAngles (AcDbArcDimension* acArcdim, double sweptAngle, DPoint3dArray const& points, RotMatrixCR matrix) const
    {
    DVec3d      xAxis = DVec3d::From (1.0, 0.0, 0.0);
    DVec3d      witness1Vec;
    witness1Vec.DifferenceOf (points[1], points[0]);
    matrix.MultiplyTranspose (witness1Vec);

    double      startAngle = xAxis.AngleToXY (witness1Vec);
    startAngle = Angle::AdjustToSweep (startAngle, 0, msGeomConst_2pi);

    double      endAngle = Angle::AdjustToSweep (startAngle + sweptAngle, 0, msGeomConst_2pi);

    acArcdim->setArcStartParam (startAngle);
    acArcdim->setArcEndParam (endAngle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ConvertAngularDimension
(
AcDbDimension*                  pDwgDim,
DPoint3dArray                   pointArray,
double                          angle,
bool                            hasEcs,
RotMatrixCR                     defMatrix,
DPoint3dCR                      textCenter,
RotMatrixCR                     textMatrix
) const
    {
    DimensionElmCP  pDgnDim = (DimensionElmCP) m_sourceElementHandle->GetElementCP ();
    if (NULL == pDgnDim)
        return  CantCreateDimension;

    // get height of dimension line from vertex
    double  height = pDgnDim->GetDimTextCP(1)->offset * m_fromDgnContext->GetScaleFromDGN();

    // Add witness height to defpoint height:
    height += pointArray.at(0).Distance (pointArray.at(1));

    DPoint3d    point;
    double      sweptAngle = GetArcEdgePoint (point, pointArray, height, pDgnDim, defMatrix);

    AcDb3PointAngularDimension*   p3PointAngdim = NULL;
    AcDb2LineAngularDimension*    p2LineAngdim = NULL;
    AcDbArcDimension*             pArcdim = NULL;
    if (NULL != (p3PointAngdim = AcDb3PointAngularDimension::cast (pDwgDim)))
        {
        // Set group code 15 - angular vertex (center):
        p3PointAngdim->setCenterPoint (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[0]));
        // Set group code 13 & 14 - witness lines:
        p3PointAngdim->setXLine1Point (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[1]));
        p3PointAngdim->setXLine2Point (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[2]));

        /* Set group code 10 - edge point of arc: */
        p3PointAngdim->setArcPoint (RealDwgUtil::GePoint3dFromDPoint3d(point));
        }
    else if (NULL != (pArcdim = AcDbArcDimension::cast (pDwgDim)))
        {
        // Set group code 15 - angular vertex (center):
        pArcdim->setCenterPoint (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[0]));
        // Set group code 13 & 14 - witness lines:
        pArcdim->setXLine1Point (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[1]));
        pArcdim->setXLine2Point (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[2]));

        /* Set group code 10 - edge point of arc: */
        pArcdim->setArcPoint (RealDwgUtil::GePoint3dFromDPoint3d(point));

        // set start & end angles for the sake of round tripping when we need to check for chord alignment for 90-degree or less measurement:
        SetStartEndAngles (pArcdim, sweptAngle, pointArray, defMatrix);

        // set arc symbol to 1(above) or 2(none). We currently do not have 0(front).
        pArcdim->setArcSymbolType (pDgnDim->tmpl.above_symbol ? 1 : 2);
        }
    else if (NULL != (p2LineAngdim = AcDb2LineAngularDimension::cast (pDwgDim)))
        {
        // 2-line angular dimension existing in DWG file
        // set group code 15 & 13 - start points of 1st and 2nd witness lines which happen to be our center point
        p2LineAngdim->setXLine1Start (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[0]));
        p2LineAngdim->setXLine2Start (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[0]));

        // set group code 10 & 14 - end points of the 2 witness lines
        p2LineAngdim->setXLine1End (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[1]));
        p2LineAngdim->setXLine2End (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[2]));

        /* Set group code 16 - edge point of the arc: */
        p2LineAngdim->setArcPoint (RealDwgUtil::GePoint3dFromDPoint3d(point));
        }
    else
        {
        assert (false);     // dimension type not recognized.
        return UnexpectedDimensionType;
        }

    // reset the witness line suppressing flags
    if (pDgnDim->tmpl.left_witness != pDgnDim->tmpl.right_witness)
        {
        UInt16  quadrant = 0;
        mdlDim_overallGetAngleQuadrant (&quadrant, *m_sourceElementHandle);

        // revert the order set in the export process
        if ( (0 == quadrant || 2 == quadrant) &&
            (pDgnDim->extFlag.uAngClockwiseSweep || (!pDgnDim->extFlag.uAngClockwiseSweep && (NULL != p2LineAngdim))) )
            {
            pDwgDim->setDimse1 (!pDwgDim->dimse1());
            pDwgDim->setDimse2 (!pDwgDim->dimse2());
            }
        }

    /* Set text middle point, group code 11*/
    SetTextLocation (pDwgDim, pDgnDim, textCenter, angle, hasEcs, defMatrix);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
*
* Given 2 vectors vec1 and vec2, and distance r from vec1 towards vec2, calculate
*   vec3 which cuts the line vec1-vec2 by r.
*
* @param        pVec3       <= interesecting point
* @param        pVec2       => first point
* @param        pVec2       => second point
* @param        r           => distance from first point to second point
*
* @bsimethod                                                    DonFu           05/01
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                       GetPointOnLine
(
DPoint3dR                       vec3,
DPoint3dCR                      vec1,
DPoint3dCR                      vec2,
double                          radius
) const
    {
    double  dist = vec1.distance (&vec2);

    if ((fabs(dist) - 1.e-8) < 0)
        {
        vec3 = vec1;
        return  BSIERROR;
        }

    double  delta = radius / dist;

    /* make a new vector v = vec2 - vec1: */
    DPoint3d    v = vec1;
    v.subtract (&vec2);
    v.scale (delta);

    // obtain vec3 by adding vec1 and v:
    vec3 = vec1;
    vec3.add (&v);

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            CirdimHasDefaultTextPosition
(
AcDbDimension*                  pDwgDim,
const DPoint3dArray&            pointArray,
DPoint3dCP                      textCenter = NULL
) const
    {
    /*-----------------------------------------------------------------------------------
    Use default text position if a DWG circular dimension defaults text to inside circle,
    and the text is positioned at the center.  We could do the same for text default to
    outside circle with position outside, but finding outside default location causes more
    computation and we do not benifit from doing that as much as we do for inside default.
    For that, we always use user text position.

    According to ACAD dimstyle GUI as well as its document, the control of cirdim text
    position should be DIMTIX - off to default text inside and on to push it outside.
    But that is not what I found in ACAD 2012 - DIMTIX does not appear to behave that way.
    Instead, it seems the best fit, DIMATFIT=3, that pushes text outside.  All other values
    keep text inside.
    -----------------------------------------------------------------------------------*/
    bool    isTextNearCenter = false;
    bool    isTextInsideCircle = IsTextInCircle (pointArray, &isTextNearCenter, textCenter);
    bool    isDefaultInside = 3 != pDwgDim->dimatfit ();

    return  isTextNearCenter && isDefaultInside;
    }

/*---------------------------------------------------------------------------------**//**
* Check of a radial/diametric dimension text is inside the circle it measures
*
* @bsimethod                                                    Don.Fu          02/04
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            IsTextInCircle
(
const DPoint3dArray&            pointArray,
bool*                           pIsNearCenter = NULL,
DPoint3dCP                      testPoint = NULL
) const
    {
    /*-----------------------------------------------------------------------------------
    This method attempts to determine if the text position of a radial dimension is inside
    the circle/arc measured by the dimension.  The input pDefpoints are assumed to be
    correct, i.e. pDefpoint[0] to be the arc center and pDefpoint[1] to be the def location
    which has to be on the radial dimension.  Invalid text location should be corrected
    prior to calling this method (in ConvertRadialDimension).
    -----------------------------------------------------------------------------------*/
    DSegment3d  centerToEdge = DSegment3d::From (pointArray[0], pointArray[1]);

    DPoint3d    textPoint;
    double      param = 0.0;
    centerToEdge.ProjectPoint (textPoint, param, NULL == testPoint ? pointArray[2] : *testPoint);

    // if the point is near the center of arc/circle, param should be very small
    if (NULL != pIsNearCenter)
        *pIsNearCenter = param < 0.1;

    return  param >= (-1.0-1.e-8) && param <= (1.0+1.e-8);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/04
+---------------+---------------+---------------+---------------+---------------+------*/
void                            UpdateCirdimDimstyle
(
AcDbDimension*                  pDwgDim,
DimensionElmCP                  pDgnDim,
const DPoint3dArray&            pointArray
) const
    {
    /*-----------------------------------------------------------------------------------
    Decide when a diametric dimension has its dimension line connected from edge to edge
    -----------------------------------------------------------------------------------*/

    // for unextended dimension, make sure dimtmove!=2, in all cases except for below cases
    if (0 == pDgnDim->tmpl.first_term && 2 == pDwgDim->dimtmove())
        pDwgDim->setDimtmove (1);

    if (IsTextInCircle(pointArray))
        {
        /*-------------------------------------------------------------------------------
        text sits inside the circle (between two edge points), set dimtmove to connect
        edges by dimline. dimtofl has not effect in this case.

        needed for diameter dimensions with any term option or radial dimensions with a 
        option to keep text location correctly.  However, setting dimtmove=2 causes text
        to change from above to inline.
        -------------------------------------------------------------------------------*/
        if (DimensionType::Diameter != (DimensionType)pDgnDim->dimcmd && DimensionType::DiameterExtended != (DimensionType)pDgnDim->dimcmd &&
            pDgnDim->tmpl.first_term > 0 && 2 != pDwgDim->dimtmove())
            pDwgDim->setDimtmove (2);
        }
    else
        {
        // text is outside of the circle, both dimtofl and dimtmove control dimline display
        if (pDgnDim->tmpl.first_term > 0)
            {
            // set dimtofl to connect edges by dimline
            if (1 != pDwgDim->dimtofl())
                pDwgDim->setDimtofl (1);

            // for extended dimension, only dimtmove=0 or 1 can extend leader out of the circle
            if (2 == pDwgDim->dimtmove())
                pDwgDim->setDimtmove (1);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ConvertRadialDimension
(
AcDbDimension*                  pDwgDim,
DPoint3dArray&                  pointArray,
double                          angle,
bool                            hasEcs,
RotMatrixCR                     defMatrix,
DPoint3dCR                      textCenter,
RotMatrixCR                     textMatrix,
DimDerivedDataCP                pDerivedData
) const
    {
    DimensionElmCP  pDgnDim = (DimensionElmCP) m_sourceElementHandle->GetElementCP ();
    if (NULL == pDgnDim)
        return  CantAccessMstnElement;

    AcDbRadialDimension*   pDwgRaddim;
    if (NULL == (pDwgRaddim = AcDbRadialDimension::cast (pDwgDim)))
        return  UnexpectedDimensionType;

    // Get radius of maesuring circle:
    double  radius = pointArray[0].Distance (pointArray[1]);

    DPoint3d  point = pointArray[1];
    if (NULL != pDerivedData->pArcDefPoint)
        {
        point = *pDerivedData->pArcDefPoint;
        m_transformFromDgn->Multiply (point);
        // update pDefpoints as it may contain bad user point which will be used again, such as a case in TR 321126.
        pointArray[1] = point;
        }

    /* Set group code 10: */
    pDwgRaddim->setCenter (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[0]));

    // Set point group code 15:
    pDwgRaddim->setChordPoint (RealDwgUtil::GePoint3dFromDPoint3d(point));

    // Set text middle point, group code 11
    SetTextLocation (pDwgDim, pDgnDim, textCenter, angle, hasEcs, defMatrix);

    // check and reset some radial specific variables
    UpdateCirdimDimstyle (pDwgDim, pDgnDim, pointArray);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ConvertDiametricDimension
(
AcDbDimension*                  pDwgDim,
const DPoint3dArray&            pointArray,
double                          angle,
bool                            hasEcs,
RotMatrixCR                     defMatrix,
DPoint3dCR                      textCenter,
RotMatrixCR                     textMatrix
) const
    {
    DimensionElmCP  pDgnDim = (DimensionElmCP) m_sourceElementHandle->GetElementCP ();
    if (NULL == pDgnDim)
        return  CantAccessMstnElement;

    AcDbDiametricDimension*   pDwgDiamdim;
    if (NULL == (pDwgDiamdim = AcDbDiametricDimension::cast (pDwgDim)))
        return  UnexpectedDimensionType;

    // Get radius of maesuring circle:
    double  radius = pointArray[0].distance (&pointArray[1]);

    DPoint3d    point;

    /* Set group code 10: */
    if (SUCCESS != GetPointOnLine (point, pointArray[0], pointArray[2], radius))
        point = pointArray[1];

    pDwgDiamdim->setFarChordPoint (RealDwgUtil::GePoint3dFromDPoint3d(point));

    // Set point group code 15:
    if (SUCCESS != GetPointOnLine (point, pointArray[0], pointArray[2], -radius))
        GetPointOnLine (point, pointArray[0], pointArray[1], radius);

    pDwgDiamdim->setChordPoint (RealDwgUtil::GePoint3dFromDPoint3d(point));

    // Set text middle point, group code 11
    SetTextLocation (pDwgDim, pDgnDim, textCenter, angle, hasEcs, defMatrix);

    // check and reset some diametric specific variables
    UpdateCirdimDimstyle (pDwgDim, pDgnDim, pointArray);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           09/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                            JustifyTextDefinitionPoint
(
DPoint3dR                       textCenter,
DimensionElmCP                  pDgnDim,
DSegment3dCR                    witnessHeight
) const
    {
    if (pDgnDim->flag.embed)
        {
        /* do not move text point if embedded on dimension line */
        return;
        }

    double  param = 0.0;
    witnessHeight.projectPoint (&textCenter, &param, &textCenter);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ConvertDiamdimToLindim
(
AcDbDimension*                  pDwgDim,
const DPoint3dArray&            pointArray,
double                          angle,
bool                            hasEcs,
RotMatrixCR                     defMatrix,
RotMatrixCR                     rotMatrix,
DPoint3dCR                      textCenter,
RotMatrixCR                     textMatrix
) const
    {
    DimensionElmCP  pDgnDim = (DimensionElmCP) m_sourceElementHandle->GetElementCP ();
    if (NULL == pDgnDim)
        return  CantAccessMstnElement;

    AcDbRotatedDimension* pDwgLindim;
    if (NULL == (pDwgLindim = AcDbRotatedDimension::cast (pDwgDim)))
        return  UnexpectedDimensionType;

    DPoint3d    textDefpoint = textCenter;

    double  radius = pointArray[0].distance (&pointArray[1]);

    DPoint3d    xAxis, yAxis, zAxis;
    rotMatrix.getColumn ((DVec3d*)&xAxis, 0);
    rotMatrix.getColumn ((DVec3d*)&yAxis, 1);
    rotMatrix.getColumn ((DVec3d*)&zAxis, 2);

    DPoint3d    point;
    point.sumOf (&pointArray[0], &xAxis, radius);

    // set group codes 13 & 14
    pDwgLindim->setXLine1Point (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[1]));
    pDwgLindim->setXLine2Point (RealDwgUtil::GePoint3dFromDPoint3d(point));

    DPoint3d    dimlineDefpoint;
    if (DimensionType::DiameterParallel == (DimensionType)pDgnDim->dimcmd)
        {
        DSegment3d  witSeg, dimlineSeg;
        DPoint3d    delta;

        // make witness line and dimension line vectors for intersection calculation
        witSeg.setStartPoint (&point);
        delta.sumOf (&point, &yAxis);
        witSeg.setEndPoint (&delta);

        point = pointArray[2];
        dimlineSeg.setStartPoint (&point);
        delta.sumOf (&point, &xAxis);
        dimlineSeg.setEndPoint (&delta);

        // set group code 10 = dim line definition point, by finding intersection of witness line and dimension line
        if (DSegment3d::closestApproach (NULL, NULL, &dimlineDefpoint, &point, &witSeg, &dimlineSeg))
            pDwgLindim->setDimLinePoint (RealDwgUtil::GePoint3dFromDPoint3d(dimlineDefpoint));
        else
            pDwgLindim->setDimLinePoint (RealDwgUtil::GePoint3dFromDPoint3d(point));
        }
    else
        {
        // perpdendicular diameter dimension has no witness lines.
        // so group code 10 is the same as group code 14:
        pDwgLindim->setDimLinePoint (pDwgLindim->xLine2Point());

        // suppress witness lines
        if (DimensionType::DiameterPerpendicular == (DimensionType)pDgnDim->dimcmd)
            {
            pDwgLindim->setDimse1 (true);
            pDwgLindim->setDimse2 (true);
            }

        DSegment3d  dimlineSeg;
        dimlineSeg.setStartPoint (&pointArray[0]);
        dimlineSeg.setEndPoint (&pointArray[1]);

        // place text def point on dimension line
        JustifyTextDefinitionPoint (textDefpoint, pDgnDim, dimlineSeg);
        }

    // set group code 11 = text def point
    SetTextLocation (pDwgDim, pDgnDim, textDefpoint, angle, hasEcs, defMatrix);

    // set linear dimension rotation angle
    pDwgLindim->setRotation (bsiTrig_getPositiveNormalizedAngle(angle));

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           04/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                            GetWitnessVector
(
DSegment3dR                     witHeight,
DPoint3dR                       gc10Defpoint,
const DPoint3dArray&            pointArray,
DPoint3dCR                      xAxis,
DPoint3dCR                      yAxis,
double                          height
) const
    {
    // Project the height to the 1st witness line to get the 1st dimline definition point
    DPoint3d    startPoint;
    startPoint.sumOf (&pointArray[0], &yAxis, height);

    witHeight.initFromOriginAndDirection (&startPoint, &xAxis);

    // get the 2nd witness line segment
    DSegment3d  witness2;
    witness2.initFromOriginAndDirection (&pointArray[1], &yAxis);

    // Shoot the 1st point across to the 2nd witness line to get group code 10
    double      param0=0, param1=0;

    gc10Defpoint = pointArray[1];

    DSegment3d::closestApproach (&param0, &param1, &gc10Defpoint, &startPoint, &witHeight, &witness2);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/10
+---------------+---------------+---------------+---------------+---------------+------*/
void                            Redefine2ndDefpoint
(
DPoint3dR                       defPoint2,
DPoint3dCR                      defPoint1,
DPoint3dCR                      xAxis,
DPoint3dCR                      yAxis
) const
    {
    /*-----------------------------------------------------------------------------------
    Find the intersection of line segment1, which starts from defPoint2 and extends along
    xAxis, and line segment2, which starts from defPoint1 and extends along yAxis.
    -----------------------------------------------------------------------------------*/
    DSegment3d  segment1, segment2;
    segment1.initFromOriginAndDirection (&defPoint1, &xAxis);
    segment2.initFromOriginAndDirection (&defPoint2, &yAxis);

    double      param1=0, param2=0;
    DSegment3d::closestApproach (&param1, &param2, NULL, &defPoint2, &segment1, &segment2);
    }
    
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/03
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            IsDimlineRunningReversed
(
const DPoint3dArray&            pointArray,
RotMatrixCR                     defMatrix
) const
    {
    DVec3d    dimlineDir, xAxis;

    dimlineDir.NormalizedDifference (pointArray[1], pointArray[0]);

    defMatrix.GetColumn (xAxis, 0);

    return  xAxis.DotProduct(dimlineDir) < -TOLERANCE_VectorEqual;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           08/01
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                       GetTextOffset
(
DPoint3dR                       textPoint,
DPoint3dCR                      dimlineDefpoint,
const DPoint3dArray&            pointArray,
DimensionElmCP                  pDgnDim,
RotMatrixCR                     defMatrix,
const double                    dimlineLength,
DimDerivedDataCP                pDerivedData
) const
    {
     /*--------------------------------------------------------------------------------------------------
     This method determines dimension text position by these scenarios:

         1) Use the input text point without any change, by returning SUCCESS
         2) Recalculate the text point with text offset, by returning SUCCESS
         3) Need to justify the text position, i.e. forcing text on dimension line, by returning BSIERROR
     ---------------------------------------------------------------------------------------------------*/
    if (NULL != mdlDim_getOptionBlock(*m_sourceElementHandle, ADBLK_EXTOFFSET, NULL))
        {
        DPoint2d        offset2d;
        int             segNo = 0;
        if (DIM_LINEAR(pDgnDim->dimcmd))
            segNo = 1;
        else if (DIM_ANGULAR((DimensionType)pDgnDim->dimcmd))
            segNo = 2;

        if (SUCCESS == m_sourceDimHandler->GetTextOffset(*m_sourceElementHandle, segNo, offset2d) && fabs(offset2d.y) > TOLERANCE_PointEqual)
            {
            DPoint3d    textOffset = DPoint3d::From(offset2d.x, offset2d.x, 0);

            // when there is no chain added to text, no need to find the leader attachment point
            if (DIMSTYLE_VALUE_BallAndChain_ChainType_None == pDerivedData->pChainType[0])
                return  SUCCESS;

             /*-------------------------------------------------------------------------------------------
             A chain/leader is added to text, but if the text is docked on dimension line, we want the same
             effect by setting text position on the dimension line, i.e "justifying" the text.
             -------------------------------------------------------------------------------------------*/
             if (pDerivedData->pIsTextDocked[0])
                 return  BSIERROR;

            // the offset we get contains text margin but ACAD has def point right at text edge without text margin
            if (textOffset.x < 0.5 * dimlineLength * m_fromDgnContext->GetScaleToDGN())
                textOffset.x -= pDgnDim->geom.textMargin;
            else
                textOffset.x += pDgnDim->geom.textMargin;

            if (IsDimlineRunningReversed(pointArray, defMatrix))
                textOffset.x = -textOffset.x;

            defMatrix.Multiply (textOffset);

            // text offset is relative to the first dimension line point
            // therefore there is no translation involved:
            DPoint3d    dimlinePoint = dimlineDefpoint;
            m_transformFromDgn->Multiply (dimlinePoint);

            // in DWG the text location is absolute to the world coordinate system
            textPoint.SumOf (dimlinePoint, textOffset);

            m_transformFromDgn->Multiply (textPoint);

            return  SUCCESS;
            }
        else if (pDerivedData->pIsTextOutside[0] || DIMTEXT_OFFSET == pDgnDim->GetDimTextCP(segNo)->flags.b.just)
            {
            /*-------------------------------------------------------------------------------------------
            Either the text is manually placed, or it is automatically pushed outside of an extension line,
            we will NOT use default text position, i.e. we will call pDim->useSetTextPosition() to place 
            the text right at the same text center position we have calculated.  This is one case in which
            we do not want to force the text position to sit on the dimension line by "justifying" it -
            TRS 297311, 348927.
            -------------------------------------------------------------------------------------------*/
            return  SUCCESS;
            }            
        }

    return  BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           10/01
+---------------+---------------+---------------+---------------+---------------+------*/
double                          GetObliqueAngle
(
DimensionElmCP                  pDgnDim,
RotMatrixCR                     defMatrix
) const
    {
    RotMatrix   slantMatrix;
    m_sourceDimHandler->GetRotationMatrix (*m_sourceElementHandle, slantMatrix);

    // get y-axis of dimension
    DVec3d      xAxis, yAxis;
    slantMatrix.GetColumn (xAxis, 0);
    slantMatrix.GetColumn (yAxis, 1);

    double      dotp = yAxis.DotProduct (xAxis);

    // if the 2 axes are not orthogonal, there is a sheering factor
    if (fabs(dotp) > MIN_AngleRadians && fabs(dotp) < 1.0-MIN_AngleRadians)
        {
        // get oblique angle
        double  oblique = acos (dotp);

        // need to determine the oblique direction
        DVec3d  orthoYAxis, zAxis;
        defMatrix.GetColumn (zAxis, 2);
        orthoYAxis.CrossProduct (zAxis, xAxis);

        // Is dimension y-axis on the same side as the view y-axis?
        if (yAxis.DotProduct(orthoYAxis) < 0)
            oblique *= -1;

        return  oblique;
        }

    return  0.0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            HasRealUserText
(
WStringCR                       string
) const
    {
    // if the only character in the text is the place holder, it's not a user text
    if (string.empty())
        return  false;

    if ('*' == string.at(0))
        {
        for (size_t iChar = 1; iChar < string.length(); iChar++)
            {
            WChar ch = string.at(iChar);

            // Document may have added a data link escape:
            if (' ' != ch && ch > 20)
                return  true;
            }

        return  false;
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                       ReplacePlaceHolder
(
WStringR                        string,
DimensionElmCP                  pDgnDim
) const
    {
    if (!string.empty())
        {
        size_t  index = string.find (L'*');

        if (WString::npos != index)
            {
            WString     replacement = string.substr(0, index) + WString(L"<>") + string.substr(index+1,-1);

            /*---------------------------------------------------------------------------
            Revert the open process that adds R or diamter symbol for circular dimensions because ACAD
            at run time adds them.  We now must remove them from user text.
            ---------------------------------------------------------------------------*/
            if ((DimensionType::Radius == (DimensionType)pDgnDim->dimcmd || DimensionType::Diameter == (DimensionType)pDgnDim->dimcmd ||
                 DimensionType::RadiusExtended == (DimensionType)pDgnDim->dimcmd || DimensionType::DiameterExtended == (DimensionType)pDgnDim->dimcmd) &&
                index > 0)
                {
                WChar     prependChar = replacement.at (index-1);
                size_t      numRemove = 0;

                if ('R' == prependChar)
                    numRemove = 1;
                else if ('c' == prependChar && index > 2 && '%' == replacement.at(index-2) && '%' == replacement.at(index-3))
                    numRemove = 3;

                if (numRemove > 0)
                    replacement.erase (index - numRemove, numRemove);
                }

            string = replacement;

            return  SUCCESS;
            }
        }

    return  BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                            SetSecondaryUnitsUserText
(
WStringR                        string1,
WStringR                        string2,
AcDbDimension*                  pDwgDim
) const
    {
    /*-----------------------------------------------------------------------------------
    ACAD only allows one place holder for both primary and secondary units, regardless
    what the secondary units format is (above-below or before-after).  DIMPOST automatically
    separates them above/below or before/after.  A value place holder for secondary unit
    is invalid, always remove it.
    -----------------------------------------------------------------------------------*/
    size_t      placeHolderIndex = string2.find (L'*');
    if (WString::npos != placeHolderIndex)
        string2.erase (placeHolderIndex, 1);

    /*-----------------------------------------------------------------------------------
    When primary units user text has a value place holder, ACAD displays both primary and
    secondary units.  If the secondary units user text never has a value place holder,
    as in a case where the \X was removed from secondary units user text when a DWG was
    opened and it is now in its reverse course back to DWG, we need to turn off the
    secondary units to prevent secondary value to be displayed.
    -----------------------------------------------------------------------------------*/
    if (placeHolderIndex < 0 && WString::npos != string1.find(L'*'))
        pDwgDim->setDimalt (false);

    /*-----------------------------------------------------------------------------------
    Do not insert an extra \X in user text if all below conditions hold:

    1) the secondary units toggle is on
    2) the user text has a value place holder
    3) dimpost already has a \X
    -----------------------------------------------------------------------------------*/
    AcString    dimpost (pDwgDim->dimpost());
    bool        insertX = WString::npos == string1.find(L'*') || WString::npos == dimpost.find(L"\\X") || !pDwgDim->dimalt();

    string1 += WString(insertX ? L"\\X" : L"") + string2;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/06
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            NeedsUserTextOverride
(
WStringR                        userText,
TextBlockPtr                    textBlock,
AcDbDimension*                  pDwgDim
) const
    {
    /*-----------------------------------------------------------------------------------
    This is an attempt to workaround a text height override issue caused by model units:
    when text height is set in text style, dimension text always inherits height from
    text style whose geometric sizes are however defined in default model.  Dimensions
    and texts placed in other models have different height than that defined in their
    style if the model in which they are placed has different units.  ACAD does not work
    this way therefore dimensions in the other models will appear in wrong size.  For
    these dimensions we need to add a user text which overrides the (wrong) height from
    text style.
    -----------------------------------------------------------------------------------*/
    if (userText.empty())
        return  false;

    bool                bChanged = false;
    DgnTextStylePtr     textStyle = textBlock->GetTextStyle ();
    if (textStyle.IsValid())
        {
        double          heightFromStyle = 1.0;
        if (SUCCESS != textStyle->GetProperty(TextStyle_Height, heightFromStyle))
            return  bChanged;

        double          heightFromText = m_fromDgnContext->GetTextSizeFromTextBlock (NULL, *textBlock.get());

        if (heightFromStyle > TOLERANCE_TextHeight && fabs(heightFromStyle - heightFromText) > TOLERANCE_TextHeight)
            {
            char        overrideChars[50];

            sprintf (overrideChars, "\\H%g;", heightFromText);

            userText.insert (0, WString(overrideChars));

            bChanged = true;
            }
        }

    return  bChanged;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
const AcString                  GetDimensionUserText
(
AcDbDimension*                  pDwgDim,
DimensionElmCP                  pDgnDim
) const
    {
    AcString            textString;

    // for a dropped ordinate dimension, the text point is at the 2nd point:
    int segNo = (DimensionType::Ordinate == (DimensionType)pDgnDim->dimcmd && pDgnDim->nPoints > 1) ? 1 : 0;

    // get text string for primary units
    ITextPartIdPtr      textPartId = DimensionTextPartId::Create (segNo, ADTYPE_TEXT_UPPER, ADSUB_NONE);
    TextBlockPtr        upperTextBlock = m_sourceDimHandler->GetTextPart(*m_sourceElementHandle, *textPartId.get());

    if (upperTextBlock.IsNull() || upperTextBlock->IsEmpty())
        return  textString;

    DwgContextForTextBlock  contextForTextBlock(NULL, m_fromDgnContext, pDwgDim);
    WString                 upperString;
    upperTextBlock->ConvertToMTextCompatibleTextBlock ();
    upperTextBlock->ToMText (upperString, m_modelRef, &contextForTextBlock, m_fromDgnContext->GetScaleFromDGN(), false, false);

    if (!upperString.empty())
        {
        // get text string for secondary units
        if (pDgnDim->flag.dual)
            {
            textPartId = DimensionTextPartId::Create (segNo, ADTYPE_TEXT_LOWER, ADSUB_NONE);
            TextBlockPtr    lowerTextBlock = m_sourceDimHandler->GetTextPart (*m_sourceElementHandle, *textPartId.get());

            // append text string of secondary units by \X
            if (!lowerTextBlock.IsNull() && !lowerTextBlock->IsEmpty())
                {
                WString     lowerString;
                lowerTextBlock->ConvertToMTextCompatibleTextBlock ();
                lowerTextBlock->ToMText (lowerString, m_modelRef, &contextForTextBlock, m_fromDgnContext->GetScaleFromDGN(), false, false);

                if (HasRealUserText(lowerString))
                    SetSecondaryUnitsUserText (upperString, lowerString, pDwgDim);
                }
            }

        // set user text string for the primary units
        if (HasRealUserText(upperString) || NeedsUserTextOverride(upperString, upperTextBlock, pDwgDim))
            {
            // replace place holder for single line text
            ReplacePlaceHolder (upperString, pDgnDim);
            textString.assign (upperString.c_str());
            }

        // AutoCAD does not allow ^ as the last character in the dimension user text from a DXF file.
        // In such case ACAD appends a space char at the end.  We do the same here.
        if (DgnFileFormatType::DXF == m_fromDgnContext->GetFormat())
            {
            unsigned int last = textString.length();
            if (last != 0 && '^' == textString.kwszPtr()[last-1])
                textString += ' ';
            }
        }

    return  textString;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/05
+---------------+---------------+---------------+---------------+---------------+------*/
void                            RemoveCellTransform
(
RotMatrixR                      defMatrix
) const
    {
    // remove type-2 cell transform from raw dimension matrix to avoid double transform
    defMatrix.InitProduct (m_fromDgnContext->GetLocalTransform(), defMatrix);

    DPoint3d    scales;
    RotMatrix   rotation1, rotation2;
    defMatrix.FactorRotateScaleRotate (rotation1, scales, rotation2);
    scales.x = fabs(scales.x);
    scales.y = fabs(scales.y);
    scales.z = fabs(scales.z);

    if (scales.x > TOLERANCE_ZeroScale && scales.y > TOLERANCE_ZeroScale && scales.z > TOLERANCE_ZeroScale &&
        (fabs(scales.x - 1.0) > TOLERANCE_ZeroScale ||
         fabs(scales.y - 1.0) > TOLERANCE_ZeroScale ||
         fabs(scales.z - 1.0) > TOLERANCE_ZeroScale))
        {
        defMatrix.ScaleColumns (defMatrix, 1.0/scales.x, 1.0/scales.y, 1.0/scales.z);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                            GetLinearDimensionData
(
DPoint3dR                       point10,
DPoint3dR                       point14,
DPoint3dR                       point11,
double&                         viewAngle,
DimensionElmCP                  pDgnDim,
const DPoint3dArray&            pointArray,
RotMatrixCR                     defMatrix,
DPoint3dCR                      textCenter,
DimDerivedDataCP                pDerivedData
) const
    {
    // get original DGN dimension's x- and y-axes
    RotMatrix   dgnDefmatrix;
    m_sourceDimHandler->GetRotationMatrix (*m_sourceElementHandle, dgnDefmatrix);
    RemoveCellTransform (dgnDefmatrix);

    DVec3d    xAxis, yAxis;
    dgnDefmatrix.GetColumn (xAxis, 0);
    dgnDefmatrix.GetColumn (yAxis, 1);

    // Determine what type linear dimension this is:
    DPoint3d    viewXAxis;
    GetAxisFromView (viewXAxis, 0, pDgnDim->view);

    viewAngle = acos (fabs(xAxis.DotProduct(viewXAxis)));

    // calculate dimension line height vector, and get group code 10 def point
    double      textOffsetX = pDgnDim->GetDimTextCP(0)->offset * m_fromDgnContext->GetScaleFromDGN();
    DSegment3d  witnessHeight;
    GetWitnessVector (witnessHeight, point10, pointArray, xAxis, yAxis, textOffsetX);

    double      dimlineLength = point10.Distance (witnessHeight.point[0]);

    /*-------------------------------------------------------------------
    Set text middle point, group code 11
    When there is textOffset, i.e. ball & chain present, use it's text
    coordinates.  For other cases, text justification is needed.
    -------------------------------------------------------------------*/
    point11 = textCenter;
    if (SUCCESS != GetTextOffset (point11, witnessHeight.point[0], pointArray, pDgnDim, dgnDefmatrix, dimlineLength, pDerivedData))
        JustifyTextDefinitionPoint (point11, pDgnDim, witnessHeight);

    /*-----------------------------------------------------------------------------------
    For a regular linear dimension, group code 14 is the same as the 2nd def point.

    However, for an arbitrary dimension, if the witness line length is modified after it 
    is initially placed, such an uneven witness lines will cause AutoCAD to produce wrong
    measurement because AutoCAD always measures the distance between two def points, not
    parallel distance between two witness lines.  To make AutoCAD also measure parallel 
    witness lines, we have to project the 1st def point to the 2nd witness line.  This
    redefined point obviously would change the 2nd witness line length, but I believe that
    correct measurement should precede desired display of withness lines.  I don't see
    any other way around this incompatibity.
    -----------------------------------------------------------------------------------*/
    point14 = pointArray[1];
    if (3 == pDgnDim->flag.alignment)
        {
        xAxis.scale (dimlineLength);
        yAxis.scale (dimlineLength);
        Redefine2ndDefpoint (point14, pointArray[0], xAxis, yAxis);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ConvertLinearDimension
(
AcDbDimension*                  pDwgDim,
const DPoint3dArray&            pointArray,
double                          angle,
bool                            hasEcs,
RotMatrixCR                     defMatrix,
DPoint3dCR                      textCenter,
RotMatrixCR                     textMatrix,
DimDerivedDataCP                pDerivedData
) const
    {
    DimensionElmCP  pDgnDim = (DimensionElmCP) m_sourceElementHandle->GetElementCP ();
    if (NULL == pDgnDim)
        return  CantAccessMstnElement;

    DPoint3d    point10, point11, point14;
    double      viewAngle = 0.0;

    // compute dim def points (dxf group code 10 and 14) and text def point (group code 11):
    GetLinearDimensionData (point10, point14, point11, viewAngle, pDgnDim, pointArray, defMatrix, textCenter, pDerivedData);

    RealDwgStatus               status = UnexpectedDimensionType;
    AcDbRotatedDimension*       pRotatedDim;
    AcDbAlignedDimension*       pAlignedDim;

    if (NULL != (pRotatedDim = AcDbRotatedDimension::cast (pDwgDim)))
        {
        // Set point group codes 10, 13 & 14:
        pRotatedDim->setDimLinePoint (RealDwgUtil::GePoint3dFromDPoint3d(point10));
        pRotatedDim->setXLine1Point (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[0]));
        pRotatedDim->setXLine2Point (RealDwgUtil::GePoint3dFromDPoint3d(point14));

        // Set dimension angles:
        // dimrotang - tells nature of dimention (horizontal, vertical, or aligned = group code 50)
        // dimtextrot - rotation angle of dimension text (group code 53)
        // ucsxangle - UCS x-axis angle wrt dimension x-axis (group code 51)
        pRotatedDim->setRotation (bsiTrig_getPositiveNormalizedAngle(angle));

        pRotatedDim->setOblique (GetObliqueAngle(pDgnDim, defMatrix));

        status = RealDwgSuccess;
        }
    else if (NULL != (pAlignedDim = AcDbAlignedDimension::cast (pDwgDim)))
        {
        // Set point group codes 10, 13 & 14:
        pAlignedDim->setDimLinePoint (RealDwgUtil::GePoint3dFromDPoint3d(point10));
        pAlignedDim->setXLine1Point (RealDwgUtil::GePoint3dFromDPoint3d(pointArray[0]));
        pAlignedDim->setXLine2Point (RealDwgUtil::GePoint3dFromDPoint3d(point14));

        pAlignedDim->setOblique (GetObliqueAngle(pDgnDim, defMatrix));

        status = RealDwgSuccess;
        }

    if (RealDwgSuccess == status)
        SetTextLocation (pDwgDim, pDgnDim, point11, angle, hasEcs, defMatrix);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
double                          GetDimensionPlaneDepth
(
DimensionElmCP                  pDgnDim,
RotMatrixCR                     defMatrix
) const
    {
    if (!pDgnDim->dhdr.props.b.is3d || m_fromDgnContext->GetSettings().IsZeroZCoordinateEnforced())
        return  0.0;

    DPoint3d    defPoint1 = pDgnDim->GetPoint (0);

    defPoint1.Scale (m_fromDgnContext->GetScaleFromDGN());
    m_fromDgnContext->GetLocalTransform().Multiply (defPoint1);
    defPoint1.Scale (m_fromDgnContext->GetScaleToDGN());

    defMatrix.MultiplyTranspose (defPoint1);

    return  defPoint1.z;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/04
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            IsAxisOpposite
(
DVec3dCR                        dimAxis,
RotMatrixCR                     viewMatrix,
int                             axisNo
) const
    {
    DVec3d      viewAxis;

    viewMatrix.GetRow (viewAxis, axisNo);

    double  dotp = dimAxis.DotProduct (viewAxis);

    return  dotp < 0.0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/04
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            RevertDimensionAxis
(
RotMatrixR                      newMatrix,
RotMatrixCR                     oldMatrix,
RotMatrixCR                     viewMatrix,
const int                       axisNo
) const
    {
    DVec3d      dimAxis;

    oldMatrix.GetColumn (dimAxis, axisNo);

    // if the dimension and view axes are running on opposite direction, revert the dim axis:
    if (IsAxisOpposite(dimAxis, viewMatrix, axisNo))
        {
        dimAxis.Negate ();
        newMatrix.SetColumn (dimAxis, axisNo);

        // dim matrix updated
        return  true;
        }

    // dim matrix not changed
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/04
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            IsTextMatrixValid
(
RotMatrixR                      textMatrix,
double                          textAngle,
DVec3dCR                        zAxisDim,
DimensionElmCP                  pDgnDim
) const
    {
    // text matrix currently does not include an angle (i.e. neither override nor vertical)
    if (fabs(textAngle) > MIN_AngleRadians || pDgnDim->tmpl.vertical_text)
        return  false;

    DVec3d      zAxisText;
    textMatrix.GetColumn (zAxisText, 2);

    // don't use text matrix if it is out of dimension plane
    if (fabs(1.0 - fabs(zAxisText.DotProduct(zAxisDim))) > MIN_AngleRadians)
        return  false;

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/03
+---------------+---------------+---------------+---------------+---------------+------*/
int                             UpdateDimensionMatrix
(
RotMatrixR                      dimMatrix,
double&                         dimAngle,
RotMatrixR                      rotMatrix,
RotMatrixR                      textMatrix,
AcDbDimension*                  pDwgDim,
const DPoint3dArray&            pointArray
) const
    {
    DimensionElmCP  pDgnDim = (DimensionElmCP) m_sourceElementHandle->GetElementCP ();
    if (NULL == pDgnDim)
        return  0;

    int     axisUpdated = 0; // to record which axis gets updated
    DVec3d  xAxis, yAxis, zAxis, xAxisView, zAxisView;

    // 2D arbitary dimension element stores slant angle in the cooked matrix. Get orthogonal matrix:
    if (3 == pDgnDim->flag.alignment && !pDgnDim->dhdr.props.b.is3d && !rotMatrix.IsOrthogonal())
        {
        rotMatrix.GetColumn (xAxis, 0);
        rotMatrix.GetColumn (zAxis, 2);

        yAxis.CrossProduct (zAxis, xAxis);

        rotMatrix.SetColumn (yAxis, 1);

        axisUpdated |= AXIS_Y;
        }

    RotMatrix       viewMatrix = RotMatrix::FromIdentity ();
    ViewInfoP       viewInfo;
    if (NULL != mdlDim_getOptionBlock(*m_sourceElementHandle, ADBLK_VIEWROT, NULL))
        m_sourceDimHandler->GetViewRotation (*m_sourceElementHandle, viewMatrix);
    else if (NULL != (viewInfo = m_modelRef->GetViewInfo(pDgnDim->view)))
        viewMatrix = viewInfo->GetRotation ();

    /*-----------------------------------------------------------------------------------
    An ordinate dimension always has the x-axis line on dimension line, but can point in
    either directions.  To ensure ACAD to display text properly, we check the x-axis
    against the view's x-axis.  If opposite, reverse the dimension x-axis to avoid text
    running backwards. Do the same for y-axis.
    -----------------------------------------------------------------------------------*/
    if (DimensionType::Ordinate == (DimensionType)pDgnDim->dimcmd)
        {
        RotMatrix   newMatrix = rotMatrix;

        if (RevertDimensionAxis(newMatrix, rotMatrix, viewMatrix, 0))
            axisUpdated |= AXIS_X;

        if (RevertDimensionAxis(newMatrix, rotMatrix, viewMatrix, 1))
            axisUpdated |= AXIS_Y;

        /*-------------------------------------------------------------------------------
        Since V8.11.7 dimcreate seems to place ordinate dimension with WCC matrix but
        with the z-axis opposite to the view.  Negative z-axis flips text in ACAD, so
        we want to use positive z-axis.
        -------------------------------------------------------------------------------*/
        if (RevertDimensionAxis(newMatrix, rotMatrix, viewMatrix, 2))
            axisUpdated |= AXIS_Z;

        // if either axis is reverted, reset the z-axis to ensure the right-hand convention
        if (axisUpdated)
            {
            if (axisUpdated & AXIS_Z)
                {
                newMatrix.GetColumn (zAxis, 2);
                zAxis.Normalize ();
                if (axisUpdated & AXIS_Y)
                    {
                    newMatrix.GetColumn (yAxis, 1);
                    xAxis.NormalizedCrossProduct (yAxis, zAxis);
                    }
                else
                    {
                    newMatrix.GetColumn (xAxis, 0);
                    yAxis.NormalizedCrossProduct (zAxis, xAxis);
                    }

                newMatrix.InitFromColumnVectors (xAxis, yAxis, zAxis);
                }
            else
                {
                newMatrix.GetColumn (xAxis, 0);
                newMatrix.GetColumn (yAxis, 1);

                zAxis.NormalizedCrossProduct (xAxis, yAxis);
                newMatrix.SetColumn (zAxis, 2);

                axisUpdated |= AXIS_Z;
                }
            }

        dimMatrix = newMatrix;

        return  axisUpdated;
        }
    else if (DimensionType::Radius == (DimensionType)pDgnDim->dimcmd || DimensionType::RadiusExtended == (DimensionType)pDgnDim->dimcmd ||
             DimensionType::Diameter == (DimensionType)pDgnDim->dimcmd || DimensionType::DiameterExtended == (DimensionType)pDgnDim->dimcmd)
        {
        /*-------------------------------------------------------------------------------
        The definition matrix for a radial or diametric dimensions comes from the arcs it
        defines.  Thus its x-axis is not necessarily on dimension line.  We want to align
        the x-axis to the dimension line.  The new x-axis should point from center to the
        circle edge if it runs from left to right in view, but point reversed if it runs
        against the view's x-axis, to ensure text to display upward in ACAD.

        In addition to x direction, the z direction can also run opposite against the view
        z-axis (e.g. when arc runs clockwise).  We have to revert the z to postive side
        with the view plane.
        -------------------------------------------------------------------------------*/

        /*-------------------------------------------------------------------------------
        Get a point on dim line: in general the 3rd point defines the direction of the
        dimension line except for an extended dimension whose text may sit at the center,
        in which case the 2nd point should be on dimension line.
        -------------------------------------------------------------------------------*/
        DPoint3d    dimPoint = pointArray[2];

        if (dimPoint.Distance(pointArray[0]) < TOLERANCE_PointEqual)
            dimPoint = pointArray[1];

        xAxis.NormalizedDifference (pointArray[0], dimPoint);

        if (IsAxisOpposite(xAxis, viewMatrix, 0))
            {
            xAxis.Negate ();
            axisUpdated |= AXIS_X;
            }

        rotMatrix.GetColumn (zAxis, 2);

        if (IsAxisOpposite(zAxis, viewMatrix, 2))
            {
            zAxis.Negate ();
            axisUpdated |= AXIS_Z;
            }

        yAxis.NormalizedCrossProduct (zAxis, xAxis);

        axisUpdated |= AXIS_Y;

        dimMatrix.InitFromColumnVectors (xAxis, yAxis, zAxis);

        return  axisUpdated;
        }
    else if (DimensionType::DiameterParallel == (DimensionType)pDgnDim->dimcmd || DimensionType::DiameterPerpendicular == (DimensionType)pDgnDim->dimcmd)
        {
        // x-axis is along dimension points, shooting from edge to center
        xAxis.NormalizedDifference (pointArray[0], pointArray[1]);
        // z-axis is obtained from cross product x-axis with the vector to dim line
        yAxis.NormalizedDifference (pointArray[2], pointArray[1]);
        if (yAxis.IsParallelTo(xAxis))
            {
            rotMatrix.GetColumn (zAxis, 2);
            yAxis.NormalizedCrossProduct (zAxis, xAxis);
            }
        zAxis.NormalizedCrossProduct (xAxis, yAxis);
        if (fabs(zAxis.Magnitude() - 1.0) > TOLERANCE_VectorEqual)
            rotMatrix.GetColumn (zAxis, 2);
        // y-axis is the result of z-axis X x-axis
        yAxis.NormalizedCrossProduct (zAxis, xAxis);

        axisUpdated |= AXIS_X | AXIS_Y | AXIS_Z;

        dimMatrix.InitFromColumnVectors (xAxis, yAxis, zAxis);

        // also update the original matrix which will be used in later dimension process
        rotMatrix = dimMatrix;
        }
    else if (DIM_ANGULAR((DimensionType)pDgnDim->dimcmd))
        {
        /*-------------------------------------------------------------------------------
        Rebuild angular dimension matrix from scratch because some angular dim tools can
        create completely wrong dimension matrix, Arc Size for instance is among them!
        -------------------------------------------------------------------------------*/
        xAxis.NormalizedDifference (pointArray[1], pointArray[0]);
        yAxis.NormalizedDifference (pointArray[2], pointArray[0]);

        viewMatrix.GetRow (zAxisView, 2);

        /*-------------------------------------------------------------------------------
        Use cross product in place of zAxis.isParallelTo(zAxisView) which uses a constant
        tolerance msGeomConst_smallAngle which seems too tight for some dimensions as
        seen in TR 178864 for example.
        -------------------------------------------------------------------------------*/
        DVec3d  check;
        if (fabs(zAxis.NormalizedCrossProduct(xAxis, yAxis)) < TOLERANCE_VectorEqual ||
            fabs(check.NormalizedCrossProduct(zAxis, zAxisView)) < TOLERANCE_VectorEqual)
            {
            /*---------------------------------------------------------------------------
            Directly use view matrix for dimension matrix either if definition points are
            colinear or if the dimension is on view plane.
            ---------------------------------------------------------------------------*/
            dimMatrix = viewMatrix;
            dimMatrix.Transpose ();
            }
        else
            {
            /*---------------------------------------------------------------------------
            Dimension has a valid plane but it is out of the view plane.  Let dimension
            stay on its plane, but revert z-axis if opposing to view z-axis
            ---------------------------------------------------------------------------*/
            if (zAxis.DotProduct(zAxisView) < 0)
                zAxis.Negate ();

            yAxis.NormalizedCrossProduct (zAxis, xAxis);

            dimMatrix.InitFromColumnVectors (xAxis, yAxis, zAxis);
            }

        axisUpdated |= AXIS_X | AXIS_Y | AXIS_Z;

        return  axisUpdated;
        }

    /*-----------------------------------------------------------------------------------
    DGN dimension matrix in general is completely different than DWG's.  AutoCAD generally
    use a view matrix as a dimension matrix, and may add a UCS angle whenever needed.
    For a rotated dimension, a dimension angle may also be used in a way such that an
    inclined dimension does not need a UCS angle.

    Here we try to rebuild a dimension matrix that works the best in AutoCAD environment
    and make it as if the dimension is created in AutoCAD.  This basically comes down to
    build a matrix that puts text to a readable orientation.  When a dimension plane is
    parallel to its view matrix, we replace dimension matrix with the view matrix and
    we set the angle between their x-axes.  If a dimension is out of the view plane, but
    is in plane with its text matrix, then we either replace dimension matrix with the text 
    matrix when the text's x-axis is parallel to the dimension x-axis, or we use dimension 
    matrix and set a text angle to reflect the difference.
    Another special case is when a dimension is placed on top view from a view that is out
    of the plane with dimension, in which case we simply treat it as top view.  We can do
    all 8 views if we need to, but they are uncommon cases so we skip from them for now.

    Above logic should produce ACAD style matrix and should cover majority DGN dimensions.
    For all other cases, such as text falling out of dimension plane etc, we have little
    choice but directly apply original dimension matrix, which will still end up with a
    correct dimension but may cause editing difficulty in ACAD.  The ultimate solution is
    to eliminate chances such dimensions being created in dimension creation tools.
    -----------------------------------------------------------------------------------*/
    rotMatrix.GetColumn (xAxis, 0);
    rotMatrix.GetColumn (zAxis, 2);
    viewMatrix.GetRow (xAxisView, 0);
    viewMatrix.GetRow (zAxisView, 2);

    double  dotp = zAxis.DotProduct (zAxisView);
    double  textAngle = pDwgDim->textRotation ();

    if (fabs(1.0 - fabs(dotp)) < MIN_AngleRadians)
        {
        // dimension plane is parallel to view plane, redefine dimension matrix with view matrix plus an angle
        dimMatrix = viewMatrix;
        dimMatrix.Transpose ();

        // get dimension angle to its x-axis
        dimAngle = xAxisView.PlanarAngleTo (xAxis, zAxisView);

        axisUpdated |= AXIS_X | AXIS_Y | AXIS_Z;

        return axisUpdated;
        }
    else if (IsTextMatrixValid(textMatrix, textAngle, zAxis, pDgnDim))
        {
        // dimension and view are NOT on the same plane, but dimension and text are - check if dim & text have parallel x-axes:
        DVec3d      textDir;
        textMatrix.GetColumn (textDir, 0);
        textAngle = textDir.DotProduct (xAxis);

        if (fabs(1.0 - fabs(textAngle)) <= MIN_AngleRadians)
            {
            // text is parallel to the dimension line - use text matrix as dimension matrix for aligned dimensions
            dimMatrix = textMatrix;

            dimMatrix.GetColumn (xAxisView, 0);

            // get dimension angle to its x-axis
            dimAngle = xAxisView.PlanarAngleTo (xAxis, zAxisView);

            if (fabs(dimAngle) > MIN_AngleRadians)
                axisUpdated |= AXIS_X | AXIS_Y | AXIS_Z;
            }
        else
            {
            // text has an angle to the dimension line, e.g. a case in TFS# 7408, use dimension matrix, but add a text angle:
            dimMatrix = rotMatrix;

            textAngle = xAxis.PlanarAngleTo (textDir, zAxis);

            pDwgDim->setTextRotation (textAngle);
            }

        return axisUpdated;
        }
    else if (fabs(zAxis.z - 1.0) < TOLERANCE_VectorEqual)
        {
        // dimension is on top view, simply use identity matrix plus an angle
        dimMatrix.InitIdentity ();

        xAxisView.Init (1, 0, 0);
        zAxisView.Init (0, 0, 1);

        // get dimension angle to its x-axis
        dimAngle = xAxisView.PlanarAngleTo (xAxis, zAxisView);

        if (fabs(dimAngle) > MIN_AngleRadians)
            axisUpdated |= AXIS_X | AXIS_Y;

        return axisUpdated;
        }

    // default all other cases to dimension matrix
    dimMatrix = rotMatrix;

    return  axisUpdated;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/03
+---------------+---------------+---------------+---------------+---------------+------*/
Symbology                       GetWitnessSymbology
(
DimensionElmCP                  pDgnDim,
int                             iPoint
) const
    {
    static Symbology    witSymb;
    bool                useAltSymb = false;
    m_sourceDimHandler->GetWitnessUseAltSymbology (*m_sourceElementHandle, iPoint, useAltSymb);

    witSymb.color  = useAltSymb ? pDgnDim->altSymb.color  : COLOR_BYCELL;
    witSymb.weight = useAltSymb ? pDgnDim->altSymb.weight : WEIGHT_BYCELL;
    witSymb.style  = useAltSymb ? pDgnDim->altSymb.style  : STYLE_BYCELL;

    return  witSymb;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/04
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            IsInlineTextForced
(
AcDbDimension*                  pDwgDim,
DimensionElmCP                  pDgnDim,
DimDerivedDataP                 pDerivedData
) const
    {
    // ordinate dimension always respects to the embed text flag, regardless docking status
    if (DimensionType::Ordinate == (DimensionType)pDgnDim->dimcmd)
        return  false;

    // ball&chain without an elbow has free text location.
    if (2 == pDwgDim->dimtmove())
        return  false;

    // inline flag is forced when ball&chain is active and dimension text is not docked.
    if (NULL != mdlDim_getOptionBlock (*m_sourceElementHandle, ADBLK_EXTOFFSET, NULL))
        {
        bool    isTextDocked = false;

        if (SUCCESS == mdlDimDerivedData_getIsTextDocked(&isTextDocked, pDerivedData, 0))
            return  !isTextDocked;

        // text is not docked - force text to be inline.
        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/05
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            IsAlternateLabelApplied
(
double                          uorValue,
DimAltFmtBlock *                pAltfmt
) const
    {
    if (NULL == pAltfmt)
        return  false;
    else if (fabs (uorValue - pAltfmt->thresholdValue) < TOLERANCE_UORPointEqual)
        return  0 != pAltfmt->flags.equalToThreshold;       // =
    else if (uorValue > pAltfmt->thresholdValue)
        return  0 != pAltfmt->flags.greaterThanThreshold;   // >
    else
        return  0 == pAltfmt->flags.greaterThanThreshold;   // <
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           02/05
+---------------+---------------+---------------+---------------+---------------+------*/
void                            ApplyAlternateLabel
(
AcDbDimension*                  pDwgDim,
DimensionElmCP                  pDgnDim,
DimDerivedDataCP                pDerivedData
) const
    {
    DimAltFmtBlock  *pAltfmt = (DimAltFmtBlock*) mdlDim_getOptionBlock (*m_sourceElementHandle, ADBLK_ALTFORMAT, NULL);
    if (NULL == pAltfmt)
        return;

    double              unitScale = pDgnDim->GetScale ();
    double              roundOff = 0.0, uorValue = pDerivedData->pDimValues[0];
    // The stroke logic applies dimscale to measurement in threshold check, so should we here:
    if (unitScale > TOLERANCE_ZeroScale)
        uorValue *= unitScale;

    DimensionStylePtr   pStyle;

    // translate primary unit's alternative label parameters to override unit settings
    if (IsAlternateLabelApplied(uorValue, pAltfmt) && !(pStyle = m_sourceDimHandler->GetDimensionStyle(*m_sourceElementHandle)).IsNull())
        {
        DimensionStyleP dgnDimstyle = pStyle.get ();

        auto& dgnStyleData = DwgConversionDataAccessor::GetDimStyleSettingsR (*dgnDimstyle);
        dgnStyleData.ad1.format.adp_nomastunits   = pAltfmt->flags.adp_nomastunits;
        dgnStyleData.ad1.format.adp_delimiter     = pAltfmt->flags.adp_delimiter;
        dgnStyleData.ad1.format.adp_label         = pAltfmt->flags.adp_label;
        dgnStyleData.ad1.format.adp_subunits      = pAltfmt->flags.adp_subunits;
        dgnStyleData.ad1.primaryAccuracy          = pAltfmt->flags.accuracy;

        // reset unit
        pDwgDim->setDimlunit (m_fromDgnContext->GetDimstyleUnits(dgnDimstyle, m_modelRef, true));

        // reset accuracy
        pDwgDim->setDimdec (m_fromDgnContext->GetPrecisionWithRoundoff(&roundOff, pAltfmt->flags.accuracy));

        // reset dimlfac if there is a unit scale
        if (pDwgDim->dimlfac() > TOLERANCE_ZeroScale)
            {
            unitScale = m_fromDgnContext->GetDimstyleUnitConversionScale (pDwgDim->dimlunit(), dgnDimstyle, m_modelRef);

            double  dimScale = 1.0;
            if (SUCCESS == pStyle->GetDoubleProp(dimScale, DIMSTYLE_PROP_General_DimensionScale_DOUBLE) && dimScale > TOLERANCE_ZeroScale)
                unitScale *= dimScale;

            pDwgDim->setDimlfac (unitScale);
            }
        }

    pAltfmt = (DimAltFmtBlock*) mdlDim_getOptionBlock (*m_sourceElementHandle, ADBLK_SECALTFORMAT, NULL);
    if (NULL == pAltfmt)
        return;

    // translate secondary unit's alternative label parameters to override unit settings
    if (IsAlternateLabelApplied(uorValue, pAltfmt) && (!pStyle.IsNull() || !(pStyle = m_sourceDimHandler->GetDimensionStyle(*m_sourceElementHandle)).IsNull()))
        {
        DimensionStyleP dgnDimstyle = pStyle.get ();

        auto& dgnStyleData = DwgConversionDataAccessor::GetDimStyleSettingsR (*dgnDimstyle);
        dgnStyleData.ad1.format.adp_nomastunits2   = pAltfmt->flags.adp_nomastunits;
        dgnStyleData.ad1.format.adp_delimiter2     = pAltfmt->flags.adp_delimiter;
        dgnStyleData.ad1.format.adp_label2         = pAltfmt->flags.adp_label;
        dgnStyleData.ad1.format.adp_subunits2      = pAltfmt->flags.adp_subunits;
        dgnStyleData.ad1.secondaryAccuracy         = pAltfmt->flags.accuracy;

        // reset unit
        pDwgDim->setDimlunit (m_fromDgnContext->GetDimstyleUnits(dgnDimstyle, m_modelRef, false));

        // reset accuracy
        pDwgDim->setDimaltd (m_fromDgnContext->GetPrecisionWithRoundoff(&roundOff, pAltfmt->flags.accuracy));

        // needs work for secondary unit scale factor
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                            UpdateDimensionStyledata
(
AcDbDimension*                  pDwgDim,
DimensionElmCP                  pDgnDim,
DimDerivedDataP                 pDerivedData
) const
    {
    /*-----------------------------------------------------------------------------------
    Apply alternate label per threshold
    -----------------------------------------------------------------------------------*/
    if (DIM_LINEAR(pDgnDim->dimcmd) && pDgnDim->text.b.hasAltFormat)
        ApplyAlternateLabel (pDwgDim, pDgnDim, pDerivedData);

    /*-----------------------------------------------------------------------------------
    Calculate a positive DIMEXO from a negative extension line offset for linear dimension
    -----------------------------------------------------------------------------------*/
    if (DIM_LINEAR(pDgnDim->dimcmd) && pDwgDim->dimexo() < 0.0)
        {
        double  extOffset = pDgnDim->GetDimTextCP(0)->offset * m_fromDgnContext->GetScaleFromDGN();

        // Revert annotation scale which should have been applied in style
        double  annoScale = 1;
        if (mdlDim_overallGetModelAnnotationScale(&annoScale, *m_sourceElementHandle) && annoScale > 0.0)
            extOffset /= annoScale;

        extOffset += pDwgDim->dimexo();

        pDwgDim->setDimexo (fabs(extOffset));
        }

    /*-----------------------------------------------------------------------------------
    Ball&chain is not affected by the embed flag(vertical text location), in fact it does
    not currently support underline.  In other words if ball&chain is set, dimensions
    placed in MicroStation ignore the text vertical location flag, unless it is docked.

    So, dimtad set in style can be wrong for a dimension which uses ball&chain.

    This can be seen by setting ball&chain to use elbow line and text location to above
    dimension line.  When text is moved away from default location, it will turn from above
    to inline.  This is because text above dimension line is not supported for ball&chain
    (which should effectively become underline text).  It does not however affect ball&chain
    without an elbow line or curve, in which case the text location is not restrained by
    the elbow.

    As such, whenever we begin to support underline for ball&chain with elbow in core we
    shall remove this code.
    -----------------------------------------------------------------------------------*/
    if (0 != pDwgDim->dimtad() && IsInlineTextForced(pDwgDim, pDgnDim, pDerivedData))
        pDwgDim->setDimtad (0);

    int         iPoint1=0, iPoint2=1;
    Symbology   witSymb;

    // get point numbers representing left and right of a linear/angular dimension witness lines
    if (DIM_ANGULAR((DimensionType)pDgnDim->dimcmd))
        {
        iPoint1++;
        iPoint2++;
        }
    else if (!DIM_LINEAR(pDgnDim->dimcmd) && !DIM_ORDINATE((DimensionType)pDgnDim->dimcmd))
        return;

    if (DIM_ORDINATE((DimensionType)pDgnDim->dimcmd))
        {
        // for ordinate dimension, extension line in DWG is the dimension line in DGN
        witSymb = pDgnDim->dhdr.symb;
        }
    else
        {
        // if there is a local override which applies to both witness lines, or any one witness
        // with the other one not displayed, set them here:
        if (!pDgnDim->GetDimTextCP(iPoint1)->flags.b.noWitness)
            witSymb = GetWitnessSymbology (pDgnDim, iPoint1);
        else if (!pDgnDim->GetDimTextCP(iPoint2)->flags.b.noWitness)
            witSymb = GetWitnessSymbology (pDgnDim, iPoint2);
        else
            return;
        }

    bool    isColorOverridden = false, isWeightOverridden = false, isStyleOverridden = false;
    if (m_fromDgnContext->UseLevelSymbologyOverrides())
        m_fromDgnContext->GetFileHolder().GetLayerIndex()->GetSymbologyOverrides (&isColorOverridden, &isStyleOverridden, &isWeightOverridden, pDgnDim->ehdr.level);

    AcCmColor color = m_fromDgnContext->GetColorFromDgn (isColorOverridden ? COLOR_BYLEVEL : witSymb.color, pDwgDim->dimclre().colorIndex());
    if (color != pDwgDim->dimclre())
        pDwgDim->setDimclre (color);
    AcDb::LineWeight weight = m_fromDgnContext->GetLineWeightFromDgn (isWeightOverridden ? WEIGHT_BYLEVEL : witSymb.weight, pDwgDim->dimlwe());
    if (weight != pDwgDim->dimlwe())
        pDwgDim->setDimlwe (weight);
    AcDbObjectId    ltype = m_fromDgnContext->GetLineTypeFromDgn (isStyleOverridden ? STYLE_BYLEVEL : witSymb.style);
    if (ltype != pDwgDim->dimltex1())
        pDwgDim->setDimltex1 (ltype);
    if (ltype != pDwgDim->dimltex2())
        pDwgDim->setDimltex2 (ltype);

    // check dimension line suppression
    DimSegmentFlagOverrides     segOverrides;

    if (SUCCESS == mdlDim_getSegmentFlagOverride (&segOverrides, *m_sourceElementHandle, 0))
        {
        if ((segOverrides.modifiers & SEGMENTFLAG_Override_SuppressLeftDimLine) && !pDwgDim->dimsd1())
            pDwgDim->setDimsd1 (true);
        if ((segOverrides.modifiers & SEGMENTFLAG_Override_SuppressRightDimLine) && !pDwgDim->dimsd2())
            pDwgDim->setDimsd2 (true);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/04
+---------------+---------------+---------------+---------------+---------------+------*/
void                            FindBoxCentroid
(
DPoint3dR                       center,
DPoint3dCR                      origin,
RotMatrixCR                     matrix,
DPoint2dCR                      boxSize
) const
    {
    DVec3d      delta;

    delta.x = 0.5 * boxSize.x;
    delta.y = 0.5 * boxSize.y;
    delta.z = 0.0;

    matrix.Multiply (delta);

    center.SumOf (origin, delta);

    m_transformFromDgn->Multiply (center);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/04
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                       GetTextMiddlePoint
(
DPoint3dR                       textCenter,
RotMatrixR                      textMatrix,
DimDerivedDataP                 pDerivedData,
const DPoint3dArray&            pointArray,
DimensionElmCP                  pDgnDim
) const
    {
    // initialize the center and matrix
    if (pDgnDim->nPoints > 2)
        {
        textCenter.SumOf (pointArray[1], pointArray[2]);
        textCenter.Scale (0.5);
        }
    else if (pDgnDim->nPoints > 1)
        {
        textCenter.SumOf (pointArray[0], pointArray[1]);
        textCenter.Scale (0.5);
        }
    else
        textCenter = pointArray[0];
    textMatrix.InitIdentity ();

    DPoint3d    origin, xAxis, zAxis;
    DPoint2d    textSize;
    int         segmentNo = 0;

    if (DIM_ORDINATE((DimensionType)pDgnDim->dimcmd))
        segmentNo = GetOrdinatePointNumber (pDgnDim);

    // get primary text box
    StatusInt   status = mdlDimDerivedData_getTextBox (pDerivedData, &origin, &xAxis, &zAxis, &textSize, true, segmentNo);
    if (SUCCESS != status)
        return  status;

    // get text matrix
    DPoint3d    yAxis;
    yAxis.NormalizedCrossProduct (zAxis, xAxis);

    textMatrix.InitFromColumnVectors (DVec3d::From(xAxis), DVec3d::From(yAxis), DVec3d::From(zAxis));

    // find the center point of primary text
    FindBoxCentroid (textCenter, origin, textMatrix, textSize);

    if (DIMSTYLE_VALUE_BallAndChain_ChainType_None != pDerivedData->pChainType[0] && !pDerivedData->pIsTextDocked[0] && nullptr != pDerivedData->pChainTextPoint[0])
        {
        /*-------------------------------------------------------------------------------------------------
        For an effective ball & chain with a leader, the attachment point needs to be on left or right side 
        of the text, instead of the center point.
        -------------------------------------------------------------------------------------------------*/
        textCenter = *pDerivedData->pChainTextPoint[0];
        m_transformFromDgn->Multiply (textCenter);
        }

    // get the secondary text box
    status = mdlDimDerivedData_getTextBox (pDerivedData, &origin, &xAxis, &zAxis, &textSize, false, segmentNo);
    if (SUCCESS != status)
        return  SUCCESS;

    // find the center point of secondary text
    DPoint3d    center2;
    FindBoxCentroid (center2, origin, textMatrix, textSize);

    // get the center of primary and secondary text union
    textCenter.Add (center2);
    textCenter.Scale (0.5);

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/04
+---------------+---------------+---------------+---------------+---------------+------*/
void                            ProjectPointToPlane
(
DPoint3dR                       textCenter,
RotMatrixCR                     textMatrix,
RotMatrixCR                     defmatrix,
double                          zDepth
) const
    {
    DVec3d          zAxisDim, zAxisText;

    defmatrix.GetColumn (zAxisDim, 2);
    textMatrix.GetColumn (zAxisText, 2);

    // project point to plane only if the 2 planes are not parallel
    if (!zAxisDim.IsParallelTo(zAxisText))
        {
        defmatrix.MultiplyTranspose (textCenter);
        textCenter.z = zDepth;
        defmatrix.Multiply (textCenter);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/05
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            NeedHorizontalRotation
(
const DimensionElm *            pDgnDim
) const
    {
    switch (pDgnDim->dimcmd)
        {
        case DimensionType::Diameter:
        case DimensionType::DiameterExtended:
        case DimensionType::Radius:
        case DimensionType::RadiusExtended:
            {
            /*---------------------------------------------------------------------------
            When a circular dimension has "horizontal" text, it is horizontal to the x-axis
            of the view.  Since we have redefined def matrix for circular dimensions the
            ucsAngle should not be used as it redefines the orientation of the text.
            ---------------------------------------------------------------------------*/
            if (pDgnDim->flag.horizontal)
                return  false;
            break;
            }
        case DimensionType::SizeArrow:
        case DimensionType::SizeStroke:
        case DimensionType::LocateSingle:
        case DimensionType::LocateStacked:
        case DimensionType::CustomLinear:
            {
            /*---------------------------------------------------------------------------
            Arbitrary dimensions do not need groupd code 51. TR 304106.
            ---------------------------------------------------------------------------*/
            if (3 == pDgnDim->flag.alignment)
                return  false;
            break;
            }
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   SetDimensionBlockPosition
(
AcDbDimension*                  pDwgDim,
const DPoint3d&                 clonePoint
) const
    {
    RecordingFiler              filer (70);
    filer.RecordData (pDwgDim);

#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("AcDbDimension data fields from DWGOUT filing:");
    /*-----------------------------------------------------------------------------------
    AcDbDimension data fields from DWGOUT filing:
      0: kDwgSoftPointerId, ffc03478 fccec959 AcDbBlockTableRecord
      1: kDwgUInt32,    0 (0x0)
      2: kDwgHardOwnershipId, NULL
      3: kDwgHardPointerId, ffc01e80 fccee3a1 AcDbLayerTableRecord
      4: kDwgHardPointerId, ffc01c98 fccee1b9 AcDbLinetypeTableRecord
      5: kDwgHardPointerId, ffc035b8 fccec899 AcDbMaterial
      6: kDwgUInt8,    0 (0x0)
      7: kDwgBool false
      8: kDwgBool false
      9: kDwgUInt16,  256 (0x100)
     10: kDwgReal 1.000000
     11: kDwgUInt16,    0 (0x0)
     12: kDwgUInt8,   28 (0x1c)
     13: kDwgBool true
     14: kDwgHardPointerId, NULL
     15: kDwgInt32,    1 (0x1)
     16: kDwgString(ACHAR) ACDB_ANNOTATIONSCALES
     17: kDwgHardPointerId, NULL
     18: kDwgBool false
     19: kDwgBool true
     20: kDwgInt32,    0 (0x0)
     21: kDwgInt16,    3 (0x3)
     22: kDwgBool true
     23: kDwgBool true
     24: kDwgHardPointerId, NULL
     25: AcGePoint2d, 129.255026, -0.000000, 0.000000
     26: kDwgBool true
     27: kDwgReal 0.000000
     28: kDwgHardPointerId, ffc03358 fccece79 AcDbBlockTableRecord
     29: kDwgBool true
     30: kDwgBool false
     31: kDwgBool false
     32: kDwgInt32,    0 (0x0)
     33: kDwgBool false
     34: kDwgInt32,    0 (0x0)
     35: kDwgUInt8,    0 (0x0)
     36: kDwgBool false
     37: kDwgBool false
     38: kDwgBool false
     39: AcGePoint3d, 130.101950, -30.398506, 0.000000
     40: AcGeVector3d, 0.000000, 0.000000, 1.000000
     41: AcGePoint2d, 129.255026, -0.000000, 0.000000
     42: kDwgReal 0.000000
     43: kDwgUInt8,   11 (0xb)
     44: kDwgString(ACHAR)
     45: kDwgReal 0.000000
     46: kDwgHardPointerId, ffc03268 fccecf49 AcDbDimStyleTableRecord
     47: kDwgReal 0.000000
     48: kDwgHardPointerId, ffc03358 fccece79 AcDbBlockTableRecord
     49: AcGeScale3d, 1.000000, 1.000000, 1.000000
     50: kDwgReal 1.570796
     51: kDwgInt32,    5 (0x5)
     52: kDwgInt32,    1 (0x1)
     53: kDwgReal 1.000000
     54: kDwgReal 1.396263
     55: kDwgBool false
     56: kDwgBool false
     57: kDwgBool false
     58: AcGePoint2d, 877600.468070, -709000.681281, 0.000000           <= clone point
     59: AcGePoint2d, 130.101950, -30.398506, 0.000000
     60: AcGePoint3d, 1.840566, -0.000000, 0.000000
     61: AcGePoint3d, 308.258344, -257.115044, 0.000000
     62: AcGePoint3d, 1.840566, -0.000000, 0.000000
     63: AcGePoint3d, 308.258344, 257.115044, 0.000000
    -----------------------------------------------------------------------------------*/
#endif
    FilerDataList&      dataList = filer.GetDataList ();

#if RealDwgVersion == 2009
    int                 index = 58;
#else
    int                 index = 62;
#endif
    Point2dFilerData*   pointData = dynamic_cast <Point2dFilerData *> (dataList[index]);
    if (NULL == pointData)
        return BadDataSequence;

    pointData->SetValue (clonePoint);

    Acad::ErrorStatus   es = filer.PlaybackData (pDwgDim);

#if defined (REALDWG_FILER_DEBUG)
    if (Acad::eOk == es)
        {
        RecordingFiler check (70);
        check.RecordData (pDwgDim);
        check.DumpList ("AcDbDimension after DWGIN filing:");
        }
#endif

    return (Acad::eOk == es) ? RealDwgSuccess : BadDataSequence;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/11
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsDINDimension (DimensionElmCP pDgnDim, DimDerivedDataP pDerivedData) const
    {
    // check for DIN dimension: if it is superscripted, drop the dimension
    int         segNo = DIM_ORDINATE((DimensionType)pDgnDim->dimcmd) ? 1 : 0;
    if (pDgnDim->frmt.superscriptLSD && pDgnDim->flag.trailingZeros && NULL != pDerivedData->pSuperscripted && pDerivedData->pSuperscripted[segNo])
        {
        switch (pDgnDim->dimcmd)
            {
            case DimensionType::SizeArrow:
            case DimensionType::SizeStroke:
            case DimensionType::LocateSingle:
            case DimensionType::LocateStacked:
            case DimensionType::Diameter:
            case DimensionType::DiameterParallel:
            case DimensionType::DiameterPerpendicular:
            case DimensionType::Radius:
            case DimensionType::RadiusExtended:
            case DimensionType::CustomLinear:
            case DimensionType::Ordinate:
            case DimensionType::DiameterExtended:
            case DimensionType::ArcSize:
            case DimensionType::ArcLocation:
                return  true;
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsLeadingDecimalOmitted (DimensionElmCP pDgnDim, DimDerivedDataP pDerivedData) const
    {
    // check for omitted leading delimiter in a decimal value display, say 0.600 displayed as 600
    int         segNo = DIM_ORDINATE((DimensionType)pDgnDim->dimcmd) ? 1 : 0;
    if (pDgnDim->frmt.omitLeadDelim && NULL != pDerivedData->pDelimiterOmitted && pDerivedData->pDelimiterOmitted[segNo])
        return  true;

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/16
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsOverriddenOrdinateDimension (DimensionElm const* pDgnDim) const
    {
    /*-----------------------------------------------------------------------------------
    TerrainModel overrides ordinate dimension value to show z-datum on XY plane, which is
    not supported by ACAD - TFS 560364.
    -----------------------------------------------------------------------------------*/
    if (DimensionType::Ordinate == (DimensionType)pDgnDim->dimcmd)
        {
        DimOrdinateOverrides    overrides;
        if (BSISUCCESS == mdlDim_getOrdinateOverride(&overrides, *m_sourceElementHandle) && 
            (overrides.modifiers & ORDINATE_Override_StartValueX) &&
            fabs(overrides.startValueX) > 1.0)
            return  true;
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/11
+---------------+---------------+---------------+---------------+---------------+------*/
bool            NeedToPreDropDimension (DimensionElm const* pDgnDim, DimDerivedDataP pDerivedData) const
    {
    if (!m_fromDgnContext->GetSettings().DropUnsupportedDimensions())
        return  false;

    if (IsDINDimension(pDgnDim, pDerivedData) || IsLeadingDecimalOmitted(pDgnDim, pDerivedData))
        return  true;
    
    if ((DimensionType::Radius == (DimensionType)pDgnDim->dimcmd || DimensionType::RadiusExtended == (DimensionType)pDgnDim->dimcmd) && !IsRadialDimensionValidForRealDwg(pDgnDim, pDerivedData))
        return  true;

    if (IsOverriddenOrdinateDimension(pDgnDim))
        return  true;

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/11
+---------------+---------------+---------------+---------------+---------------+------*/
bool            NeedToPostDropDimension (AcDbDimension* pDwgDim, DimensionElm const* pDgnDim, DimDerivedDataP pDerivedData, int segmentNo) const
    {
    if (!m_fromDgnContext->GetSettings().DropUnsupportedDimensions())
        return  false;

    int         segNo = DIM_ORDINATE((DimensionType)pDgnDim->dimcmd) ? this->GetOrdinatePointNumber(pDgnDim) : 0;

    DimensionStylePtr       style;
    DimensionHandler const* handler = dynamic_cast<DimensionHandler const*>(&m_sourceElementHandle->GetHandler());
    if (nullptr != handler)
        style = handler->GetDimensionStyle (*m_sourceElementHandle);

    /*---------------------------------------------------------------------------------------
    Check ordinate dimension applied with a user datum and the option of decrement on reverse:
    when the segment is on the reverse side and the measured size is greater than the datum,
    the subtracted result can be a negative dimension value which is not supported in ACAD.
    ---------------------------------------------------------------------------------------*/
    if (DIM_ORDINATE((DimensionType)pDgnDim->dimcmd) && style.IsValid())
        {
        bool    useDatum = false, decreaseOnReverse = false;
        double  datum = 0.0, scale = 1.0;
        if (BSISUCCESS == style->GetBooleanProp(useDatum, DIMSTYLE_PROP_Value_OrdUseDatumValue_BOOLINT) && useDatum &&
            BSISUCCESS == style->GetBooleanProp(decreaseOnReverse, DIMSTYLE_PROP_Value_OrdDecrementReverse_BOOLINT) && decreaseOnReverse &&
            BSISUCCESS == style->GetDistanceProp(datum, DIMSTYLE_PROP_Value_OrdDatumValue_DISTANCE, m_sourceElementHandle->GetDgnModelP()) &&
            BSISUCCESS == style->GetDoubleProp(scale, DIMSTYLE_PROP_General_DimensionScale_DOUBLE))
            {
            // calculate the display value, albeit in UOR's
            double  displayValue = scale * pDerivedData->pDimValues[segNo] + datum;
            if (displayValue < 0.0)
                return  true;
            }
        }

    // check for text origin: if is pushed outside of the location comparing to its DGN origin, drop the dimension:
    if (NULL != pDerivedData->pTextBoxes && pDerivedData->pTextBoxes[segNo].flags.hasPrimary)
        {
        // initialize dwg text center with calculated value, but let RealDWG generate its proxy from which we can get the mtext
        AdimSegmentTextBoxes&   textBox = pDerivedData->pTextBoxes[segNo];
        DPoint3d                dwgTextCenter;
        RealDwgUtil::DPoint3dFromGePoint3d (dwgTextCenter, pDwgDim->textPosition());

        // we will need the proxy block, so we have save the dimension to have the block created:
        Acad::ErrorStatus       es;
        AcDbObjectId            dimId = pDwgDim->objectId ();
        // if not already saved, do it now:
        if (!dimId.isValid() && !(dimId = m_fromDgnContext->AddEntityToCurrentBlock(pDwgDim, segmentNo > 0 ? 0 : m_sourceElementHandle->GetElementId())).isValid())
            return  false;
        // close the dimension to get the proxy created, then re-open it back immediately:
        if (Acad::eOk == (es = pDwgDim->close()) && Acad::eOk != (es = acdbOpenObject(pDwgDim, dimId, AcDb::kForWrite)))
            return  false;

        bool                    foundText = false;
        AcDbBlockTableRecordPointer dimBlock (pDwgDim->dimBlockId(), AcDb::kForRead);
        if (Acad::eOk == dimBlock.openStatus())
            {
            AcDbBlockTableRecordIterator*   entIter;
            dimBlock->newIterator (entIter, true, true);
            for ( ; !entIter->done(); entIter->step())
                {
                AcDbEntity*     pEntity = NULL;
                if (Acad::eOk != entIter->getEntity (pEntity, AcDb::kForRead))
                    continue;

                AcDbMText*      pMText = AcDbMText::cast (pEntity);
                if (NULL != pMText)
                    {
                    RealDwgUtil::DPoint3dFromGePoint3d (dwgTextCenter, pMText->location());
                    pEntity->close();
                    foundText = true;
                    break;
                    }

                pEntity->close();
                }
            delete entIter;
            }

        if (foundText)
            {
            Transform   matrix;
            if (NULL != RealDwgUtil::GetExtrusionTransform(matrix, pDwgDim->normal(), pDwgDim->elevation()))
                matrix.Multiply (dwgTextCenter);

            // get DGN text center in local coordinate system:
            double      textWidth  = textBox.primary.width * m_fromDgnContext->GetScaleFromDGN();
            double      textHeight = textBox.primary.height * m_fromDgnContext->GetScaleFromDGN();
            DPoint3d    dgnTextCenter = DPoint3d::From(textWidth / 2.0, textHeight / 2.0, 0.0);

            // find text definition matrix, with origin at the first character:
            DVec3d      yAxis = DVec3d::FromNormalizedCrossProduct (textBox.primary.zvec, textBox.primary.baseDir);

            DPoint3d    origin = textBox.primary.baseFirstChar;
            m_transformFromDgn->Multiply (origin);
            
            matrix.InitFromOriginAndVectors (origin, textBox.primary.baseDir, yAxis, textBox.primary.zvec);

            // transform DGN text center point to the world:
            matrix.Multiply (dgnTextCenter);

            // compare two centers: if off by the text width, drop the dimension
            return dgnTextCenter.Distance(dwgTextCenter) >= textWidth;
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/11
+---------------+---------------+---------------+---------------+---------------+------*/
bool             IsRadialDimensionValidForRealDwg (DimensionElm const* pDgnDim, DimDerivedDataP pDerivedData) const
    {
    /*-------------------------------------------------------------------------------------------------------------------
    We want to check if radial dimension's text origin falls between the measuring point and the center point of the arc.
    To do this we need to make a copy of the radial dimension, derive the arc edge point from stroking the dimension, get
    the center->edge and center->text vectors, then determine if these two vectors are running on the same direction or not.
    -------------------------------------------------------------------------------------------------------------------*/
    // extract original def points stored on dimension element:
    DPoint3d    centerPoint = pDgnDim->GetPoint (0);
    DPoint3d    textPoint = pDgnDim->GetPoint (2);

    // use the arc def point that is used by stroke code:
    DPoint3d    arcDefpoint = NULL == pDerivedData->pArcDefPoint ? pDgnDim->GetPoint(1) : *(pDerivedData->pArcDefPoint);
    
    // build two vectors of center->edge and center->text:
    DVec3d      centerToEdge, centerToText;
    centerToEdge.NormalizedDifference (arcDefpoint, centerPoint);
    centerToText.NormalizedDifference (textPoint, centerPoint);

    // reversed text vector indicates that the text is placed beyond arc center point - RealDWG does not accept that(TR305690)!
    return centerToEdge.DotProduct(centerToText) > -TOLERANCE_VectorEqual;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ConvertSingleDimension
(
AcDbDimension*                  pDwgDim,
ElementHandleCR                 dimElement,
int                             segmentNo = 0
) const
    {
    DimensionElmCP  pDgnDim = (DimensionElmCP) dimElement.GetElementCP ();
    // dimension stroker seems to begin ignoring dims with less than 3 points! TFS988539
    if (nullptr == pDgnDim || (pDgnDim->nPoints < 3 && DIM_ANGULAR((DimensionType)pDgnDim->dimcmd)))
        return  MstnElementUnacceptable;

    /*-----------------------------------------------------------------------------------
    Make a copy of dimension element as its data may be altered during stroking as we call
    mdlDim_getDerivedData.  For instance text offset for lindim and perpendicular raddim
    are two of those that get changed in stroke code.
    -----------------------------------------------------------------------------------*/
    ElementHandle   elementCopy(*m_sourceElementHandle);

    DimDerivedDataP pDerivedData = mdlDimDerivedData_create (DIMDERIVEDDATA_FLAGS_TEXTDOCKED |
                                                             DIMDERIVEDDATA_FLAGS_TEXTOUTSIDE |
                                                             DIMDERIVEDDATA_FLAGS_SUPERSCRIPTED |
                                                             DIMDERIVEDDATA_FLAGS_ARCDEFPOINT |
                                                             DIMDERIVEDDATA_FLAGS_CHAINTYPE |
                                                             DIMDERIVEDDATA_FLAGS_CHAINTEXTLOC |
                                                             DIMDERIVEDDATA_FLAGS_DIMVALUES |
                                                             DIMDERIVEDDATA_FLAGS_DELIMITEROMITTED |
                                                             DIMDERIVEDDATA_FLAGS_TEXTBOX);

    mdlDim_getDerivedData (pDerivedData, elementCopy);

    // if it is a DIN dimension with a superscript, no need to go any further - just drop it:
    if (NeedToPreDropDimension(pDgnDim, pDerivedData) && RealDwgSuccess == DropUnsupportedDimensionToBlock(pDwgDim))
        {
        mdlDimDerivedData_free (&pDerivedData);
        return  ReplacedObjectType;
        }

    // set segment/point overriden parameters
    UpdateDimensionStyledata (pDwgDim, pDgnDim, pDerivedData);

    // get def points
    DPoint3dArray   pointArray;
    for (int i = 0; i < pDgnDim->nPoints; i++)
        {
        DPoint3d    point = pDgnDim->GetPoint(i);
        m_transformFromDgn->Multiply (point);
        // if user wants to remove z-coordinate in DWG file, do so now:
        if (m_fromDgnContext->GetSettings().IsZeroZCoordinateEnforced())
            point.z = 0.0;
        pointArray.push_back (point);
        }

    // get text center and matrix from derived data
    DPoint3d    textCenter;
    RotMatrix   textMatrix;
    GetTextMiddlePoint (textCenter, textMatrix, pDerivedData, pointArray, pDgnDim);

    DimSegmentOverrides segOverride;
    int                 iSegNo = DIM_ORDINATE((DimensionType)pDgnDim->dimcmd) ? 1 : 0;

    if (SUCCESS == mdlDim_getSegmentOverride(&segOverride, dimElement, iSegNo) && 
        segOverride.modifiers & SEGMENT_Override_TextRotation &&
        fabs(segOverride.textRotation) > MIN_AngleRadians)
        pDwgDim->setTextRotation (segOverride.textRotation);
    else
        pDwgDim->setTextRotation (0.0);

    // extract the raw matrix from the dimension
    RotMatrix   rotMatrix;
    mdlDim_getDimRawMatrix (&rotMatrix, *m_sourceElementHandle);
    RemoveCellTransform (rotMatrix);
    RotMatrix   defMatrix;
    double      angle = 0.0;
    int axisUpdated = UpdateDimensionMatrix (defMatrix, angle, rotMatrix, textMatrix, pDwgDim, pointArray);

    // make the arbitary extrusion direction from dimension definition matrix:
    DPoint3d    extrusionDir;
    double      ucsAngle = 0.0;
    RealDwgUtil::ExtractExtrusion (extrusionDir, ucsAngle, defMatrix);

    // dimension rotation angle starts from the x-axis of its definition matrix
    angle += ucsAngle;

    /*-----------------------------------------------------------------------------------
    Set extrusion if we have one, which should work together with ucs angle plus an angle
    for a rotated dimension.
    -----------------------------------------------------------------------------------*/
    // do we have to set the extrusion direction?
    bool        hasEcs = fabs(extrusionDir.z - 1.0) > 1.e-5;
    if (hasEcs)
        pDwgDim->setNormal (RealDwgUtil::GeVector3dFromDPoint3d(extrusionDir));

    /*-----------------------------------------------------------------------------------
    UCS angle, i.e. group 51, is the negative of the angle between the ECS X axis and
    the UCS X axis.  In other words, it is an angle sweeping from ECS X axis to UCS X axis:
    -----------------------------------------------------------------------------------*/
    if (fabs(ucsAngle) > MIN_AngleRadians && NeedHorizontalRotation(pDgnDim))
        pDwgDim->setHorizontalRotation (bsiTrig_getPositiveNormalizedAngle(2 * PI - ucsAngle));
    else
        pDwgDim->setHorizontalRotation (0.0);

    double      zDepth = GetDimensionPlaneDepth (pDgnDim, defMatrix);

    // middle-center mtext attachment
    pDwgDim->setTextAttachment (AcDbMText::kMiddleCenter);

    // fix up the bogus text origin when text is out of dimension plane
    ProjectPointToPlane (textCenter, textMatrix, defMatrix, zDepth * m_fromDgnContext->GetScaleFromDGN());

    // if user wants to remove z-coordinate in DWG file, do so for text origin and zdepth:
    if (m_fromDgnContext->GetSettings().IsZeroZCoordinateEnforced())
        textCenter.z = zDepth = 0.0;

    pDwgDim->setElevation (zDepth * m_fromDgnContext->GetScaleFromDGN());

    RealDwgStatus   status = UnexpectedDimensionType;
    AcDbObjectId    blockId = AcDbObjectId::kNull;
    ElementRefP     elemRef = m_sourceElementHandle->GetElementRef ();
    int             elemType;

    if (NULL != elemRef && NULL != (elemRef = elemRef->GetParentElementRef()) && (CELL_HEADER_ELM == (elemType = elemRef->GetElementType()) || SHAREDCELL_DEF_ELM == elemType))
        {
        /*-----------------------------------------------------------------------------------------------------
        Create an anonymous block for the dimension.
        While a dimension proxy in a layout will always be updated by RealDWG, in which case it makes no sense
        to create proxies, a dimension in a non-layout block can be kept intact as RealDWG won't update it.
        -----------------------------------------------------------------------------------------------------*/
        DPoint3d    clonePoint;
        RealDwgUtil::DPoint3dFromGePoint3d (clonePoint, pDwgDim->dimBlockPosition());

        status = this->CreateAnonymousDimensionBlock (blockId);
        if (RealDwgSuccess == status)
            {
            // if user wants to remove z-coordinate in DWG file, do so now:
            if (m_fromDgnContext->GetSettings().IsZeroZCoordinateEnforced())
                clonePoint.z = 0.0;

            pDwgDim->setDimBlockId (blockId);
            this->SetDimensionBlockPosition (pDwgDim, clonePoint);
            }
        }

    bool            forceUserTextPosition = false;

    // create dimension block and dimension entity based on type:
    switch (pDgnDim->dimcmd)
        {
        case DimensionType::SizeArrow:
        case DimensionType::SizeStroke:
        case DimensionType::CustomLinear:
        case DimensionType::LocateSingle:
        case DimensionType::LocateStacked:
            status = ConvertLinearDimension (pDwgDim, pointArray, angle, hasEcs, defMatrix, textCenter, textMatrix, pDerivedData);
            // append defpoints to the block
            if (blockId.isValid())
                AddLindimDefpointsToBlock (blockId, pDwgDim, ucsAngle, zDepth, hasEcs, defMatrix);
            break;
        case DimensionType::Diameter:
        case DimensionType::DiameterExtended:
            status = ConvertDiametricDimension (pDwgDim, pointArray, angle, hasEcs, defMatrix, textCenter, textMatrix);
            forceUserTextPosition = !CirdimHasDefaultTextPosition (pDwgDim, pointArray, &textCenter);
            break;
        case DimensionType::Radius:
        case DimensionType::RadiusExtended:
            status = ConvertRadialDimension (pDwgDim, pointArray, angle, hasEcs, defMatrix, textCenter, textMatrix, pDerivedData);
            forceUserTextPosition = !CirdimHasDefaultTextPosition (pDwgDim, pointArray);
            break;
        case DimensionType::DiameterParallel:
        case DimensionType::DiameterPerpendicular:
            status = ConvertDiamdimToLindim (pDwgDim, pointArray, angle, hasEcs, defMatrix, rotMatrix, textCenter, textMatrix);
            break;
        case DimensionType::AngleLines:
        case DimensionType::AngleAxis:
        case DimensionType::AngleAxisX:
        case DimensionType::AngleAxisY:
        case DimensionType::AngleSize:
        case DimensionType::AngleLocation:
        case DimensionType::ArcSize:
        case DimensionType::ArcLocation:
            status = ConvertAngularDimension (pDwgDim, pointArray, angle, hasEcs, defMatrix, textCenter, textMatrix);
            break;
        case DimensionType::Ordinate:
            if (!hasEcs && fabs(angle) > MIN_AngleRadians)
                hasEcs = true;
            status = ConvertOrdinateDimension (pDwgDim, pointArray, angle, hasEcs, defMatrix, axisUpdated, textCenter, textMatrix);
            forceUserTextPosition = true;
            break;

        default:
            assert (false && L"Missing dimension type support!");
            return  UnexpectedDimensionType;
        }

    // set dimension's user text location flag
    int pointNo = mdlDim_getPointNumber (dimElement, 0, ADTYPE_TEXT_SINGLE, ADSUB_NONE, 0);

    if (forceUserTextPosition || DIMTEXT_OFFSET == pDgnDim->GetDimTextCP(pointNo)->flags.b.just || pDerivedData->pIsTextOutside[0])
        pDwgDim->useSetTextPosition ();
    else
        pDwgDim->useDefaultTextPosition ();

    // set user text
    if (RealDwgSuccess == status)
        pDwgDim->setDimensionText (GetDimensionUserText(pDwgDim, pDgnDim).kwszPtr());

    // if the newly created dimension does not match DGN dimension, drop it:
    bool    shouldDrop = NeedToPostDropDimension (pDwgDim, pDgnDim, pDerivedData, segmentNo);

    mdlDimDerivedData_free (&pDerivedData);

    /*---------------------------------------------------------------------------------------------------------
    Since setting dimension annotative prerequisites the dimension to be database residency, so we don't want 
    to set annotative until we know for sure that this dimension will not be dropped.
    ---------------------------------------------------------------------------------------------------------*/
    if (shouldDrop && RealDwgSuccess == DropUnsupportedDimensionToBlock(pDwgDim))
        status = ReplacedObjectType;
    else if (RealDwgSuccess == status)
        this->SetDimensionAnnotationScale (pDwgDim, segmentNo);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            SetDimensionAnnotationScale (AcDbDimension* acDimension, int segmentNo) const
    {
    // set annotation scale if the dimension is in the default model
    if (m_isAnnotative)
        {
        /*-------------------------------------------------------------------------------------------------
        For dropped segments, there is no need to go through the full procedure to set it annotative, just
        turn it on.  But we still have to save the dimension prior to adding the annotation scale extended
        dictionary.  The text size we have now is the model text size.  Pre-checking in here will correctly
        scale text size back to paper size, whereas a post-checking will result in keeping current size as
        the model size which is incorrect.
        -------------------------------------------------------------------------------------------------*/
        if (segmentNo > 0)
            {
            if (!acDimension->objectId().isValid())
                m_fromDgnContext->AddEntityToCurrentBlock (acDimension, 0);
            return  RealDwgSuccess == RealDwgUtil::SetObjectAnnotative(acDimension, true);
            }
        else
            {
            double  annoScale = 1.0;
            if (m_modelRef->GetModelId() == m_fromDgnContext->GetFile()->GetDefaultModelId())
                annoScale = m_modelRef->GetModelInfo().GetAnnotationScaleFactor ();
            return  m_fromDgnContext->AddAnnotationScaleToObject(acDimension, annoScale, m_sourceElementHandle->GetElementId());
            }
        }

    RealDwgUtil::SetObjectAnnotative (acDimension, false);
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/03
+---------------+---------------+---------------+---------------+---------------+------*/
int             DwgDimensionPointIndexFromDgn (int dgnPointIndex, DimensionType dimensionCommand) const
    {
    // create dimension block and dimension entity based on type:
    switch (dimensionCommand)
        {
        case DimensionType::SizeArrow:
        case DimensionType::SizeStroke:
        case DimensionType::CustomLinear:
        case DimensionType::LocateSingle:
        case DimensionType::LocateStacked:
            return  DWG_DIMPOINT_INDEX (s_linearDimensionDwgPointIndices, sizeof(s_linearDimensionDwgPointIndices)/sizeof(int), dgnPointIndex);

        case DimensionType::Diameter:
        case DimensionType::DiameterExtended:
            break;

        case DimensionType::Radius:
        case DimensionType::RadiusExtended:
            break;

        case DimensionType::DiameterParallel:
        case DimensionType::DiameterPerpendicular:
            break;

        case DimensionType::AngleLines:
        case DimensionType::AngleAxis:
        case DimensionType::AngleAxisX:
        case DimensionType::AngleAxisY:
        case DimensionType::AngleSize:
        case DimensionType::AngleLocation:
        case DimensionType::ArcSize:
        case DimensionType::ArcLocation:
            break;

        case DimensionType::Ordinate:
            break;
        }
    return -1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                            ScheduleAssociativeDimensionsForPostprocessing
(
AcDbDimension*                  pDimension,
ElementHandleCR                 inElement
) const
    {
    MSElementDescrCP    pElmdscr = inElement.GetElementDescrCP();
    DimensionType      dimType = m_sourceDimHandler->GetDimensionType (inElement);

    for (int i=0; i<4; i++)
        {
        AssocPoint      assocPoint;

        if (DwgDimensionPointIndexFromDgn (i, dimType) >= 0 &&
            SUCCESS == AssociativePoint::ExtractPoint (assocPoint, inElement, i, 4))
            {
            if (!pDimension->objectId().isValid())
                m_fromDgnContext->AddEntityToCurrentBlock (pDimension, pElmdscr->el.ehdr.uniqueId);
            m_fromDgnContext->PostProcessingRequired (inElement, pDimension->objectId());
            return;
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ExtractSubEntity
(
AcDbFullSubentPath&         subentPathOut,
AcDbObjectId&               objectId,
ElementHandleCR             elemHandle,
ElementId                   refAttachmentId
) const
    {
    if (0 != refAttachmentId)
        return BSIERROR;               // No long associations yet.

    if (!elemHandle.IsValid())
        return BSIERROR;

    if ((objectId = m_fromDgnContext->ExistingObjectIdFromElementId(elemHandle.GetElementId())).isNull())
        return BSIERROR;

    subentPathOut = AcDbFullSubentPath (objectId, AcDb::kEdgeSubentType, 0);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/12
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt    ConvertElementToCurve (MSBsplineCurveR curve, ElementHandleCR inElement) const
    {
    CurveVectorPtr  curveVector = ICurvePathQuery::ElementToCurveVector (inElement);
    if (curveVector.IsNull() || curveVector->size() < 1)
        return  MstnElementUnacceptable;

    return curveVector->ToBsplineCurve (curve);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ExtractSnapPointRefFromAssocPoint
(
AssocPointCR                assocPoint,
AcDbDimAssoc*               pDimAssoc,
int                         pointIndex,
ElementHandleCR             dimElement
) const
    {
    // the methods we used in DwgDirect are not available in RealDwg.
    const AssocGeom&        assocGeom = (const AssocGeom&) assocPoint;
    bool                    validAssociation = false;

    // First make sure it is a valid association.
    StatusInt               status;
    DPoint3d                currentPoint;
    if (SUCCESS != (status = AssociativePoint::GetPoint (&currentPoint, assocPoint, m_modelRef)))
        return status;

    // If there is already association, don't change it if the DGN representation is the same.
    const AcDbOsnapPointRef*    pCurr = AcDbOsnapPointRef::cast (pDimAssoc->pointRef ((AcDbDimAssoc::AssocFlags)pointIndex));
    if (NULL != pCurr)
        {
        AssocPoint          currAssocPoint;

        if (SUCCESS == m_fromDgnContext->AssocPointFromSnapPointRef (&currAssocPoint, *pCurr, pointIndex, dimElement) &&
            0 == memcmp (&currAssocPoint, &assocPoint, sizeof(currAssocPoint)))
            return SUCCESS;
        }

    AcDbOsnapPointRef*      pNew = new AcDbOsnapPointRef ();
    if (NULL == pNew)
        return  MDLERR_INSFMEMORY;

    size_t                  nPoints;
    AcDbObjectId            object1Id, object2Id;
    AcDbFullSubentPath      mainSubentPath;
    MSBsplineCurve          curve;
    memset (&curve, 0, sizeof(curve));

    switch (assocGeom.type)
        {
        case  LINEAR_ASSOC:
            {
            const LinearAssoc       *pLinearAssoc = &assocGeom.line;
            ElementHandle           linearElem (pLinearAssoc->uniqueId, m_modelRef);

            if (SUCCESS == ExtractSubEntity (mainSubentPath, object1Id, linearElem, pLinearAssoc->refAttachmentId))
                {
                DPoint3dArray       tmpPoints;
                if (RealDwgSuccess == RealDwgUtil::GetPointArrayFromLinearElement(tmpPoints, linearElem) &&
                    (nPoints = tmpPoints.size() > 0) &&
                    (0 == pLinearAssoc->vertex || pLinearAssoc->nVertex || pLinearAssoc->nVertex == nPoints))       // topology change test.
                    {
                    double          snapRatio = (double) pLinearAssoc->numerator / (double) pLinearAssoc->divisor;
                    int             iVertex = pLinearAssoc->vertex + (int) snapRatio;

                    snapRatio = fmod (snapRatio, 1.0);
                    if (0.0 == snapRatio)
                        {
                        if (0 ==  iVertex)
                            {
                            mainSubentPath.subentId().setIndex(1);
                            pNew->setOsnapType (AcDbPointRef::kOsnapStart);
                            }
                        else
                            {
                            mainSubentPath.subentId().setIndex( iVertex);
                            pNew->setOsnapType (AcDbPointRef::kOsnapEnd);
                            }
                        }
                    else if (.5 == snapRatio)
                        {
                        pNew->setOsnapType (AcDbPointRef::kOsnapMid);
                        mainSubentPath.subentId().setIndex (0 == iVertex ? 0 : iVertex + 1);
                        }
                    else
                        {
                        mainSubentPath.subentId().setIndex (0 == iVertex ? 0 : iVertex + 1);
                        pNew->setNearPointParam (snapRatio);
                        pNew->setOsnapType (AcDbPointRef::kOsnapNear);
                        }
                    validAssociation = true;
                    }
                }
            break;
            }

        case PROJECTION_ASSOC:
            {
            const ProjectionAssoc   *pProjectionAssoc = &assocGeom.projection;
            ElementHandle           projElem (pProjectionAssoc->uniqueId, m_modelRef);

            if (SUCCESS == ExtractSubEntity (mainSubentPath, object1Id, projElem, pProjectionAssoc->refAttachmentId))
                {
                DPoint3dArray       tmpPoints;
                if (RealDwgSuccess == RealDwgUtil::GetPointArrayFromLinearElement(tmpPoints, projElem) &&
                    (nPoints = tmpPoints.size()) > 0 &&
                    (0 == pProjectionAssoc->vertex || pProjectionAssoc->nVertex || pProjectionAssoc->nVertex == nPoints))       // topology change test.
                    {
                    mainSubentPath.subentId().setIndex (0 == pProjectionAssoc->vertex ? 0 : pProjectionAssoc->vertex + 1);
                    pNew->setNearPointParam (pProjectionAssoc->ratioVal);
                    pNew->setOsnapType (AcDbPointRef::kOsnapNear);
                    validAssociation = true;
                    }
                else if (BSISUCCESS == ConvertElementToCurve(curve, projElem))
                    {
                    double arcLength = curve.LengthBetweenFractions (0.0, pProjectionAssoc->ratioVal);
                    pNew->setNearPointParam (arcLength);
                    pNew->setOsnapType (AcDbPointRef::kOsnapNear);
                    validAssociation = true;
                    curve.ReleaseMem ();
                    }
                }
            break;
            }

        case BCURVE_ASSOC:
            {
            const BCurveAssoc   *pBCurveAssoc = &assocGeom.bCurve;
            ElementHandle       curveElem (pBCurveAssoc->uniqueId, m_modelRef);

            if (SUCCESS == ExtractSubEntity (mainSubentPath, object1Id, curveElem, pBCurveAssoc->refAttachmentId))
                {
                if (BSPLINE_CURVE_ELM == curveElem.GetElementType() && BSPLINE_CONSTRUCTION_INTERPOLATION == curveElem.GetElementCP()->bspline_curve.flags.construct_type)
                    {
                    if (BSISUCCESS == ConvertElementToCurve(curve, curveElem))
                        {
                        double arcLength = curve.LengthBetweenFractions (0.0, pBCurveAssoc->uParam);
                        pNew->setNearPointParam (arcLength * m_fromDgnContext->GetScaleFromDGN());
                        pNew->setOsnapType (AcDbPointRef::kOsnapNear);
                        validAssociation = true;
                        curve.ReleaseMem ();
                        }
                    }
                else
                    {
                    pNew->setNearPointParam (pBCurveAssoc->uParam);
                    pNew->setOsnapType (AcDbPointRef::kOsnapNear);
                    validAssociation = true;
                    }
                }
            break;
            }

        case ARC_ASSOC:
            {
            const ArcAssoc  *pArcAssoc = &assocGeom.arc;
            double          start, sweep;
            ElementHandle   arcElem (pArcAssoc->uniqueId, m_modelRef);

            if (SUCCESS == ExtractSubEntity(mainSubentPath, object1Id, arcElem, pArcAssoc->refAttachmentId) &&
                SUCCESS == ArcHandler::Extract(NULL, NULL, &start, &sweep, NULL, NULL, NULL, NULL, NULL, arcElem))
                {
                double  angle = Angle::AdjustToSweep (pArcAssoc->angle, 0, msGeomConst_2pi);

                pNew->setNearPointParam (angle);

                switch (pArcAssoc->keyPoint)
                    {
                    case ASSOC_ARC_START:
                        pNew->setOsnapType (AcDbPointRef::kOsnapStart);
                        break;

                    case ASSOC_ARC_END:
                        pNew->setOsnapType (AcDbPointRef::kOsnapEnd);
                        break;

                    case ASSOC_ARC_CENTER:
                        pNew->setOsnapType (AcDbPointRef::kOsnapCen);
                        break;

                    default:
                        {
                        if (fabs (angle - Angle::AdjustToSweep(start + sweep/2, 0, msGeomConst_2pi)) < TOLERANCE_ArcAngle)
                            pNew->setOsnapType (AcDbPointRef::kOsnapMid);
                        else
                            pNew->setOsnapType (AcDbPointRef::kOsnapNear);
                        break;
                        }
                    }
                validAssociation = true;
                }
            break;
            }

        case INTERSECT_ASSOC:
        case INTERSECT2_ASSOC:
            {
            const TwoElmAssoc       *pTwoElmAssoc = &assocGeom.twoElm;
            DPoint3d                center;
            double                  start, sweep, r1, r2;
            RotMatrix               rMatrix;
            AcDbFullSubentPath      intSubentPath;
            ElementHandle           elem1 (pTwoElmAssoc->uniqueId1, m_modelRef);
            ElementHandle           elem2 (pTwoElmAssoc->uniqueId2, m_modelRef);

            if (SUCCESS == ExtractSubEntity (mainSubentPath, object1Id, elem1, pTwoElmAssoc->refAttachmentId1) &&
                SUCCESS == ExtractSubEntity (intSubentPath, object2Id, elem2, pTwoElmAssoc->refAttachmentId2))
                {
                // Need to find the parameter of the intersection point on the first element.
                DPoint3dArray       tmpPoints;
                if (RealDwgSuccess == RealDwgUtil::GetPointArrayFromLinearElement(tmpPoints, elem1) &&
                    (nPoints = tmpPoints.size()) > 0)
                    {
                    if (INTERSECT2_ASSOC == assocGeom.type)
                        {
                        if (assocGeom.intersect2.seg1 < nPoints - 1)
                            {
                            double      segDistance;
                            DPoint3d    *pSegPoint = &tmpPoints[0] + assocGeom.intersect2.seg1;

                            if (NULL != pSegPoint && 0.0 != (segDistance = pSegPoint->distance (pSegPoint + 1)))
                                pNew->setNearPointParam (pSegPoint->distance (&currentPoint) / segDistance);
                            }

                        mainSubentPath.subentId().setIndex (0 == assocGeom.intersect2.seg1 ? 0 : assocGeom.intersect2.seg1 + 1);
                        validAssociation = true;
                        }
                    }
                else if (SUCCESS == ArcHandler::Extract(NULL, NULL, &start, &sweep, &r1, &r2, &rMatrix, NULL, &center, elem1))
                    {
                    DPoint3d            arcPoint = currentPoint;

                    arcPoint.subtract (&center);
                    rMatrix.multiplyTranspose (&arcPoint);
                    pNew->setNearPointParam (Angle::AdjustToSweep(atan2(arcPoint.y * r1, arcPoint.x * r2), 0.0, msGeomConst_2pi));
                    mainSubentPath.subentId().setIndex (0);
                    validAssociation = true;
                    }
                else if (BSISUCCESS == ConvertElementToCurve(curve, elem1))
                    {
                    double      u;

                    // Find the parameter or distance from close point and curve.
                    bsprcurv_minDistToCurve (NULL, NULL, &u, &currentPoint, &curve, NULL, NULL);

                    if (BSPLINE_CURVE_ELM == elem1.GetElementType() && BSPLINE_CONSTRUCTION_INTERPOLATION == elem1.GetElementCP()->bspline_curve.flags.construct_type)
                        {
                        double arcLength = curve.LengthBetweenFractions (0.0, u);
                        pNew->setNearPointParam (arcLength * m_fromDgnContext->GetScaleFromDGN());
                        mainSubentPath.subentId().setIndex (0);
                        validAssociation = true;
                        }
                    else
                        {
                        pNew->setNearPointParam (u);
                        mainSubentPath.subentId().setIndex (0);
                        validAssociation = true;
                        }

                    curve.ReleaseMem ();
                    }

                if (validAssociation && object2Id.isValid())
                    {
                    pNew->setOsnapType (AcDbPointRef::kOsnapInt);
                    intSubentPath.subentId().setIndex (0 == assocGeom.intersect2.seg2 ? 0 : assocGeom.intersect2.seg2 + 1);
                    intSubentPath.subentId().setType (AcDb::kEdgeSubentType);
                    pNew->setIntIdPath (intSubentPath);
                    AcDbObjectPointer<AcDbObject> pObject2 (object2Id, AcDb::kForWrite);
                    if (Acad::eOk == pObject2.openStatus())
                        pObject2->addPersistentReactor (pDimAssoc->objectId());
                    }
                }
            break;
            }
        }

    if (validAssociation && object1Id.isValid())
        {
        pNew->setIdPath (mainSubentPath);
        pDimAssoc->setPointRef ((AcDbDimAssoc::AssocFlags) pointIndex, pNew);
        AcDbObjectPointer<AcDbObject> pObject1 (object1Id, AcDb::kForWrite);
        if (Acad::eOk == pObject1.openStatus())
            pObject1->addPersistentReactor (pDimAssoc->objectId());
        }
    else
        {
        delete pNew;
        }

    return SUCCESS;
    }

};  // ToDwgExtDimension




/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 UpdateDimstyle
(
AcDbDimStyleTableRecord*    pDimstyle,
AcDbDimension*              pDimension,
AcDbLeader*                 pLeader
)
    {
    AcCmColor           color  = pDimstyle->dimclrd();
    AcDb::LineWeight    weight = pDimstyle->dimlwd();
    AcDbObjectId        ltype  = pDimstyle->dimltype();

    if (NULL != pDimension)
        {
        AcDbObjectId    ltypeByBlock = acdbSymUtil()->linetypeByBlockId (pDimension->database());

        // set dimension line symbology to byblock as needed
        if (!color.isByBlock() && color == pDimension->color())
            {
            color.setColorIndex (DWG_COLOR_ByBlock);
            pDimstyle->setDimclrd (color);
            }
        if (AcDb::kLnWtByBlock != weight && weight == pDimension->lineWeight())
            pDimstyle->setDimlwd (AcDb::kLnWtByBlock);
        if (!ltypeByBlock.isNull() && ltype != ltypeByBlock && ltype == pDimension->dimltype())
            pDimstyle->setDimltype (ltypeByBlock);

        // set witness line symbology to byblock as needed
        color = pDimstyle->dimclre ();
        weight = pDimstyle->dimlwe ();
        ltype = pDimstyle->dimltex1 ();

        if (!color.isByBlock() && color == pDimension->color())
            {
            color.setColorIndex (DWG_COLOR_ByBlock);
            pDimstyle->setDimclre (color);
            }
        if (AcDb::kLnWtByBlock != weight && weight == pDimension->lineWeight())
            pDimstyle->setDimlwe (AcDb::kLnWtByBlock);
        if (!ltypeByBlock.isNull() && ltype != ltypeByBlock && ltype == pDimension->dimltex1())
            pDimstyle->setDimltex1 (ltypeByBlock);
        if (!ltypeByBlock.isNull() && (ltype = pDimstyle->dimltex2()) != ltypeByBlock && ltype == pDimension->dimltex2())
            pDimstyle->setDimltex2 (ltypeByBlock);
        }
    else if (NULL != pLeader)
        {
        // set leader line color and weight to byblock as needed
        if (!color.isByBlock() && color == pLeader->color())
            {
            color.setColorIndex (DWG_COLOR_ByBlock);
            pDimstyle->setDimclrd (color);
            }
        if (AcDb::kLnWtByBlock != weight && weight == pLeader->lineWeight())
            pDimstyle->setDimlwd (AcDb::kLnWtByBlock);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/03
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsAngularDimension
(
AcDbDimension*              pDimension
)
    {
    /*-----------------------------------------------------------------------------------
    As a DGN non-angular dimension element does not store angular specific data, editting
    of existing non-angular dimension can result in unnecessary style overrides.  In such
    cases we try to reserve the original data.
    -----------------------------------------------------------------------------------*/
    return  pDimension->isKindOf(AcDb2LineAngularDimension::desc()) ||
            pDimension->isKindOf(AcDb3PointAngularDimension::desc());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/04
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsColorSame
(
AcCmColor                   color1,
AcCmColor                   color2,
AcDbDimension*              pDimension
)
    {
    if (color1 == color2)
        return  true;

    /*-----------------------------------------------------------------------------------
    This is only a workaround before we support independent symbology for dimension line.
    Currently symbology for dimension line comes from element header, thus we lost
    original DWG symbology for dimension line.
    -----------------------------------------------------------------------------------*/
    if (!pDimension->layerId().isNull())
        {
        AcDbLayerTableRecordPointer pLayer (pDimension->layerId(), AcDb::kForRead);
        if (Acad::eOk != pLayer.openStatus())
            {
            DIAGNOSTIC_PRINTF ("Error opening dimension layer for ByLayer color!\n");
            return  false;
            }

        AcCmColor                   layerColor = pLayer->color ();
        AcCmColor                   blockColor = pDimension->color ();

        // check dimension entity and layer colors for byblock and bylayer
        if (((color1.isByBlock() && color2.isByLayer()) || (color2.isByBlock() && color1.isByLayer())) &&
            layerColor == blockColor)
            return  true;

        // check for other actual color match
        if (color1.isByLayer() && layerColor == color2 || color2.isByLayer() && layerColor == color1 ||
            color1.isByBlock() && blockColor == color2 || color2.isByBlock() && blockColor == color1)
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/04
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsLineWeightSame
(
AcDb::LineWeight            weight1,
AcDb::LineWeight            weight2,
AcDbDimension*              pDimension
)
    {
    if (weight1 == weight2)
        return  true;

    /*-----------------------------------------------------------------------------------
    This is only a workaround before we support independent symbology for dimension line.
    Currently symbology for dimension line comes from element header, thus we lost
    original DWG symbology for dimension line.
    -----------------------------------------------------------------------------------*/
    if (!pDimension->layerId().isNull())
        {
        AcDbLayerTableRecordPointer pLayer (pDimension->layerId(), AcDb::kForRead);
        if (Acad::eOk != pLayer.openStatus())
            {
            DIAGNOSTIC_PRINTF ("Error opening dimension layer for ByLayer weight!\n");
            return  false;
            }

        AcDb::LineWeight            layerWeight = pLayer->lineWeight();
        AcDb::LineWeight            blockWeight = pDimension->lineWeight();

        // check dimension entity and layer lineweights for byblock and bylayer
        if (((AcDb::kLnWtByBlock == weight1 && AcDb::kLnWtByLayer == weight2) ||
             (AcDb::kLnWtByBlock == weight2 && AcDb::kLnWtByLayer == weight1)) &&
            blockWeight == layerWeight)
            return  true;

        // check for other actual lineweight match
        if (AcDb::kLnWtByLayer == weight1 && layerWeight == weight2 ||
            AcDb::kLnWtByLayer == weight2 && layerWeight == weight1 ||
            AcDb::kLnWtByBlock == weight1 && blockWeight == weight2 ||
            AcDb::kLnWtByBlock == weight2 && blockWeight == weight1)
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/04
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsLinetypeSame
(
AcDbObjectId const&         linetype1,
AcDbObjectId const&         linetype2,
AcDbDimension*              pDimension
)
    {
    if (linetype1 == linetype2)
        return  true;

    /*-----------------------------------------------------------------------------------
    This is only a workaround before we support independent symbology for dimension line.
    Currently symbology for dimension line comes from element header, thus we lost
    original DWG symbology for dimension line.
    -----------------------------------------------------------------------------------*/
    if (!pDimension->layerId().isNull())
        {
        AcDbLayerTableRecordPointer pLayer (pDimension->layerId(), AcDb::kForRead);
        if (Acad::eOk != pLayer.openStatus())
            {
            DIAGNOSTIC_PRINTF ("Error opening dimension layer for ByLayer linetype!\n");
            return  false;
            }

        AcDbObjectId    layerLinetype = pLayer->linetypeObjectId ();
        AcDbObjectId    blockLinetype = pDimension->linetypeId ();
        AcDbObjectId    bylayerLinetype = acdbSymUtil()->linetypeByLayerId (pDimension->database());
        AcDbObjectId    byblockLinetype = acdbSymUtil()->linetypeByBlockId (pDimension->database());

        // check dimension entity and layer linetypes for byblock and bylayer
        if (((linetype1 == byblockLinetype && linetype2 == bylayerLinetype) || (linetype2 == byblockLinetype && linetype1 == bylayerLinetype)) &&
            layerLinetype == blockLinetype)
            return  true;

        // check for other actual linetype match
        if (linetype1 == bylayerLinetype && linetype2 == layerLinetype ||
            linetype2 == bylayerLinetype && linetype1 == layerLinetype ||
            linetype1 == byblockLinetype && linetype2 == blockLinetype ||
            linetype2 == byblockLinetype && linetype1 == blockLinetype)
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/04
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsDimdsepSame
(
Adesk::Int16                dimdsep1,
Adesk::Int16                dimdsep2
)
    {
    if (dimdsep1 == dimdsep2)
        return  true;

    /*-----------------------------------------------------------------------------------
    Although the valid values for dimdsep are 32(space), 44(comma) and 46(period), ACAD
    is able to handle value 0 and treat it as same as 46.
    -----------------------------------------------------------------------------------*/
    return  ('.' == dimdsep1 && 0 == dimdsep2) || (0 == dimdsep1 && '.' == dimdsep2);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SetDimensionDimstyleData
(
AcDbDimension*              pDimension,
AcDbDimStyleTableRecord*    pDimstyle
)
    {
    if (IsAngularDimension(pDimension) && pDimension->dimadec() != pDimstyle->dimadec())
        pDimension->setDimadec (pDimstyle->dimadec());
    if (pDimension->dimalt() != pDimstyle->dimalt())
        pDimension->setDimalt(pDimstyle->dimalt());
    if (pDimension->dimaltd() != pDimstyle->dimaltd())
        pDimension->setDimaltd(pDimstyle->dimaltd());
    if (fabs(pDimension->dimaltf() - pDimstyle->dimaltf()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimaltf(pDimstyle->dimaltf());
    if (fabs(pDimension->dimaltrnd() - pDimstyle->dimaltrnd()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimaltrnd(pDimstyle->dimaltrnd());
    if (pDimension->dimalttd() != pDimstyle->dimalttd())
        pDimension->setDimalttd(pDimstyle->dimalttd());
    if (pDimension->dimalttz() != pDimstyle->dimalttz())
        pDimension->setDimalttz(pDimstyle->dimalttz());
    if (pDimension->dimaltu() != pDimstyle->dimaltu())
        pDimension->setDimaltu(pDimstyle->dimaltu());
    if (pDimension->dimaltz() != pDimstyle->dimaltz())
        pDimension->setDimaltz(pDimstyle->dimaltz());
    if (wcscmp(pDimension->dimapost(), pDimstyle->dimapost()) != 0)
        pDimension->setDimapost(pDimstyle->dimapost());
    if (fabs(pDimension->dimasz() - pDimstyle->dimasz()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimasz(pDimstyle->dimasz());
    if (pDimension->dimatfit() != pDimstyle->dimatfit())
        pDimension->setDimatfit(pDimstyle->dimatfit());
    if (IsAngularDimension(pDimension) && pDimension->dimaunit() != pDimstyle->dimaunit())
        pDimension->setDimaunit(pDimstyle->dimaunit());
    if (IsAngularDimension(pDimension) && pDimension->dimazin() != pDimstyle->dimazin())
        pDimension->setDimazin(pDimstyle->dimazin());
    if (pDimension->dimblk() != pDimstyle->dimblk())
        pDimension->setDimblk(pDimstyle->dimblk());
    if (pDimension->dimblk1() != pDimstyle->dimblk1())
        pDimension->setDimblk1(pDimstyle->dimblk1());
    if (pDimension->dimblk2() != pDimstyle->dimblk2())
        pDimension->setDimblk2(pDimstyle->dimblk2());
    if (fabs(pDimension->dimcen() - pDimstyle->dimcen()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimcen(pDimstyle->dimcen());
    if (!IsColorSame(pDimension->dimclrd(), pDimstyle->dimclrd(), pDimension))
        pDimension->setDimclrd(pDimstyle->dimclrd());
    if (!IsColorSame(pDimension->dimclre(), pDimstyle->dimclre(), pDimension))
        pDimension->setDimclre(pDimstyle->dimclre());
    if (!IsColorSame(pDimension->dimclrt(), pDimstyle->dimclrt(), pDimension))
        pDimension->setDimclrt(pDimstyle->dimclrt());
    if (pDimension->dimdec() != pDimstyle->dimdec())
        pDimension->setDimdec(pDimstyle->dimdec());
    if (fabs(pDimension->dimdle() - pDimstyle->dimdle()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimdle(pDimstyle->dimdle());
    if (fabs(pDimension->dimdli() - pDimstyle->dimdli()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimdli(pDimstyle->dimdli());
    if (fabs(pDimension->dimexe() - pDimstyle->dimexe()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimexe(pDimstyle->dimexe());
    if (!IsDimdsepSame(pDimension->dimdsep(), pDimstyle->dimdsep()))
        pDimension->setDimdsep(pDimstyle->dimdsep());
    if (fabs(pDimension->dimexo() - pDimstyle->dimexo()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimexo(pDimstyle->dimexo());
    if (pDimension->dimfrac() != pDimstyle->dimfrac())
        pDimension->setDimfrac(pDimstyle->dimfrac());
    if (fabs(pDimension->dimfxlen() - pDimstyle->dimfxlen()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimfxlen(pDimstyle->dimfxlen());
    if (pDimension->dimfxlenOn() != pDimstyle->dimfxlenOn())
        pDimension->setDimfxlenOn(pDimstyle->dimfxlenOn());
    if (fabs(pDimension->dimgap() - pDimstyle->dimgap()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimgap(pDimstyle->dimgap());
    if (pDimension->dimjust() != pDimstyle->dimjust())
        pDimension->setDimjust(pDimstyle->dimjust());
    if (fabs(pDimension->dimlfac() - pDimstyle->dimlfac()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimlfac(pDimstyle->dimlfac());
    if (pDimension->dimlim() != pDimstyle->dimlim())
        pDimension->setDimlim(pDimstyle->dimlim());
    if (pDimension->dimlunit() != pDimstyle->dimlunit())
        pDimension->setDimlunit(pDimstyle->dimlunit());
    if (!IsLineWeightSame(pDimension->dimlwd(), pDimstyle->dimlwd(), pDimension))
        pDimension->setDimlwd(pDimstyle->dimlwd());
    if (!IsLineWeightSame(pDimension->dimlwe(), pDimstyle->dimlwe(), pDimension))
        pDimension->setDimlwe(pDimstyle->dimlwe());
    if (!IsLinetypeSame(pDimension->dimltype(), pDimstyle->dimltype(), pDimension))
        pDimension->setDimltype(pDimstyle->dimltype());
    if (!IsLinetypeSame(pDimension->dimltex1(), pDimstyle->dimltex1(), pDimension))
        pDimension->setDimltex1(pDimstyle->dimltex1());
    if (!IsLinetypeSame(pDimension->dimltex2(), pDimstyle->dimltex2(), pDimension))
        pDimension->setDimltex2(pDimstyle->dimltex2());
    if (wcscmp(pDimension->dimpost(), pDimstyle->dimpost()) != 0)
        pDimension->setDimpost(pDimstyle->dimpost());
    if (fabs(pDimension->dimrnd() - pDimstyle->dimrnd()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimrnd(pDimstyle->dimrnd());
    if (pDimension->dimsah() != pDimstyle->dimsah())
        pDimension->setDimsah(pDimstyle->dimsah());
    if (fabs(pDimension->dimscale() - pDimstyle->dimscale()) > TOLERANCE_ZeroScale && pDimstyle->dimscale() > TOLERANCE_ZeroScale)
        pDimension->setDimscale(pDimstyle->dimscale());
    if (pDimension->dimsd1() != pDimstyle->dimsd1())
        pDimension->setDimsd1(pDimstyle->dimsd1());
    if (pDimension->dimsd2() != pDimstyle->dimsd2())
        pDimension->setDimsd2(pDimstyle->dimsd2());
    if (pDimension->dimse1() != pDimstyle->dimse1())
        pDimension->setDimse1(pDimstyle->dimse1());
    if (pDimension->dimse2() != pDimstyle->dimse2())
        pDimension->setDimse2(pDimstyle->dimse2());
    if (pDimension->dimsoxd() != pDimstyle->dimsoxd())
        pDimension->setDimsoxd(pDimstyle->dimsoxd());
    if (pDimension->dimtad() != pDimstyle->dimtad())
        pDimension->setDimtad(pDimstyle->dimtad());
    if (pDimension->dimtdec() != pDimstyle->dimtdec())
        pDimension->setDimtdec(pDimstyle->dimtdec());
    if (fabs(pDimension->dimtfac() - pDimstyle->dimtfac()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimtfac(pDimstyle->dimtfac());
    if (pDimension->dimtih() != pDimstyle->dimtih())
        pDimension->setDimtih(pDimstyle->dimtih());
    if (pDimension->dimtix() != pDimstyle->dimtix())
        pDimension->setDimtix(pDimstyle->dimtix());
    if (fabs(pDimension->dimtm() - pDimstyle->dimtm()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimtm(pDimstyle->dimtm());
    if (pDimension->dimtmove() != pDimstyle->dimtmove())
        pDimension->setDimtmove(pDimstyle->dimtmove());
    if (pDimension->dimtofl() != pDimstyle->dimtofl())
        pDimension->setDimtofl(pDimstyle->dimtofl());
    if (pDimension->dimtoh() != pDimstyle->dimtoh())
        pDimension->setDimtoh(pDimstyle->dimtoh());
    if (pDimension->dimtol() != pDimstyle->dimtol())
        pDimension->setDimtol(pDimstyle->dimtol());
    if (pDimension->dimtolj() != pDimstyle->dimtolj())
        pDimension->setDimtolj(pDimstyle->dimtolj());
    if (fabs(pDimension->dimtp() - pDimstyle->dimtp()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimtp(pDimstyle->dimtp());
    if (fabs(pDimension->dimtsz() - pDimstyle->dimtsz()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimtsz(pDimstyle->dimtsz());
    if (fabs(pDimension->dimtvp() - pDimstyle->dimtvp()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimtvp(pDimstyle->dimtvp());
    if (pDimension->dimtxsty() != pDimstyle->dimtxsty())
        pDimension->setDimtxsty(pDimstyle->dimtxsty());
    if (fabs(pDimension->dimtxt() - pDimstyle->dimtxt()) > TOLERANCE_RelativeDistanceRatio)
        pDimension->setDimtxt(pDimstyle->dimtxt());
    if (pDimension->dimtzin() != pDimstyle->dimtzin())
        pDimension->setDimtzin(pDimstyle->dimtzin());
    if (pDimension->dimupt() != pDimstyle->dimupt())
        pDimension->setDimupt(pDimstyle->dimupt());
    if (pDimension->dimzin() != pDimstyle->dimzin())
        pDimension->setDimzin(pDimstyle->dimzin());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SetLeaderDimstyleData
(
AcDbLeader*                 pLeader,
AcDbDimStyleTableRecord*    pDimstyle
)
    {
    Acad::ErrorStatus       es = Acad::eOk;
    bool                    isAnnotative = RealDwgUtil::IsObjectAnnotative (pLeader);

    AcCmColor   styleColor = pDimstyle->dimclrd();
    if (pLeader->dimclrd() != styleColor)
        pLeader->setDimclrd(styleColor);

    if (fabs(pLeader->dimgap() - pDimstyle->dimgap()) > TOLERANCE_RelativeDistanceRatio)
        pLeader->setDimgap(pDimstyle->dimgap());
    if (pLeader->dimlwd() != pDimstyle->dimlwd())
        pLeader->setDimlwd(pDimstyle->dimlwd());
    if (pLeader->dimldrblk() != pDimstyle->dimldrblk())
        pLeader->setDimldrblk(pDimstyle->dimldrblk());
    if (pLeader->dimsah() != pDimstyle->dimsah())
        pLeader->setDimsah(pDimstyle->dimsah());
    if (pLeader->dimtad() != pDimstyle->dimtad())
        pLeader->setDimtad(pDimstyle->dimtad());
    if (pLeader->dimtxsty() != pDimstyle->dimtxsty())
        pLeader->setDimtxsty(pDimstyle->dimtxsty());

    if (fabs(pLeader->dimasz() - pDimstyle->dimasz()) > TOLERANCE_RelativeDistanceRatio)
        es = pLeader->setDimasz(pDimstyle->dimasz());
    if (Acad::eOk != es)
        DIAGNOSTIC_PRINTF ("Error setting leader arrowhead size to %g! [%ls]\n", pDimstyle->dimasz(), acadErrorStatusText(es));
    if (fabs(pLeader->dimtxt() - pDimstyle->dimtxt()) > TOLERANCE_RelativeDistanceRatio)
        es = pLeader->setDimtxt(pDimstyle->dimtxt());
    if (Acad::eOk != es)
        DIAGNOSTIC_PRINTF ("Error setting leader text size to %g! [%ls]\n", pDimstyle->dimtxt(), acadErrorStatusText(es));

    // annotative leader would fail on setting dimscale, but we should have taken care of scale prior to this call anyway
    if (isAnnotative)
        return;
    
    if (fabs(pLeader->dimscale() - pDimstyle->dimscale()) > TOLERANCE_ZeroScale && pDimstyle->dimscale() > TOLERANCE_ZeroScale)
        es = pLeader->setDimscale(pDimstyle->dimscale());
    if (Acad::eOk != es)
        DIAGNOSTIC_PRINTF ("Error setting leader dimscale to %g! [%ls]\n", pDimstyle->dimscale(), acadErrorStatusText(es));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SetDimstyleData
(
AcDbEntity*                 pDimOrLeader,
AcDbDimStyleTableRecord*    pDimstyle
)
    {
    AcDbDimension*    pDimension;
    AcDbLeader*       pLeader;

    pDimension  = AcDbDimension::cast (pDimOrLeader);
    pLeader     = AcDbLeader::cast (pDimOrLeader);

    if ( (NULL == pDimension) && (NULL == pLeader) )
        return  BSIERROR;

    if ( ((NULL != pDimension) && pDimension->dimensionStyle().isNull()) || ( (NULL != pLeader) && pLeader->dimensionStyle().isNull()) )
        {
        /*-------------------------------------------------------------------------------
        Dimstyle created from dimension element which does not have a style attached needs
        to point to a style entry in table.  We set it to STANDARD in accordance with how
        a unnamed style gets treated in dimstyle import.
        -------------------------------------------------------------------------------*/
        if (pDimstyle->objectId().isNull())
            {
            AcDbDimStyleTablePointer    pTable (this->GetDatabase()->dimStyleTableId(), AcDb::kForRead);
            if (Acad::eOk != pTable.openStatus())
                {
                DIAGNOSTIC_PRINTF ("Error opening dim style table!\n");
                return  BSIERROR;
                }

            AcDbObjectId         standardStyleId;

            // set dimension to point to style STANDARD
            if (Acad::eOk == pTable->getAt (DIMSTYLE_StandardName, standardStyleId))
                {
                if (NULL != pDimension)
                    pDimension->setDimensionStyle (standardStyleId);
                else
                    pLeader->setDimensionStyle (standardStyleId);
                }
            }
        else
            {
            if (NULL != pDimension)
                pDimension->setDimensionStyle (pDimstyle->objectId());
            else
                pLeader->setDimensionStyle (pDimstyle->objectId());
            }
        }

    if ( ((NULL != pDimension) && pDimension->dimensionStyle().isNull()) || ((NULL != pLeader) && pLeader->dimensionStyle().isNull()) )
        {
        // dimension style is still null, set it to the very first dimstyle in the table
        AcDbDimStyleTablePointer    pTable (this->GetDatabase()->dimStyleTableId(), AcDb::kForRead);
        if (Acad::eOk != pTable.openStatus())
            {
            DIAGNOSTIC_PRINTF ("Error opening dim style table!\n");
            return  BSIERROR;
            }

        AcDbDimStyleTableIterator*    pIter;
        if (Acad::eOk != pTable->newIterator (pIter, true, true))
            return BSIERROR;

        AcDbObjectId    firstStyleId;
        if (Acad::eOk != pIter->getRecordId (firstStyleId))
            {
            // serious trouble: we can't find any dimension style in table!!
            delete pIter;
            return  BSIERROR;
            }
        else
            {
            if (NULL != pDimension)
                pDimension->setDimensionStyle (firstStyleId);
            else
                pLeader->setDimensionStyle (firstStyleId);
            }
        delete pIter;
        }

    // reset dimstyle specific to dimension/leader
    UpdateDimstyle (pDimstyle, pDimension, pLeader);

    /*-----------------------------------------------------------------------------------
    Ideally we should call ODA's pDimension->setDimstyleData method to set the dimension
    data.  The problem is the lack of tolerance check for double variables and thus to
    result in wrong overrides.  For example, a never edited dimasz=0.18 could end up with
    an override of dimasz=0.18 which really should not occur.  My request to ODA to
    improve their method by adding a tolerance for checking doubles did not get any
    response.  Therefore, we have to go through each variable here which is a less
    efficient way.
    -----------------------------------------------------------------------------------*/
    if (NULL != pDimension)
        SetDimensionDimstyleData (pDimension, pDimstyle);
    else
        SetLeaderDimstyleData (pLeader, pDimstyle);

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                ConvertFromDgnContext::SetDimstyleRecordId
(
AcDbEntity*                 pDimOrLeader,
ElementHandleCR             dimElement
)
    {
    AcDbObjectId        tableRecordId;
    DimensionHandler*   dimHandler = dynamic_cast <DimensionHandler*> (&dimElement.GetHandler());
    if (NULL == dimHandler)
        return  tableRecordId;

    AcDbDimension*      pDimension  = AcDbDimension::cast (pDimOrLeader);
    AcDbLeader*         pLeader     = AcDbLeader::cast (pDimOrLeader);
    if ( (NULL == pDimension) && (NULL == pLeader) )
        return  tableRecordId;

    // if the style exists in dimstyle table, set the id to dimension
    DimensionStylePtr   dimStyle = dimHandler->GetDimensionStyle (dimElement);
    if (!dimStyle.IsValid() || RealDwgUtil::IsMultiLeaderStyleName(dimStyle->GetName()))
        return  tableRecordId;

    tableRecordId = this->ExistingObjectIdFromElementId (dimStyle->GetID());

    /*-----------------------------------------------------------------------------------
    Do not check for null style id here.  If there is a dimstyle attached to dimension
    element, set the existing style id.  If no style attached, clean up existing table
    record id in input dimension object, such that an actual dimstyle deletion from
    dimention element can be preserved here.  Dimension with no style will be handled
    later on in setDimstyleData.
    -----------------------------------------------------------------------------------*/
    if (NULL != pDimension)
        pDimension->setDimensionStyle (tableRecordId);
    else
        pLeader->setDimensionStyle (tableRecordId);

    return  tableRecordId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::CreateMTextFromDimElement
(
AcDbMText*                  pMText,
RotMatrixP                  textMatrix,
DPoint2dP                   textSize,
bool*                       pIsUnionText,
ElementHandleCR             elemHandle,
const AcString&             separator
)
    {
    // the input pMText must have been created prior to calling this method
    if (NULL == pMText)
        return  NullObject;

    DisplayHandlerP displayHandler = elemHandle.GetDisplayHandler ();
    if (NULL == displayHandler)
        return  BadElementHandler;

    ElementAgenda   droppedAgenda;
    DropGeometry    dropOptions (DropGeometry::OPTION_Dimensions);
    dropOptions.SetDimensionOptions (DropGeometry::DIMENSION_Geometry);

    if (BSISUCCESS != displayHandler->Drop(elemHandle, droppedAgenda, dropOptions))
        return  DropFailed;

    RealDwgStatus           status = MstnElementUnacceptable;

    FOR_EACH (EditElementHandleR childEeh, droppedAgenda)
        {
        // filter in text & text nodes only:
        MSElementTypes      childType = (MSElementTypes) childEeh.GetElementType ();
        if (TEXT_NODE_ELM != childType && TEXT_ELM != childType)
            continue;

        TextBlockPtr        textBlock = TextBlock::Create (childEeh);
        ACHAR*              contents = NULL == pMText ? NULL : pMText->contents();
        if (NULL == contents || 0 == *contents)
            {
            // create first text
            status = this->MTextFromTextBlock (pMText, *textBlock.get(), childEeh, true);

            // return requested text params
            if (NULL != textSize)
                {
                DRange3d    textRange = textBlock->GetExactRange ();
                textSize->Init (textRange.XLength(), textRange.YLength());
                }

            if (NULL != textMatrix)
                *textMatrix = textBlock->GetOrientation ();
            }
        else
            {
            // insert this in front the previous one, separated by the input separator
            AcDbMText*  pMText1 = new AcDbMText ();
            if (NULL == pMText1)
                return  OutOfMemoryError;

            status = this->MTextFromTextBlock (pMText1, *textBlock.get(), childEeh, false);

            if (RealDwgSuccess == status)
                {
                ACHAR*      string1 = pMText1->contents ();
                ACHAR*      string2 = pMText->contents ();
                AcString    newString = string1 + separator + string2;

                if (NULL != string1)
                    acutDelString (string1);
                if (NULL != string2)
                    acutDelString (string2);

                pMText->setContents (newString);

                if (NULL != pIsUnionText)
                    *pIsUnionText = true;
                }

            delete pMText1;
            }

        if (NULL != contents)
            acutDelString (contents);
        }

    // MTextFromTextBlock sets entity header - do not post set it here!

    return  status;
    }


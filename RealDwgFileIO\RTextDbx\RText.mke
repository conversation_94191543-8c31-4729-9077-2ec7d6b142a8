#----------------------------------------------------------------------------------------
#
#     $Source: mstn/mdlapps/RealDwgFileIO/RTextDbx/RText.mke $
#
#  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
#
#----------------------------------------------------------------------------------------

%ifndef RealDwgVersion
    RealDwgVersion = 2023
%endif

appName = RText

%include mdl.mki
%include realdwg.mki

dirToSearch = $(BuildContext)VendorAPI/RealDwg/Base/
%include cincapnd.mki

CCompOpts + -DBUILD_RTEXT_DBX

## warning LNK4099: PDB 'rxapi.pdb' was not found with 'rxapi.lib(libinit.obj)
CLinkOpts + -Ignore:4099

%if defined(DEBUG) && !defined(NDEBUG)
always:
    |  -------- -------- --------- -------- -------- ---------
    |  Compiler options: $(cDefs) $(CCompOpts) $(cIncs)
    |  -------- -------- --------- -------- -------- ---------
%endif

#----------------------------------------------------------------------
#   Create output directories.
#----------------------------------------------------------------------
always:
    !~@mkdir $(o)
    ~mkdir $(RealDwgFileHandlerDir)

#----------------------------------------------------------------------
#   Set up to use dlmlink.mki
#----------------------------------------------------------------------
DLM_NAME            = $(appName)
DLM_EXTENSION       = .dbx
DLM_DEST            = $(RealDwgFileHandlerDir)
DLM_OBJECT_DEST     = $(o)
DLM_LIBDEF_SRC      = $(baseDir)
DLM_OBJECT_FILES    = $(appObjects)
DLM_EXPORT_DEST     = $(o)
DLM_NOENTRY         = 1
DLM_NO_DEF          = 1
DLM_NO_DLS          = 1
DLM_NOMSBUILTINS    = 1

DLM_CONTEXT_LOCATION = $(BuildContext)Delivery/RealDwg$(RealDwgVersion)/
DLM_LIB_CONTEXT_LOCATION = $(DLM_CONTEXT_LOCATION)
DLM_CREATE_LIB_CONTEXT_LINK = 1

LINKER_LIBRARIES    = $(RealDwgCoreLibs)

#-----------------------------------------------------------------------------
# Compile DLM Source Files
#-----------------------------------------------------------------------------
MultiCompileDepends=$(_MakeFileSpec)
%include MultiCppCompileRule.mki

$(o)RText$(oext)    : $(baseDir)RText.cpp $(baseDir)RText.h ${MultiCompileDepends}

%include MultiCppCompileGo.mki
appObjects =% $(MultiCompileObjectList)

%include linkLibrary.mki

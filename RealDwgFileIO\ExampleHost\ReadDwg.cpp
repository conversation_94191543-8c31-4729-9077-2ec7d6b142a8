/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/ExampleHost/ReadDwg.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include    <DgnPlatform\DgnPlatformApi.h>
#include    <DgnPlatform\DgnFile.h>
#include    "DgnPlatformHosts.h"
#include    "DwgExampleHost.h"

DwgExampleHost*         g_dwgExampleHost;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BentleySystems  
+---------------+---------------+---------------+---------------+---------------+------*/
int             wmain (int argc,wchar_t *argv[])
    {
    if (argc < 2)
        return  1;

    BeFileName  dwgFileName(argv[1]);
    if (dwgFileName.IsEmpty() || !BeFileName::DoesPathExist(dwgFileName.GetName()))
        return  2;
    
    // initialize DgnPlatformHost
    HostInterface::DgnConsoleAppHost::Initialize ();
    if (!Raster::RasterCoreLib::IsInitialized())
        Raster::RasterCoreLib::Initialize (*new Raster::RasterCoreLib::Host);

    // create and initialize DwgPlatformHost
    g_dwgExampleHost = new DwgExampleHost ();
    if (NULL == g_dwgExampleHost)
        return  3;

    // register this app as a file handler so DgnPlatform can demand load us, but do not want file ECProperties:
    RealDwg::DwgPlatformHost::Initialize (*g_dwgExampleHost, true, false);

    // prepare a new DgnDocument to open a DWG file
    DgnDocumentPtr  dgnDocument = DgnDocument::CreateForLocalFile (dwgFileName.GetName());
    if (!dgnDocument.IsValid())
        return  4;

    // create a DgnFile
    DgnFilePtr      dwgFile = DgnFile::Create (*dgnDocument.get(), DgnFileOpenMode::ReadWrite);
    if (!dwgFile.IsValid())
        return  5;

    // load the DWG file and fill the dictionary model
    StatusInt       status;
    if (DGNFILE_STATUS_Success != dwgFile->LoadDgnFile(&status))
        return  6;
        
    // fill the default model section
    if (NULL == dwgFile->LoadRootModelById(NULL, dwgFile->GetDefaultModelId(), true))
        return  7;

    // load DGN ECSchemas - requires folder \ECSchemas\ to exist!
    DgnECManagerR   dgnECManager = DgnECManager::GetManager ();

    bvector<SchemaInfo> schemaInfos;
    dgnECManager.DiscoverSchemas (schemaInfos, *dwgFile, ECSCHEMAPERSISTENCE_Stored);

    if (schemaInfos.size() == 0)
        printf ("No DGNEC schemas are loaded!\n");

    // save the loaded file as a V8 DGN file
    BeFileName  dgnFileName;
    if (argc > 2 && BeFileName::DoesPathExist(BeFileName::GetDirectoryName(argv[2]).c_str()))
        dgnFileName.assign (argv[2]);
    else
        dgnFileName.assign (L"C:\\savedfromdwg.dgn");

    if (BSISUCCESS != dwgFile->DoSaveTo(dgnFileName.c_str(), DgnFileFormatType::V8))
        return  8;

    return  0;    
    }

/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdAcisData.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExt3dSolid : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    /*----------------------------------------------------------------------------------------------
    Some object enablers, such as PipeDesigner and Cadworx, subclass their objects from ACIS objects.
    These objects should be worldDrawn such that they look the same as they are in ACAD.  We only try
    converting ACIS to Parasolids if they are native ACAD objects, or if user explicitly wants solids
    by setting MS_DWG_OEBREP_AS_BREP.
    When MS_DWG_BREP_AS_PROXY is set, it unconditionally goes to worldDraw to create a proxy.
    ----------------------------------------------------------------------------------------------*/
    RealDwgStatus   status = CantCreateAcisElement;
    if (!context.DropBrepAsProxy() && (acObject->isA() == AcDb3dSolid::desc() || context.KeepBrepDerivitiveAsBrep()))
        {
        if (!context.UseAcisOutForBrep())
            status = context.CreateElementFromBrep (outElement, AcDbEntity::cast(acObject));
        if (RealDwgSuccess != status)
            {
            if (CantCreateAcisElement != status)
                DIAGNOSTIC_PRINTF ("Failed Brep conversion for 3DSOLID(%I64d) - try converting it via ACISOUT!\n", context.ElementIdFromObject(acObject));
            status = context.CreateElementFromAcisOut (outElement, AcDbEntity::cast(acObject));
            }
        }

    if (RealDwgSuccess != status && ConversionInChildrenProc != status && WorldDrawFailed != status)
        status = context.WorldDrawToElements (AcDbEntity::cast(acObject), outElement);

    return  status;
    }
};  // ToDgnExt3dSolid

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtBody : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    /*----------------------------------------------------------------------------------------------
    Some object enablers, such as PipeDesigner and Cadworx, subclass their objects from ACIS objects.
    These objects should be worldDrawn such that they look the same as they are in ACAD.  We only try
    converting ACIS to Parasolids if they are native ACAD objects, or if user explicitly wants solids
    by setting MS_DWG_OEBREP_AS_BREP.
    When MS_DWG_BREP_AS_PROXY is set, it unconditionally goes to worldDraw to create a proxy.
    ----------------------------------------------------------------------------------------------*/
    RealDwgStatus   status = CantCreateAcisElement;
    if (!context.DropBrepAsProxy() && (acObject->isA() == AcDbBody::desc() || context.KeepBrepDerivitiveAsBrep()))
        {
        if (!context.UseAcisOutForBrep())
            status = context.CreateElementFromBrep (outElement, AcDbEntity::cast(acObject));
        if (RealDwgSuccess != status)
            {
            if (CantCreateAcisElement != status)
                DIAGNOSTIC_PRINTF ("Failed Brep conversion for BODY(%I64d) - try converting it via ACISOUT!\n", context.ElementIdFromObject(acObject));
            status = context.CreateElementFromAcisOut (outElement, AcDbEntity::cast(acObject));
            }
        }

    if (RealDwgSuccess != status && ConversionInChildrenProc != status && WorldDrawFailed != status)
        status = context.WorldDrawToElements (AcDbEntity::cast(acObject), outElement);

    return  status;
    }
};  // ToDgnExtBody

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtRegion : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    /*----------------------------------------------------------------------------------------------
    Some object enablers, such as PipeDesigner and Cadworx, subclass their objects from ACIS objects.
    These objects should be worldDrawn such that they look the same as they are in ACAD.  We only try
    converting ACIS to Parasolids if they are native ACAD objects, or if user explicitly wants solids
    by setting MS_DWG_OEBREP_AS_BREP.
    When MS_DWG_BREP_AS_PROXY is set, it unconditionally goes to worldDraw to create a proxy.
    ----------------------------------------------------------------------------------------------*/
    if (context.DropBrepAsProxy() || (acObject->isA() != AcDbRegion::desc() && !context.KeepBrepDerivitiveAsBrep()))
        return context.WorldDrawToElements (AcDbEntity::cast(acObject), outElement);

    RealDwgStatus   status = CantCreateAcisElement;
    status = context.CreateElementFromBrep (outElement, AcDbEntity::cast(acObject)); // Should always create a shape or complex shape cannot hatch a BREP

    if (RealDwgSuccess != status && WorldDrawFailed != status)
        {
        if (CantCreateAcisElement != status)
            DIAGNOSTIC_PRINTF ("Failed Brep conversion for BODY(%I64d) - try converting it via ACISOUT!\n", context.ElementIdFromObject(acObject));
        status = context.CreateElementFromAcisOut (outElement, AcDbEntity::cast(acObject));
        }
    if (!outElement.IsValid() || ConversionInChildrenProc == status)
        return  status;

    MSElementCP     element = outElement.GetElementCP ();
    if (RealDwgSuccess == status && element->ehdr.isComplexHeader)
        {
        /*-------------------------------------------------------------------------------
        Workaround QV's uniform symbology override over complex chain segments by adding
        a nonuniform display attribute on the complex header (TR 217691):
        -------------------------------------------------------------------------------*/
        MSElement   newElement;
        element->CopyTo (newElement);

        Display_attribute_nonUniformSymb    nonUniformSymb;
        Display_attribute                   dispAttr;

        nonUniformSymb.value = 0;

        if (SUCCESS == mdlElement_displayAttributeCreate (&dispAttr, NONUNIFORMSYMB_ATTRIBUTE, sizeof nonUniformSymb, (UShort*)&nonUniformSymb) &&
            SUCCESS == mdlElement_displayAttributeAdd (&newElement, &dispAttr))
            outElement.ReplaceElement (&newElement);
        }

    return  status;
    }
};  // ToDgnExtRegion



/*---------------------------------------------------------------------------------**//**
*  The default solids extent is set up for the old V7 design plane - This produces
*  poor results for most DWG files which map the DWG storage units directly to the
*  ACIS design plane.  - Therefore we change the model solid extents temporarily to
*  the DWG storage units*  solid extent and apply an adjustment linkage to the solids
*  created.  After we create the body we reset the model Solid extent back to the original
*  V7 Design plane solid extent so the ACIS routines still work.
*
* @bsimethod                                                    RayBentley      08/2007
+---------------+---------------+---------------+---------------+---------------+------*/
static double           AdjustModelSolidExtentForDwgUnits
(
double&                 solidExtent,
double                  storageUnitSolidExtent,
DgnModelP               model
)
    {
    if (NULL == model)
        return  1.0;

    solidExtent = model->GetModelInfo().GetSolidExtent();

    if (storageUnitSolidExtent > 0.0 && solidExtent != storageUnitSolidExtent)
        {
        ModelInfoPtr    modelInfoCopy = model->GetModelInfoCP()->MakeCopy ();
        if (modelInfoCopy.IsValid() && SUCCESS == modelInfoCopy->SetSolidExtent(storageUnitSolidExtent) && SUCCESS == model->GetDgnModelP()->SetModelInfo(*modelInfoCopy))
            return  storageUnitSolidExtent / (0.0 == solidExtent ? RMAXUI4 : solidExtent);
        }

    return 1.0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
static WCharCP  GetLogFileName (WStringR logFileName, WCharCP satFileName)
    {
    logFileName.assign (satFileName);
    logFileName.erase (logFileName.rfind(L".sat"));
    logFileName.append (L".log");
    return  logFileName.GetWCharCP ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    PsFileToElement 
(
ElementAgendaR          elemList,
WCharCP                 psFileName,
TransformCR             transform,
StandardUnit            units,          // NOT USED...
ElementHandleCP         templateElm,
ConvertToDgnContextR    context
)
    {
    PSolidKernelManager::StartSession ();
    
    double          convertScale = SatFileConversionMP::ConvertScaleFromPsFile (BeFileName (psFileName));
    if (convertScale < 0)
        return CantCreateAcisElement;
    WCharCP         logFileName = NULL;
    WString         logFile;

    if (context.GetSettings().AllowPsolidAcisInteropLogging())
        logFileName = GetLogFileName (logFile, psFileName);

    PK_PART_receive_o_t receiveOptions;
    PK_PART_receive_o_m (receiveOptions);
    receiveOptions.transmit_format = PK_transmit_format_text_c;

    int nParts = 0;
    PK_PART_t*  parts = nullptr;
    PK_UCHAR_t const*   pkname = reinterpret_cast <PK_UCHAR_t const*> (psFileName);

    auto pkerror = PK_PART_receive_u (pkname, &receiveOptions, &nParts, &parts);
    if (pkerror == PK_ERROR_wrong_format)
        {
        // try binary XMT format again
        receiveOptions.transmit_format = PK_transmit_format_binary_c;
        pkerror = PK_PART_receive_u (pkname, &receiveOptions, &nParts, &parts);
        }

    if (pkerror == PK_ERROR_no_errors)
        {
        Transform   entityTransform;
        entityTransform.ScaleMatrixColumns (transform, convertScale, convertScale, convertScale);

        for (int i = 0; i < nParts; i++)
            {
            ISolidKernelEntityPtr  kernelEnt = PSolidKernelManager::CreateEntityPtr (parts[i], entityTransform);
            EditElementHandle      eeh;

            if (!kernelEnt.IsValid () || SUCCESS != PSolidUtil::CreateElement (eeh, *kernelEnt, templateElm, *context.GetModel ()))
                {
                DIAGNOSTIC_PRINTF ("Failed creating element(ID=%I64d) from solid kernel entity!\n", templateElm->GetElementId());
                continue;
                }

            if (eeh.IsValid ())
                elemList.Insert (eeh);
            }
        }

    if (nullptr != parts)
        PK_MEMORY_free (parts);
    
    return elemList.IsEmpty() ? CantCreateAcisElement : RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    SatFileToElement 
(
ElementAgendaR          elemList,
WCharCP                 satFileName,
TransformCR             transform,
StandardUnit            units,          // NOT USED...
ElementHandleCP         templateElm,
ConvertToDgnContextR    context
)
    {
    PSolidKernelManager::StartSession ();

    bvector<int>    xmtEntities;
    double          convertScale;
    WCharCP         logFileName = NULL;
    WString         logFile;

    if (context.GetSettings().AllowPsolidAcisInteropLogging())
        logFileName = GetLogFileName (logFile, satFileName);

    if (SUCCESS == PSolidAcisInterop::SATFileToXMTEntities (xmtEntities, satFileName, &convertScale, logFileName))
        {
        Transform   entityTransform;

        entityTransform.ScaleMatrixColumns (transform, convertScale, convertScale, convertScale);

        FOR_EACH (PK_ENTITY_t entityTag, xmtEntities)
            {
            ISolidKernelEntityPtr  kernelEnt = PSolidKernelManager::CreateEntityPtr (entityTag, entityTransform);
            EditElementHandle      eeh;

            if (!kernelEnt.IsValid () || SUCCESS != PSolidUtil::CreateElement (eeh, *kernelEnt, templateElm, *context.GetModel ()))
                {
                DIAGNOSTIC_PRINTF ("Failed creating element(ID=%I64d) from solid kernel entity!\n", templateElm->GetElementId());
                continue;
                }

            if (eeh.IsValid ())
                elemList.Insert (eeh);
            }
        }
    
    return elemList.IsEmpty() ? CantCreateAcisElement : RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     MoveAcisEntityToWorldOrigin (DPoint3dR translation, AcDbEntityP acEntity)
    {
    /*------------------------------------------------------------------------------------------------------
    Parasolid kernel has a 1 KM bounding box, which can throw out ACIS entities that are located near this
    limit.  In such a case, we translate the body to the world origin such that its output of .sat file will 
    have smaller coordinate readout and it will have a better chance to succeed the Parasolid conversion.
    We will later post-transform the new Parasold element back to the entity's origin.
    ------------------------------------------------------------------------------------------------------*/
    AcDbExtents extents;
    if (Acad::eOk == acEntity->getGeomExtents(extents))
        {
        AcGePoint3d     min = extents.minPoint ();
        AcGePoint3d     max = extents.maxPoint ();
        if (fabs(min.x) > MAX_ACISPoint || fabs(min.y) > MAX_ACISPoint || fabs(min.z) > MAX_ACISPoint ||
            fabs(max.x) > MAX_ACISPoint || fabs(max.y) > MAX_ACISPoint || fabs(max.z) > MAX_ACISPoint)
            {
            AcGePoint3d center = min + max.asVector();
            center *= 0.5;

            AcGeMatrix3d    acMatrix = AcGeMatrix3d::kIdentity;
            acMatrix.setToTranslation (center.asVector().negate());

            // prepare the entity for write
            Acad::ErrorStatus   status = acEntity->upgradeOpen ();
            if ((Acad::eOk == status || Acad::eWasOpenForWrite == status) && Acad::eOk == acEntity->transformBy(acMatrix))
                {
                RealDwgUtil::DPoint3dFromGePoint3d (translation, center);
                return  true;
                }
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/19
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::CreateAcisKernelElement (ElementAgendaR elems, AcDbEntityP asmEntity, bool isSurface)
    {
    WCharCP cellName = isSurface ? L"Smart Surface" : L"Smart Solid";
    WCharCP acisFile = this->GetAcisOutputFilePath ();
    if (acisFile == nullptr || !BeFileName::DoesPathExist(acisFile))
        return  CantCreateAcisElement;

    // prepare to draw the ASM entity in wireframe
    auto oldRegentype = m_worldDrawRegenType;
    auto oldViewdir = m_customObjectViewDirection;
    m_worldDrawRegenType = AcGiRegenType::kAcGiStandardDisplay;
    m_customObjectViewDirection.set (-0.577, -0.577, 0.557);

    // stroke entity to collect wireframe proxy:
    EditElementHandle   brepCell;
    auto status = this->WorldDrawToElements (asmEntity, brepCell, AcDbObjectId::kNull, cellName);

    m_worldDrawRegenType = oldRegentype;
    m_customObjectViewDirection = oldViewdir;

    if (status != RealDwgSuccess)
        return  RealDwgStatus::WorldDrawFailed;

    // invert the proxy cell transform
    bool        hasTransform = false;
    Transform   transform;
    RotMatrix   matrix;
    DPoint3d    origin;
    if (CellUtil::ExtractRotation(matrix, brepCell) == BSISUCCESS && CellUtil::ExtractOrigin(origin, brepCell) == BSISUCCESS &&
        (!origin.IsEqual(DPoint3d::FromZero(), TOLERANCE_UORPointEqual) || !matrix.IsIdentity()))
        {
        matrix.Transpose ();
        origin.Negate ();

        this->GetTransformFromDGN().Multiply (origin);

        transform.InitFrom (matrix, origin);
        hasTransform = true;
        }

    // read in ACIS buffer and append it as a DgnStore element to the cell
    if (PSolidAcisInterop::AppendAcisToBrepCell(brepCell, acisFile, hasTransform ? &transform : nullptr) != BSISUCCESS)
        return  RealDwgStatus::WorldDrawFailed;

    //Defect 1068059:[Regression][InCycle] ACIS surfaces could not be modified
    brepCell.GetElementP()->hdr.ehdr.locked = false;

    elems.Insert (brepCell);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    JP.Wenger       04/2019
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertToDgnContext::CreateElementFromAcisOut
(
EditElementHandleR      outElement,
AcDbEntityP             pEntity,
bool                    isSurface
)
    {
    // don't bother multi-processor if we are creating ACIS kernel elements:
    if (this->UseAcisOutForBrep())
        return ConvertToDgnContext::_CreateElementFromAcisOut (outElement, pEntity, isSurface);
    return _CreateElementFromAcisOut (outElement, pEntity, isSurface);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertToDgnContext::_CreateElementFromAcisOut
(
EditElementHandleR      outElement,
AcDbEntityP             pEntity,
bool                    isSurface
)
{
    // don't go any further if InterOp is not available
    if (!this->CanConvertACIS())
        return  CantCreateAcisElement;

    WCharCP             acisFile = GetAcisOutputFilePath ();
    if (0 == acisFile[0])
        {
        DIAGNOSTIC_PRINTF ("Cannot find ACIS output file\n.");
        return  CantCreateAcisElement;
        }

    // make a copy of the ACIS entity such that we can transform it to the world origin for the sake of Parasolids bounding cube.
    // also, AcDbBody::acisOut appears to change the input entity such that a subsequent call to worldDraw may crash ASM!
    AcDbEntityP         entityCopy = dynamic_cast <AcDbEntityP> (pEntity->isA()->create());
    if (NULL == entityCopy)
        {
        DIAGNOSTIC_PRINTF ("ACIS entity clone failed!\n.");
        entityCopy = pEntity;
        }
    else
        {
        entityCopy->copyFrom (pEntity);
        }
    
    // will either convert ACIS to Parasolid, or create an ACIS element
    bool        convertToParasolid = !this->UseAcisOutForBrep ();
    bool        movedOrigin = false;
    DPoint3d    oldOrigin = DPoint3d::FromZero ();

    if (convertToParasolid)
        movedOrigin = MoveAcisEntityToWorldOrigin (oldOrigin, entityCopy);

    // an entity list for AcDbBody::acisOut - the only member is the copy of the input entity
    AcDbVoidPtrArray    entities;
    entities.append ((void*)entityCopy);

    Acad::ErrorStatus   status = AcDbBody::acisOut(acisFile, entities);

    // restore the input entity or clean up the clone before return to the caller in case a worldDraw is needed:
    if (entityCopy == pEntity)
        {
        // clone has failed - move the source entity back to its original location
        if (movedOrigin)
            {
            AcGeMatrix3d    acMatrix = AcGeMatrix3d::kIdentity;
            AcGeVector3d    acVector = RealDwgUtil::GeVector3dFromDPoint3d (oldOrigin);
            acMatrix.setToTranslation (acVector.negate());
            pEntity->transformBy (acMatrix);
            }
        }
    else
        {
        // clone succeeded - delete the clone:
        delete entityCopy;
        }

    if (Acad::eOk != status)
        return  CantCreateAcisElement;

    int isolines = m_pFileHolder->GetDatabase()->isolines();

    /*
    MS Isoline adjustment for UNTRIMMED ACAD bodies (isolines = n>0):

    Sphere:
    ACAD: equator, n half-meridians, 6*(n/8)+2*((n%8)/3) additional parallels
    MS  : equator, n half-meridians, NO additional parallels
    Adjustment: impossible without new low-level code

    Cone:
    ACAD/MS: n slant-height lines
    Adjustment: unnecessary

    Torus:
    ACAD/MS: n parallels/meridians---always starting w/ innermost meridian
    Adjustment: unnecessary

    B-spline Surface: (open directions have 2 exterior contours; closed, 1)
    ACAD: # interior contours in each direction = n>=4 ? n-4 : 0
    MS  : # interior contours in each direction = n>=2 ? n-2 : 0
    Adjustment: n = n>=4 ? n-2 : 2

    The adjustments (at this point, only for B-spline surfaces) are done
    deep down in the bowels of the acis conversion code if the last (new)
    parameter bAlternateIsos in the below function is nonzero.  -- DA4
    */

    /* Unlike the case of the SmartSolid class, the data stored in the DwgAcisData is
    the data of ACIS bodies alone.  It does not include any linkages as in the
    case of SmartSolid.  Hence, everytime we will need to convert the binary
    data into a set of elements */

    DPoint3d        points[2];
    points[0].x = points[0].y = points[0].z = 0;
    points[1].x = points[1].y = points[1].z = 0;

    EditElementHandle   templateElem;
    if (RealDwgSuccess != this->CreateElementFromVertices(templateElem, points, 2, false, this->GetTransformToDGN()))
        return  CantCreateAcisElement;
    this->ElementHeaderFromEntity (templateElem, pEntity, HEADERRESTOREMASK_Default ^ HEADERRESTOREMASK_XAttributes);

    double          solidExtent = 0.0;
    double          adjustScale = AdjustModelSolidExtentForDwgUnits (solidExtent, m_storageUnitSolidExtent, m_model);

    Transform       transform, compTransform;
    this->GetTransformForACISBodies (true, &transform);
    compTransform.InitProduct (transform, this->GetTransformToDGN());

    if (movedOrigin)
        {
        DPoint3d    translation;
        compTransform.GetTranslation (translation);

        this->GetTransformToDGN().Multiply (oldOrigin);
        translation.Add (oldOrigin);

        compTransform.SetTranslation (translation);
        }

    ElementAgenda   elemList;
    RealDwgStatus   convertStatus = RealDwgStatus::NotApplicable;

    // either convert ACIS to Parasolid then create a PSOLID element, or directly create an ACIS kernel element
    if (convertToParasolid)
        convertStatus = SatFileToElement (elemList, acisFile, compTransform, StandardUnit::MetricMeters, &templateElem, *this);
    else
        convertStatus = this->CreateAcisKernelElement (elemList, pEntity, isSurface);

    if (solidExtent != m_storageUnitSolidExtent)
        {
        ModelInfoPtr    modelInfo = m_model->GetModelInfoCP()->MakeCopy ();
        if (SUCCESS == modelInfo->SetSolidExtent(solidExtent))
            m_model->GetDgnModelP()->SetModelInfo (*modelInfo);
        }

    // now that the solid extent has been restored, we can return the error if the conversion has failed:
    if (RealDwgSuccess != convertStatus)
        return  convertStatus;

    if (1.0 != adjustScale)
        {
        FOR_EACH (EditElementHandleR brepElement, elemList)
            BrepCellHeaderHandler::AdjustBRepDataScale (brepElement, adjustScale, true);
        }

    // TR# 132287 - If body contains multiple wires then it will come back as seperate elements - wrap these in cell.
    if (elemList.size() > 1)
        this->CreateProxyCell (outElement, elemList, pEntity);
    else
        outElement.Duplicate (elemList.front());

    HandlerR        handler = outElement.GetHandler ();

    if (!this->GetLocalTransform().isIdentity())
        {
        Transform   localTrans;

        localTrans.InverseOf (this->GetLocalTransform());
        localTrans.InitProduct (localTrans, this->GetTransformToModelFromDGN());
        localTrans.InitProduct (this->GetTransformFromModelToDGN(), localTrans);
        handler.ApplyTransform (outElement, TransformInfo(localTrans));
        }

    // If creating 2d dgn...need to flatten pDescr which will always be returned as 3d
    if (!this->GetThreeD())
        {
        Transform       flattenTrans = Transform::FromIdentity ();
        DVec3d          zAxis = DVec3d::From (0.0, 0.0, 1.0);
        // ConvertTo2d requires a 2D transform. TFS#3892 - the 2nd part of the fix is in brep handler to force the cell's wireframe children as 3D.
        flattenTrans.form3d[2][2] = 0.0;
        handler.ConvertTo2d (outElement, flattenTrans, zAxis);
        }

    this->ElementHeaderFromEntity (outElement, pEntity,  HEADERRESTOREMASK_Default ^ HEADERRESTOREMASK_Color);

    // Set the header color in case the body is "by block"... (TR# 174544)
    outElement.GetElementP()->hdr.dhdr.symb.color = templateElem.GetElementCP()->hdr.dhdr.symb.color;

    // for cells, materials are set on the first child elements.
    if (CELL_HEADER_ELM == outElement.GetElementType() || SHAREDCELL_DEF_ELM == outElement.GetElementType())
        {
        ElementId   materialId = MaterialManager::GetManagerR().FindMaterialAttachmentId (outElement);
        if (0 != materialId && INVALID_ELEMENTID != materialId)
            {
            // would like to use ChildEditElemIter child(outElement), but it does not expose children!
            EditElementHandle   child(outElement.GetElementDescrCP()->h.firstElem, false, false, m_model);
            if (child.IsValid())
                MaterialManager::GetManagerR().SetMaterialAttachmentId (child, materialId);
            }
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    JP.Wenger       04/2019
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertToDgnMultiProcessingContext::_CreateElementFromAcisOut
(
EditElementHandleR      outElement,
AcDbEntityP             pEntity,
bool                    isSurface
)
    {
    // don't go any further if InterOp is not available
    if (!this->CanConvertACIS())
        return  CantCreateAcisElement;

    WCharCP             acisFile = GetAcisOutputFilePath ();
    if (0 == acisFile[0])
        {
        DIAGNOSTIC_PRINTF ("Cannot find ACIS output file\n.");
        return  CantCreateAcisElement;
        }

    // make a copy of the ACIS entity such that we can transform it to the world origin for the sake of Parasolids bounding cube:
    AcDbEntityP         entityCopy = dynamic_cast <AcDbEntityP> (pEntity->isA()->create());
    if (NULL == entityCopy)
        {
        DIAGNOSTIC_PRINTF ("ACIS entity clone failed!\n.");
        entityCopy = pEntity;
        }
    else
        {
        entityCopy->copyFrom (pEntity);
        }

    DPoint3d            oldOrigin;
    bool                movedOrigin = MoveAcisEntityToWorldOrigin (oldOrigin, entityCopy);

    AcDbVoidPtrArray    entities;
    entities.append ((void*)entityCopy);

    if (SatFileProcessingMode::WriteToFile == m_satFileProcessingMode)
        {
        //Create SAT file
        CurrentSatToPs ().SetSatOutputStrategy ([&entities] (WCharCP fileName)
            {
            Acad::ErrorStatus status = AcDbBody::acisOut (fileName, entities);
            return Acad::eOk == status;
            });

        //We failed to convert the current Acis entity to PS.
        //We convert it to a SAT file, and we map the filename of the sat file to the entity id.
        //Thereafter, multiple processes will convert this SAT file to PS asynchronously.
        BeFileName fullFileName = CurrentSatToPs ().OutputToSatAsynch ();
        BeFileName satBaseName = BeFileName (BeFileName::GetFileNameWithoutExtension (fullFileName.c_str ()).c_str ());
        m_satEntityMapping[satBaseName] = m_curEntityId;
        m_curPSFileName = fullFileName;

        CurrentSatToPs ().SpawnSatToPsChildrenProcesses ();
        }

    // restore the input entity or clean up the clone before return to the caller in case a worldDraw is needed:
    if (entityCopy == pEntity)
        {
        // clone has failed - move the source entity back to its original location
        if (movedOrigin)
            {
            AcGeMatrix3d    acMatrix = AcGeMatrix3d::kIdentity;
            AcGeVector3d    acVector = RealDwgUtil::GeVector3dFromDPoint3d (oldOrigin);
            acMatrix.setToTranslation (acVector.negate());
            pEntity->transformBy (acMatrix);
            }
        }
    else
        {
        // clone succeeded - delete the clone:
        delete entityCopy;
        }

    if (SatFileProcessingMode::WriteToFile == m_satFileProcessingMode)
        return ConversionInChildrenProc;

    BeFileName psFileName = m_curPSFileName;
    if (!BeFileName::DoesPathExist (psFileName.c_str ()))
        return CantCreateAcisElement;

    int isolines = m_pFileHolder->GetDatabase()->isolines();

    /*
    MS Isoline adjustment for UNTRIMMED ACAD bodies (isolines = n>0):

    Sphere:
    ACAD: equator, n half-meridians, 6*(n/8)+2*((n%8)/3) additional parallels
    MS  : equator, n half-meridians, NO additional parallels
    Adjustment: impossible without new low-level code

    Cone:
    ACAD/MS: n slant-height lines
    Adjustment: unnecessary

    Torus:
    ACAD/MS: n parallels/meridians---always starting w/ innermost meridian
    Adjustment: unnecessary

    B-spline Surface: (open directions have 2 exterior contours; closed, 1)
    ACAD: # interior contours in each direction = n>=4 ? n-4 : 0
    MS  : # interior contours in each direction = n>=2 ? n-2 : 0
    Adjustment: n = n>=4 ? n-2 : 2

    The adjustments (at this point, only for B-spline surfaces) are done
    deep down in the bowels of the acis conversion code if the last (new)
    parameter bAlternateIsos in the below function is nonzero.  -- DA4
    */

    /* Unlike the case of the SmartSolid class, the data stored in the DwgAcisData is
    the data of ACIS bodies alone.  It does not include any linkages as in the
    case of SmartSolid.  Hence, everytime we will need to convert the binary
    data into a set of elements */

    DPoint3d        points[2];
    points[0].x = points[0].y = points[0].z = 0;
    points[1].x = points[1].y = points[1].z = 0;

    EditElementHandle   templateElem;
    if (RealDwgSuccess != this->CreateElementFromVertices(templateElem, points, 2, false, this->GetTransformToDGN()))
        return  CantCreateAcisElement;
    this->ElementHeaderFromEntity (templateElem, pEntity, HEADERRESTOREMASK_Default ^ HEADERRESTOREMASK_XAttributes);

    double          solidExtent = 0.0;
    double          adjustScale = AdjustModelSolidExtentForDwgUnits (solidExtent, m_storageUnitSolidExtent, m_model);

    Transform       transform, compTransform;
    this->GetTransformForACISBodies (true, &transform);
    compTransform.InitProduct (transform, this->GetTransformToDGN());

    if (movedOrigin)
        {
        DPoint3d    translation;
        compTransform.GetTranslation (translation);

        this->GetTransformToDGN().Multiply (oldOrigin);
        translation.Add (oldOrigin);

        compTransform.SetTranslation (translation);
        }

    ElementAgenda   elemList;

    RealDwgStatus   convertStatus = PsFileToElement (elemList, psFileName.c_str (), compTransform, StandardUnit::MetricMeters, &templateElem, *this);

    if (solidExtent != m_storageUnitSolidExtent)
        {
        ModelInfoPtr    modelInfo = m_model->GetModelInfoCP()->MakeCopy ();
        if (SUCCESS == modelInfo->SetSolidExtent(solidExtent))
            m_model->GetDgnModelP()->SetModelInfo (*modelInfo);
        }

    // now that the solid extent has been restored, we can return the error if the conversion has failed:
    if (RealDwgSuccess != convertStatus)
        return  convertStatus;

    BeFileName::BeDeleteFile (psFileName.c_str ());

    if (1.0 != adjustScale)
        {
        FOR_EACH (EditElementHandleR brepElement, elemList)
            BrepCellHeaderHandler::AdjustBRepDataScale (brepElement, adjustScale, true);
        }

    // TR# 132287 - If body contains multiple wires then it will come back as seperate elements - wrap these in cell.
    if (elemList.size() > 1)
        this->CreateProxyCell (outElement, elemList, pEntity);
    else
        outElement.Duplicate (elemList.front());

    HandlerR        handler = outElement.GetHandler ();

    if (!this->GetLocalTransform().isIdentity())
        {
        Transform   localTrans;

        localTrans.InverseOf (this->GetLocalTransform());
        localTrans.InitProduct (localTrans, this->GetTransformToModelFromDGN());
        localTrans.InitProduct (this->GetTransformFromModelToDGN(), localTrans);
        handler.ApplyTransform (outElement, TransformInfo(localTrans));
        }

    // If creating 2d dgn...need to flatten pDescr which will always be returned as 3d
    if (!this->GetThreeD())
        {
        Transform       flattenTrans = Transform::FromIdentity ();
        DVec3d          zAxis = DVec3d::From (0.0, 0.0, 1.0);
        // ConvertTo2d requires a 2D transform. TFS#3892 - the 2nd part of the fix is in brep handler to force the cell's wireframe children as 3D.
        flattenTrans.form3d[2][2] = 0.0;
        handler.ConvertTo2d (outElement, flattenTrans, zAxis);
        }

    this->ElementHeaderFromEntity (outElement, pEntity,  HEADERRESTOREMASK_Default ^ HEADERRESTOREMASK_Color);

    // Set the header color in case the body is "by block"... (TR# 174544)
    outElement.GetElementP()->hdr.dhdr.symb.color = templateElem.GetElementCP()->hdr.dhdr.symb.color;

    // for cells, materials are set on the first child elements.
    if (CELL_HEADER_ELM == outElement.GetElementType() || SHAREDCELL_DEF_ELM == outElement.GetElementType())
        {
        ElementId   materialId = MaterialManager::GetManagerR().FindMaterialAttachmentId (outElement);
        if (0 != materialId && INVALID_ELEMENTID != materialId)
            {
            // would like to use ChildEditElemIter child(outElement), but it does not expose children!
            EditElementHandle   child(outElement.GetElementDescrCP()->h.firstElem, false, false, m_model);
            if (child.IsValid())
                MaterialManager::GetManagerR().SetMaterialAttachmentId (child, materialId);
            }
        }

    return  RealDwgSuccess;
    }

#if RealDwgVersion < 2012
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    SetEntityHeaderColor
(
AcDbEntityP             entity,
AcCmColor               color
)
    {
    RecordingFiler      filer (60);
    filer.RecordData (entity);

#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("ACIS entity fields from DWGOUT filing:");
    /*-----------------------------------------------------------------------------------
    ACIS entity fields from DWGOUT filing:
      0: kDwgSoftPointerId, NULL
      1: kDwgUInt32,    0 (0x0)
      2: kDwgHardOwnershipId, NULL
      3: kDwgHardPointerId, NULL
      4: kDwgHardPointerId, NULL
      5: kDwgHardPointerId, NULL
      6: kDwgUInt8,    0 (0x0)
      7: kDwgBool false
      8: kDwgBool false
      9: kDwgBool false
     10: kDwgBool true
     11: kDwgBool true
     12: kDwgHardPointerId, NULL
     13: kDwgUInt16,  257 (0x101)       <= color index in R2010
     14: kDwgReal 1.000000
     15: kDwgUInt16,    0 (0x0)
     16: kDwgUInt8,   28 (0x1c)
     17: kDwgUInt32,    0 (0x0)
     18: kDwgHardOwnershipId, NULL
    -----------------------------------------------------------------------------------*/
#endif
    FilerDataList&      dataList = filer.GetDataList ();

#if RealDwgVersion == 2009
    int                 indexAt = 10;
#else
    int                 indexAt = 13;
#endif

    UInt16FilerData*    uint16Data = dynamic_cast <UInt16FilerData *> (dataList[indexAt]);
    if (NULL == uint16Data)
        return BadDataSequence;

#if RealDwgVersion >= 2011
    // preserve hight bit value on this uint16:
    UInt16              oldValue = uint16Data->GetValue ();
    uint16Data->SetValue ((oldValue&0xF000) | color.colorIndex());
#else
    uint16Data->SetValue (color.colorIndex());
#endif

    Acad::ErrorStatus   es = filer.PlaybackData (entity);
#if defined (REALDWG_FILER_DEBUG)
    if (Acad::eOk == es)
        {
        RecordingFiler check (60);
        check.RecordData (entity);
        check.DumpList ("ACIS entity after DWGIN filing:");
        }
#endif

    return (Acad::eOk == es) ? RealDwgSuccess : BadDataSequence;
    }
#endif // RealDwgVersion < 2012

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
static int      SatVersionForDwg (DwgFileVersion dwgVersion)
    {
    int         majorVersion = 0, minorVersion = 0;
    if (dwgVersion <= 0 || dwgVersion >= DwgFileVersion_2004)
        {
        majorVersion = 7;
        }
    else if (dwgVersion > DwgFileVersion_14)
        {
        majorVersion = 4;
        }
    else
        {
        majorVersion = 1;
        minorVersion = 6;
        }

    return  (majorVersion * 100 + minorVersion);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Brien.Bastings  12/10
+---------------+---------------+---------------+---------------+---------------+------*/
static void     AdjustEntityTransform (TransformR entityTransform, ModelInfoCR modelInfo, bool isDictionary, UnitDefinitionCR targetUnit)
    {
    // NOTE: Exported bodies are sized so that 1.0 should represent 1.0 meter...
    UnitDefinition  unitDefStorage = modelInfo.GetStorageUnit ();
    UnitDefinition  unitDefMeter = UnitDefinition::GetStandardUnit (StandardUnit::MetricMeters);
    double          factor = 1.0;

    unitDefStorage.GetConversionFactorFrom (factor, unitDefMeter);

    double          uorPerStorage = modelInfo.GetUorPerStorage ();
    double          uorPerMeterDgn = uorPerStorage * factor;
    double          uorToMeter = (0.0 == uorPerMeterDgn) ? 1.0 : (1.0 / uorPerMeterDgn);

    /*---------------------------------------------------------------------------------------------------------
    Also apply the target unit scale to meters so we can use meters for SAT file.  When the SAT units are smaller
    then meters, InterOp can fail on large body size as a case shown in TFS# 67780.  Using meters for SAT and
    scaling the body from meters prevents InterOp to scale the input Parasolid body.
    ---------------------------------------------------------------------------------------------------------*/
    if (BSISUCCESS == targetUnit.GetConversionFactorFrom(factor, unitDefMeter))
        uorToMeter *= factor;

    entityTransform.ScaleMatrixRows (entityTransform, uorToMeter, uorToMeter, uorToMeter);

    // translate the body to ACIS output units (target units)
    if (BSISUCCESS != unitDefStorage.GetConversionFactorFrom(factor, targetUnit))
        factor = 1.0;

    double          uorToTarget = (0.0 == uorPerStorage) ? 1.0 : 1.0 / (factor * uorPerStorage);
    DPoint3d        translation;
    entityTransform.GetTranslation (translation);
    if (!isDictionary)
        translation.Subtract (modelInfo.GetGlobalOrigin());
    translation.Scale (uorToTarget);
    entityTransform.SetTranslation (translation);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    SatFileFromKernelEntity (WCharCP satFileName, ISolidKernelEntityR kernelEnt, ConvertFromDgnContextR context)
    {
    PK_ENTITY_t     entityTag = PSolidUtil::GetEntityTagForModify (kernelEnt);
    if (BSISUCCESS != PSolidUtil::CheckBody(entityTag, false, false, true))
        DIAGNOSTIC_PRINTF ("Error found in Parasolid body %d\n", entityTag);

    Transform       entityTransform = kernelEnt.GetEntityTransform ();
    entityTransform.InitProduct (context.GetTransformFromDGN(), entityTransform);

    // the target units have been invert scaled to meters in above call, so use meters for the output SAT body:
    StandardUnit    units = StandardUnit::MetricMeters;

    WCharCP         logFileName = NULL;
    WString         logFile;
    if (context.GetSettings().AllowPsolidAcisInteropLogging())
        logFileName = GetLogFileName (logFile, satFileName);
                            
    StatusInt status = PSolidAcisInterop::XMTEntitiesToSATFile (satFileName, &entityTag, &entityTransform, 1, units, SatVersionForDwg(context.GetTargetVersion()), logFileName);
 
    return  SUCCESS == status ? RealDwgSuccess : CantCreateAcisElement;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SetAcisEntityFromKernelEntity (AcDbObjectP& newObject, AcDbObjectP desiredObject, ElementHandleCR elemIn, ISolidKernelEntityR kernelEnt)
    {
    // don't go further if InterOp is not available
    if (!this->CanConvertACIS())
        return  CantCreateAcisElement;

#ifdef BREP_FROM_PARSOLID
    AcBRepFromParasolid     parasolid2AcBrep (this);
    parasolid2AcBrep.Convert (PSolidUtil::GetEntityTagForModify(kernelEnt));
#endif

    WCharCP         satFileName = this->GetAcisOutputFilePath ();


    return ConvertSolidElementToAcis(newObject, desiredObject, elemIn);

    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SetAcDbEntityFromAcisIn (AcDbObjectP& newObject, AcDbObjectP desiredObject, WCharCP satFileName, ElementHandleCR elemIn)
    {
    /*------------------------------------------------------------------------------------------------------------
    This method attempts to create an ACIS entity from the SAT file. After a valid ACIS entity is created:
    1. If desiredObject == nullptr, simply set the newly created ACIS entity to newObject and return success.
    2. If the newly created ACIS entity type is the same as desired, copy data to desiredObject and return success.
    3. If the two are not the same type, set the new entity to newObject and return ReplacedObjectType, in which
        case the caller may have to reset the object handle as needed.
    ------------------------------------------------------------------------------------------------------------*/
    RealDwgStatus       status = CantCreateAcisElement;
    AcDbVoidPtrArray    entities;
    if (Acad::eOk == AcDbBody::acisIn(satFileName, entities))
        {
        /*-------------------------------------------------------------------
        Attempt to retain original color-adesk-attrib's in ACIS data:

        If the entity color has been changed, set new entity's color from
        element's; otherwise, keep the color coming from ACIS data by setting
        color for entity header only, not propagating into AICS body.  This
        is to workaround the flag doSubents in setColor that does not seem to
        work in this case.

        Do not add xattributes to the template entity which can be saved to database -
        must do that directly on the source entity.
        -------------------------------------------------------------------*/
        AcDbBody*       entityTemplate = new AcDbBody ();
        this->UpdateEntityPropertiesFromElement(entityTemplate, elemIn, HEADERRESTOREMASK_Default & ~HEADERRESTOREMASK_XAttributes);
        // handle level id=0, as in TR 274630:
        if (!entityTemplate->layerId().isValid())
            entityTemplate->setLayer (this->GetFileHolder().GetDatabase()->layerZero());

        int                 numEntities = entities.length ();
        EditElementHandle   copyElem;

        // use a copy of the element with ID=0 for adding new trailing entities into database:
        if (numEntities > 1)
            {
            copyElem.Duplicate (elemIn);

            MSElementP  el = copyElem.GetElementP ();
            if (nullptr != el)
                el->ehdr.uniqueId = 0;
            }

        if (numEntities > 0)
            status = RealDwgSuccess;

        for (int i = 0; i < numEntities; i++)
            {
            AcDbEntityP entity = (AcDbEntityP)entities.at (i);

#if RealDwgVersion < 2012
            // RD2012 seems to have fixed TR 280756
            if (acEntity->color() == entityTemplate->color())
                SetEntityHeaderColor (entity, acEntity->color());
            else
#endif
            entity->setColor (entityTemplate->color());
            entity->setLineWeight (entityTemplate->lineWeight());
            entity->setLinetype (entityTemplate->linetype());
            entity->setLinetypeScale (entityTemplate->linetypeScale());
            entity->setLayer (entityTemplate->layer());
            entity->setVisibility (entityTemplate->visibility());
            entity->setMaterial (entityTemplate->materialId());
            entity->setTransparency (entityTemplate->transparency());
            entity->setXData (entityTemplate->xData());

            // directly save XAttributes to the entity which may be forced to save to database:
            this->UpdateExtensionDictionaryFromXAttributes (entity, (0 == i || !copyElem.IsValid()) ? elemIn : copyElem);

            if (0 == i)
                {
                if (nullptr == desiredObject)
                    {
                    // the caller does not request for a specific type, return whatever type acisIn has created:
                    newObject = entity;
                    }
                else if (desiredObject->isA() != entity->isA() || entity->objectId().isValid())
                    {
                    /*-------------------------------------------------------
                    Cases that require the caller to replace desired object with a new object:

                    1) In the event an entity created by acisIn is not the same
                    type as the one we had initially created, we must replace
                    the old object with the one created by AcDbBody::acisIn.
                    Currently due to lack of editing capability in standard
                    surface handler, we choose to drop SURFACE entities to
                    smart surfaces. As a result, we hit here for each such
                    a surface.  Some time in the future we shall add editing
                    capability in standard surface handler.

                    2) The new entity created by acisIn is the same type as desired, but the new entity 
                    has already been checked into database when saving xattributes.  We still have to
                    abandon the desired object and replace it with the new db resident object - TFS 657830.

                    Caller to hand the desired object's id over to the new object.
                    -------------------------------------------------------*/
                    newObject = entity;
                    status = ReplacedObjectType;
                    }
                else
                    {
                    // new object matches what we anticipated, just copy data over:
                    desiredObject->copyFrom (entity);
                    ACHAR*  materialName = entity->material();
                    if (NULL != materialName)
                        {
                        AcDbEntityP acEntity = AcDbEntity::cast (desiredObject);
                        if (NULL != acEntity)
                            acEntity->setMaterial (materialName);
                        acutDelString (materialName);
                        }
                    }
                }
            else if (entity->objectId().isValid())
                {
                // we were added to the database when saving xattributes as xrecords.
                entity->close ();
                }
            else
                {
                AcDbObjectId    newId = this->AddEntityToCurrentBlock (entity, 0);
                if (!newId.isNull())
                    entity->close ();
                }
            }

        if (nullptr != entityTemplate)
            {
            if (entityTemplate->objectId().isValid())
                entityTemplate->erase ();
            else
                delete entityTemplate;
            }
        }

    return  status;
    }

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct BRepCollector : IElementGraphicsProcessor
{
protected:

DgnModelP               m_model;
bvector<PK_ENTITY_t>    m_xmtEntities;
bvector<Transform>      m_entityTransforms;
UnitDefinition          m_targetUnits;
bool                    m_sizeWarning;
Transform               m_localTransform;

public:

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BrienBastings   12/10
+---------------+---------------+---------------+---------------+---------------+------*/
explicit BRepCollector (DgnModelP modelIn, TransformCR transform, UnitDefinitionCR toUnits)
    {
    m_model = modelIn;
    m_sizeWarning = false;
    m_localTransform = transform;
    m_targetUnits = toUnits;
    m_entityTransforms.empty ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BrienBastings   12/10
+---------------+---------------+---------------+---------------+---------------+------*/
~BRepCollector ()
    {
    for each (PK_ENTITY_t entityTag in m_xmtEntities)
        pki_delete_entity (1, &entityTag);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BrienBastings   12/10
+---------------+---------------+---------------+---------------+---------------+------*/
bvector<PK_ENTITY_t>* GetXmtEntities (bvector<Transform>& transforms)
    {
    transforms = m_entityTransforms;
    return &m_xmtEntities;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BrienBastings   12/10
+---------------+---------------+---------------+---------------+---------------+------*/
bool GetSizeBoxWarning () {return m_sizeWarning;}

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BrienBastings   12/10
+---------------+---------------+---------------+---------------+---------------+------*/
void CollectBody (ISolidKernelEntityR entity)
    {
    // NOTE: Preserving relative location of solids is problematic, can easily exceeed SWA...
    PK_ENTITY_t entityTag = PSolidUtil::GetEntityTagForModify (entity);
    Transform   entityTransform = entity.GetEntityTransform ();

    AdjustEntityTransform (entityTransform, m_model->GetModelInfo(), m_model->IsDictionaryModel(), m_targetUnits);

    // apply local transform
    entityTransform.InitProduct (m_localTransform, entityTransform);

    if (SUCCESS != PSolidUtil::CheckBody (entityTag, false, false, true))
        m_sizeWarning = true;

    m_xmtEntities.push_back (entityTag);
    m_entityTransforms.push_back (entityTransform);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
void ConvertToParaSolids (ElementHandleCR eh)
    {
    bvector<ISolidKernelEntityPtr> bodies;

    if (SUCCESS != PSolidUtil::ElementToBodies (bodies, NULL, eh))
        return;

    FOR_EACH (ISolidKernelEntityPtr entityPtr, bodies)
        CollectBody (*entityPtr);
    }

}; // BRepCollector

RealDwgStatus           ConvertFromDgnContext::ConvertSolidElementToAcis(AcDbObjectP& newObject, AcDbObjectP desiredObject, ElementHandleCR inElement)
    {
    return _ConvertSolidElementToAcis (newObject, desiredObject, inElement);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertFromDgnContext::_ConvertSolidElementToAcis (AcDbObjectP& newObject, AcDbObjectP desiredObject, ElementHandleCR inElement)
    {
    // don't go any further if InterOp is not available
    if (!this->CanConvertACIS())
        return  CantCreateAcisElement;

    // this method can replace input object type with a different type
    PSolidKernelManager::StartSession ();

    BRepCollector   solidConverter(m_model, m_localTransform, m_targetUnit);
    solidConverter.ConvertToParaSolids (inElement);

    bvector<Transform>      transformList;
    bvector<PK_ENTITY_t>*   psolidList = solidConverter.GetXmtEntities (transformList);
    if (NULL == psolidList || 0 == psolidList->size())
        return  CantCreateAcisElement;

    WCharCP         satFileName = this->GetAcisOutputFilePath ();
    if (NULL == satFileName)
        return  CantCreateAcisElement;

    WCharCP         logFileName = NULL;
    WString         logFile;
    if (this->GetSettings().AllowPsolidAcisInteropLogging())
        logFileName = GetLogFileName (logFile, satFileName);

    // since we have applied the target scale to meters in the matrix in AdjustEntityTransform, use meters for the output SAT:
    StandardUnit    units = StandardUnit::MetricMeters;

    // convert PSOLID's to an SAT file
    StatusInt       status = PSolidAcisInterop::XMTEntitiesToSATFile (satFileName, &psolidList->front(), &transformList.front(), psolidList->size(), units, SatVersionForDwg(m_targetVersion), logFileName);

    RealDwgStatus   rdStatus = RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    // restore an ACIS entity from the SAT file
    if (BSISUCCESS == status)
        rdStatus = this->SetAcDbEntityFromAcisIn (newObject, desiredObject, satFileName, inElement);

    return  rdStatus;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Peter.Yu                        08/2021
+---------------+---------------+---------------+---------------+---------------+------*/
static PK_ERROR_code_t GetPsdFileName (WCharCP psdFileName, bvector<PK_ENTITY_t>* psolidList)
    {
    PK_PART_transmit_o_t    options;
    PK_PART_transmit_o_m(options);
    options.transmit_format = PK_transmit_format_binary_c;
    options.transmit_version = 0;

    PK_ERROR_code_t pkError = PK_PART_transmit_u ((int)psolidList->size(), &psolidList->front(), (PK_UCHAR_t const*)psdFileName, &options);

    return pkError;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Peter.Yu                        08/2021
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertFromDgnMultiProcessingContext::_ConvertSolidElementToAcis (AcDbObjectP& newObject, AcDbObjectP desiredObject, ElementHandleCR inElement)
    {
    // don't go any further if InterOp is not available
    if (!this->CanConvertACIS())
        return  CantCreateAcisElement;

    // this method can replace input object type with a different type
    PSolidKernelManager::StartSession ();
    ISolidKernelEntity::SolidKernelType kernel;

    BentleyStatus   brepStatus = BrepCellHeaderHandler::GetBRepDataKernel (kernel, inElement);

    if (brepStatus == SUCCESS && ISolidKernelEntity::SolidKernel_ACIS == kernel)
        {
        void*       pData = nullptr;
        UInt32      dataSize;

        if (SUCCESS == BrepCellHeaderHandler::ExtractDgnStoreBRepData (&pData, &dataSize, inElement))
            {
            Transform   bodyTransform = BrepCellHeaderHandler::TransformOfBRepDataEntity (inElement);
            WString     satFileName (this->GetAcisOutputFilePath ());
            // since we have applied the target scale to meters in the matrix in AdjustEntityTransform, use meters for the output SAT:
            StandardUnit    units = StandardUnit::MetricMeters;

            AdjustEntityTransform (bodyTransform, m_model->GetModelInfo(), m_model->IsDictionaryModel(), m_targetUnit);

            if (!satFileName.empty () && SUCCESS == PSolidAcisInterop::SATEntityToSATFile (satFileName, pData, dataSize, bodyTransform, SatVersionForDwg(m_targetVersion)))
                {
                RealDwgStatus  rdStatus = this->SetAcDbEntityFromAcisIn (newObject, desiredObject, satFileName.c_str (), inElement);

                return  rdStatus;
                }
            }
        }

    BRepCollector   solidConverter(m_model, m_localTransform, m_targetUnit);
    solidConverter.ConvertToParaSolids (inElement);

    bvector<Transform>      transformList;
    bvector<PK_ENTITY_t>*   psolidList = solidConverter.GetXmtEntities (transformList);
    if (NULL == psolidList || 0 == psolidList->size())
        return  CantCreateAcisElement;

    // since we have applied the target scale to meters in the matrix in AdjustEntityTransform, use meters for the output SAT:
    StandardUnit    units = StandardUnit::MetricMeters;
    // convert PSOLID's to an SAT file
    if (PsdFileProcessingMode::WriteToFile == m_psdFileProcessingMode)
        {
        //Create PSD file
        BeFileName psdFileName = CurrentPsdToAcis ().NewPsdFileName ();

        PSolidUtil::WriteEntities (psdFileName.c_str (), *psolidList, transformList, SatVersionForDwg(m_targetVersion));

        BeFileName fullFileName = CurrentPsdToAcis ().OutputToPsdAsynch (psdFileName);
        //We failed to convert the current Acis entity to PS.
        //We convert it to a SAT file, and we map the filename of the sat file to the entity id.
        //Thereafter, multiple processes will convert this SAT file to PS asynchronously.
        BeFileName psdBaseName = BeFileName (BeFileName::GetFileNameWithoutExtension (fullFileName.c_str ()).c_str ());
        m_psdEntityMapping[psdBaseName] = ElementHandle (inElement.GetElementRef (), inElement.GetModelRef ());
        m_curAcisFileName = fullFileName;

        CurrentPsdToAcis ().SpawnPsdToSatChildrenProcesses ();
        }

    if (PsdFileProcessingMode::WriteToFile == m_psdFileProcessingMode)
        return ConversionInChildrenProc;

    BeFileName acisFileName = m_curAcisFileName;
    if (!BeFileName::DoesPathExist (acisFileName.c_str ()))
        return CantCreatePsdElement;

    // restore an ACIS entity from the SAT file
    RealDwgStatus rdStatus = this->SetAcDbEntityFromAcisIn (newObject, desiredObject, acisFileName, inElement);

    return  rdStatus;
    }

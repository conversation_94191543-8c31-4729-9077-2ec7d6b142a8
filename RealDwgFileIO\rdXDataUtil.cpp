/*----------------------------------------------------------------------+
|
|   $Source: mstn/mdlapps/RealDwgFileIO/rdXDataUtil.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+----------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

#define ELEMENTHANDLERID_PCM                    22756
#define ELEMENTHANDLERID_SmartFeatureHandler    22849

/*----------------------------------------------------------------------+
|                                                                       |
|   Local Static Data                                                   |
|                                                                       |
+----------------------------------------------------------------------*/
static  UInt32       s_excludedXAttributeIds[] = {
                                               XATTRIBUTEID_CellIndexTool,
                                               XATTRIBUTEID_TabBasedViewGroup,
                                               XATTRIBUTEID_FilletChamferTool,
                                               XATTRIBUTEID_CameraNavigationSpeed,
                                               XATTRIBUTEID_ObjectStateID,
                                               //XATTRIBUTEID_String,                   // ECX schema uses this one
                                               //XATTRIBUTEID_XML,                      // ABD Groupdata info, albeit currently only in linkages
                                               XATTRIBUTEID_StdsCheckIgnoredError,
                                               XATTRIBUTEID_Relationship,
                                               XATTRIBUTEID_DesignLinks,
                                               XATTRIBUTEID_Generic,
                                               XATTRIBUTEID_ColorBook,
                                               XATTRIBUTEID_ECField,
                                               XATTRIBUTEID_IcoData,
                                               XATTRIBUTEID_MstnSettings,
                                               XATTRIBUTEID_MaterialProperties,
                                               XATTRIBUTEID_ExtendedColorTable,
                                               XATTRIBUTEID_MaterialTable,
                                               XATTRIBUTEID_RasterFrame,
                                               XATTRIBUTEID_ConflictRevisions,
                                               XATTRIBUTEID_BSurfTrimCurve,
                                               XATTRIBUTEID_ViewInfo,
                                               XATTRIBUTEID_XGraphics,
                                               XATTRIBUTEID_REFERENCE_PROVIDERID,
                                               XATTRIBUTEID_DynamicViewSettings,
                                               XATTRIBUTEID_SectionGeometryGenerator,       // type 106 not supported
                                               XATTRIBUTEID_ViewDisplayOverrides,
                                               XATTRIBUTEID_DisplayStyle,
                                               XATTRIBUTEID_StandardSurface,
                                               XATTRIBUTEID_SectionGeometryCached,          // type 106 not supported
                                               XATTRIBUTEID_DisplayStyleMap,
                                               XATTRIBUTEID_DisplayStyleIndex,
                                               XATTRIBUTEID_AnnotationScale,
                                               ELEMENTHANDLERID_PCM,                        // Parametric cell
                                               ELEMENTHANDLERID_SmartFeatureHandler
                                               };

static  UInt32       s_fileHeaderXAttributeIds[3] = {
                                               XATTRIBUTEID_IcoData,
                                               XATTRIBUTEID_NamedExpressionData,
                                               XATTRIBUTEID_NamedExpressionKeywordData
                                               };

static  UInt32       s_maxXDataSizePerApp = 16384;   // 16KB cap



/*=================================================================================**//**
*
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class   RealDwgXDataUtil
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AddStringToBinaryData
(
RealDwgBinaryData&          binaryData,
RealDwgResBuf*              pResBuf,
LangCodePage                codePage,
int                         resType
)
    {
    ACHAR const*    resBufString    = pResBuf->GetString();
    int             stringLen       = (int) wcslen (resBufString);
    int             bytesToCopy     = stringLen * sizeof(WChar);
    AString         mbString        = AString (bytesToCopy + 1, 0x0);

    // Note: This is a problem, but it's what's in older files.
    if (SUCCESS == BeStringUtilities::WCharToLocaleChar(mbString, (UInt32)codePage, resBufString, stringLen))
        binaryData.AddGroupCode (resType, mbString.c_str(), bytesToCopy);
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 BinaryDataFromXData
(
RealDwgBinaryData&          binaryData,
RealDwgResBuf*              pResBuf,
AcDbDatabase*               pDatabase
)
    {
    /*-----------------------------------------------------------------------------------
    DD 2.0.3 gives us unicode strings in xdata.  Ideally we should store them as unicode
    but we face backward compatibility issue (how xdata is handled in V8.5 for instance).
    Text element has the same issue.  For now we have to convert them to multibyte strings
    and remove the conversion when file format change is allowed.
    -----------------------------------------------------------------------------------*/
    LangCodePage            codePage = LangCodePage::LatinI;
    // default to system codepage
    BeStringUtilities::GetCurrentCodePage (codePage);
    GetAnsiCodePageFromXData (&codePage, pResBuf);

    binaryData.clear ();  //  Keep the buffer to avoid all of the realloc overhead

    for (; NULL != pResBuf; pResBuf = pResBuf->GetNext())
        {
        int     resType = pResBuf->GetResType();

        if (AcDb::kDxfRegAppName == resType)
            {
            AcDbObjectId    regAppId;
            if (Acad::eOk != AcDbSymUtil::getRegAppId (regAppId, pResBuf->GetString(), pDatabase))
                {
                // should not happen.
                BeAssert (false && L"XDATA util - found no regapp from name!");
                continue;
                }
            binaryData.AddHandleGroupCode (resType, regAppId.handle());
            }
        else
            {
            AcDb::DwgDataType dataType = pResBuf->GetDwgDataType ();
            switch (dataType)
                {
                case AcDb::kDwgText:
                    {
                    // moved to subroutine to avoid alloca in loop.
                    AddStringToBinaryData (binaryData, pResBuf, codePage, resType);
                    break;
                    }

                case AcDb::kDwgInt8:
                    {
                    byte            value = pResBuf->GetInt8();
                    binaryData.AddGroupCode (resType, &value, sizeof(value));
                    break;
                    }

                case AcDb::kDwgInt16:
                    {
                    Int16           value = pResBuf->GetInt16();
                    binaryData.AddGroupCode (resType, &value, sizeof(value));
                    break;
                    }

                case AcDb::kDwgInt32:
                    {
                    Int32           value = pResBuf->GetInt32();
                    binaryData.AddGroupCode (resType, &value, sizeof(value));
                    break;
                    }

                case AcDb::kDwgReal:
                    {
                    double          value = pResBuf->GetDouble();
                    binaryData.AddGroupCode (resType, &value, sizeof(value));
                    break;
                    }

                case AcDb::kDwg3Real:
                    {
                    AcGePoint3d     point = pResBuf->GetPoint3d();
                    binaryData.AddGroupCode (resType, &point, sizeof(point));
                    break;
                    }

                case AcDb::kDwgBChunk:
                    {
                    RealDwgBinaryData    chunk;
                    pResBuf->GetBinaryChunk (chunk, false);
                    binaryData.AddGroupCode (resType, &chunk.front(), (int) chunk.size());
                    break;
                    }

                case AcDb::kDwgHandle:
                case AcDb::kDwgSoftPointerId:
                case AcDb::kDwgHardPointerId:
                case AcDb::kDwgSoftOwnershipId:
                case AcDb::kDwgHardOwnershipId:
                    binaryData.AddHandleGroupCode (resType, pResBuf->GetHandle());
                    break;

                case AcDb::kDwgNull:
                    break;

                default:
                    BeAssert (false && L"XDATA util - Bad Data Type!");
                    break;
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   05/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       GetNextRegApp (RealDwgResBuf* pStart, RealDwgResBuf*& pLastInCurrAppRB)
    {
    pLastInCurrAppRB = pStart;

    RealDwgResBuf*      pNextAppRB;
    for (pNextAppRB = pStart->GetNext(); (NULL != pNextAppRB) && (AcDb::kDxfRegAppName != pNextAppRB->GetResType()); pNextAppRB = pNextAppRB->GetNext())
        pLastInCurrAppRB = pNextAppRB;

    return pNextAppRB;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       SeparateXData
(
RealDwgResBuf*&             pNonMatchingXData,      // <=
RealDwgResBuf*              pCombinedXData,         // =>
ACHAR const*                appName
)
    {
    pNonMatchingXData = pCombinedXData;

    // this method takes the incoming RealDwgResBuf, removes the ResBuf sequence associated with appName, and returns that separated xData sequence
    //  as the return value. pNonMatchingXData is set to the remaning part of the original sequence without the appName sequence, or NULL if the only thing in the original resubuf
    //  was the appName sequence. The caller of this routine MUST call RealDwgResBuf::Free on pNonMatchingXData and on the return value after this routine is called, and must NOT
    //  call RealDwgResBuf::Free on pCombinedXData.

    // If pCombinedXData start with other than a kDxfRegAppName, something is wrong with it. We return NULL and pNonMatchingXData points to pCombinedXData.
    if ( (NULL == pCombinedXData) || (AcDb::kDxfRegAppName != pCombinedXData->GetResType()) )
        return NULL;

    // we know the first thing in the string is a kDxfRefAppName, see if it's the one we want.
    if (0 == wcscmp (appName, pCombinedXData->GetString()))
        {
        // We want the first one. Find the next kDxfRegAppName
        RealDwgResBuf*      pNextAppRB;
        RealDwgResBuf*      pLastInCurrAppRB;
        pNextAppRB = GetNextRegApp (pCombinedXData, pLastInCurrAppRB);

        pLastInCurrAppRB->SetNext (NULL);

        pNonMatchingXData = pNextAppRB;
        return pCombinedXData;
        }

    RealDwgResBuf*      pLastInPrevAppRB = NULL;
    for (RealDwgResBuf* pCurrAppRB = pCombinedXData; (NULL != pCurrAppRB); )
        {
        RealDwgResBuf*      pNextAppRB;
        RealDwgResBuf*      pLastInCurrAppRB;

        // look ahead for the next App ResBuf, finding the last one in this chain while we're at it. After this loop pNextAppRB points to the next one, or is NULL.
        pNextAppRB = GetNextRegApp (pCurrAppRB, pLastInCurrAppRB);

        // see if pCurrAppRB is the part of the chain is what we want.
        if (0 == wcscmp (appName, pCurrAppRB->GetString()))
            {
            // this is the one we want. Isolate it from the rest of the chain.
            pLastInCurrAppRB->SetNext (NULL);

            // skip over pCurrAppAB by adjusting its Next. If there is no previous, we're at the beginning, set output.
            if (NULL != pLastInPrevAppRB)
                pLastInPrevAppRB->SetNext (pNextAppRB);
            else
                pNonMatchingXData = pNextAppRB;

            return pCurrAppRB;
            }

        pCurrAppRB       = pNextAppRB;
        pLastInPrevAppRB = pLastInCurrAppRB;
        }

    // if we got all the way through, didn't find the desired AppName resbuf, and we didn't change the input resbuf.
    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   05/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       ConsolidateXData (RealDwgResBuf* pFirstXData, RealDwgResBuf* pSecondXData)
    {
    // Check for empty xdata groups:
    if (NULL == pFirstXData || NULL == pFirstXData->GetNext())
        return pSecondXData;
    if (NULL == pSecondXData || NULL == pSecondXData->GetNext())
        return pFirstXData;

    BeAssert ((AcDb::kDxfRegAppName == pFirstXData->GetResType()) && (AcDb::kDxfRegAppName == pSecondXData->GetResType()) );
    RealDwgResBuf* pEndofFirst = pFirstXData->GetTail();
    pEndofFirst->SetNext (pSecondXData);

    return  pFirstXData;
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 RemoveXDataByAppName
(
AcDbObject*                 pObject,
const ACHAR*                appName
)

    {
    // you delete it by putting on a blank one.
    RealDwgResBuf *pXData = RealDwgResBuf::CreateRegAppXData (appName);
    pObject->setXData (pXData);
    RealDwgResBuf::Free (pXData);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 RemoveXDataByAppName
(
RealDwgResBuf*&             pXData,
const ACHAR*                appName
)
    {
    if (NULL == pXData)
        return  false;

    RealDwgResBuf*  pBeforeDelete = pXData;
    RealDwgResBuf*  pDeleteApp    = pXData;
    for (; NULL != pDeleteApp; pBeforeDelete = pDeleteApp, pDeleteApp = pDeleteApp->GetNext())
        {
        if (AcDb::kDxfRegAppName == pDeleteApp->GetResType() && 0 == wcscmp(appName, pDeleteApp->GetString()))
            {
            RealDwgResBuf*  pBeforeNext = pDeleteApp;
            RealDwgResBuf*  pNextApp    = pDeleteApp->GetNext ();
            for (; NULL != pNextApp; pBeforeNext = pNextApp, pNextApp = pNextApp->GetNext())
                {
                if (AcDb::kDxfRegAppName == pNextApp->GetResType())
                    {
                    pBeforeNext->SetNext (NULL);
                    break;
                    }
                }
            RealDwgResBuf::Free (pDeleteApp);
            if (pBeforeDelete == pXData)
                pXData = pNextApp;
            else
                pBeforeDelete->SetNext (pNextApp);
            return  true;
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/05
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            CreateHyperlinkXData
(
RealDwgResBuf**             ppHyperlinkXData,
AcString const&             pTitle1,   // title
AcString const&             pTitle2,   // model name
AcString const&             pUrl
)
    {
    if (pTitle2.isEmpty() && pUrl.isEmpty())
        return  BSIERROR;

    // create a new PE_URL xdata:
    *ppHyperlinkXData = RealDwgResBuf::CreateRegAppXData (StringConstants::RegAppName_PeUrl);

    RealDwgResBuf* pLast = *ppHyperlinkXData;

    // add hyperlink group codes
    pLast = pLast->Append (RealDwgResBuf::CreateString (pUrl));
    pLast = pLast->Append (RealDwgResBuf::CreateBeginGroup ());
    pLast = pLast->Append (RealDwgResBuf::CreateString (pTitle1));
    if (!pTitle2.isEmpty())
        pLast = pLast->Append (RealDwgResBuf::CreateString (pTitle2));

    // set "convert to DWF" flag to on
    pLast = pLast->Append (RealDwgResBuf::CreateBeginGroup ());
    pLast = pLast->Append (RealDwgResBuf::CreateInt32 (AcDb::kDxfXdInteger32, 1));
    pLast = pLast->Append (RealDwgResBuf::CreateEndGroup ());
    pLast = pLast->Append (RealDwgResBuf::CreateEndGroup ());

    return  SUCCESS;
    }


// this is public for performance reasons. John Gooding discovered in the OpenDWG source that continually
//   reallocating this was the cause for big performance penalties. As a global it grows to the biggest it
//   needs to be and stays that way.
static RealDwgBinaryData    XferBinaryData;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*           CreateStringResBuf
(
RealDwgBinaryData::XDataHeader& header,
const byte*                     pCurr
)
    {
    char        *pStringChars = (char *) alloca (header.m_bytesToFollow + 1);

    // In the ODT version of the code, the data was converted to multibyte to store in the binarydata,
    //  and then converted back. That is actually a bad idea, and we should change it to store/retrieve
    //  unicode, but there may be save-as'ed DGNs out there with this format, so we have to support it
    //  until we have a newformat/oldformat way of doing it.
    memcpy (pStringChars, pCurr, header.m_bytesToFollow);
    pStringChars[header.m_bytesToFollow] = '\0';

    return RealDwgResBuf::CreateString (header.m_groupCode, pStringChars);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsEmptyRegappXData
(
UInt32&                     skipSize,
const byte*                 pThisXData,
const byte*                 pEnd
)
    {
    const byte              *pCurr = pThisXData;

    /*-----------------------------------------------------------------------------------
    ACAD/RealDWG does not like empty xdata for the entire regapp, in which case the host
    entity gets deleted.  We have to make sure we do not create such an empty xdata.

    Walk through all items in this app data to find the sum of its size.  For most cases,
    the very first step in this loop should return false, so there is little performance
    to sacrifice for a good element.
    -----------------------------------------------------------------------------------*/
    while (pCurr < pEnd)
        {
        RealDwgBinaryData::XDataHeader  header (pCurr);

        if (AcDb::kDxfRegAppName == header.m_groupCode)
            break;
        else if (header.m_bytesToFollow > 0)
            return  false;

        pCurr += sizeof header;
        }

    skipSize = (UInt32) (pCurr - pThisXData);

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 XDataFromBinaryData
(
RealDwgResBuf*&             pXData,                     // <=>
const byte*                 pBinaryXData,
int                         dataSize,                   // <= true if changed.
AcDbDatabase*               pDatabase,
ConvertContext&             context
)
    {
    if (0 == dataSize)
        {
        if (NULL == pXData)
            return false;
        else
            {
            pXData = RealDwgResBuf::Create(0);
            return true;
            }
        }
    BinaryDataFromXData (XferBinaryData, pXData, pDatabase);
    if ( (XferBinaryData.size() == (UInt32) dataSize) && (0 == memcmp (&XferBinaryData[0], pBinaryXData, dataSize)) )
        return false;

    RealDwgResBuf* pLast = NULL;
    for (const byte *pCurr = pBinaryXData, *pEnd = pCurr + dataSize; pCurr < pEnd; )
        {
        RealDwgResBuf*                  pThis = NULL;
        RealDwgBinaryData::XDataHeader  header (pCurr);

        pCurr += sizeof(header);

        if (AcDb::kDxfRegAppName == header.m_groupCode)
            {
            ElementId           handle;
            byte*               pOut        = ((byte *) &handle) + 7;

            for (const byte *pIn = pCurr, *pHandleEnd = pIn + 8; pIn < pHandleEnd;)
                *pOut-- = *pIn++;

            AcDbObjectId        objectId;
            pDatabase->getAcDbObjectId (objectId, false, context.DBHandleFromElementId (handle), 0);
            AcDbRegAppTableRecordPointer    pRecord (objectId, AcDb::kForRead);
            if (Acad::eOk == pRecord.openStatus())
                {
                const ACHAR*    name;
                pRecord->getName (name);

                UInt32  skipSize = 0;
                if (IsEmptyRegappXData(skipSize, pCurr+header.m_bytesToFollow, pEnd))
                    {
                    DIAGNOSTIC_PRINTF ("Skipped empty binary xdata for regapp %ls\n", name);
                    pCurr += skipSize + header.m_bytesToFollow;
                    continue;
                    }

                pThis = RealDwgResBuf::CreateString (header.m_groupCode, name);
                }
            else
                {
                DIAGNOSTIC_PRINTF ("Ignoring XDataLinkage - unable to find RegApp\n");
                return true;
                }
            }
        else
            {
            // don't add null data potentially created by a MicroStation app, as a case in TFS332819.
            if (0 == header.m_bytesToFollow)
                continue;
             
            switch (RealDwgResBuf::DataTypeFromInputCode (header.m_groupCode))
                {
                case AcDb::kDwgText:
                    {
                    // moved to another method to avoid alloca in loop.
                    pThis = CreateStringResBuf (header, pCurr);
                    break;
                    }

                case AcDb::kDwgInt8:
                    {
                    Adesk::Int8     value;
                    memcpy (&value, pCurr, sizeof(value));
                    pThis = RealDwgResBuf::CreateInt8 (header.m_groupCode, value);
                    break;
                    }

                case AcDb::kDwgInt16:
                    {
                    Adesk::Int16    value;

                    memcpy (&value, pCurr, sizeof(value));
                    pThis = RealDwgResBuf::CreateInt16 (header.m_groupCode, value);
                    break;
                    }

                case AcDb::kDwgInt32:
                    {
                    Adesk::Int32    value;

                    memcpy (&value, pCurr, sizeof(value));
                    pThis = RealDwgResBuf::CreateInt32 (header.m_groupCode, value);
                    break;
                    }

                case AcDb::kDwgReal:
                    {
                    double          value;
                    memcpy (&value, pCurr, sizeof(value));
                    pThis = RealDwgResBuf::CreateDouble (header.m_groupCode, value);
                    break;
                    }

                case AcDb::kDwg3Real:
                    {
                    AcGePoint3d     point;
                    memcpy (&point, pCurr, sizeof(point));
                    pThis = RealDwgResBuf::CreatePoint (header.m_groupCode, point);
                    break;
                    }

                case AcDb::kDwgBChunk:
                    {
                    pThis = RealDwgResBuf::CreateBinaryChain (header.m_groupCode, pCurr, header.m_bytesToFollow);
                    break;
                    }
                default:
                    {
                    BeAssert (false && L"XDATA util - unknown refbuf data type!");
                    }
                }
            }

        if (NULL != pThis)
            {
            if (NULL == pLast)
                pXData = pLast = pThis;
            else
                pLast = pLast->Append (pThis);

            // in case a binary chain is created.
            pLast = pLast->GetTail();
            }
        pCurr += header.m_bytesToFollow;
        }
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsSupportedAppDependency
(
UInt32                      appId
)
    {
    // Dependency application that is supported when saved to DWG.
    // For now, only non-MicroStation apps and named groups are saved.
    return  (appId >= DEPENDENCYAPPID_First3rdParty___ || DEPENDENCYAPPID_ALNA == appId || DEPENDENCYAPPID_NamedGroup == appId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 SetAppDependencies
(
RealDwgResBuf**             ppMicroStationXData,                // <=> XData
DependencyLinkage**         ppDependencies,
int                         nDependencies,
AcDbDatabase*               pDatabase,
ConvertFromDgnContextR      context
)                                                               // <= true if linkage changed.
    {
    int             nApplicationDependencies = 0;

    // Count dependencies to be saved to xdata
    for (int iDependency = 0; iDependency < nDependencies; iDependency++)
        if (IsSupportedAppDependency (ppDependencies[iDependency]->appID))
            nApplicationDependencies++;

    if (NULL == *ppMicroStationXData)
        {
        if (0 == nApplicationDependencies)
            {
            return false;
            }
        else
            {
            AddRegApp (StringConstants::RegAppName_MicroStation, pDatabase);
            *ppMicroStationXData = RealDwgResBuf::CreateMicroStationRegAppXData ();
            }
        }

    if (0 == nApplicationDependencies)
        return RemoveXDataByKey (ppMicroStationXData, StringConstants::XDataKey_ApplicationDependency);

    // find the current ApplicationDependency subgroup. Looks Like this:
    //   pKeyResBuf => kDxfXdAsciiString XDataKey_ApplicationDependency
    //                    kDxfXdControlString "{"
    //   pFirstAppDep       kDxfXdInteger16First Actual Dependency data
    //                         ....
    //   pLastApp Dep       Right Before pEndGroup
    //   pEndGroup       kDxfXdControlString "}"
    RealDwgResBuf*      pAppKey;
    RealDwgResBuf*      pFirstAppDep = FindXDataByKey (*ppMicroStationXData, AcDb::kDxfXdInteger16, StringConstants::XDataKey_ApplicationDependency, true, &pAppKey);

    BeAssert ( (NULL != pFirstAppDep) && (NULL != pAppKey) && (AcDb::kDxfXdInteger16 == pFirstAppDep->GetResType()) && (AcDb::kDxfXdAsciiString == pAppKey->GetResType()) );
    RealDwgResBuf*      pBeginGroup  = pAppKey->GetNext();

    BeAssert ( (NULL != pBeginGroup) && (AcDb::kDxfXdControlString == pBeginGroup->GetResType()) && (pBeginGroup->GetNext() == pFirstAppDep) );

    // AppDepData points to the beginning kDxfXdInteger16 appData within the XDataGroup. Eliminate all of the dependency XDatas within the group.
    RealDwgResBuf*      pLastAppDep  = pFirstAppDep;
    RealDwgResBuf*      pEndGroup;
    for (pEndGroup = pFirstAppDep->GetNext(); (NULL != pEndGroup) && (AcDb::kDxfXdControlString != pEndGroup->GetResType()); pEndGroup = pEndGroup->GetNext())
        pLastAppDep = pEndGroup;

    // pEndGroup now points to the end. There should always be one an EndGroup.
    BeAssert ( (NULL != pEndGroup) && (AcDb::kDxfXdControlString == pEndGroup->GetResType()) && (NULL != pLastAppDep) );

    if ( (NULL != pEndGroup) && (NULL != pFirstAppDep) )
        {
        // make the beginGroup point to the EndGroup (skip all the app datas).
        pBeginGroup->SetNext (pEndGroup);

        // if we had any AppDependencies (should be at least one), clip them out of the chain and free them.
        pLastAppDep->SetNext (NULL);
        RealDwgResBuf::Free (pFirstAppDep);
        }

    int iDependency = nDependencies - 1;
    for (DependencyLinkage* pLinkage = *(ppDependencies + iDependency); iDependency >= 0; iDependency--, pLinkage = *(ppDependencies + iDependency))
        {
        RealDwgResBuf*  pDependencyXData;

        if (NULL != (pDependencyXData = XDataFromDependencyLinkage (pLinkage, context)))
            pBeginGroup->InsertAfter (pDependencyXData);
        }

    return true;
    }



typedef bool TraverseFunc (RealDwgResBuf* pResBuf, void* pTraverseArg);

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 TraverseXData       // returns true if TraverseFunc stops it, false if continued to end.
(
RealDwgResBuf*      pXData,
TraverseFunc        pTraverseFunc,
void*               pTraverseArg
)
    {
    for (RealDwgResBuf* pCurr = pXData; NULL != pCurr; pCurr = pCurr->GetNext())
        {
        if (pTraverseFunc (pCurr, pTraverseArg))
            return true;
        }
    return false;
    }


/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
struct              FindMSDataTraverseArg
{
int                     m_resType;
const ACHAR*            m_xDataKey;
RealDwgResBuf*          m_keyResBuf;
RealDwgResBuf*          m_resBufWhereFound;
RealDwgResBuf*          m_lastResBuf;
RealDwgResBuf*          m_resBufPrecedingData;
bool                    m_foundKey;
bool                    m_foundBeginGroup;

FindMSDataTraverseArg (int resType, const ACHAR* xDataKey, RealDwgResBuf* pResBuf)
    {
    m_resType               = resType;
    m_xDataKey              = xDataKey;
    m_lastResBuf            = pResBuf;
    m_keyResBuf             = NULL;
    m_resBufWhereFound      = NULL;
    m_resBufPrecedingData   = NULL;
    m_foundKey              = false;
    m_foundBeginGroup       = false;
    }
};


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 FindMSDataTraverseFunc
(
RealDwgResBuf*      pCurr,
void*               pTraverseArg
)
    {
    FindMSDataTraverseArg*  findArg = reinterpret_cast <FindMSDataTraverseArg*> (pTraverseArg);

    // the last one we've seen.
    findArg->m_lastResBuf = pCurr;

    if (!findArg->m_foundKey)
        {
        if ( (pCurr->restype == AcDb::kDxfXdAsciiString) && (0 == wcscmp (pCurr->resval.rstring, findArg->m_xDataKey)) )
            {
            findArg->m_foundKey = true;
            findArg->m_keyResBuf = pCurr;
            }
        }
    else if (!findArg->m_foundBeginGroup)
        {
        if ( (pCurr->restype == AcDb::kDxfXdControlString) && (0 == wcscmp (pCurr->resval.rstring, StringConstants::XData_BeginGroup)))
            {
            findArg->m_foundBeginGroup      = true;
            findArg->m_resBufPrecedingData  = pCurr;
            }
        else
            findArg->m_foundKey = false;
        }
    else
        {
        // the very next key should match the type we're looking for.
        if (pCurr->restype == findArg->m_resType)
            {
            findArg->m_resBufWhereFound = pCurr;
            return true;
            }
        findArg->m_foundKey = false;
        findArg->m_foundBeginGroup = false;
        }

    return false;
    }


public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/08
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 ExtractMicroStationXDataFromObject
(
RealDwgResBuf&      pDataInOut,  // <=> need restype from data in and return resbuf to data out
const ACHAR*        xDataKey,
AcDbObject*         pObject
)
    {
    RealDwgResBuf*  pXData;
    if (NULL == (pXData = static_cast <RealDwgResBuf*> (pObject->xData (StringConstants::RegAppName_MicroStation))))
        return  false;

    FindMSDataTraverseArg   traverseArg (pDataInOut.GetResType(), xDataKey, pXData);

    bool    returnVal = false;
    TraverseXData (pXData, FindMSDataTraverseFunc, &traverseArg);

    if (NULL != traverseArg.m_resBufWhereFound)
        returnVal = pDataInOut.CopyFrom (*traverseArg.m_resBufWhereFound);

    RealDwgResBuf::Free (pXData);
    return  returnVal;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AddRegApp (const ACHAR* appName, AcDbDatabase* pDatabase)
    {
    AcDbRegAppTable*    pTable;
    Acad::ErrorStatus status = pDatabase->getRegAppTable (pTable, AcDb::kForRead);
    if (Acad::eOk != status)
        {
        DIAGNOSTIC_PRINTF ("Failed opening regApp table for %ls! [%ls]\n", appName, acadErrorStatusText(status));
        return;
        }

    AcDbObjectId appObjectId;
    if (Acad::eOk == pTable->getAt (appName, appObjectId, false))
        {
        pTable->close();
        return;
        }

    status = pTable->upgradeOpen ();
    if (Acad::eOk != status)
        {
        DIAGNOSTIC_PRINTF ("Failed upgrading regApp table to add a new entry! [%ls]\n", acadErrorStatusText(status));
        return;
        }

    AcDbRegAppTableRecord*  pTableRecord = new AcDbRegAppTableRecord();
    pTableRecord->setName (appName);
    if (Acad::eOk == pTable->add (pTableRecord))
        pTableRecord->close ();
    else
        delete pTableRecord;

    pTable->close();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AddMicroStationRegApp (AcDbDatabase* pDatabase)
    {
    AddRegApp (StringConstants::RegAppName_MicroStation, pDatabase);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateMicroStationXData
(
RealDwgResBuf*              pNewXData,      // => the data to save. This buffer is incorporated into the ResBuf chain. Caller does not free it.
const AcString&             xDataKey,       // => the string key for the data.
AcDbDatabase*               pDatabase       // <=> database where MStation regapp table need to be added
)
    {
    // ensure MSation regapp table exists:
    AddMicroStationRegApp (pDatabase);

    // return value is the header of the newly created XData group.
    RealDwgResBuf*  pHeader = RealDwgResBuf::CreateMicroStationRegAppXData ();
    pHeader->Append (CreateXDataGroup (pNewXData, xDataKey));
    return pHeader;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       CreateXDataGroup
(
RealDwgResBuf*              pNewXData,      // => the data to save. This buffer is incorporated into the ResBuf chain. Caller does not free it.
const AcString&             xDataKey        // => the string key for the data.
)
    {
    // return value is the header of the newly created XData group.
    RealDwgResBuf*  pHeader = RealDwgResBuf::CreateString (xDataKey.kwszPtr());
    RealDwgResBuf*  pBegin  = pHeader->Append (RealDwgResBuf::CreateBeginGroup ());
    RealDwgResBuf*  pData   = pBegin->Append (pNewXData);
    pData->GetTail()->InsertAfter (RealDwgResBuf::CreateEndGroup ());

    return pHeader;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/08
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 AddMicroStationXDataToObject
(
AcDbObject*                 pObject,        // The ACDbObject to put the XData on.
RealDwgResBuf*              pNewXData,      // This resBuf is freed by this method. Caller does not free it.
const AcString&             xDataKey,       // The XData key string.
AcDbDatabase*               pDatabase       // Containint database.
)
    {
    // this assumes that pNewXData represents a simple ResBuf, not a chain.
    BeAssert (nullptr == pNewXData->GetNext() && L"More resbuf in xdata than expected!");

    RealDwgResBuf*  pMicroStationXData;
    // try to find existing MicroStation XData
    if (NULL == (pMicroStationXData = static_cast <RealDwgResBuf*> (pObject->xData (StringConstants::RegAppName_MicroStation))))
        {
        // Create a new MicroStation RegAppId, and add the specified xDataKey/XData to it. pMicroStationXdata points to the group header.
        pMicroStationXData = CreateMicroStationXData (pNewXData, xDataKey, pDatabase);
        }
    else
        {
        // have XData, see if we have any that matches that which we're adding.
        FindMSDataTraverseArg   traverseArg (pNewXData->GetResType(), xDataKey, pMicroStationXData);

        bool    returnVal = false;
        TraverseXData (pMicroStationXData, FindMSDataTraverseFunc, &traverseArg);

        if (NULL != traverseArg.m_resBufWhereFound)
            {
            // found matching XData. Check equality. If same, don't do anything.
            if (traverseArg.m_resBufWhereFound->Equals (*pNewXData))
                {
                RealDwgResBuf::Free (pMicroStationXData);
                RealDwgResBuf::Free (pNewXData);
                return true;
                }
            // not equal - copy the new value to the current XData.
            traverseArg.m_resBufWhereFound->CopyFrom (*pNewXData);
            RealDwgResBuf::Free (pNewXData);
            }
        else if (nullptr != traverseArg.m_lastResBuf)
            {
            // have some MicroStation XData, but didn't find the key we were looking for. The end of the chain is in findArg->m_lastResBuf;
            traverseArg.m_lastResBuf->InsertAfter (CreateXDataGroup (pNewXData, xDataKey));
            }
        else
            {
            BeAssert (L"XDATA util - unexpected empty resbuf!");
            }
        }
    // replace or add the XData to the object. This copies the data we pass in.
    pObject->setXData (pMicroStationXData);

    // free our internal buffer.
    RealDwgResBuf::Free (pMicroStationXData);
    return true;
    }


private:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/07
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsXAttributeSaved (XAttributeHandlerId handlerId, ElementRefP elementRef)
    {
    MSElementTypes      elementType = static_cast <MSElementTypes> (elementRef->GetElementType());
    UInt32              xattrId = handlerId.GetMajorId ();

    if (DGNFIL_HEADER_ELM == elementType)
        {
        // for type 9 element, only allow a few xattributes to be saved:
        for (int iId =0 ; iId < _countof (s_fileHeaderXAttributeIds); iId++)
            {
            if (xattrId == s_fileHeaderXAttributeIds[iId])
                return  true;
            }
        return  false;
        }
    else
        {
        // for other elements, allow all but a list of unwanted xattributes to be saved:
        for (int iId=0; iId < _countof (s_excludedXAttributeIds); iId++)
            {
            if (xattrId == s_excludedXAttributeIds[iId])
                return  false;
            }

        // for cells, do not allow item type instances as these will be converted as DWG block attributes:
        if ((CELL_HEADER_ELM == elementType || SHARED_CELL_ELM == elementType) && XATTRIBUTEID_ECXAttributes == xattrId)
            {
            ElementHandle::XAttributeIter   xaIter (ElementHandle(elementRef), handlerId);
            WString                         schemaName, className;
            if (xaIter.IsValid() && ECXAREADERSTATUS_Success == ECXAttributesReader::ExtractSchemaAndClassName(xaIter, schemaName, className) && ItemTypeLibrary::IsItemTypeSchema(schemaName))
                return  false;
            }

        if (EXTENDED_ELM == elementType)
            {
            // do not allow text table specific ECXData to be saved as we convert text tables:
            ElementHandle       eh (elementRef);
            TextTableHandler*   textTableHandler = dynamic_cast <TextTableHandler*> (&eh.GetHandler());

            if (nullptr != textTableHandler)
                {
                if (XATTRIBUTEID_ElementHandler == xattrId || TextTableHandler::GetElemHandlerId().GetMajorId() == xattrId)
                    return  false;

                XAttributeHandle    xh (elementRef, handlerId, XAttributeHandle::MATCH_ANY_ID);
                if (xh.IsValid() && textTableHandler->IsTablePrivateData(xh))
                    return  false;
                }
            }
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/07
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 BuildXAttributeIdsString
(
AcString&                       string,
ElementHandle::XAttributeIter   xAttrIter
)
    {
    ACHAR           contents[256];

    swprintf (contents, L"%hu,%hu,%u\0", xAttrIter.GetHandlerId().GetMajorId(), xAttrIter.GetHandlerId().GetMinorId(), xAttrIter.GetId());
    string.assign (contents);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/07
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 ExtractXAttributeIdsFromString
(
UInt16*                     pMajorId,
UInt16*                     pMinorId,
UInt32*                     pXAttrId,
RealDwgResBuf*              pXAttrXData
)
    {
    ACHAR const *string = pXAttrXData->GetString();

    return  (3 == swscanf(string, L"%hu,%hu,%u", pMajorId, pMinorId, pXAttrId));
    }

/*---------------------------------------------------------------------------------**//**
* Sets up a chain of BinaryChunk resbufs for a particular XAttribute.
* @bsimethod                                                    RayBentley      08/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 SetBinaryXData
(
RealDwgResBuf*              pBeforeOld,
RealDwgResBuf*              pOldData,
byte const*                 pBytesIn,
UInt32                      numBytesIn
)
    {
    UInt32                  maxChunkSize = 0;

    BeAssert (AcDb::kDxfXdBinaryChunk == pOldData->GetResType() && L"XDATA util - expecting binary data type!");

    // get what's there now so we can compare with what we want to put there.
    RealDwgBinaryData       currBinaryData;
    pOldData->GetBinaryChunk (currBinaryData, false);

    RealDwgResBuf*          pNext;
    RealDwgResBuf*          pLastInOldData = pOldData;
    for (pNext = pOldData->GetNext(); (NULL != pNext) && (AcDb::kDxfXdBinaryChunk == pNext->GetResType()); pNext = pNext->GetNext())
        {
        currBinaryData.AppendResBuf (*pNext);
        pLastInOldData = pNext;
        }

    if ( (numBytesIn != currBinaryData.size()) || (0 != memcmp (pBytesIn, &currBinaryData.front(), numBytesIn)) )
        {
        // Different - must replace. Isolate the old chain and free it.
        pBeforeOld->SetNext (pLastInOldData->GetNext());
        pLastInOldData->SetNext (NULL);
        RealDwgResBuf::Free (pOldData);

        // Create the new binary data ResBuf chain.
        RealDwgResBuf*  newData = RealDwgResBuf::CreateBinaryChain (AcDb::kDxfXdBinaryChunk, pBytesIn, numBytesIn);

        pBeforeOld->InsertAfter (newData);

        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ReplaceXAttributeXData
(
bool&                       changed,
RealDwgResBuf*&             pPrecedingResBuf,
RealDwgResBuf*&             pExistingXAResBuf,
const byte *                pData,
UInt32                      numBytes
)
    {
    // we have XData of the same major,minor,xAttribId at pExistingXAResBuf, but we don't know whether the content is the same.
    RealDwgResBuf*  pBinaryData = pExistingXAResBuf->GetNext();
    BeAssert (AcDb::kDxfXdBinaryChunk == pBinaryData->GetResType() && L"XDATA util - expecting binary data type!");

    // pNextXA will point past the last binary chunk.
    RealDwgResBuf* pNextXA;
    for (pNextXA = pBinaryData; (NULL != pNextXA) && (AcDb::kDxfXdBinaryChunk == pNextXA->GetResType()); pNextXA = pNextXA->GetNext())
        ;

    // pNextXA should point to either a string starting the next XA, or to the end of the XData block.
    BeAssert ( (NULL != pNextXA) && ( (AcDb::kDxfXdAsciiString == pNextXA->GetResType()) || (AcDb::kDxfXdControlString == pNextXA->GetResType())) );

    if (SetBinaryXData (pExistingXAResBuf, pBinaryData, pData, numBytes))
        changed = true;

    // update pPrecedingResBuf to point to the last BinaryChunk in this XAttribute data.
    RealDwgResBuf*  pEndThisXA;
    RealDwgResBuf*  pTestXA = NULL;
    for (pEndThisXA = pExistingXAResBuf->GetNext(); (NULL != pEndThisXA); pEndThisXA = pTestXA)
        {
        pTestXA = pEndThisXA->GetNext();

        // this will never happen.
        if (NULL == pTestXA)
            {
            BeAssert (false && L"XDATA util - unexpected end of resbuf!");
            return;
            }

        if (AcDb::kDxfXdBinaryChunk != pTestXA->GetResType())
            break;
        }

    BeAssert (pEndThisXA->GetNext() == pNextXA && L"XDATA util - updating an unmatching resbuf");

    // update pExistingXAResBuf to point to the next string.
    pExistingXAResBuf = pNextXA;

    // and update pPrecedingResBuf to point to the one that's right before it.
    pPrecedingResBuf = pEndThisXA;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 InsertXAttributeXData
(
RealDwgResBuf*&             pPrecedingResBuf,
const AcString&             idString,
const byte *                pData,
UInt32                      numBytes
)
    {
    RealDwgResBuf*  pStartXA = RealDwgResBuf::CreateString (idString.kwszPtr());
    pPrecedingResBuf->InsertAfter (pStartXA);

    RealDwgResBuf*  newData = RealDwgResBuf::CreateBinaryChain (AcDb::kDxfXdBinaryChunk, pData, numBytes);
    RealDwgResBuf*  endBinaryChain = newData->GetTail();
    pStartXA->InsertAfter (newData);

    pPrecedingResBuf = endBinaryChain;
    }


public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
public: static bool                 RemoveXDataByKey
(
RealDwgResBuf**             ppIn,
ACHAR const*                key
)
    {
    RealDwgResBuf*  pFound;
    if (NULL == (pFound = *ppIn))
        return false;

    RealDwgResBuf*  pBeforeFound = NULL;
    for (; (NULL != pFound); pFound = pFound->GetNext())
        {
        if ( (AcDb::kDxfXdAsciiString == pFound->GetResType()) && (0 == wcscmp (key, pFound->GetString())) )
            break;
        pBeforeFound = pFound;
        }

    // if we didn't find the data we're looking for, get out.
    if (NULL == pFound)
        return false;

    RealDwgResBuf*  pBeforeEnd;
    RealDwgResBuf*  pGroupEnd   = pFound;
    int             groupStack  = 0;
    do
        {
        pBeforeEnd  = pGroupEnd;
        pGroupEnd   = pGroupEnd->GetNext();
        if (AcDb::kDxfXdControlString == pGroupEnd->GetResType())
            {
            if (0 == wcscmp (StringConstants::XData_BeginGroup, pGroupEnd->GetString()) )
                groupStack++;
            else if (0 == wcscmp (StringConstants::XData_EndGroup, pGroupEnd->GetString()))
                groupStack--;
            }
        } while ( (NULL != pGroupEnd) && (groupStack > 0) );

    // we should always find a group end without running out of data.
    BeAssert (nullptr != pGroupEnd && L"XDATA util - unexpected end of resbuf!");
    if (NULL != pGroupEnd)
        {
        // isolate and free the resbuf chain from pFound to pBeforeEnd
        pBeforeEnd->SetNext (NULL);
        RealDwgResBuf::Free (pFound);

        if (NULL != pBeforeFound)
            pBeforeFound->SetNext (pGroupEnd->GetNext());
        else
            *ppIn = pGroupEnd->GetNext();
        }
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/07
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 GetXDataFromXAttributes
(
RealDwgResBuf**             ppMicroStationXData,
ElementRefP                 elementRef,
AcDbDatabase*               pDatabase,
bool                        checkSize = true
)
    {
    /*-----------------------------------------------------------------------------------
    Structure to save XAttributes:

    kDxfXdAsciiString (1000) containing XDataKey_XAttributes
     kDxfXdControlString (1002) containing XData_BeginGroup
      kDxfXdInteger32 (1071) containing count of XAttributes following
       --- XAttribute 1 ----
       {
       kDxfXdAsciiString (1000) containing formatted major,minor,attr id
        one or more collection of kDxfXdBinaryChunk (1004)'s containing actual data
          or
        kDxfNull if no data.
       }
       --- XAttribute 2 ----
       {
       kDxfXdAsciiString (1000) containing formatted major,minor,attr id
        one or more collection of kDxfXdBinaryChunk (1004)'s containing actual data
          or
        kDxfNull if no data.
       }
     kDxfXdControlString (1002) containing xData_EndGroup.


    Variables:
     *ppMicroStationXData   Points to MicroStation XData, or NULL if the caller just wants to find out if there is savable XAttributes.
                            on Entry, can point to NULL, if there is no MicroStation XData.
                            on Exit, points to header of XData that represents the current XAttributes.

    -----------------------------------------------------------------------------------*/
    if (NULL == elementRef)
        return  false;

    // get total number of xattributes attached to this element that we want to save
    UInt32                          numXAttrs = 0;
    UInt32                          xAttrSize = 0;
    ElementHandle eh(elementRef);
    ElementHandle::XAttributeIter   xAttrIter (eh);
    while (xAttrIter.IsValid())
        {
        if (IsXAttributeSaved (xAttrIter.GetHandlerId(), elementRef))
            {
            xAttrSize += xAttrIter.GetSize ();
            numXAttrs++;
            }
        xAttrIter.ToNext();
        }

    // if the caller is only checking to see if there are xattributes, stop.
    if (NULL == ppMicroStationXData)
        return  numXAttrs > 0;

    // DWG has a cap of 16K per application, for XDATA
    if (checkSize && xAttrSize > s_maxXDataSizePerApp)
        return  false;

    if (NULL == *ppMicroStationXData)
        {
        // currently, there is no MicroStation XData.
        if (0 == numXAttrs || 0 == xAttrSize)
            {
            // we still don't need any.
            return  false;
            }
        else
            {
            // we need XData, make sure MicroStation is registered, and create XData.
            RealDwgResBuf* pNewXData = RealDwgResBuf::Create (AcDb::kDxfXdInteger32);
            *ppMicroStationXData = CreateMicroStationXData (pNewXData, StringConstants::XDataKey_XAttribute, pDatabase);
            }
        }

    bool    changed = false;
    if (0 == numXAttrs || 0 == xAttrSize)
        {
        // We have no XAttributes, remove any XData representing XData.
        return RemoveXDataByKey (ppMicroStationXData, StringConstants::XDataKey_XAttribute);
        }

    /*-------------------------------------------------------------------------------
    Here we have determined that we need to save XAttributes as XData.
    We start by finding any existing XData that represents XAttributes.
    If we find any, we loop through and update each with the current XAttribute data.
    Clear up the chain if appearing invalid.
    Otherwise, create and append new ones.
    -------------------------------------------------------------------------------*/
    RealDwgResBuf* pXAStartResBuf = FindXDataByKey (*ppMicroStationXData, AcDb::kDxfXdInteger32, StringConstants::XDataKey_XAttribute, true);

    // Now, pXAStartResBuf should point to the kDxfXDInteger32 resbuf that indicates the number of XAttributes currently stored in the the pXAStartResBuf resbuf chain.
    BeAssert (AcDb::kDxfXdInteger32 == pXAStartResBuf->GetResType());

    // pExistingXAResBuf points to the ResBuf that corresponds to the current XData. If NULL, we've cleared all of the following XAttribute XData.
    RealDwgResBuf*   pExistingXAResBuf = NULL;

    // the chain starts with number of xattributes to follow:
    if (numXAttrs != pXAStartResBuf->GetInt32())
        {
        pXAStartResBuf->DeleteUntilEndGroup();
        pXAStartResBuf->SetInt32 (numXAttrs);
        changed = true;
        }
    else
        {
        pExistingXAResBuf = pXAStartResBuf->GetNext();
        BeAssert (AcDb::kDxfXdAsciiString == pExistingXAResBuf->GetResType());
        }

    // pPrecedingResBuf is the resbuf we put the next XAttribute after.
    RealDwgResBuf*  pPrecedingResBuf = pXAStartResBuf;

    xAttrIter = ElementHandle::XAttributeIter (eh);
    while (xAttrIter.IsValid())
        {
        if (!IsXAttributeSaved (xAttrIter.GetHandlerId(), elementRef))
            {
            xAttrIter.ToNext();
            continue;
            }

        AcString    xaString;
        BuildXAttributeIdsString (xaString, xAttrIter);
        if (NULL != pExistingXAResBuf)
            {
            if (0 != wcscmp (pExistingXAResBuf->GetString(), xaString.kwszPtr()))
                {
                pExistingXAResBuf->DeleteUntilEndGroup();
                pExistingXAResBuf = NULL;
                changed = true;
                }
            }

        // insert or reset string of xattribute ID's
        if (NULL == pExistingXAResBuf)
            InsertXAttributeXData (pPrecedingResBuf, xaString, (byte*)xAttrIter.PeekData(), xAttrIter.GetSize());
        else
            ReplaceXAttributeXData (changed, pPrecedingResBuf, pExistingXAResBuf, (byte*)xAttrIter.PeekData(), xAttrIter.GetSize());

        xAttrIter.ToNext();
        }

    // we should exit with pPrecedingResBuf's next being the end of the XAttribute XData group.
    BeAssert (AcDb::kDxfXdControlString == pPrecedingResBuf->GetNext()->GetResType());

    return  changed;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       FindXDataByKey
(
RealDwgResBuf*              pResBufChain,       // => res buf chain to look in
int                         resType,            // => the type that's the first thing in the XData group.
const AcString&             key,                // => the contents of the DxfXdControlString that identifies the XData.
bool                        createIfNotFound,   // => if true, create an XData group and put it at the end of pResBufChain
RealDwgResBuf**             pKeyResBuf = NULL   // <= if not NULL, fill in with the ResBuf of the key.
)
    {
    // XData has this structure:
    //  1. DxfXdAsciiString Resbuf containing the XData key.
    //  2.    DxfXdControlString Resbuf containing "{" (BeginGroup).
    //  3.        ResType ResBuf
    //            possibly other ResBufs.
    //  n.    DxfXdControlString ResBuf containing "}" (EndGroup).
    // When this method returns, it returns either NULL or a pointer to the third ResBuf (the ResType one).
    // If the optional pKeyResBuf argument is not NULL, it is filled in with a pointer to the first ResBuf.
    FindMSDataTraverseArg   traverseArg (resType, key.kwszPtr(), pResBufChain);
    TraverseXData (pResBufChain, FindMSDataTraverseFunc, &traverseArg);

    if ( (NULL == traverseArg.m_resBufWhereFound) && createIfNotFound)
        {
        // xData with that key not found, create empty resBuf of that type and return it.
        RealDwgResBuf* pNewResBuf = RealDwgResBuf::Create (resType);
        traverseArg.m_keyResBuf = CreateXDataGroup (pNewResBuf, key);
        traverseArg.m_lastResBuf->InsertAfter (traverseArg.m_keyResBuf);
        traverseArg.m_resBufWhereFound = pNewResBuf;
        }
    if (NULL != pKeyResBuf)
        *pKeyResBuf = traverseArg.m_keyResBuf;

    return traverseArg.m_resBufWhereFound;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            GetMicroStationXDataStringByKey
(
AcString&                   pString,
AcDbObject*                 pObject,                    // => object
const AcString&             microStationXDataKey        // => MicroStation XData key.
)
    {
    RealDwgResBuf*          pMicroStationXData = static_cast <RealDwgResBuf*> (pObject->xData (StringConstants::RegAppName_MicroStation));

    if (NULL == pMicroStationXData)
        return BSIERROR;

    StatusInt               returnVal = GetXDataStringByKey (pString, pMicroStationXData, microStationXDataKey);

    // free the XData.
    RealDwgResBuf::Free (pMicroStationXData);

    return returnVal;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            GetXDataStringByKey
(
AcString&                   pString,
RealDwgResBuf*              pResBuf,
const AcString&             microStationXDataKey        // => MicroStation XData key.
)
    {
    RealDwgResBuf*          pStringResBuf = FindXDataByKey (pResBuf, AcDb::kDxfXdAsciiString, microStationXDataKey, false);
    if (NULL != pStringResBuf)
        pString.assign (pStringResBuf->GetString());

    return (NULL != pStringResBuf) ? SUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 SetMicroStationXDataStringByKey
(
AcDbObject*                 pObject,                    // => object
const AcString&             microStationXDataKey,       // => MicroStation XData key.
const AcString&             value,                      // => String value.
AcDbDatabase*               pDwg                        // <=> DWg database to add MStation Regapp as needed
)                                                       // <= true if linkage changed.
    {
    RealDwgResBuf*  pMicroStationXData;

    if (nullptr == (pMicroStationXData = static_cast <RealDwgResBuf*> (pObject->xData (StringConstants::RegAppName_MicroStation))))
        {
        // will try creating a new MicroStation XDATA and add it to the object:
        if (nullptr == pDwg)
            return false;

        RealDwgResBuf*      pNewXData = RealDwgResBuf::Create (AcDb::kDxfXdAsciiString);
        if (nullptr == pNewXData || nullptr == (pMicroStationXData = CreateMicroStationXData(pNewXData, microStationXDataKey, pDwg)))
            return false;
        }

    bool            returnVal = SetXDataStringByKey (pMicroStationXData, microStationXDataKey, value);

    if (returnVal)
        pObject->setXData (pMicroStationXData);

    RealDwgResBuf::Free (pMicroStationXData);
    return returnVal;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 SetXDataStringByKey         // <= returns true if changed.
(
RealDwgResBuf*              pResBuf,
const AcString&             microStationXDataKey,       // => MicroStation XData key.
const AcString&             value
)
    {
    RealDwgResBuf*          pStringResBuf = FindXDataByKey (pResBuf, AcDb::kDxfXdAsciiString, microStationXDataKey, true);
    BeAssert ( (NULL != pStringResBuf) && (AcDb::kDxfXdAsciiString == pStringResBuf->GetResType()) );

    if (NULL != pStringResBuf->GetString() && 0 == wcscmp (pStringResBuf->GetString(), value.kwszPtr()))
        return false;

    pStringResBuf->SetString (value.kwszPtr());
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 SetXDataInt32ByKey           // <= returns true if changed.
(
RealDwgResBuf*              pResBuf,
const AcString&             microStationXDataKey,       // => MicroStation XData key.
int                         value
)
    {
    RealDwgResBuf*          pInt32ResBuf = FindXDataByKey (pResBuf, AcDb::kDxfXdInteger32, microStationXDataKey, true);
    BeAssert ( (NULL != pInt32ResBuf) && (AcDb::kDxfXdInteger32 == pInt32ResBuf->GetResType()) );

    // if the same, don't need to set.
    if (pInt32ResBuf->GetInt32() == value)
        return false;

    pInt32ResBuf->SetInt32 (value);
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 SetXDataDoubleByKey           // <= returns true if changed.
(
RealDwgResBuf*              pResBuf,
const AcString&             microStationXDataKey,       // => MicroStation XData key.
double                      value
)
    {
    RealDwgResBuf*          pDoubleResBuf = FindXDataByKey (pResBuf, AcDb::kDxfXdReal, microStationXDataKey, true);
    BeAssert ( (NULL != pDoubleResBuf) && (AcDb::kDxfXdReal == pDoubleResBuf->GetResType()) );

    // if the same, don't need to set.
    if (pDoubleResBuf->GetDouble() == value)
        return false;

    pDoubleResBuf->SetDouble (value);
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            DependencyLinkageFromXData
(
DependencyLinkage       **ppDependencyLinkage,  // caller responsible to free on SUCCESS
RealDwgResBuf**           ppXData,
ConvertToDgnContextR      context
)
    {
    DependencyLinkage   dependencyLinkage;
    RealDwgResBuf*      pXData = *ppXData;

    if ( (NULL != pXData) && (AcDb::kDxfXdInteger16 == pXData->GetResType()))
        dependencyLinkage.appID = pXData->GetInt16();
    else
        return BSIERROR;

    if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdInteger16 == pXData->GetResType()))
        dependencyLinkage.appValue = pXData->GetInt16();
    else
        return BSIERROR;

    if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdInteger16 == pXData->GetResType()))
        dependencyLinkage.u.flags = pXData->GetInt16();
    else
        return BSIERROR;

    if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdInteger16 == pXData->GetResType()))
        dependencyLinkage.nRoots = pXData->GetInt16();
    else
        return BSIERROR;

    if (DEPENDENCY_DATA_TYPE_PATH_V == dependencyLinkage.u.f.rootDataType)
        {
        if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdInteger32 == pXData->GetResType()))
            dependencyLinkage.root.path_v.numElemsInPath = pXData->GetInt32();
        else
            return BSIERROR;

        dependencyLinkage.root.path_v.reserved = 0;

        if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdReal == pXData->GetResType()))
            dependencyLinkage.root.path_v.value = pXData->GetDouble();
        else
            return BSIERROR;

        size_t  pathSize = offsetof(DependencyLinkage, root) + sizeof(DependencyRootPath_V) + dependencyLinkage.root.path_v.numElemsInPath * sizeof(ElementId);
        if (NULL == (*ppDependencyLinkage = (DependencyLinkage*)malloc(pathSize)))
            return  BSIERROR;

        // copy header & some data we have collected so far before use of extended memory for path array:
        memcpy (*ppDependencyLinkage, &dependencyLinkage, offsetof(DependencyLinkage, root) + sizeof(DependencyRootPath_V));

        for (UInt32 i = 0; i < (*ppDependencyLinkage)->root.path_v.numElemsInPath; i++)
            {
            if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdHandle == pXData->GetResType()))
                {
                (*ppDependencyLinkage)->root.path_v.path[i] = context.ElementIdFromDBHandle (pXData->GetHandle());
                }
            else
                {
                free (*ppDependencyLinkage);
                return BSIERROR;
                }
            }

        *ppXData = pXData->GetNext();

        return  SUCCESS;
        }

    size_t  maxLinkageSize = offsetof(DependencyLinkage, root) + dependencyLinkage.nRoots * sizeof(DependencyRootFarElementID_V);

    if (NULL == (*ppDependencyLinkage = (DependencyLinkage*)malloc(maxLinkageSize)))
        return  BSIERROR;

    // copy header before use of extended memory for element array:
    memcpy (*ppDependencyLinkage, &dependencyLinkage, offsetof(DependencyLinkage, root));

    for (int i=0; i<(*ppDependencyLinkage)->nRoots; i++)
        {
        switch ((*ppDependencyLinkage)->u.f.rootDataType)
            {
            case DEPENDENCY_DATA_TYPE_ELEM_ID:
                if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdHandle == pXData->GetResType()))
                    {
                    (*ppDependencyLinkage)->root.elemid[i] = context.ElementIdFromDBHandle (pXData->GetHandle());
                    }
                else
                    {
                    free (*ppDependencyLinkage);
                    return BSIERROR;
                    }
                break;

            case DEPENDENCY_DATA_TYPE_ELEM_ID_V:
                if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdHandle == pXData->GetResType()))
                    {
                    (*ppDependencyLinkage)->root.e_v[i].elemid = context.ElementIdFromDBHandle (pXData->GetHandle());
                    }
                else
                    {
                    free (*ppDependencyLinkage);
                    return BSIERROR;
                    }

                if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdReal == pXData->GetResType()))
                    {
                    (*ppDependencyLinkage)->root.e_v[i].value = pXData->GetDouble();
                    }
                else
                    {
                    free (*ppDependencyLinkage);
                    return BSIERROR;
                    }
                break;

            case DEPENDENCY_DATA_TYPE_FAR_ELEM_ID_V:
                if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdHandle == pXData->GetResType()))
                    {
                    (*ppDependencyLinkage)->root.far_e_v[i].s.elemid = context.ElementIdFromDBHandle (pXData->GetHandle());
                    }
                else
                    {
                    free (*ppDependencyLinkage);
                    return BSIERROR;
                    }

                if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdReal == pXData->GetResType()))
                    {
                    (*ppDependencyLinkage)->root.far_e_v[i].s.value = pXData->GetDouble();
                    }
                else
                    {
                    free (*ppDependencyLinkage);
                    return BSIERROR;
                    }

                if ( (NULL != (pXData = pXData->GetNext())) && (AcDb::kDxfXdHandle == pXData->GetResType()))
                    {
                    (*ppDependencyLinkage)->root.far_e_v[i].refattid = context.ElementIdFromDBHandle (pXData->GetHandle());
                    }
                else
                    {
                    free (*ppDependencyLinkage);
                    return BSIERROR;
                    }
                break;

            default:
                free (*ppDependencyLinkage);
                return BSIERROR;
            }
        }

    *ppXData = pXData->GetNext();

    return SUCCESS;
    }

private:

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsSupportedDataType
(
UInt32                      dataType
)
    {
    switch (dataType)
        {
        case DEPENDENCY_DATA_TYPE_ELEM_ID:
        case DEPENDENCY_DATA_TYPE_ELEM_ID_V:
        case DEPENDENCY_DATA_TYPE_FAR_ELEM_ID_V:
        case DEPENDENCY_DATA_TYPE_PATH_V:
            return true;
        }

    return  false;
    }


public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*       XDataFromDependencyLinkage
(
const DependencyLinkage*    pDependencyLinkage,
ConvertFromDgnContextR      context
)
    {
    if (!IsSupportedAppDependency (pDependencyLinkage->appID) || !IsSupportedDataType(pDependencyLinkage->u.f.rootDataType))
        return NULL;

    RealDwgResBuf*          pXData = RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, pDependencyLinkage->appID);
    RealDwgResBuf*          pLast = pXData;

    pLast = pLast->Append (RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, pDependencyLinkage->appValue));
    pLast = pLast->Append (RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, pDependencyLinkage->u.flags));
    pLast = pLast->Append (RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, pDependencyLinkage->nRoots));

    if (DEPENDENCY_DATA_TYPE_PATH_V == pDependencyLinkage->u.f.rootDataType)
        {
        pLast = pLast->Append (RealDwgResBuf::CreateInt32 (AcDb::kDxfXdInteger32, pDependencyLinkage->root.path_v.numElemsInPath));
        pLast = pLast->Append (RealDwgResBuf::CreateDouble (AcDb::kDxfXdReal, pDependencyLinkage->root.path_v.value));

        for (UInt32 iRoot = 0; iRoot < pDependencyLinkage->root.path_v.numElemsInPath; iRoot++)
            {
            pLast = pLast->Append (RealDwgResBuf::CreateHandle (AcDb::kDxfXdHandle, context.DBHandleFromElementId (pDependencyLinkage->root.path_v.path[iRoot])));
            }

        return pXData;
        }

    for (int iRoot=0; iRoot < pDependencyLinkage->nRoots; iRoot++)
        {
        switch (pDependencyLinkage->u.f.rootDataType)
            {
            case DEPENDENCY_DATA_TYPE_ELEM_ID:
                pLast = pLast->Append (RealDwgResBuf::CreateHandle (AcDb::kDxfXdHandle, context.DBHandleFromElementId (pDependencyLinkage->root.elemid[iRoot])));
                break;

            case DEPENDENCY_DATA_TYPE_ELEM_ID_V:
                pLast = pLast->Append (RealDwgResBuf::CreateHandle (AcDb::kDxfXdHandle, context.DBHandleFromElementId (pDependencyLinkage->root.e_v[iRoot].elemid)));
                pLast = pLast->Append (RealDwgResBuf::CreateDouble (AcDb::kDxfXdReal, pDependencyLinkage->root.e_v[iRoot].value));
                break;

            case DEPENDENCY_DATA_TYPE_FAR_ELEM_ID_V:
                pLast = pLast->Append (RealDwgResBuf::CreateHandle (AcDb::kDxfXdHandle, context.DBHandleFromElementId (pDependencyLinkage->root.far_e_v[iRoot].s.elemid)));
                pLast = pLast->Append (RealDwgResBuf::CreateDouble (AcDb::kDxfXdReal, pDependencyLinkage->root.far_e_v[iRoot].s.value));
                pLast = pLast->Append (RealDwgResBuf::CreateHandle (AcDb::kDxfXdHandle, context.DBHandleFromElementId (pDependencyLinkage->root.far_e_v[iRoot].refattid)));
                break;

            case DEPENDENCY_DATA_TYPE_ASSOC_POINT:
                break;
            }
        }

    return pXData;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 SetAppLinkage
(
RealDwgResBuf**             ppMicroStationXData,    // <=> linkage updated with linkage
const UShort*               pLinkage,               // => data to store
int                         linkageSize,            // => size in bytes.
AcDbDatabase*               pDatabase               // => needed in case we have to create new XData.
)                                                   // <= true if linkage changed.
    {
    if (NULL == *ppMicroStationXData)
        {
        if (0 == linkageSize)
            return false;

        // put together a binary chunk resbuf.
        RealDwgResBuf*  pNewXData = RealDwgResBuf::CreateBinaryChain (AcDb::kDxfXdBinaryChunk, (const byte*) pLinkage, linkageSize*2);
        *ppMicroStationXData = CreateMicroStationXData (pNewXData, StringConstants::XDataKey_ApplicationLinkage, pDatabase);
        return true;
        }

    if (0 == linkageSize)
        return RemoveXDataByKey (ppMicroStationXData, StringConstants::XDataKey_ApplicationLinkage);

    FindMSDataTraverseArg   traverseArg (AcDb::kDxfXdBinaryChunk, StringConstants::XDataKey_ApplicationLinkage, *ppMicroStationXData);
    TraverseXData (*ppMicroStationXData, FindMSDataTraverseFunc, &traverseArg);

    RealDwgResBuf*  pData;
    if (NULL != (pData = traverseArg.m_resBufWhereFound))
        return SetBinaryXData (traverseArg.m_resBufPrecedingData, pData, (const byte *) pLinkage, 2*linkageSize);

    // xData with that key not found, create binary chain and store it.
    RealDwgResBuf* pNewResBuf = RealDwgResBuf::CreateBinaryChain (AcDb::kDxfXdBinaryChunk, (const byte *) pLinkage, linkageSize*2);
    traverseArg.m_lastResBuf->InsertAfter (CreateXDataGroup (pNewResBuf, StringConstants::XDataKey_ApplicationLinkage));
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      8/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 SetGraphicGroup
(
RealDwgResBuf**             ppMicroStationXData,        // <=> linkage updated with linkage
UInt32                      graphicGroup,
AcDbDatabase*               pDatabase               // => needed in case we have to create new XData.
)                                               // <= true if linkage changed.
    {
    if (NULL == *ppMicroStationXData)
        {
        if (0 == graphicGroup)
            return false;

        // put together an Int32 resbuf representing the graphic group.
        RealDwgResBuf*  pNewXData = RealDwgResBuf::CreateInt32 (AcDb::kDxfXdInteger32, graphicGroup);
        *ppMicroStationXData = CreateMicroStationXData (pNewXData, StringConstants::XDataKey_GraphicGroup, pDatabase);
        return true;
        }

    if (0 == graphicGroup)
        return RemoveXDataByKey (ppMicroStationXData, StringConstants::XDataKey_GraphicGroup);

    return SetXDataInt32ByKey (*ppMicroStationXData, StringConstants::XDataKey_GraphicGroup, graphicGroup);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/2005
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 SetTransparency
(
RealDwgResBuf**             ppMicroStationXData,        // <=> linkage updated with linkage
double                      transparency,
AcDbDatabase*               pDatabase
)                                                       // <= true if linkage changed.
    {
    if (NULL == *ppMicroStationXData)
        {
        if (0.0 == transparency)
            return false;

        RealDwgResBuf*  pTransparency = RealDwgResBuf::CreateDouble (AcDb::kDxfXdReal, transparency);
        *ppMicroStationXData = CreateMicroStationXData (pTransparency, StringConstants::XDataKey_Transparency, pDatabase);
        return true;
        }

    if (0.0 == transparency)
        return RemoveXDataByKey (ppMicroStationXData, StringConstants::XDataKey_Transparency);

    return SetXDataDoubleByKey (*ppMicroStationXData, StringConstants::XDataKey_Transparency, transparency);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/05
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            ExtractHyperlinkXData
(
AcString&                   title1,
AcString&                   title2,
AcString&                   url,
RealDwgResBuf*              pXData
)
    {
    if (NULL == pXData)
        return  BSIERROR;

    RealDwgResBuf*  pHyperlinkXData = GetXDataAt (pXData, StringConstants::RegAppName_PeUrl);
    if (NULL == pHyperlinkXData)
        return  BSIERROR;

    pHyperlinkXData = pHyperlinkXData->GetNext ();

    bool        hasStarted = false, hasEnded = false;
    int         version = 0;

    while (NULL != pHyperlinkXData)
        {
        switch (pHyperlinkXData->GetResType())
            {
            case AcDb::kDxfXdControlString:
                if (hasStarted && (0 == wcscmp (pHyperlinkXData->GetString(), L"}")))
                    hasEnded = true;
                else if (!hasStarted)
                    hasStarted = true;
                break;

            case AcDb::kDxfXdAsciiString:
                if (hasStarted)
                    {
                    if (title1.isEmpty())
                        title1.assign (pHyperlinkXData->GetString ());
                    else if (title2.isEmpty())
                        title2.assign (pHyperlinkXData->GetString ());
                    }
                else
                    {
                    url.assign (pHyperlinkXData->GetString ());
                    }
                break;

            case AcDb::kDxfXdInteger32:
                version = pHyperlinkXData->GetInt32 ();
                break;
            }

        if (hasEnded || AcDb::kDxfRegAppName == pHyperlinkXData->GetResType())
            break;

        pHyperlinkXData = pHyperlinkXData->GetNext ();
        }

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddXAttributes
(
MSElementDescrP             pElmdscr,
RealDwgResBuf*              pXAttributeXData,
DgnModelP                   modelRef
)
    {
    EditElementHandle  elemHandle(pElmdscr, false, false);
    elemHandle.SetModelRef (modelRef);
    return  RealDwgXDataUtil::AddXAttributes(elemHandle, pXAttributeXData);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/07
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AddXAttributes
(
EditElementHandleR          elemHandle,
RealDwgResBuf*              pXAttributeXData
)
    {
    /*-----------------------------------------------------------------------------------
    See the counterpart method SetXDataFromXAttributes for xdata structure of xattributes saved to DWG.
    -----------------------------------------------------------------------------------*/
    if ( (NULL == pXAttributeXData) || (AcDb::kDxfXdInteger32 != pXAttributeXData->GetResType()) )
        return  MDLERR_BADDATADEF;

    StatusInt       status = SUCCESS;
    UInt32          numXAttrs = pXAttributeXData->GetInt32 ();
    pXAttributeXData = pXAttributeXData->GetNext ();

    for (UInt16 iXAttr = 0; (iXAttr < numXAttrs) && (NULL != pXAttributeXData); iXAttr++)
        {
        if (AcDb::kDxfXdAsciiString != pXAttributeXData->GetResType())
            {
            status = MDLERR_BADDATADEF;
            break;
            }

        UInt16          majorId = 0, minorId = 0;
        UInt32          xAttrId = 0;

        if (!ExtractXAttributeIdsFromString (&majorId, &minorId, &xAttrId, pXAttributeXData))
            {
            status = MDLERR_BADDATADEF;
            break;
            }
        pXAttributeXData = pXAttributeXData->GetNext ();

        XAttributeHandlerId handlerId (majorId, minorId);

        // collect binary chunks
        RealDwgBinaryData        binaryData;
        while ( (NULL != pXAttributeXData) && (AcDb::kDxfXdBinaryChunk == pXAttributeXData->GetResType()) )
            {
            pXAttributeXData->GetBinaryChunk (binaryData, true);
            pXAttributeXData = pXAttributeXData->GetNext ();
            }

        UInt32  xAttrSize = (UInt32) binaryData.size ();

        if (xAttrSize > 0)
            elemHandle.ScheduleWriteXAttribute (handlerId, xAttrId, xAttrSize, &binaryData.front());
        else
            status = MDLERR_BADDATADEF;

        // by now pXAttributeXData should be either the id string for the next xaatribute or the end mark } if done.
        if ( (NULL != pXAttributeXData) && (AcDb::kDxfXdControlString == pXAttributeXData->GetResType()) )
            pXAttributeXData = pXAttributeXData->GetNext ();
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgResBuf*           GetXDataAt
(
RealDwgResBuf*                  pXData,
const ACHAR*                    appName
)
    {
    RealDwgResBuf*              pCurr = pXData;

    while (NULL != pCurr && AcDb::kDxfRegAppName == pCurr->GetResType())
        {
        if (wcscmp(appName, pCurr->GetString()) == 0)
            return pCurr;
        pCurr = pCurr->GetNext ();
        }

    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/05
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt                ExtractStringInfo
(
WChar                         *pCodepage,
int                             maxChars,
int                             *pFlags,
RealDwgResBuf*                  pXData
)
    {
    if (NULL == pXData)
        return  BSIERROR;

    RealDwgResBuf*      pStringXData = GetXDataAt (pXData, L"AcadStringInfo");
    if (NULL == pStringXData)
        return  BSIERROR;

    if (NULL != pCodepage)
        *pCodepage = 0;
    if (NULL != pFlags)
        *pFlags = 0;

    int     numItems = 0;
    bool    done = false;

    for (pStringXData = pStringXData->GetNext(); NULL != pStringXData; pStringXData = pStringXData->GetNext())
        {
        // get codepage name and 1070 integer
        switch (pStringXData->GetResType())
            {
            case AcDb::kDxfXdAsciiString:       // 1000
                if (maxChars > 0 && NULL != pCodepage)
                    RealDwgUtil::TerminatedStringCopy (pCodepage, pStringXData->GetString(), maxChars);

                numItems++;
                break;

            case AcDb::kDxfXdInteger16:         // 1070
                if (NULL != pFlags)
                    *pFlags = pStringXData->GetInt16 ();
                numItems++;
                break;

            case AcDb::kDxfXdControlString:           // '{' or '}'
                if (wcscmp (pStringXData->GetString(), L"}"))
                    done = true;
                break;

            case AcDb::kDxfRegAppName:          // next regapp
                done = true;
                break;
            }

        if (done)
            break;
        }

    return  numItems >= 2 ? SUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      04/01
+---------------+---------------+---------------+---------------+---------------+------*/
static int                  GetAnsiCodePageFromDwgCodePage
(
code_page_id                codePage
)
    {
    // ref http://msdn.microsoft.com/library/default.asp?url=/library/en-us/intl/unicode_81rn.asp
    switch (codePage)
        {
        case code_page_id::CODE_PAGE_ANSI_1252:
            return  1252;
        case code_page_id::CODE_PAGE_ANSI_1250:
            return  1250;
        case code_page_id::CODE_PAGE_ANSI_1251:
            return  1251;
        case code_page_id::CODE_PAGE_ANSI_1253:
            return  1253;
        case code_page_id::CODE_PAGE_ANSI_1254:
            return  1254;
        case code_page_id::CODE_PAGE_ANSI_1255:
            return  1255;
        case code_page_id::CODE_PAGE_ANSI_1256:
            return  1256;
        case code_page_id::CODE_PAGE_ANSI_1257:
            return  1257;
        case code_page_id::CODE_PAGE_ANSI_1258:
            return  1258;
        case code_page_id::CODE_PAGE_ANSI_874:
            return  874;
        case code_page_id::CODE_PAGE_DOS932:
        case code_page_id::CODE_PAGE_ANSI_932:
            return  932;
        case code_page_id::CODE_PAGE_ANSI_936:
        case code_page_id::CODE_PAGE_GB2312:
            return  936;
        case code_page_id::CODE_PAGE_ANSI_949:
        case code_page_id::CODE_PAGE_KSC5601:
            return  949;
        case code_page_id::CODE_PAGE_ANSI_950:
        case code_page_id::CODE_PAGE_BIG5:
            return  950;
        case code_page_id::CODE_PAGE_ANSI_1361:
            return  1361;
        case code_page_id::CODE_PAGE_DOS437:
            return  437;
        case code_page_id::CODE_PAGE_DOS850:
            return  850;
        case code_page_id::CODE_PAGE_DOS852:
            return  852;
        case code_page_id::CODE_PAGE_DOS855:
            return  855;
        case code_page_id::CODE_PAGE_DOS857:
            return  857;
        case code_page_id::CODE_PAGE_DOS860:
            return  860;
        case code_page_id::CODE_PAGE_DOS861:
            return  861;
        case code_page_id::CODE_PAGE_DOS863:
            return  863;
        case code_page_id::CODE_PAGE_DOS864:
            return  864;
        case code_page_id::CODE_PAGE_DOS865:
            return  865;
        case code_page_id::CODE_PAGE_DOS869:
            return  869;
        case code_page_id::CODE_PAGE_MACINTOSH:
            return  10000;
        case code_page_id::CODE_PAGE_DOS866:
            return  866;
        case  code_page_id::CODE_PAGE_8859_1:
            return  28591;
        case  code_page_id::CODE_PAGE_8859_2:
            return  28592;
        case  code_page_id::CODE_PAGE_8859_3:
            return  28593;
        case  code_page_id::CODE_PAGE_8859_4:
            return  28594;
        case  code_page_id::CODE_PAGE_8859_5:
            return  28595;
        case  code_page_id::CODE_PAGE_8859_6:
            return  28596;
        case  code_page_id::CODE_PAGE_8859_7:
            return  28597;
        case  code_page_id::CODE_PAGE_8859_8:
            return  28598;
        case  code_page_id::CODE_PAGE_8859_9:
            return  28599;
        default :
            return  1252;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/06
+---------------+---------------+---------------+---------------+---------------+------*/
static bool             GetAnsiCodePageFromXData
(
LangCodePage*           pAnsiCodePage,
RealDwgResBuf*          pXData
)
    {
    WChar             cpName[256];
    int                 mPlusCode;

    if (SUCCESS == ExtractStringInfo(cpName, _countof (cpName), &mPlusCode, pXData) && 0 == wcsicmp(cpName, L"MIFCodepage"))
        {
        unsigned        cpId = AdCharFormatter::getMIFCodePage('0' + mPlusCode);

        // RealDWG returns ANSI codepage from above method:
        *pAnsiCodePage = (LangCodePage)cpId;

        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/09
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDb::UnitsValue     GetMaterialScaleUnits (const AcDbMaterial* dwgMaterial)
    {
    AcDb::UnitsValue        scaleUnits = AcDb::kUnitsUndefined;
    RealDwgResBuf*          pXData = static_cast <RealDwgResBuf*> (dwgMaterial->xData(L"ACAD"));
    if (NULL != pXData)
        {
        /*-------------------------------------------------------------------------------
        1001    <USER>
        <GROUP>    int16   => index of material list
        1070    int16   => number of materials in the list
        1070    int16   => material scale units value
        1000    string
        1071    int32
        1070    int16
        -------------------------------------------------------------------------------*/
        int                 count = 0;
        for (pXData = pXData->GetNext(); NULL != pXData; pXData = pXData->GetNext())
            {
            if (AcDb::kDxfXdInteger16 == pXData->GetResType())
                {
                if (count++ > 1)
                    {
                    scaleUnits = (AcDb::UnitsValue) pXData->GetInt16 ();
                    break;
                    }
                }
            }
        RealDwgResBuf::Free (pXData);
        }

    return  scaleUnits;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/10
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetMaterialScaleUnits (AcDbMaterial* dwgMaterial, AcDb::UnitsValue scaleUnits, UInt32 index)
    {
    bool                    updated = false;
    RealDwgResBuf*          pXData = static_cast <RealDwgResBuf*> (dwgMaterial->xData(L"ACAD"));
    if (NULL == pXData)
        {
        // add a new xdata
        AddRegApp (StringConstants::RegAppName_Acad, dwgMaterial->database());
        pXData = RealDwgResBuf::CreateRegAppXData (StringConstants::RegAppName_Acad);

        RealDwgResBuf*      pTail = pXData->GetTail ();
        pTail->InsertAfter (RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, 0));
        pTail = pTail->GetTail ();
        pTail->InsertAfter (RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, index));
        pTail = pTail->GetTail ();
        pTail->InsertAfter (RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, scaleUnits));
        pTail = pTail->GetTail ();
        pTail->InsertAfter (RealDwgResBuf::CreateString (AcDb::kDxfXdAsciiString, L""));
        pTail = pTail->GetTail ();
        pTail->InsertAfter (RealDwgResBuf::CreateInt32 (AcDb::kDxfXdInteger32, 0));
        pTail = pTail->GetTail ();
        pTail->InsertAfter (RealDwgResBuf::CreateInt16 (AcDb::kDxfXdInteger16, 0));
        
        updated = true;
        }
    else
        {
        // update existing xdata
        int                 count = 0;
        for (pXData = pXData->GetNext(); NULL != pXData; pXData = pXData->GetNext())
            {
            if (AcDb::kDxfXdInteger16 == pXData->GetResType())
                {
                switch (count++)
                    {
                    case 1:
                        if (index != (AcDb::UnitsValue)pXData->GetInt16())
                            {
                            pXData->SetInt16 (index);
                            updated = true;
                            }
                        break;
                    case 2:
                        if (scaleUnits != (AcDb::UnitsValue)pXData->GetInt16())
                            {
                            pXData->SetInt16 (scaleUnits);
                            updated = true;
                            }
                        break;
                    }
                }
            }
        }

    Acad::ErrorStatus   errorStatus = Acad::eOk;
    if (updated)
        errorStatus = dwgMaterial->setXData (pXData);
    
    RealDwgResBuf::Free (pXData);

    return  Acad::eOk == errorStatus ? RealDwgSuccess : XDataError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    don.fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        GetIesFileNameFromWebLight(AcString& iesFile, AcDbXrecord* xRecord, AcDbDatabase* database)
    {
    /*-----------------------------------------------------------------------------------
    Web light has IES file named stored on its xrecord, as the first DXF group code 300.
    -----------------------------------------------------------------------------------*/
    RealDwgResBuf*      resBuf = NULL;
    if (NULL == xRecord || Acad::eOk != xRecord->rbChain((struct resbuf**)&resBuf, database))
        return  XDataError;

    RealDwgResBuf*      xData = resBuf;
    bool                foundName = false;
    for (; NULL != xData; xData = xData->GetNext())
        {
        if (AcDb::kDxfXTextString == xData->GetResType())
            {
            // As of R2011, we should get here the very 1st entry in resbuf.
            iesFile = xData->GetString ();
            foundName = true;
            break;
            }
        }
    
    RealDwgResBuf::Free (resBuf);
    
    return  foundName ? RealDwgSuccess : XDataError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/14
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     GetDynamicBlockHandleFromProxyBlock (AcDbHandle& dynBlockHandle, AcDbBlockTableRecord* proxyBlock)
    {
    RealDwgResBuf*      pXData = static_cast <RealDwgResBuf*> (proxyBlock->xData(L"AcDbBlockRepBTag"));
    if (NULL == pXData)
        return  false;

    /*-------------------------------------------------------------------------------
    1001    <USER>
    <GROUP>    int16
    1005    Inter-object ID
    -------------------------------------------------------------------------------*/
    dynBlockHandle.setNull ();

    for (pXData = pXData->GetNext(); NULL != pXData; pXData = pXData->GetNext())
        {
        if (AcDb::kDxfXdHandle == pXData->GetResType())
            {
            dynBlockHandle = pXData->GetHandle ();
            break;
            }
        }

    RealDwgResBuf::Free (pXData);

    return  !dynBlockHandle.isNull();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/15
+---------------+---------------+---------------+---------------+---------------+------*/
static UInt32   ExtractXDataLinkage (byte const** xdataLinkage, ElementHandleCR element)
    {
    for (ConstElementLinkageIterator iter = element.BeginElementLinkages(); iter != element.EndElementLinkages(); ++iter)
        {
        if (LINKAGEID_XData == iter->primaryID && iter->user)
            {
            XDataLinkage const* linkage = static_cast <XDataLinkage const*> (iter.GetData());

            *xdataLinkage = linkage->byteBuffer;

            return  linkage->bytesToFollow;
            }
        }

    *xdataLinkage = nullptr;
    return  0;
    }


};

RealDwgBinaryData    RealDwgXDataUtil::XferBinaryData;


/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdAttribute.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

// Note: No AcDbAttribute ToDgnExtension is needed, because the attributes are saved from the
//       owning block references.

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtTagElement : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleR elemHandle, AcDbObjectP existingObject, ConvertFromDgnContextR context) const
    {
    // Tag that is not associated with another element? Create an orphan definition.
    AttributeElm const* pElm = reinterpret_cast<AttributeElm const*> (elemHandle.GetElementCP());
    if (!(pElm->flags & ATTR_FLAG_ASSOC))
        {
        // will try to create a attrdef, but simply drop the tag to text if its set def is missing as seen in TFS 313333.
        ElementId   setElemId = TagElementHandler::GetSetDefinitionID (elemHandle);
        return (0 == setElemId || INVALID_ELEMENTID == setElemId) ? AcDbText::desc() : AcDbAttributeDefinition::desc();
        }

    // cant find the target element? Create a plain old text element for it.
    // if the tagset exists but not in the dictionary model, it's a failure case which is handled by dropping to it text.
    ElementHandle       targetElement;
    if (BSISUCCESS != TagElementHandler::GetTargetElement(targetElement, elemHandle) || 
        NULL == context.GetFile()->FindByElementId(TagElementHandler::GetSetDefinitionID(elemHandle), true))
        return AcDbText::desc();

    SharedCellHandler*  scHandler;
    if (NULL != (scHandler = dynamic_cast<SharedCellHandler*> (&targetElement.GetHandler())))
        {
        /*-------------------------------------------------------------------------------------------------
        Prior to Vancouver, below code was only used when a new tag was added.  Updating existing attribute
        would have only gone through the FromElement method.  Now we've moved the code here for both adding
        and updating the attribute, we need to separate the logic correctly.  When updating existing attribute
        with a valid target, we want to just update the attribute.  Only when a new tag is added, the old 
        logic of adding all attributes for the same block reference kicks in.
        -------------------------------------------------------------------------------------------------*/
        if (NULL != existingObject && existingObject->isKindOf(AcDbAttribute::desc()))
            return  AcDbAttribute::desc ();

        DPoint3d        origin;
        RotMatrix       rMatrix;
        scHandler->GetTransformOrigin (targetElement, origin);
        scHandler->GetOrientation (targetElement, rMatrix);

        AcDbObjectId    targetObjectId;
        // If we're saving changes and the shared cell already exists, check in the attached tag here.
        // If it doesn't exist, it will get checked in when the shared cell is checked in.  If we don't
        // check it in here it may not get checked in at all.
        if ( !(targetObjectId = context.ExistingObjectIdFromElementId (targetElement.GetElementId())).isNull())
            {
            AcDbBlockReferencePointer pInsert (targetObjectId, AcDb::kForWrite);
            if (Acad::eOk == pInsert.openStatus())
                {
                Transform                   inverseTransform;
                context.SaveTagsAsAttributeDefinitions (pInsert->blockTableRecord(), context.ComputeInverseCellTransform (inverseTransform, origin, rMatrix), targetElement);
                context.SaveBlockTagsToDatabase (pInsert, targetElement);
                }
            }
        return NULL;
        }
    else if (NULL == dynamic_cast <NormalCellHeaderHandler*> (&targetElement.GetHandler()))
        {
        // If associated to a cell, we'll handle it while handling the cell.
        // Otherwise, make a text element out of it, if it is visible
        if (!pElm->dhdr.props.b.invisible && 0 == (pElm->flags & ATTR_FLAG_HIDEINSTANCE))
            return AcDbText::desc();
        }
    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    // First find out which kind of AcDbObject we want.
    AcRxClass*          typeNeeded = GetTypeNeeded (elemHandle, existingObject, context);

    if (NULL == typeNeeded)
        return DwgObjectChangesIgnored;

    acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);

    AcDbAttribute*          acAttribute;
    if (NULL != (acAttribute = AcDbAttribute::cast(acObject)))
        return SetAcDbAttributeFromTagElement (acAttribute, elemHandle, context);

    // We created an ATTDEF
    AcDbAttributeDefinition* acAttributeDefinition;
    if (NULL != (acAttributeDefinition = AcDbAttributeDefinition::cast (acObject)))
        return SetAcDbAttributeDefinitionFromTagElement (acAttributeDefinition, elemHandle, context);

    // AcDbText should be checked after ATTRIB and ATTDEF:
    AcDbText*   acText;
    if (NULL != (acText = AcDbText::cast (acObject)))
        return SetAcDbTextFromTagElement (acText, elemHandle, context);

    BeAssert (false && "Unsupported attribute type!");
    return NoConversionMethod;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbTextFromTagElement (AcDbText* acText, ElementHandleR elemHandle, ConvertFromDgnContextR context) const
    {
    EditElementHandle   textElement;
    StatusInt           status = TagElementHandler::ExtractTextElement (textElement, elemHandle, true, NULL);
    if (BSISUCCESS != status)
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    TextBlock           textBlock(textElement);
    return context.DTextFromTextBlock (acText, textBlock, textElement);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbAttributeDefinitionFromTagElement (AcDbAttributeDefinition* acAttributeDefinition, ElementHandleR elemHandle, ConvertFromDgnContextR context) const
    {
    StatusInt status = context.AttributeDefinitionFromElement (acAttributeDefinition, elemHandle);
    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbAttributeFromTagElement (AcDbAttribute* acAttribute, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    // Attributes attached to nested cells cannot be changed - furthermore if they are checked in,
    // they are going to be transformed incorrectly (they need to be in the coordinate system of the parent
    // TR# 147387
    bool                            nested = false;
    AcDbBlockTableRecordPointer     acBlock(acAttribute->blockId(), AcDb::kForRead);
    if (Acad::eOk == acBlock.openStatus())
        {
        nested = !acBlock->isLayout();
        acBlock.close ();
        }

    StatusInt   status = context.AttributeFromTagElement (acAttribute, inElement, nested);

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

};      // ToDwgExtTagElement


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          03/15
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtAttribute : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    // do nothing... should not be called anyway and should be processed from an AcDbBlockReference
    DIAGNOSTIC_PRINTF ("Unexpected call to ToDgnExtAttribute::ToElement from ID=%I64d.\n", context.ElementIdFromObject(acObject));
    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContextR context) const override
    {
    // this method is called if an mtext attribute contains fields
    EditElementHandle   textElement(context.ElementIdFromObject(acObject), context.GetModel());
    if (textElement.IsValid())
        return  ToDgnExtAttribute::ConvertFieldsForAttribute (textElement);

    return  MstnElementUnacceptable;
    }

public:    
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    ConvertFieldsForAttribute (EditElementHandleR textElement)
    {
    // Convert fields from dwg to dgn format
    if (TextField::ConvertFieldsFromDWG(textElement))
        {
        // rewrite the text element to cache - if it is in a cell, must replace the whole cell:
        StatusInt       status = BSISUCCESS;
        ElementRefP     replaceRef = textElement.GetElementRef ();
        ElementRefP     parentRef = nullptr == replaceRef ? nullptr : replaceRef->GetOutermostParent ();
        if (nullptr != parentRef && SHAREDCELL_DEF_ELM == parentRef->GetElementType())
            {
            EditElementHandle   scDef (parentRef);
            status = RealDwgUtil::ReplaceChildElementInCell (scDef, textElement);

            if (RealDwgSuccess == status)
                status = scDef.ReplaceInModel (parentRef);
            }
        else
            {
            status = textElement.ReplaceInModel (replaceRef);
            }
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
        }

    return RealDwgSuccess;
    }

};  // ToDgnExtAttribute


/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtAttributeDefinition : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbAttributeDefinition*    acAttributeDef = AcDbAttributeDefinition::cast (acObject);
    MSElementDescrP             pDescr = NULL;

    StatusInt   status;
    if (SUCCESS != (status = context.OrphanAttributesToElements (outElement, acAttributeDef)))
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContextR context) const override
    {
    // this method is called if an mtext attrdef contains fields
    EditElementHandle   textElement(context.ElementIdFromObject(acObject), context.GetModel());
    if (textElement.IsValid())
        return  ToDgnExtAttribute::ConvertFieldsForAttribute (textElement);

    return  MstnElementUnacceptable;
    }

};  // ToDgnExtAttributeDefinition


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Venkat.Kalyan                   07/2006
+---------------+---------------+---------------+---------------+---------------+------*/
AcString&                   StripEncoding (AcString& outString, AcString const& inString, TextParamWide* pTextParams, TextSizeParam* pTextSize)
    {
    // copy input to output.
    outString = inString;

    size_t      length = outString.length ();
    if (length < 3)
        return  outString;

    for (size_t iChar = 0; iChar < length - 2; iChar++)
        {
        const ACHAR*  pStr = outString.kwszPtr();

        bool    markupChanged = false;
        if ((CHAR_Percent == pStr[iChar]) && (CHAR_Percent == pStr[iChar+1]))
            {
            // if underline or overline is switched on we need to change style
            ACHAR final = pStr[iChar+2];

            // While AutoCAD can have multiple styles within a tag using the %%u, %%o, %%U, %%O markups,
            // MicroStation tags can have only one style per tag.  Hence, the presence of one of these
            // markups simply turns on the flag.
            switch (final)
                {
                case 'u':
                case 'U':
                    if (NULL != pTextParams && NULL != pTextSize)
                        {
                        pTextParams->flags.underline = true;
                        if (IS_TRUETYPE_FONTNUMBER (pTextParams->font))
                            pTextParams->underlineSpacing = pTextSize->size.height * TRUETYPE_UNDERLINEOFFSET;
                        else
                            pTextParams->underlineSpacing = pTextSize->size.height * SHX_UNDERLINEOFFSET;
                        }
                    markupChanged = true;
                    break;

                case 'o':
                case 'O':
                    if (NULL != pTextParams && NULL != pTextSize)
                        {
                        pTextParams->exFlags.overline = true;
                        if (IS_TRUETYPE_FONTNUMBER (pTextParams->font))
                            pTextParams->overlineSpacing = pTextSize->size.height * TRUETYPE_UNDERLINEOFFSET;
                        else
                            pTextParams->overlineSpacing = pTextSize->size.height * SHX_UNDERLINEOFFSET;
                        }
                    markupChanged = true;
                    break;
                }
            }
        if (markupChanged)
            {
            RealDwgUtil::DeleteCharsFromAcString (outString, outString, (UInt32)iChar, 3);

            length -= 3;
            if (length < 3)
                break;

            if (iChar > 0)
                iChar--;
            }
        }
    return outString;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            ExtractTagSpec
(
DgnTagSpec*                 pTagSpec,
AcString&                   tagString,
ConvertToDgnContextR        context
)
    {
    memset (pTagSpec, 0, sizeof(*pTagSpec));

    RealDwgUtil::AcStringToMSWChar (pTagSpec->tagName, tagString, TAG_NAME_MAX);

    pTagSpec->set.setName[0] = 0;
    pTagSpec->set.reportName[0] = 0;    // no report name
    pTagSpec->set.modelRef = context.GetModel();

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            ExtractTagValue (DgnTagValue& pTagValue, AcString const& tagString)
    {
    std::unique_ptr <WChar> wideStr(new WChar[tagString.length() + 1]);
    RealDwgUtil::AcStringToMSWChar (wideStr.get(), tagString, tagString.length() + 1);
    pTagValue.SetTagValue(wideStr.get());
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ExtractPropertyMaskFromDefinition
(
UInt16*                     pMask,
AcDbAttributeDefinition*    pDefinition
)
    {
    *pMask  = 0;

    if (pDefinition->isInvisible())
        *pMask |= TAG_PROP_DISPOFF;
    if (pDefinition->isConstant())
        *pMask |= TAG_PROP_CONST;
    if (pDefinition->isVerifiable())
        *pMask |= TAG_PROP_CONF;
    if (pDefinition->isPreset())
        *pMask |= TAG_PROP_DEF;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ExtractPropertyMaskFromAttribute
(
UInt16*                     pMask,
AcDbAttribute*              pAttribute
)
    {
    *pMask  = 0;

    if (pAttribute->isInvisible())
        *pMask |= TAG_PROP_DISPOFF;
    if (pAttribute->isConstant())
        *pMask |= TAG_PROP_CONST;
    if (pAttribute->isVerifiable())
        *pMask |= TAG_PROP_CONF;
    if (pAttribute->isPreset())
        *pMask |= TAG_PROP_DEF;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/06
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 GetGenerationFlagsFromTextstyle
(
bool*                       pIsBackwards,
bool*                       pIsUpsidedown,
AcDbText*                   pText
)
    {
    /*-----------------------------------------------------------------------------------
    An attribute entity does not use dxf group code 71 for text generation, instead it is
    controlled by group code 71 from its text style.  In AutoCAD 2007, the presence of
    dxf group 71 in an attribute is treated as an error and the DXF file can not open.
    Ironically the DXF document still states it as a legitimate dxf group code!
    -----------------------------------------------------------------------------------*/
    AcDbObjectId    textStyleId = pText->textStyle ();
    if (!textStyleId.isNull())
        {
        AcDbTextStyleTableRecordPointer  pDwgTextStyle (textStyleId, AcDb::kForRead);
        if (Acad::eOk != pDwgTextStyle.openStatus())
            {
            BeAssert (false && "Cannot open DWG teststyle!");
            return;
            }

        *pIsBackwards   = (0 != (pDwgTextStyle->flagBits() & 0x02));    // "second bit"
        *pIsUpsidedown  = (0 != (pDwgTextStyle->flagBits() & 0x04));    // "third bit"
        }
    /*-----------------------------------------------------------------------------------
    Although we do not expect dxf group code 71 to present in an attribute entity, we are
    not sure about that in old versions of DWG.  Particularly since it is still listed in
    AutoDesk's DXF document as a valid group code, we have to handle this possible
    scenario here.  That is, if the dxf group code ever presents, we still want to honor it.
    Unfortunately we do not have a way to test it.  ACAD would promptly report it as an
    error if we ever set a value for it in a DXF file.
    -----------------------------------------------------------------------------------*/
    if (pText->isMirroredInX())
        *pIsBackwards = true;
    if (pText->isMirroredInY())
        *pIsUpsidedown = true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ExtractAttributeTextParams
(
TextParamWide*              pTextParams,
TextSizeParam*              pTextSize,
DPoint3d*                   pOrigin,
RotMatrix*                  pRMatrix,
AcDbText*                   pText,
bool                        isAnnotative,
double                      annotationScale,
ConvertToDgnContextR        context
)
    {
    double  textHeight = pText->height ();
    if (isAnnotative && annotationScale > TOLERANCE_ZeroScale)
        textHeight /= annotationScale;

    memset (pTextSize, 0, sizeof(*pTextSize));
    memset (pTextParams, 0, sizeof(*pTextParams));

    context.GetDgnTextParamsFromDwg (pTextParams, pText->horizontalMode(), pText->verticalMode(), pText->oblique(), false /* Vertical */, pText->textStyle());
    context.GetDgnTextTransformFromDwg (pTextSize, pOrigin, pRMatrix, pText->normal(),
                                        (pTextParams->just == TextElementJustification::LeftBaseline) ? pText->position() : pText->alignmentPoint(), pText->rotation(), textHeight, pText->widthFactor());

    bool    isBackwards = false, isUpsidedown = false;
    GetGenerationFlagsFromTextstyle (&isBackwards, &isUpsidedown, pText);

    context.AdjustForBackwardsUpsideDownText (pTextSize, pRMatrix, isBackwards, isUpsidedown);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ExtractTagDefFromDefinition
(
DgnTagDefinitionR           tagdef,
AcDbAttributeDefinition*    pAttrdef,
UInt32                      id,
ConvertToDgnContextR        context
)
    {
    tagdef.id = id;

    ExtractPropertyMaskFromDefinition (&tagdef.propsMask, pAttrdef);

    AcString                textString;
    AcString                definitionString (pAttrdef->textStringConst());

    ExtractTagValue (tagdef.value, StripEncoding (textString, definitionString, NULL, NULL));

    RealDwgUtil::AcStringToMSWChar (tagdef.name, StripEncoding (textString, pAttrdef->tagConst(), NULL, NULL), TAG_NAME_MAX);
    RealDwgUtil::AcStringToMSWChar (tagdef.prompt, StripEncoding (textString, pAttrdef->promptConst(), NULL, NULL), TAG_PROMPT_MAX);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SetTagElementVisibility (EditElementHandleR outElement, bool isVisible)
    {
    AttributeElm*   tagElement = reinterpret_cast <AttributeElm*> (outElement.GetElementP());
    if (NULL == tagElement)
        return;

    if (isVisible)
        {
        tagElement->flags &= ~ATTR_FLAG_HIDEINSTANCE;
        tagElement->dhdr.props.b.invisible = false;
        }
    else
        {
        tagElement->dhdr.props.b.invisible = true;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            TagElementFromAttributeDefinition
(
EditElementHandleR          outTagElement,
AcDbAttributeDefinition*    pDefinition,
UInt32                      index,
ElementHandleCR             tagsetElement,
ElementId                   tagsetElementId,
bool                        validate,
ConvertToDgnContextR        context
)
    {
    DgnFileP                dgnFile = context.GetFile ();
    WChar                   tagsetName[TAG_SET_NAME_MAX] = { 0 };
    DgnTagDefinition        tagdef;
    StatusInt               status = BSIERROR;

    if (tagsetElement.IsValid())
        {
        // Get tag set name and tag def from input element handle - allow name truncation:
        status = TagSetHandler::GetSetName (tagsetName, _countof(tagsetName), tagsetElement);
        if ((BSISUCCESS != status && DGNHANDLERS_STATUS_ElementTooLarge != status) || 0 == tagsetName[0])
            return  CantCreateTag;

        status = TagSetHandler::ExtractTagDefByID (tagsetElement, tagdef, index);
        if (BSISUCCESS != status)
            return  CantCreateTag;
        }
    else
        {
        // Get tag set name from input element ID - allow name truncation:
        EditElementHandle   eeh;
        if (BSISUCCESS != (status = TagSetHandler::GetByID(eeh, tagsetElementId, *dgnFile)))
            return  CantCreateTag;

        status = TagSetHandler::GetSetName (tagsetName, _countof(tagsetName), eeh);
        if ((BSISUCCESS != status && DGNHANDLERS_STATUS_ElementTooLarge != status) || 0 == tagsetName[0])
            return  CantCreateTag;

        // get tag def from saved tagset or from an orphan attrdef
        if (SUCCESS != TagSetHandler::ExtractTagDefByID(eeh, tagdef, index))
            ExtractTagDefFromDefinition (tagdef, pDefinition, index, context);
        }
        
    // will set annotation scale for orphaned attrdef's in modelspace:
    double  annoScale = 1.0;
    bool    isAnnotative = context.GetDisplayedAnnotationScale (annoScale, pDefinition);

    DPoint3d            origin;
    RotMatrix           rMatrix;
    TextSizeParam       textSize;
    TextParamWide       textParams;

    // GetDgnTextTransformFromDwg may throw an invalid point exception
    try
        {
        ExtractAttributeTextParams (&textParams, &textSize, &origin, &rMatrix, pDefinition, isAnnotative, annoScale, context);

        AcString            textString;
        DgnTagValue         tagValue;
        ExtractTagValue (tagValue, StripEncoding (textString, pDefinition->textStringConst(), &textParams, &textSize));

        Int32               styleId = context.GetFileHolder().GetTextStyleIndex()->GetDgnId (pDefinition->textStyle().handle());
        DgnTextStylePtr     textStyle = DgnTextStyle::GetByID (styleId, *context.GetFile());
        if (!textStyle.IsValid())
            {
            BeAssert (false && "Unexpected NULL text style for attrdef");
            TextStyleCollection     styleList(*context.GetFile());
            textStyle = *styleList.begin ();
            }
        // override text style from text params
        DPoint2d            scale = DPoint2d::From (textSize.size.width, textSize.size.height);
        TextStyleP          styleData = textStyle->GetElementStyle ();
        textstyle_getTextStyleFromTextParamWide (styleData, &scale, &textParams, 0, *context.GetFile());
        // invert double values as now textstyle treats them as a ratio per text height
        textStyle->AssignLegacyStyleValues (*styleData, context.GetModel());

        ITagCreateDataPtr   tagData = ITagCreateData::Create (tagdef, *textStyle, tagsetName, tagsetElementId);
        if (tagData.IsNull())
            return  CantCreateTag;

        status = tagData->SetAttributeValue (tagValue);
        if (SUCCESS != status)
            DIAGNOSTIC_PRINTF ("Failed to set tag value from attrdef ID=%I64d\n", tagsetElementId);

        status = TagElementHandler::Create (outTagElement, NULL, *tagData, *context.GetModel(), context.GetThreeD(), origin, rMatrix, NULL, validate);
        if (SUCCESS == status)
            {
            context.ElementHeaderFromEntity (outTagElement, pDefinition);
            context.ApplyThickness (outTagElement, pDefinition->thickness(), pDefinition->normal(), false);

            // Reset visibility as the Create method can't set it without the target elementRef, but do so only if the entity itself is visible:
            if (!outTagElement.GetElementCP()->hdr.dhdr.props.b.invisible)
                SetTagElementVisibility (outTagElement, !pDefinition->isInvisible());

            if (isAnnotative)
                status = RealDwgUtil::SetElementAnnotative (outTagElement, context.GetModel(), &annoScale);
            }
        }

    catch (DiscardInvalidEntityException& invalidEntityError)
        {
        if (context.GetSettings().DiscardInvalidEntities())
            {
            RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_EntitiesOffDesignPlaneDiscarded, false, L"ATTDEF", L"AcDbAttributeDefinition");
            DIAGNOSTIC_PRINTF ("Discarding invalid attrdef: id=%I64d, %ls\n", RealDwgUtil::CastDBHandle(pDefinition->objectId().handle()), invalidEntityError.ErrorMessage());

            Acad::ErrorStatus   es;
            while (Acad::eHadMultipleReaders == (es = pDefinition->upgradeOpen()))
                pDefinition->close ();
            if (Acad::eOk == es)
                es = pDefinition->erase ();

            if (Acad::eOk != es)
                DIAGNOSTIC_PRINTF ("...failed to discard invalid attrdef! [%ls]\n", acadErrorStatusText(es));
            }
        else
            {
            RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_EntitiesOffDesignPlaneIgnored, false, L"ATTDEF", L"AcDbAttributeDefinition");
            DIAGNOSTIC_PRINTF ("Invalid attrdef: id=%I64d, %ls\n", RealDwgUtil::CastDBHandle(pDefinition->objectId().handle()), invalidEntityError.ErrorMessage());
            }
        status = BadData;
        }

    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception thrown from creating tags from attrdef\n");
        status = BadData;
        }
    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::TagElementFromAttribute
(
EditElementHandleR          outTagElement,
AcDbAttribute*              pAttribute,
DPoint3dP                   pOuterInsertOrigin,
ElementId                   targetId,
ElementId                   tagsetElementId,
int                         index,
bool                        targetAnnotative,
double                      annotationScale,
WCharCP                     pSetNameIn,
DgnTagDefinitionCP          pTagdefIn,
bool                        bMissingTagDef
)
    {
    DgnFileP                dgnFile = this->GetFile ();
    DgnTagDefinition        tagdef;
    WChar                   tagsetName[TAG_NAME_MAX] = { 0 };
    BentleyStatus           status;

    // if input tagdef and set name exist, use them; otherwise try getting them from input set id, name and index:
    if (NULL == pTagdefIn)
        {
        // Get tag set element
        EditElementHandle   tagsetElement;

        status = TagSetHandler::GetByID (tagsetElement, tagsetElementId, *dgnFile);
        if (SUCCESS != status)
            return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);

        // Get tag set name: use input name or extract it from the element
        if (NULL == pSetNameIn)
            status = TagSetHandler::GetSetName (tagsetName, _countof(tagsetName), tagsetElement);
        else
            wcscpy (tagsetName, pSetNameIn);
        // allow truncated tagset name - TFS 159865.
        if (BSISUCCESS != status && DGNHANDLERS_STATUS_ElementTooLarge != status && 0 != tagsetName[0])
            return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);

        // Get tag name from tag set by getting the tag def
        status = TagSetHandler::ExtractTagDefByID (tagsetElement, tagdef, index);
        if (SUCCESS != status)
            return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
        }
    else
        {
        if (NULL == pSetNameIn)
            return  CantCreateTag;

        memcpy (&tagdef, pTagdefIn, sizeof tagdef);
        wcscpy (tagsetName, pSetNameIn);
        }

    // Get position, orientation and text size
    DPoint3d            origin;
    RotMatrix           rMatrix;
    TextSizeParam       textSize;
    TextParamWide       textParams;
    ExtractAttributeTextParams (&textParams, &textSize, &origin, &rMatrix, pAttribute, targetAnnotative, annotationScale, *this);

    // Get tag value, as well text style overrides via markups
    DgnTagValue         tagValue;
    AcString            textString;
    ExtractTagValue (tagValue, StripEncoding(textString, pAttribute->textStringConst(), &textParams, &textSize));

    // Get text style from attribute
    Int32               styleId = this->GetFileHolder().GetTextStyleIndex()->GetDgnId (pAttribute->textStyle().handle());
    DgnTextStylePtr     textStyle = DgnTextStyle::GetByID (styleId, *dgnFile);
    if (!textStyle.IsValid())
        {
        BeAssert (false && "Unexpected NULL text style!");
        TextStyleCollection     styleList(*dgnFile);
        textStyle = *styleList.begin ();
        }

    // override text style from text params
    DPoint2d            scale = DPoint2d::From (textSize.size.width, textSize.size.height);
    TextStyleP          styleData = textStyle->GetElementStyle ();
    textstyle_getTextStyleFromTextParamWide (styleData, &scale, &textParams, 0, *dgnFile);
    // invert double values as now textstyle treats them as a ratio per text height
    textStyle->AssignLegacyStyleValues (*styleData, GetModel());

    // No we have enough to create the tag data interface:
    ITagCreateDataPtr   tagData = ITagCreateData::Create (tagdef, *textStyle, tagsetName, tagsetElementId);
    if (tagData.IsNull())
        return  CantCreateTag;

    DPoint3d            insertOrigin;
    if (NULL != pOuterInsertOrigin)
        {
        // attribute origin is relative to insert origin.
        insertOrigin = *pOuterInsertOrigin;
        origin.subtract (pOuterInsertOrigin);
        }
    else
        {
        insertOrigin.zero();
        }

    // Set tag value
    if (SUCCESS != tagData->SetAttributeValue(tagValue))
        DIAGNOSTIC_PRINTF ("Failed to set tag value from attrdef ID=%I64d\n", tagsetElementId);

    // set tag visibility if different than its definition
    bool                isDefVisible = 0 != (tagdef.propsMask & TAG_PROP_DISPOFF);
    bool                isTagVisible = Adesk::kTrue != pAttribute->isInvisible();

    // Now we can create a tag element
    status = TagElementHandler::Create (outTagElement, NULL, *tagData, *this->GetModel(), this->GetThreeD(), origin, rMatrix, NULL, false);
    if (SUCCESS == status)
        {
        this->ElementHeaderFromEntity (outTagElement, pAttribute);
        this->ApplyThickness (outTagElement, pAttribute->thickness(), pAttribute->normal(), false);

        TagElementHandler::SetAssociation (outTagElement, targetId, SHARED_CELL_ELM, &insertOrigin);
        TagElementHandler::SetOffset (outTagElement, origin);

        // Reset visibility as the Create method can't set it without the target elementRef, but do this only if the entity itself is visible:
        if (!outTagElement.GetElementCP()->hdr.dhdr.props.b.invisible)
            {
            if (bMissingTagDef)
                SetTagElementVisibility(outTagElement, isTagVisible);
            else
                {
                //ADO-896165: Found case where constant flag is different in AcDbAttribute and AcDbAttributeDefinition
                //ACAD LIST(cmd) says attr def is constant but ACAD property windows shows Attrdef constant is No
                //both the cases/properties considerd below
                bool isConst = pAttribute->isConstant() && 0 != (tagdef.propsMask & TAG_PROP_CONST);
                SetTagElementVisibility(outTagElement, isConst ? isDefVisible : isTagVisible);
                }
            }
            
        if (targetAnnotative)
            RealDwgUtil::SetElementAnnotative (outTagElement, m_model, &annotationScale);

        outTagElement.GetDisplayHandler()->ValidateElementRange (outTagElement, true);
        }

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    09/98
+---------------+---------------+---------------+---------------+---------------+------*/
static TransformP       GetBlockReferenceDisplayTransform
(
TransformP              pTransform,
AcDbBlockReference*     acBlockReference
)
    {
    RotMatrix           rMatrix;
    rMatrix.initFromAxisAndRotationAngle (2, acBlockReference->rotation());
    rMatrix.scaleColumns (&rMatrix, acBlockReference->scaleFactors().sx, acBlockReference->scaleFactors().sy, acBlockReference->scaleFactors().sz);

    Transform           extrusionTransform;
    if (NULL != RealDwgUtil::GetExtrusionTransform (extrusionTransform, acBlockReference->normal(), 0.0))
        {
        RotMatrix   extrusionMatrix;
        extrusionTransform.getMatrix (&extrusionMatrix);
        rMatrix.productOf (&extrusionMatrix, &rMatrix);                     // The Origin Returned by DwgDirect is in the world transform.
        }
    pTransform->setMatrix (&rMatrix);

    DPoint3d            position;
    pTransform->setTranslation (&RealDwgUtil::DPoint3dFromGePoint3d (position, acBlockReference->position()));

    AcDbObjectId            blockId;
    if (!(blockId = acBlockReference->blockTableRecord()).isNull())
        {
        AcDbBlockTableRecordPointer acBlock (blockId, AcDb::kForRead);
        if (Acad::eOk == acBlock.openStatus())
            {
            AcGePoint3d origin = acBlock->origin();
            pTransform->translateInLocalCoordinates (pTransform, origin.x, origin.y, origin.z);
            }
        }

    return pTransform;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 CreateTagSetNameWithId
(
WCharP                      pSetName,
const ACHAR*                baseName,
UInt64                      id
)
    {
    WChar     idString[1024], setName[1024];

    // an excessively long block name causes buffer over run, as a case seen in TFS# 88632 - truncate the name now
    wcsncpy (setName, baseName, TAG_SET_NAME_MAX);
    swprintf (idString, L"_%I64d", id);

    setName[TAG_SET_NAME_MAX - 1 - wcslen (idString)] = '\0';       // Truncate name if necessary.
    wcscat (setName, idString);

    wcscpy (pSetName, setName);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SetTextnodeForAttributeRoundtrip (EditElementHandleR textnode, ElementId targetId, WCharCP attributeTag)
    {
    /*--------------------------------------------------------------------------------------------------------
    Neither a tag nor a text field supports multiline text, therefore we have to drop a multiline attribute to 
    a textnode.  Add an assoc point to help us round trip it back to multiline attribute.  If the textnode is 
    changed by a user, the assoc point should be broken and we will not attempt to create attribute from it.  
    In this case we will simply create an mtext from the text node.  We intentially use assoc point to alert
    the user from attempting to edit the text node thinking it to be a normal text node, but ending up to see
    it turned out as an attribute when saved as DWG.
    --------------------------------------------------------------------------------------------------------*/
    AssocPoint      assocPoint;
    AssociativePoint::InitOrigin (assocPoint, NULL);

    if (BSISUCCESS == AssociativePoint::SetRoot(assocPoint, targetId, 0, 0))
        AssociativePoint::InsertPoint (textnode, assocPoint, 0, 1);

    // temporarily save the tag name as a string linkage until we support multiline tag
    if (nullptr != attributeTag && 0 != attributeTag[0])
        RealDwgUtil::AppendStringLinkage (textnode, STRING_LINKAGE_KEY_Name, attributeTag);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     DropMultilineAttributeToTextnode (MSElementDescrH elemChain, AcDbAttribute* attribute, ElementId targetElementId, ConvertToDgnContextR context)
    {
    /*-------------------------------------------------------------------------------------------------------------
    This is only a workaround for lack of multiline text capability in our current tag sub-system, which is expected
    to be eventually re-worked out to support mtext.  When we have mtext for tags, we will not need this code.
    -------------------------------------------------------------------------------------------------------------*/
    AcDbMText*  mtext = attribute->getMTextAttribute ();
    if (NULL == mtext)
        return  false;

    ACHAR*      string = mtext->contents ();
    if (NULL == string)
        {
        delete mtext;
        return  false;
        }

    bool        needDrop = RealDwgUtil::IsLikelyMText (string);

    free (string);

    if (!needDrop)
        {
        double  boxHeight = mtext->actualHeight ();
        if (boxHeight > 1.5 * mtext->textHeight())
            needDrop = true;
        }

    if (needDrop)
        {
        EditElementHandle   textnode;
        ToDgnExtMText       mtextToDgn;

        needDrop = false;

        // create a text node element from the mtext
        if (RealDwgSuccess == mtextToDgn.ConvertMTextToTextnode(textnode, mtext, context))
            {
            // add an assoc point to the textnode so we can roundtrip it as multiline attribute.
            SetTextnodeForAttributeRoundtrip (textnode, targetElementId, attribute->tagConst());

            MSElementDescrP nodeElmdscr = textnode.ExtractElementDescr ();
            if (NULL != nodeElmdscr)
                {
                nodeElmdscr->el.ehdr.uniqueId = context.ElementIdFromObject (attribute);
                if (NULL == *elemChain)
                    *elemChain = nodeElmdscr;
                else
                    (*elemChain)->AddToChain (nodeElmdscr);

                needDrop = true;
                }
            }
        }

    delete mtext;

    return  needDrop;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            TagElementsFromBlockReference
(
MSElementDescrH             ppAttrSetDefDescr,
MSElementDescrH             ppAttrDescr,
AcDbBlockReference*         acBlockReference,
DPoint3dP                   pOuterOrigin,
ConvertToDgnContextR        context
)
    {
    AcDbObjectId                blockId;
    if ((blockId = acBlockReference->blockTableRecord()).isNull())
        return BSIERROR;

    AcDbBlockTableRecordPointer acBlock (blockId, AcDb::kForRead);
    if (Acad::eOk != acBlock.openStatus())
        return BSIERROR;

    AcDbObjectIdArray               definitions;
    bvector <DgnTagDefinition>      missingTagDefs;
    ElementId                       missingSetDefId = 0;
    WChar                           missingSetName[1024];

    AcDbObjectIterator*             attributeIterator = acBlockReference->attributeIterator();

    if (attributeIterator->done())
        {
        delete attributeIterator;
        return SUCCESS;         // No attributes.
        }

    ElementId attrSetDefId = context.GetFileHolder().GetTagSetDefinitionId (blockId);

    // will turn on annotation scale if the block reference is annotative:
    double  annoScale = 1.0;
    bool    isAnnotative = context.GetDisplayedAnnotationScale (annoScale, acBlockReference);

    AcDbBlockTableRecordIterator    *blockIterator;
    if (Acad::eOk != acBlock->newIterator (blockIterator))
        return BSIERROR;

    for ( ; !blockIterator->done(); blockIterator->step())
        {
        AcDbObjectId        entityId;
        if (Acad::eOk != blockIterator->getEntityId (entityId))
            continue;

        AcDbEntityPointer   pEntity (entityId, AcDb::kForRead);
        if (Acad::eOk != pEntity.openStatus())
            continue;

        if (pEntity->isKindOf (AcDbAttributeDefinition::desc()))
            definitions.append (pEntity->objectId());
        }

    ElementId       targetElementId = context.ElementIdFromObject (acBlockReference);
    bool            blockVisible = acBlockReference->visibility() == AcDb::kVisible;
    const ACHAR*    blockName;
    acBlock->getName (blockName);
    CreateTagSetNameWithId (missingSetName, blockName, targetElementId);
    for (; !attributeIterator->done(); attributeIterator->step())
        {
        try
            {
            AcDbEntityPointer   pEntity (attributeIterator->objectId(), AcDb::kForRead);
            AcDbAttribute*      pAttribute;
            if ( (Acad::eOk != pEntity.openStatus()) || (NULL == (pAttribute = AcDbAttribute::cast (pEntity))) )
                continue;

            bool                visible = blockVisible && pEntity->visibility() == AcDb::kVisible;

            // MicroStation does not have multiline tags - drop them to text nodes
            if (visible && pAttribute->isMTextAttribute() && DropMultilineAttributeToTextnode(ppAttrDescr, pAttribute, targetElementId, context))
                continue;

            AcGeVector3d    norm = pAttribute->normal ();

            const ACHAR*            attributeTag = pAttribute->tagConst();
            MSElementDescrP         tempDescr = NULL;

            int iDef;
            for (iDef = 0; iDef < definitions.length(); iDef++)
                {
                if (!definitions[iDef].isNull())
                    {
                    AcDbAttributeDefinitionPointer pDefinition (definitions[iDef], AcDb::kForRead);
                    if (Acad::eOk != pDefinition.openStatus())
                        continue;

                    const ACHAR*    tag = pDefinition->tagConst();

                    if (0 == wcscmp (attributeTag, tag))
                        {
                        definitions[iDef].setNull();
                        EditElementHandle   tagElement;
                        if (RealDwgSuccess == context.TagElementFromAttribute(tagElement, pAttribute, pOuterOrigin, targetElementId, attrSetDefId, iDef + 1, isAnnotative, annoScale))
                            tempDescr = tagElement.ExtractElementDescr ();
                        break;
                        }
                    }
                context.ReportProgress ();
                }

            if (iDef == definitions.length())
                {
                // Definition missing.
                DgnTagDefinition      tagDef;

                if (0 == missingSetDefId)
                    missingSetDefId = context.GetAndIncrementNextId();

                memset (&tagDef, 0, sizeof(tagDef));

                tagDef.id = (int) missingTagDefs.size() + 1;
                tagDef.value.SetTagType(MS_TAGTYPE_CHAR);
                RealDwgUtil::AcStringToMSWChar (tagDef.name, pAttribute->tagConst(), TAG_NAME_MAX);

                missingTagDefs.push_back (tagDef);

                EditElementHandle   tagElement;
                if (RealDwgSuccess == context.TagElementFromAttribute(tagElement, pAttribute, pOuterOrigin, targetElementId, missingSetDefId, tagDef.id, isAnnotative, annoScale, missingSetName, &tagDef, true))
                    tempDescr = tagElement.ExtractElementDescr ();
                }
            if (NULL != tempDescr)
                {
                // apply entity visibility - most likely by block reference's visibility - TFS#74775.
                if (!visible && !tempDescr->el.hdr.dhdr.props.b.invisible)
                    tempDescr->el.hdr.dhdr.props.b.invisible = true;

                if (NULL == *ppAttrDescr)
                    *ppAttrDescr = tempDescr;
                else
                    (*ppAttrDescr)->AddToChain (tempDescr);
                }
            }

        catch (...)
            {
            DIAGNOSTIC_PRINTF ("Exception caught extracting element from tag\n");
            }
        }

    // create tag sets for missing attributes
    if (0 != missingTagDefs.size())
        {
        EditElementHandle   tagsetElement;
        if (SUCCESS == TagSetHandler::Create(tagsetElement, &missingTagDefs.front(), (int)missingTagDefs.size(), missingSetName, NULL, false, *context.GetFile(), 0))
            {
            *ppAttrSetDefDescr = tagsetElement.ExtractElementDescr ();
            (*ppAttrSetDefDescr)->el.ehdr.uniqueId = missingSetDefId;
            }
        }

    delete attributeIterator;
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    BrienBastings   02/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::BlockReferenceTagsToElements
(
MSElementDescrH             ppDescr,
MSElementDescrH             ppTailDescr,
AcDbBlockReference*         acBlockReference,
AcDbBlockReference*         pOutermostBlockReference,
DPoint3d*                   pOuterOrigin
)
    {
    MSElementDescr      *attrSetDefEdP = NULL, *attrEdP = NULL;

    if (SUCCESS == TagElementsFromBlockReference (&attrSetDefEdP, &attrEdP, acBlockReference, pOuterOrigin, *this))
        {
        if (NULL != attrSetDefEdP)
            MSElementDescr::InitOrAddToChainWithTail (ppDescr, ppTailDescr, attrSetDefEdP);

        if (NULL != attrEdP)
            {
#ifdef ALLOW_NESTINGTAGS
            /* Tags do not support nesting...reassociate to outermost sc */
            if (acBlockReference != pOutermostBlockReference)
                {
                mdlTag_addDescrToCell (&attrEdP, this->GetModel (), this->ElementIdFromObject (pOutermostBlockReference), NULL, false);
                mdlTag_setFromNestedSharedCell (&attrEdP, true, this->ElementIdFromObject (acBlockReference));
                }
#endif

            MSElementDescr::InitOrAddToChainWithTail (ppDescr, ppTailDescr, attrEdP);
            }
        }

#ifdef ALLOW_NESTINGTAGS
    AcDbObjectId                    blockId;
    if ((blockId = acBlockReference->blockTableRecord()).isNull())
        return SUCCESS;

    AcDbBlockTableRecordPointer     acBlock (blockId, AcDb::kForRead);
    if (Acad::eOk != acBlock.openStatus())
        return SUCCESS;

    AcDbBlockTableRecordIterator    *blockIterator;
    if (Acad::eOk != acBlock->newIterator (blockIterator))
        return SUCCESS;

    Transform       currentToDGN, currentFromDGN, currentLocal;
    bool            pushTransform = false;
    Transform       insertTransform;

    if (pushTransform = (NULL != GetBlockReferenceDisplayTransform (&insertTransform, acBlockReference)))
        {
        insertTransform.inverseOf (&insertTransform);
        this->PushTransform (&currentToDGN, &currentFromDGN, &currentLocal, &insertTransform);
        }

    for (; !blockIterator->done(); blockIterator->step())
        {
        AcDbObjectId    entityId;
        if (Acad::eOk != blockIterator->getEntityId (entityId))
            continue;

        AcDbEntityPointer   pEntity (entityId, AcDb::kForRead);
        if (Acad::eOk != pEntity.openStatus())
            continue;

        AcDbBlockReference* acBlockReference;
        if (NULL != (acBlockReference = AcDbBlockReference::cast (pEntity)))
            {
            if (acBlockReference->blockTableRecord() != blockId)
                this->BlockReferenceTagsToElements (ppDescr, ppTailDescr, acBlockReference, pOutermostBlockReference, pOuterOrigin);
            }
        }

    if (pushTransform)
        this->PopTransform (&currentToDGN, &currentFromDGN, &currentLocal);

    delete blockIterator;
#endif  // ALLOW_NESTINGTAGS

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SaveBlockAttrdefsToDgn
(
MSElementDescrH             tagsInCelldef,
AcDbBlockTableRecord*       acBlock
)
    {
    WChar         wSetName[1024];
    const ACHAR*    blockName;
    acBlock->getName (blockName);
    if (acBlock->isAnonymous () || (wcslen (blockName) > TAG_SET_NAME_MAX))
        CreateTagSetNameWithId (wSetName, blockName, acBlock->objectId ().handle ());
    else
        RealDwgUtil::AcStringToMSWChar (wSetName, blockName, TAG_SET_NAME_MAX);

#ifdef REALDWG_DIAGNOSTICS
    wprintf (L"Processing block named %s ...\n", wSetName);
#endif

    if (! acBlock->hasAttributeDefinitions())
        return  RealDwgIgnoreEntity;

    AcDbBlockTableRecordIterator    *pIterator;
    if (Acad::eOk != acBlock->newIterator (pIterator))
        return  BlockIteratorFailure;

    bvector<DgnTagDefinition>   tagdefArray;
    UInt32                      iCount = 0;
    for (pIterator->start(); !pIterator->done(); pIterator->step())
        {
        AcDbObjectId            entityId;
        if (Acad::eOk != pIterator->getEntityId (entityId))
            continue;

        AcDbEntityPointer           pEntity (entityId, AcDb::kForRead);
        AcDbAttributeDefinition*    pAttDef = NULL;
        if ( (Acad::eOk == pEntity.openStatus()) && (NULL != (pAttDef = AcDbAttributeDefinition::cast (pEntity))) )
            {
            DgnTagDefinition        tagdef;
            ExtractTagDefFromDefinition (tagdef, pAttDef, ++iCount, *this);
            tagdefArray.push_back (tagdef);
            }
        }

    // in case a bogus flag hasAttributeDefinitions has crept in a block(TFS 7607), stop here:
    if (iCount < 1)
        return  RealDwgSuccess;

    EditElementHandle   tagsetElement;
    StatusInt           status = TagSetHandler::Create (tagsetElement, &tagdefArray.front(), iCount, wSetName, NULL, false, *this->GetFile(), 0);

    if (SUCCESS == status)
        {
        ElementId       definitionId;

        if (0 == (definitionId = m_pFileHolder->GetTagSetDefinitionId (acBlock->objectId())))
            m_pFileHolder->SetTagSetDefinitionId (acBlock->objectId(), definitionId = this->GetAndIncrementNextId());

        tagsetElement.GetElementDescrP()->el.ehdr.uniqueId = definitionId;

        // Save the type 39 element to cache, as tag creation code depends on it.
        this->LoadElementIntoCache (tagsetElement);

        // create instances for the attr defs to add to the shared cell def
        MSElementDescrP headElmdscr = NULL, tailElmdscr = NULL;
        for (iCount = 0, pIterator->start(); !pIterator->done(); pIterator->step())
            {
            AcDbObjectId                entityId;
            if (Acad::eOk != pIterator->getEntityId (entityId))
                continue;

            AcDbAttributeDefinitionPointer  pAttDef(entityId, AcDb::kForRead);
            auto openStat = pAttDef.openStatus ();
            if (Acad::eOk == openStat)
                {
                EditElementHandle       tagElement;
                bool                    visible = Adesk::kTrue == pAttDef->isConstant ();
                if (SUCCESS == TagElementFromAttributeDefinition (tagElement, pAttDef, ++iCount, tagsetElement, definitionId, visible, *this))
                    {
                    MSElementDescrP     tagElmdscr = tagElement.ExtractElementDescr();
                    // hide these non-functional tags, which are only meant to represent attrdefs.
                    if (!visible)
                        tagElmdscr->el.hdr.dhdr.props.b.invisible = true;

                    MSElementDescr::InitOrAddToChainWithTail (&headElmdscr, &tailElmdscr, tagElmdscr);
                    }
                else
                    {
                    status = ERROR;
                    WString message;
                    WString::Sprintf (message, L"Failed get TagElementFromAttributeDefinition of block named %s, error code is %d, name is %s\n", wSetName, openStat, entityId.objectClass () ? entityId.objectClass ()->name () : L"anonymous");
                    _DiagnosticPrint (message);
                    }
                }
            this->ReportProgress ();
            }
        if (NULL != headElmdscr && NULL != tagsInCelldef)
            *tagsInCelldef = headElmdscr;
        }
    else
        {
        status = ERROR;
        WString message;
        WString::Sprintf (message, L"Failed to create TagSetHandler named %s\n", wSetName);
        _DiagnosticPrint (message);
        }
    delete pIterator;

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* Convenience function, parse the name root out of the full path
* @bsimethod                                                    JoshSchifter    07/01
+---------------+---------------+---------------+---------------+---------------+------*/
static void     dgnFileObj_getFileNameRoot
(
DgnFileP        dgnFileObj,
WCharP          pNameRoot,
int             numChars
)
    {
    WString     name;
    BeFileName::ParseName (NULL, NULL, &name, NULL, dgnFileObj->GetFileName().GetWCharCP());
    RealDwgUtil::TerminatedStringCopy (pNameRoot, name.GetWCharCP(), numChars);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::ExtractOrphanTagDefinition
(
OrphanTagdefBlock*          pCurrTagsetBlock,
AcDbAttributeDefinition*    pDefinition
)
    {
    StatusInt               status = SUCCESS;
    DgnTagDefinition        tagdef;
    ExtractTagDefFromDefinition (tagdef, pDefinition, ++pCurrTagsetBlock->m_nextTagId, *this);

    pCurrTagsetBlock->m_tagDefs.push_back (tagdef);

    if (0 == pCurrTagsetBlock->m_tagDefElementId)
        {
        DgnFileP    pFile = this->GetFile();

        if (pFile->GetDefaultModelId() == this->m_pModelIndexItem->GetId())
            dgnFileObj_getFileNameRoot (pFile, pCurrTagsetBlock->m_tagSetName, TAG_SET_NAME_MAX);
        else
            RealDwgUtil::TerminatedStringCopy (pCurrTagsetBlock->m_tagSetName, this->m_pModelIndexItem->GetModelName(), TAG_SET_NAME_MAX);

        // safe guard tag API's from accessing string array beyond its bounds:
        pCurrTagsetBlock->m_tagSetName[TAG_SET_NAME_MAX-1] = 0;

        if (NULL != m_orphanTagsetBlockList)
            {
            WChar suffix[TAG_SET_NAME_MAX];
            swprintf (suffix, L"_%d", (int)m_orphanTagsetBlockList->size());

            // may have to truncate tagset name to append a suffix:
            size_t  nameLength = wcslen (pCurrTagsetBlock->m_tagSetName);
            size_t  suffixLength = wcslen (suffix);
            if (nameLength + suffixLength >= TAG_SET_NAME_MAX)
                {
                nameLength = TAG_SET_NAME_MAX - suffixLength;

                if (nameLength > 0)
                    pCurrTagsetBlock->m_tagSetName[nameLength - 1] = 0;
                else
                    return  BSIERROR;  // should never happen!
                }

            wcscat (pCurrTagsetBlock->m_tagSetName, suffix);
            }

        pCurrTagsetBlock->m_tagDefElementId = this->GetAndIncrementNextId();

        EditElementHandle   tagsetElement;
        status = TagSetHandler::Create (tagsetElement, &pCurrTagsetBlock->m_tagDefs.front(), (int)pCurrTagsetBlock->m_tagDefs.size(), pCurrTagsetBlock->m_tagSetName, NULL, false, *pFile);
        if (SUCCESS == status)
            {
            tagsetElement.GetElementP()->ehdr.uniqueId = pCurrTagsetBlock->m_tagDefElementId;
            this->LoadElementIntoCache (tagsetElement);
            /*---------------------------------------------------------------------------
            Now the new orphan attrdef has been loaded into the non-model section cache. 
            If this attrdef is in a paperspace model, and non-model cache has already been
            filled, we need to tell TagSetHandler to insert this new set.  This will allow 
            the tag to find the set by name and display.  TR 289322.

            On Vancouver, however, it appears this code is unecessary. The OnElementLoaded
            is not implemented on TagSetHandler anyway.  Brien thinks the tagset name list
            that used to be kept in DgnFile is no longer needed for Vancouver.

            if (this->GetModel()->IsFilled (DgnModelSections::Dictionary))
                {
                AcDbBlockTableRecordPointer attdefOwner(pDefinition->ownerId(), AcDb::kForRead);
                if (Acad::eOk == attdefOwner.openStatus() && attdefOwner->isLayout())
                    tagsetElement.GetHandler().OnElementLoaded (tagsetElement);
                }
            ---------------------------------------------------------------------------*/
            }

        // create the new block list for the 1st time:
        if (NULL == m_orphanTagsetBlockList)
            m_orphanTagsetBlockList = new T_OrphanTagsetBlockList ();

        // add current block into the list.
        m_orphanTagsetBlockList->push_back (pCurrTagsetBlock);
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
OrphanTagdefBlock::OrphanTagdefBlock ()
    {
    m_nextTagId         = 0;
    m_tagDefElementId   = 0;
    m_tagSetName[0]     = 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::OrphanAttributesToElements
(
EditElementHandleR          outElement,
AcDbAttributeDefinition*    pDefinition
)
    {
    OrphanTagdefBlock       *pCurrTagsetBlock = NULL;

    // start a new block for the 1st time or current block too big; otherwise use current block.
    if (NULL == m_orphanTagsetBlockList)
        {
        pCurrTagsetBlock = new OrphanTagdefBlock();
        }
    else
        {
        OrphanTagdefBlock   *pLastTagsetBlock = m_orphanTagsetBlockList->back ();

        if (pLastTagsetBlock->m_nextTagId > this->GetSettings().GetMaxOrphanTagsPerSet())
            pCurrTagsetBlock = new OrphanTagdefBlock ();
        else
            pCurrTagsetBlock = pLastTagsetBlock;
        }

    bool            visible = AcDb::kVisible == pDefinition->visibility ();
    StatusInt       status;
    if (SUCCESS == (status = this->ExtractOrphanTagDefinition (pCurrTagsetBlock, pDefinition)))
        {
        status = TagElementFromAttributeDefinition (outElement, pDefinition, pCurrTagsetBlock->m_nextTagId, EditElementHandle(), pCurrTagsetBlock->m_tagDefElementId, visible, *this);
        // do not hide orphan tags per attribute display control
        if (BSISUCCESS == status && visible)
            {
            AttributeElm* tagElem = reinterpret_cast<AttributeElm*> (outElement.GetElementP());
            if (NULL != tagElem)
                {
                tagElem->dhdr.props.b.invisible = FALSE;
                tagElem->flags &= ~ATTR_FLAG_HIDEINSTANCE;
                }
            }
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           08/01
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId                   ConvertToDgnContext::AddEngineeringLinkTagsetDef ()
    {
    // build 2 tag set defs:
    DgnTagDefinition        tagdefs[2];

    memset (tagdefs, 0, sizeof tagdefs);

    tagdefs[0].value.SetTagType(MS_TAGTYPE_CHAR);
    tagdefs[1].value.SetTagType(MS_TAGTYPE_CHAR);

    tagdefs[0].propsMask  = tagdefs[1].propsMask  = TAG_PROP_DISPOFF;
    tagdefs[0].prompt[0]  = tagdefs[1].prompt[0]  = 0;
    tagdefs[0].id = 1;
    tagdefs[1].id = 2;
    tagdefs[0].value.SetTagSize((UInt16) (2 * wcslen(NAME_TAG_TITLE) + 2));
    tagdefs[1].value.SetTagSize((UInt16) (2 * wcslen(NAME_TAG_URL) + 2));
    wcscpy (tagdefs[0].name, NAME_TAG_TITLE);
    wcscpy (tagdefs[1].name, NAME_TAG_URL);


    EditElementHandle       tagsetElement;
    if (SUCCESS == TagSetHandler::Create(tagsetElement, tagdefs, 2, NAME_TAGSET_INTERNET, NULL, false, *m_dgnFile))
        {
        MSElementP          setdefElem = tagsetElement.GetElementP ();
        setdefElem->ehdr.uniqueId = this->GetAndIncrementNextId ();

        // save to file
        if (BSISUCCESS == tagsetElement.AddToModel())
            return  setdefElem->ehdr.uniqueId;
        }

    return  INVALID_ELEMENTID;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           08/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::AddStringTagElement
(
ElementHandleCR             targetElement,
WCharCP                     pTagName,
AcString const&             stringValue,
ElementId                   setdefId,
int                         tagIndex
)
    {
    DPoint3d    insertPoint;
    StatusInt   status = TagElementHandler::GetAssociativeOriginForTarget (&insertPoint, targetElement, this->GetModel());
    if (SUCCESS != status)
        return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);

    // Use the 1st text style in DGN textstyle collection:
    TextStyleCollection     styleList(*m_dgnFile);
    DgnTextStylePtr         textStyle = *styleList.begin ();
    ITagCreateDataPtr       tagData = ITagCreateData::Create (pTagName, NAME_TAGSET_INTERNET, *textStyle, *m_dgnFile);
    if (tagData.IsNull())
        return  CantCreateTag;

    // hide the tag
    tagData->SetTagVisibility (true);

    DgnTagValue             tagValue;
    ExtractTagValue (tagValue, stringValue);

    // Set tag value
    if (SUCCESS != tagData->SetAttributeValue(tagValue))
        DIAGNOSTIC_PRINTF ("Failed to set tag value from hyperlink=%ls\n", stringValue.kwszPtr());

    RotMatrix               matrix;
    matrix.InitIdentity ();

    EditElementHandle       tagElement;
    status = TagElementHandler::Create (tagElement, NULL, *tagData, *m_model, m_threeD, insertPoint, matrix, targetElement.GetElementRef());
    if (SUCCESS == status)
        {
        TagElementHandler::SetAssociation (tagElement, targetElement.GetElementId(), targetElement.GetElementType(), &insertPoint);

        MSElementP          tagElem = tagElement.GetElementP ();
        tagElem->ehdr.uniqueId = this->GetAndIncrementNextId ();

        // save to file
        status = tagElement.AddToModel ();
        }

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           08/01
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::AddEngineeringLink
(
ElementHandleCR             targetElement,
AcString const&             pTitle,
AcString const&             pUrl
)
    {
    ElementId               tagsetId = INVALID_ELEMENTID;

    // get tag set element ID, or create a new tag set def if not found
    EditElementHandle       tagsetElement;
    if (SUCCESS != TagSetHandler::GetByName(tagsetElement, NAME_TAGSET_INTERNET, *m_dgnFile))
        tagsetId = this->AddEngineeringLinkTagsetDef ();
    else if (tagsetElement.IsValid())
        tagsetId = tagsetElement.GetElementId ();

    if (0 == tagsetId || INVALID_ELEMENTID == tagsetId)
        return  BSIERROR;

    // create a tag element for title, which is optional:
    this->AddStringTagElement (targetElement, NAME_TAG_TITLE, pTitle, tagsetId, 1);

    // create URL, which is required:
    return this->AddStringTagElement (targetElement, NAME_TAG_URL, pUrl, tagsetId, 2);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::GetAndAppendTagsToCellInstance
(
EditElementHandleR          cellHandle,
AcDbBlockReference*         blockReference,
bool                        isMInsert
)
    {
    if (!cellHandle.IsValid())
        return;

    DPoint3d                origin;
    RealDwgUtil::DPoint3dFromGePoint3d (origin, blockReference->position());
    this->GetTransformToDGN().Multiply (origin);

    MSElementDescrP         tagsElmdscr = NULL;
    MSElementDescrP         tailElmdscr = NULL;
    if (SUCCESS == this->BlockReferenceTagsToElements(&tagsElmdscr, &tailElmdscr, blockReference, blockReference, &origin) && NULL != tagsElmdscr)
        {
        if (!isMInsert)
            {
            // for a single insert entity, we are done - just add tags to the chain!
            cellHandle.GetElementDescrP()->AddToChain (tagsElmdscr);

            // put the cell + tags in a graphic group:
            this->SetElementsInNextGraphicGroup (cellHandle);

            return;
            }

        // for an minsert entity, add the tags to the first child cell and make copies for rest of the children:
        MSElementDescrP     newTagsElmdscr = nullptr;
        UInt32              cellCount = 0;
        for (ChildEditElemIter childCell(cellHandle, ExposeChildrenReason::Count); childCell.IsValid(); childCell = childCell.ToNext())
            {
            DPoint3d        childOrigin;
            if (BSISUCCESS != CellUtil::ExtractOrigin(childOrigin, childCell))
                continue;

            DPoint3d        childOffset = childOrigin;
            childOffset.Subtract (origin);

            Transform       childTransform = Transform::FromIdentity ();
            childTransform.SetTranslation (childOffset);

            TransformInfo   moveText (childTransform);

            ElementId       newCellId = this->GetAndIncrementNextId ();
            childCell.GetElementP()->ehdr.uniqueId = newCellId;

            MSElementDescrP     workTagsElmdscr = nullptr;
            if (0 == cellCount)
                workTagsElmdscr = tagsElmdscr;
            else if (BSISUCCESS != tagsElmdscr->Duplicate(&workTagsElmdscr, true, false))
                break;  // shouldn't happen

            // re-associate the target of the tags to this child cell:
            UInt32              tagCount = 0;
            for (MSElementDescrP tagElm = workTagsElmdscr; nullptr != tagElm; tagElm = tagElm->h.next)
                {
                MSElementDescrP     prev = tagElm->h.previous;
                MSElementDescrP     next = tagElm->h.next;
                //Loop is to process ATTRIBUTE_ELM element type, skipping DGNSTORE_HDR element from processing it leads to crash. ADO#915175 
                if (DGNSTORE_HDR == tagElm->el.ehdr.type)
                {
                    continue;
                }

                tagElm->h.previous = tagElm->h.next = nullptr;

                if (cellCount > 0)
                    tagElm->el.ehdr.uniqueId = this->GetAndIncrementNextId ();

                // For a tag, re-associate it to the child shared cell.  For a dropped text, move it by the child shared cell.
                EditElementHandle   tagEeh (tagElm, true, false);
                if (ATTRIBUTE_ELM == tagElm->el.ehdr.type)
                    TagElementHandler::SetAssociation (tagEeh, newCellId, SHARED_CELL_ELM, &childOrigin);
                else if (0.0 != childOffset.x || 0.0 != childOffset.y || 0.0 != childOffset.z)
                    tagEeh.GetHandler(MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (tagEeh, moveText);

                tagEeh.GetDisplayHandler()->ValidateElementRange (tagEeh, true);

                tagElm = tagEeh.ExtractElementDescr ();

                // update the links
                if (nullptr != prev)
                    {
                    prev->h.next = tagElm;
                    tagElm->h.previous = prev;
                    }
                if (nullptr != next)
                    {
                    tagElm->h.next = next;
                    next->h.previous = tagElm;
                    }

                if (0 == tagCount++)
                    workTagsElmdscr = tagElm;
                }

            if (0 == cellCount++)
                {
                // save off the first/original set of tag elements attached to the first child cell:
                tagsElmdscr = workTagsElmdscr;
                }
            else
                {
                // save off newly created/copied tag elements attached to rest of the child cells:
                if (nullptr == newTagsElmdscr)
                    newTagsElmdscr = workTagsElmdscr;
                else
                    newTagsElmdscr->AddToChain (workTagsElmdscr);
                }
            }

        // append the first/original tag list as the children of the minsert cell header:
        tailElmdscr = nullptr;
        cellHandle.GetElementDescrP()->AppendChild (&tailElmdscr, tagsElmdscr);
        // append the new tags after the first
        if (nullptr != newTagsElmdscr)
            cellHandle.GetElementDescrP()->AppendChild (&tailElmdscr, newTagsElmdscr);

        // put the cell + tags in a graphic group:
        this->SetElementsInNextGraphicGroup (cellHandle);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::SetElementsInNextGraphicGroup (EditElementHandleR eeh)
    {
    if (this->GetSettings().GraphicGroupAttributes())
        {
        UInt32  graphicGroup = m_nextGraphicGroup++;
        if (0 == graphicGroup)
            graphicGroup = m_nextGraphicGroup++;

        ElementPropertiesSetter::SetGraphicGroup (eeh, graphicGroup);

        for (MSElementDescrP next = eeh.GetElementDescrP()->h.next; nullptr != next; next = next->h.next)
            {
            EditElementHandle   nextEeh(next, false, false, m_model);
            ElementPropertiesSetter::SetGraphicGroup (nextEeh, graphicGroup);
            }

        m_dgnFile->SaveNextGraphicGroup (m_nextGraphicGroup);
        }
    }


/*---------------------------------------------------------------------------------**//**
* These methods are for DGN->DWG conversion.
+---------------+---------------+---------------+---------------+---------------+------*/

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcString        ConvertTagStringToUnicodeString (DgnTagValue* pTagValue, LangCodePage codepageIn)
    {
    LangCodePage codePage = codepageIn;
    WString str = pTagValue->GetStringValue(&codePage);
    return AcString(str.c_str());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetTextFromTagData
(
AcDbText*                   pText,
DgnTagValueP                pTagValue,
RunPropertiesCP             runProperties,
ConvertFromDgnContextR      context
)
    {
    WChar                 tmpChars[1024];

    switch (pTagValue->GetTagType())
        {
        case MS_TAGTYPE_CHAR:
            {
            if (pTagValue->GetTagSize() > 0)
                {
                // get codepage
                DgnFontCR           fontForCodePage = runProperties->GetFontForCodePage();
                LangCodePage codepage = fontForCodePage.GetTagCodePage();
                // convert string to unicode
                AcString        string = ConvertTagStringToUnicodeString (pTagValue, codepage);

                if (runProperties->IsUnderlined())
                    string = AcString ("%%U") + string + AcString ("%%U");
                if (runProperties->IsOverlined())
                    string = AcString ("%%O") + string + AcString ("%%O");

                pText->setTextString (string.kwszPtr());
                }
            break;
            }

        case MS_TAGTYPE_SINT:
            swprintf (tmpChars, L"%d", pTagValue->GetShortValue());
            pText->setTextString (tmpChars);
            break;

        case MS_TAGTYPE_LINT:
            swprintf (tmpChars, L"%d", pTagValue->GetLongValue());
            pText->setTextString (tmpChars);
            break;

        case MS_TAGTYPE_DOUBLE:
            swprintf (tmpChars, L"%.15g", pTagValue->GetDoubleValue());
            pText->setTextString (tmpChars);
            break;

        default:
            return BSIERROR;
        }
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static WChar*             ValidateAttributeString
(
WCharP                    pOutString,
WCharCP                   pInString
)
    {
    WChar *pOut = pOutString;
    for (; 0 != *pInString && 0x0a != *pInString && 0x0d != *pInString; *pOut++ = *pInString++)
        ;

    *pOut++ = 0;

    return pOutString;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/14
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsAttrdefInBlock (AcDbAttributeDefinition* attrdef, AcDbObjectId currentBlockId)
    {
    // check the owner of the output attrdef - the input attrdef should have been appended to the block if it should be so.
    AcDbSmartBlockTableRecordPointer    block(attrdef->ownerId(), AcDb::kForRead);
    Acad::ErrorStatus                   status = block.openStatus ();
    if (Acad::eOk == status && !block->isLayout())
        return  true;

    if (Acad::eOk != status)
        {
        // we have not saved the attdef to DWG - it could still be either a valid or an orhpan attdef:
        block.open (currentBlockId, AcDb::kForRead);
        if (Acad::eOk == (status = block.openStatus()) && !block->isLayout())
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AttributeDefinitionFromAttributeElement
(
AcDbAttributeDefinition*    pDefinition,
ElementHandleR              elemHandle,
ConvertFromDgnContextR      context
)
    {
    // get tag element
    MSElementCP         pElem = elemHandle.GetElementCP ();
    AttributeElm const* pTagElm = (AttributeElm const*) pElem;
    if (NULL == pTagElm)
        return  BSIERROR;
    // get tag handler
    TagElementHandler*  tagHandler = dynamic_cast<TagElementHandler*> (&elemHandle.GetHandler());
    if (NULL == tagHandler)
        return  BSIERROR;
    // get text block
    TextBlockPtr        textBlock = tagHandler->GetTextPart (elemHandle, ITextPartId());
    if (!textBlock.IsValid())
        return  BSIERROR;
    // get tag value
    DgnTagValue         tagValue;
    StatusInt           status = TagElementHandler::GetAttributeValue (elemHandle, tagValue);
    if (BSISUCCESS != status)
        return  status;
    // get tag definition & name
    DgnTagDefinition    tagDef;
    WString             setName;
    if (SUCCESS != (status = context.GetTagSetNameAndDefinition(&setName, &tagDef, elemHandle)))
        return status;
    // get text params - a tag element has only one run of text
    ParagraphPropertiesCP   paragraphProperties = textBlock->GetParagraphProperties (0);
    RunPropertiesCP         runProperties = textBlock->GetRunProperties (0);
    if (NULL == paragraphProperties || NULL == runProperties)
        return  BSIERROR;

    // get origin & tag display flag
    DPoint3d            origin = pTagElm->origin;
    bool                isVisible = !pTagElm->dhdr.props.b.invisible && 0 == (pTagElm->flags & ATTR_FLAG_HIDEINSTANCE);
    // get user origin & matrix
    DPoint3d            userOrigin;
    RotMatrix           rMatrix;
    tagHandler->GetSnapOrigin (elemHandle, userOrigin);
    tagHandler->GetOrientation (elemHandle, rMatrix);

    SetTextFromTagData (pDefinition, &tagValue, runProperties, context);

    // set flags...orphan tags and tags from scDef take visibility from tag def
    ElementHandle       targetElem;
    if (BSISUCCESS != TagElementHandler::GetTargetElement(targetElem, elemHandle) || !targetElem.IsValid())
        pDefinition->setInvisible (0 != (tagDef.propsMask & TAG_PROP_DISPOFF));
    else
        pDefinition->setInvisible (! isVisible);

    pDefinition->setPreset     (0 != (tagDef.propsMask & TAG_PROP_DEF));
    pDefinition->setVerifiable (0 != (tagDef.propsMask & TAG_PROP_CONF));
    pDefinition->setConstant   (0 != (tagDef.propsMask & TAG_PROP_CONST));

    // set attribute tag
    WChar         validateWChars[2048];

    ValidateAttributeString (validateWChars, tagDef.name);
    pDefinition->setTag (validateWChars);
    ValidateAttributeString (validateWChars, tagDef.prompt);
    pDefinition->setPrompt (validateWChars);

    // want visibility controlled by tag display flag...Note - This used to be before "updateEntityPropertiesFromElement" so it had no effect (TR# 121
    /*---------------------------------------------------------------------------------------------------------------------------
    Two scenarios when a type 37 element is invisible:

    1) An orphan tag element that is set invisible, as in TR 277216. It is not from a shared cell definition, so it gets displayed
        per element visibility. We have to keep this orphan tag invisible.
    2) An originally DWG attrdef wrapped in a shared cell definition that was set invisible by us, as in TR 312367.  In this
        scenario we have to set the entity back as visible. Note that this is about entity visibility, not attribute display.
    ---------------------------------------------------------------------------------------------------------------------------*/
    bool            isInBlock = IsAttrdefInBlock (pDefinition, context.GetCurrentBlockId());
    pDefinition->setVisibility ((pElem->hdr.dhdr.props.b.invisible && !isInBlock) ? AcDb::kInvisible : AcDb::kVisible);

    DPoint2d        fontSize = runProperties->GetFontSize ();
    TextParamWide   textParams;
    paragraphProperties->ToElementData (textParams, textBlock->GetProperties ().GetAnnotationScale());
    runProperties->ToElementData (textParams, textBlock->GetProperties ().GetAnnotationScale());

    // set attrdef annotation scale if it is orphaned in the default design model:
    DgnModelP               model = elemHandle.GetDgnModelP ();
    TextBlockPropertiesCR   textProperties = textBlock->GetProperties (); 
    double                  annoScale = textProperties.GetAnnotationScale ();
    bool                    annotative = false;
    if (!isInBlock && textProperties.HasAnnotationScale() &&
        NULL != model && (model->IsDictionaryModel() || context.CanSaveAnnotationScale()))
        annotative = context.AddAnnotationScaleToObject (pDefinition, textProperties.GetAnnotationScale(), elemHandle.GetElementId());
    else    
        RealDwgUtil::SetObjectAnnotative (pDefinition, false);

    bool    backwards  = textParams.exFlags.backwards  || fontSize.x < 0.0;
    bool    upsidedown = textParams.exFlags.upsidedown || fontSize.y < 0.0;

    return context.SetTextDataFromDgn (pDefinition, textParams, fontSize, backwards, upsidedown, origin, userOrigin, rMatrix, elemHandle);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/06
*
*   Find the parent of a nested attribute and extract its transform.  This is necessary
*    because of the crazy way we float nested attributes to the top of the hierarchy.
*   (TR# 181918)
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            ExtractInnerParentAttributeTransform
(
TransformR                  transform,
EditElementHandleCR         scInstanceEeh,
ElementId                   nestedId,
ConvertFromDgnContextR      context
)
    {
    /*---------------------------------------------------------------------------------------------------------------------------
    This expensive function gets called recursively. The performance becomes unacceptable with large share cell definitions nested 
    with lots of tags, as a case in TR 315267.  Since the sole purpose of retrieving shared cell definitions here is to find the 
    transformation of tags nested in these definitions, we do not have to transform all elements in the cell definition.  We just 
    need to transform nested shared cell instances that may be recursively searched for tags in which these shared cell definitions 
    may contain.  So here we pass FALSE in below call to not transform the entire cell.  Then we apply the transformation only to
    type 35 elements found in the definition.
    ---------------------------------------------------------------------------------------------------------------------------*/
    StatusInt       status = MDLERR_IDNOTFOUND;
    MSElementCP     pElement = scInstanceEeh.GetElementCP ();

    ElementRefP     scDefinition = CellUtil::FindSharedCellDefinition (*pElement, *context.GetFile());
    if (NULL != scDefinition)
        {
        SharedCell*     scElem = (SharedCell *) pElement;
        Transform       scTransform;
        scTransform.InitFrom (scElem->rotScale, scElem->origin);
        TransformInfo   transInfo (scTransform);

        EditElementHandle   scDefEeh (scDefinition);
        for (ChildEditElemIter child(scDefEeh); child.IsValid(); child = child.ToNext())
            {
            if (nestedId == child.GetElementId())
                {
                DPoint3d        origin;
                RotMatrix       rMatrix;

                if (SUCCESS == (status = CellUtil::ExtractOrigin(origin, scInstanceEeh)) && 
                    SUCCESS == (status = CellUtil::ExtractRotation(rMatrix, scInstanceEeh)))
                    context.ComputeInverseCellTransform (transform, origin, rMatrix);

                return status;
                }
            else if (SHARED_CELL_ELM == child.GetElementType())
                {
                child.GetHandler (MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (child, transInfo);
                }
            context.ReportProgress ();
            }
        // Didn't find the nested instance at this level - try children.
        for (ChildEditElemIter child(scDefEeh); child.IsValid(); child = child.ToNext())
            if (SHARED_CELL_ELM == child.GetElementType() && SUCCESS == (status = ExtractInnerParentAttributeTransform (transform, child, nestedId, context)))
                break;
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/06
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ExtractNestedAttributeTransform
(
TransformR                  transform,
ElementId                   nestedId,
ElementHandleCR             elemHandle,
ConvertFromDgnContextR      context
)
    {
    AssocPoint      assocPoint;

    if (BSISUCCESS == AssociativePoint::ExtractPoint(assocPoint, elemHandle, 0, 1))
        {
        ElementRefP elementRef;
        if (BSISUCCESS == AssociativePoint::GetRoot(&elementRef, NULL, NULL, NULL, assocPoint, context.GetModel(), 0) ||
            BSISUCCESS == AssociativePoint::GetRoot(&elementRef, NULL, NULL, NULL, assocPoint, elemHandle.GetDgnModelP(), 0))
            {
            EditElementHandle   eeh(elementRef);
            return ExtractInnerParentAttributeTransform (transform, eeh, nestedId, context);
            }
        }
    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::AttributeFromTagElement
(
AcDbAttribute*              pAttribute,
ElementHandleCR             elemHandle,
bool                        nested
)
    {
    /*-------------------------------------------------------------------------------------------------
    Prior to Vancouver, we used to ignore nested tags as they were pulled to the outermost shared cell.
    Now we no longer pull them out, we have to check them back in, so we only ignore them if the caller
    does not want them.
    -------------------------------------------------------------------------------------------------*/
    ElementId           nestedCellId = 0;
    bool                isNested = TagElementHandler::GetNestedSharedCellId (&nestedCellId, elemHandle);
    if (!nested && isNested)
        return BSISUCCESS;

    // get tag element
    MSElementCP         pElem = elemHandle.GetElementCP ();
    AttributeElm const* pTagElm = (AttributeElm const*) pElem;
    if (NULL == pTagElm)
        return  BSIERROR;
    // get tag handler
    TagElementHandler*  tagHandler = dynamic_cast<TagElementHandler*> (&elemHandle.GetHandler());
    if (NULL == tagHandler)
        return  BSIERROR;
    // get text block
    TextBlockPtr        textBlock = tagHandler->GetTextPart (elemHandle, ITextPartId());
    if (!textBlock.IsValid())
        return  BSIERROR;
    // get tag value
    DgnTagValue         tagValue;
    StatusInt           status = TagElementHandler::GetAttributeValue (elemHandle, tagValue);
    if (BSISUCCESS != status)
        return  status;
    // get tag definition & name
    DgnTagDefinition    tagDef;
    WString             setName;
    if (BSISUCCESS != (status = this->GetTagSetNameAndDefinition(&setName, &tagDef, elemHandle)))
        return status;
    // get text params - a tag element has only one run of text
    TextBlockPropertiesCR   textProperties = textBlock->GetProperties (); 
    ParagraphPropertiesCP   paragraphProperties = textBlock->GetParagraphProperties (0);
    RunPropertiesCP         runProperties = textBlock->GetRunProperties (0);
    if (NULL == paragraphProperties || NULL == runProperties)
        return  BSIERROR;

    // get user origin & matrix
    DPoint3d            userOrigin;
    RotMatrix           rMatrix;
    tagHandler->GetSnapOrigin (elemHandle, userOrigin);
    tagHandler->GetOrientation (elemHandle, rMatrix);

    // If this is a nested attribute then try to get the transform of its parent - this is necessary
    // because of the crazy way we float nested attributes the top of the hierarchy.
    bool                transformPushed = false;
    Transform           currentTo, currentFrom, currentLocal;
    Transform           nestedAttributeTransform;
    if (nested && SUCCESS == ExtractNestedAttributeTransform (nestedAttributeTransform, nestedCellId, elemHandle, *this))
        {
        transformPushed = true;
        this->PushTransform (&currentTo, &currentFrom, &currentLocal, &nestedAttributeTransform);
        }

    SetTextFromTagData (pAttribute, &tagValue, runProperties, *this);

    bool                isVisible = !pTagElm->dhdr.props.b.invisible && 0 == (pTagElm->flags & ATTR_FLAG_HIDEINSTANCE);
    ElementHandle       targetElem;
    // set flags...orphan tags and tags from scDef take visibility from tag def
    if (BSISUCCESS != TagElementHandler::GetTargetElement(targetElem, elemHandle) || !targetElem.IsValid())
        pAttribute->setInvisible (0 == (tagDef.propsMask & TAG_PROP_DISPOFF));
    else
        pAttribute->setInvisible (! isVisible);

    // set attribute tag
    pAttribute->setTag (tagDef.name);

    DPoint3d            origin = userOrigin;
    DPoint2d            fontSize = runProperties->GetFontSize ();
    double              tagAnnoscale = textProperties.GetAnnotationScale ();
    TextParamWide       textParams;
    paragraphProperties->ToElementData (textParams, tagAnnoscale);
    runProperties->ToElementData (textParams, tagAnnoscale);

    // we can't set annotation scale for an attribute - it goes by its target .
    bool                attrAnnotative = RealDwgUtil::IsObjectAnnotative (pAttribute);
    double              cellAnnoscale = 0.0;
    bool                cellAnnotative = RealDwgUtil::IsElementAnnotative (&cellAnnoscale, targetElem);
    if (cellAnnotative && !attrAnnotative)
        fontSize.Scale (cellAnnoscale > TOLERANCE_ZeroScale ? 1.0 / cellAnnoscale : 1.0);
    
    if (TextElementJustification::LeftBaseline != textParams.just)
        {
        origin = textBlock->GetTextAutoCADOrigin ();
        userOrigin = textBlock->GetUserOrigin ();
        }

    bool    backwards  = textParams.exFlags.backwards  || fontSize.x < 0.0;
    bool    upsidedown = textParams.exFlags.upsidedown || fontSize.y < 0.0;

    this->SetTextDataFromDgn (pAttribute, textParams, fontSize, backwards, upsidedown, origin, userOrigin, rMatrix, elemHandle);

    // want visibility controlled by tag display flag...
    pAttribute->setVisibility (AcDb::kVisible);

    if (transformPushed)
        this->PopTransform (&currentTo, &currentFrom, &currentLocal);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AttributeTextFromTextEdField
(
AcDbText*                   pText,
ElementHandleR              elemHandle,
ConvertFromDgnContextR      context
)
    {
    StatusInt           status;

    if (!context.IsEnterDataField (elemHandle.GetElementCP()))
        {
        return BSIERROR;
        }
    else
        {
        TextBlock       textBlock (elemHandle);
        status = context.DTextFromTextBlock (pText, textBlock, elemHandle);
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            AttributeDefinitionFromTextEdField
(
AcDbAttributeDefinition*    pDefinition,
ElementHandleR              elemHandle,
ConvertFromDgnContextR      context
)
    {
    StatusInt           status;

    if (SUCCESS == (status = AttributeTextFromTextEdField (pDefinition, elemHandle, context)))
        {
        AcDbObjectId    blockId;
        if ( !(blockId = context.GetCurrentBlockId()).isNull() )
            {
            AcDbBlockTableRecordPointer   acBlock (blockId, AcDb::kForRead);
            WChar         setName[1024];
            const ACHAR*    blockName;
            acBlock->getName (blockName);
            CreateTagSetNameWithId (setName, blockName, pDefinition->objectId().handle());
            pDefinition->setTag (setName);
            }
        }
    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     InvertAttributeAnnotationScale (AcDbAttribute* acAttribute, AcDbBlockReference* acBlockReference, DgnModelP dgnModel)
    {
    if (NULL == acAttribute || NULL == dgnModel)
        return  false;

    double      modelScale = dgnModel->GetModelInfo().GetAnnotationScaleFactor ();

    if (modelScale < TOLERANCE_ZeroScale || fabs(modelScale - 1.0) < TOLERANCE_ZeroScale)
        return  false;

    acAttribute->setHeight (acAttribute->height() / modelScale);

    AcGePoint3d     tagOrigin = acAttribute->position ();
    AcGeVector3d    delta = tagOrigin - acBlockReference->position ();

    delta /= modelScale;
    tagOrigin += delta;

    acAttribute->setPosition (tagOrigin);
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::AttributeDefinitionFromElement
(
AcDbAttributeDefinition*    pAttribute,
ElementHandleR              elemHandle
)
    {
    return (ATTRIBUTE_ELM == elemHandle.GetElementType()) ?
            AttributeDefinitionFromAttributeElement (pAttribute, elemHandle, *this) :
            AttributeDefinitionFromTextEdField (pAttribute, elemHandle, *this);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SaveBlockEnterDataFieldsToDatabase
(
MSElementDescrCP                pDescr,
AcDbBlockTableRecordIterator*   blockIterator,
AcDbBlockReference*             acBlockReference,
ConvertFromDgnContextR          context
)
    {
    DgnModelP       model = context.GetModel ();

    for (; NULL != pDescr; pDescr = pDescr->h.next)
        {
        if (TEXT_NODE_ELM == pDescr->el.ehdr.type)
            {
            SaveBlockEnterDataFieldsToDatabase (pDescr->h.firstElem, blockIterator, acBlockReference, context);
            }
        else if (context.IsEnterDataField (&pDescr->el))
            {
            for (; !blockIterator->done(); blockIterator->step())
                {
                AcDbObjectId    entityId;
                if (Acad::eOk != (blockIterator->getEntityId (entityId)))
                    continue;

                AcDbEntityPointer           pEntity (entityId, AcDb::kForRead);
                if (Acad::eOk != pEntity.openStatus())
                    continue;

                AcDbAttributeDefinition*    pDefinition;
                if (NULL != (pDefinition = AcDbAttributeDefinition::cast (pEntity)))
                    {
                    AcDbAttribute*            pNewAttribute = new AcDbAttribute();

                    ElementHandle childHandle (pDescr, false, false);
                    if (SUCCESS == AttributeTextFromTextEdField (pNewAttribute, childHandle, context))
                        {
                        pNewAttribute->setTag (pDefinition->tagConst());
                        pNewAttribute->setInvisible (pDefinition->isInvisible());
                        pNewAttribute->recordGraphicsModified (false);
                        context.AddAttributeToBlockReference (acBlockReference, pNewAttribute, 0);

                        /*-------------------------------------------------------------------------------------------------
                        If the text element is not annotative but the cell is, above call to attach attributes to the block
                        will force attributes annotative - we need to compensate the difference.  TFS 386368.
                        --------------------------------------------------------------------------------------------------*/
                        IAnnotationHandlerP     annoHandler = dynamic_cast <IAnnotationHandlerP> (&childHandle.GetHandler());
                        if (nullptr != annoHandler && !annoHandler->HasAnnotationScale(nullptr, childHandle) &&
                            RealDwgUtil::IsObjectAnnotative(pNewAttribute) && acdbSymUtil()->blockModelSpaceId(acBlockReference->database()) == acBlockReference->ownerId())
                            {
                            double  modelScale = context.GetModel()->GetModelInfo().GetAnnotationScaleFactor ();
                            if (fabs(modelScale - 1.0) > TOLERANCE_ZeroScale)
                                pNewAttribute->setHeight (pNewAttribute->height()/modelScale);
                            }

                        pNewAttribute->close ();
                        }
                    else
                        {
                        delete pNewAttribute;
                        }

                    blockIterator->step();
                    break;
                    }
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
static UInt32   FindUniqueTagsOnElement (ElementAgendaR tags, ElementHandleR tagSource, bool saveNestedTags)
    {
    UInt32      numTags = TagElementHandler::GetTagsFromSourceElement (&tags, tagSource, saveNestedTags, true);
    if (numTags < 2)
        return  numTags;

    // if source element is in a share cell definition, we may have duplicated tags, as a case in TR 315267
    DgnModelP   sourceModel = tagSource.GetDgnModelP ();
    if (NULL == sourceModel || !sourceModel->IsDictionaryModel())
        return  numTags;

    DgnFileP    dgnFile = sourceModel->GetDgnFileP ();
    if (NULL == dgnFile)
        return  numTags;
    
    DgnModelP       defaultModel = dgnFile->FindLoadedModelById (dgnFile->GetDefaultModelId());
    ElementAgenda   copyTags;
    for (ElementHandleCR tag : tags)
        {
        DgnModelP   tagModel = tag.GetDgnModelP ();

        if (tagModel != sourceModel && tagModel != defaultModel)
            continue;

        bool        found = false;
        ElementId   nestedId1 = INVALID_ELEMENTID;
        DgnTagSpec  tag1Spec;

        if (TagElementHandler::GetNestedSharedCellId(&nestedId1, tag) && INVALID_ELEMENTID != nestedId1 && BSISUCCESS == TagElementHandler::Extract(tag1Spec, tag, *tagModel))
            {
            // this is a legacy tag that was pulled out from DWG nested block prior to 8.21 - check previous tags for dups
            for (ElementHandleCR checkTag : copyTags)
                {
                ElementId   nestedId2 = 0;
                if (TagElementHandler::GetNestedSharedCellId(&nestedId2, checkTag) && nestedId1 == nestedId2)
                    {
                    /*-------------------------------------------------------------------------------------------
                    Two tags are attached to the same nested shared cell instance, a scenara seen in TFS 208126:
                    when the parent shared cell definition is instanced more than once with its nested tag pulled
                    out of the parent cell, each of these pulled out tags are the same and pointing to the same
                    target the nested shared cell element.  We don't want to duplicate these tags.
                    -------------------------------------------------------------------------------------------*/
                    DgnTagSpec  tag2Spec;
                    if (BSISUCCESS == TagElementHandler::Extract(tag2Spec, checkTag, *checkTag.GetDgnModelP()) && 0 == wcsicmp(tag1Spec.tagName, tag2Spec.tagName))
                        {
                        found = true;
                        break;
                        }
                    }
                }
            }

        if (!found)
            copyTags.push_back (ElemAgendaEntry(tag, true));
        }

    if (copyTags.size() < tags.size())
        tags = copyTags;

    numTags = (UInt32) tags.size ();

    return  numTags;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/15
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32          ConvertFromDgnContext::UpdateMInsertAttributes (AcDbMInsertBlock* acMInsert, ElementHandleCR cellHandle)
    {
    /*----------------------------------------------------------------------------------------------------------
    In ACAD an minsert cannot be exploded and its attributes cannot be edited either.  When converted to DGN,
    these attributes are attached to each child shared cell in a cell header, with the first child cell attached
    tags having the original attributes preserved.  If the minsert cell header gets dropped, or the file gets 
    saved as DGN now are saving back to DWG, all will be processed in a normal way and are not handled here.  
    This method only handles saving existing DWG file: update original attributes attached to the minsert.
    ----------------------------------------------------------------------------------------------------------*/
    if (nullptr == acMInsert || (acMInsert->rows() < 1 && acMInsert->columns() < 1))
        return  0;

    ChildElemIter firstChild (cellHandle);
    if (firstChild.IsValid())
        {
        ElementAgenda   tags;
        UInt32          numTags = TagElementHandler::GetTagsFromSourceElement (&tags, firstChild, false, true);

        if (numTags < 1)
            return  0;

        AcDbObjectIterator*     attrIter = acMInsert->attributeIterator ();
        if (nullptr == attrIter)
            return  0;

        for (; !attrIter->done(); attrIter->step())
            {
            ElementId   attributeId = this->ElementIdFromObjectId (attrIter->objectId());

            for each (ElementHandleCR tag in tags)
                {
                if (attributeId == tag.GetElementId())
                    {
                    AcDbAttributePointer    acAttribute(attrIter->objectId(), AcDb::kForWrite);
                    if (Acad::eOk == acAttribute.openStatus())
                        this->AttributeFromTagElement (acAttribute, tag, false);
                    break;
                    }
                }
            }
            
        delete attrIter;

        return  numTags;
        }

    return  0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveBlockTagsToDatabase
(
AcDbBlockReference*         acBlockReference,
ElementHandleR              tagSource
)
    {
    AcDbObjectId        blockId;
    MSElementDescrCP    pElmDscr = tagSource.GetElementDescrCP();

    if ((blockId = acBlockReference->blockTableRecord()).isNull())
        return BSIERROR;

    AcDbBlockTableRecordPointer acBlock (blockId, AcDb::kForRead);
    if (Acad::eOk != acBlock.openStatus())
        return BSIERROR;

    if (!acBlock->hasAttributeDefinitions())
        return SUCCESS;

    // acBlockReference must be a database resident prior to being added by attributes
    if (acBlockReference->objectId().isNull() && this->AddEntityToCurrentBlock(acBlockReference, pElmDscr->el.ehdr.uniqueId).isNull())
        return  BSIERROR;

    bool    saveNestedTags = (pElmDscr->el.ehdr.complex && CELL_HEADER_ELM != pElmDscr->el.ehdr.type);

    if (CELL_HEADER_ELM == pElmDscr->el.ehdr.type)
        {
        // roundtrip MInsert tags
        if (this->SavingChanges())
            UpdateMInsertAttributes (AcDbMInsertBlock::cast(acBlockReference), tagSource);

        // extract the single enter data fields from the cell header to tags.
        AcDbBlockTableRecordIterator*   pIterator;
        if (Acad::eOk == acBlock->newIterator (pIterator))
            {
            SaveBlockEnterDataFieldsToDatabase (pElmDscr->h.firstElem, pIterator, acBlockReference, *this);
            delete pIterator;
            }
        }

    ElementAgenda       tags;
    if (FindUniqueTagsOnElement(tags, tagSource, saveNestedTags) < 1)
        return SUCCESS;

    AcDbObjectIterator* acIterator;
    if (NULL != (acIterator = acBlockReference->attributeIterator ()))
        {
        for ( ; !acIterator->done(); )
            {
            AcDbObjectId        attributeId = acIterator->objectId();
            ElementId           attributeElementId = this->ElementIdFromObjectId (attributeId);

            acIterator->step();

            for (ElementAgenda::iterator tagIterator = tags.begin(); tagIterator != tags.end(); tagIterator++)
                {
                this->ReportProgress ();

                if (attributeElementId == tagIterator->GetElementId())
                    {
                    tags.erase (tagIterator);       // The element would have been changed elsewhere.
                    if (tagIterator == tags.end())
                        break;
                    }
                }

            if (tags.empty())
                {
                // This attribute no longer exists, delete it?
                }
            }
        delete acIterator;
        }

    if (tags.empty())
        return  SUCCESS;

    bool        savingAppData = this->GetIsSavingApplicationData ();

    // Any remaining elements are new and need to be added.
    for each (ElementHandleCR tag in tags)
        {
        AcDbAttribute*          pNewAttribute = new AcDbAttribute();

        this->ReportProgress ();

        /*-----------------------------------------------------------------------------------------------------------
        Before we call SetTextDataFromDgn which when saving application data may attempt to add an extended dictionary
        to the attribute, make sure the attribute to be a database resident as that is a prerequisite for adding an
        extended dictionary to an entity.   TFS 242036.
        -----------------------------------------------------------------------------------------------------------*/
        bool    preSaveAttribute = savingAppData && tag.BeginXAttributes() != tag.EndXAttributes();

        if (preSaveAttribute && (this->AddAttributeToBlockReference(acBlockReference, pNewAttribute, tag.GetElementId()).isNull()))
            {
            delete pNewAttribute;
            continue;
            }

        if (SUCCESS == this->AttributeFromTagElement (pNewAttribute, tag, saveNestedTags))
            {
            pNewAttribute->recordGraphicsModified (false);
            if (preSaveAttribute || !(this->AddAttributeToBlockReference (acBlockReference, pNewAttribute, tag.GetElementId()).isNull()))
                pNewAttribute->close ();
            else
                delete pNewAttribute;
            }
        else if (pNewAttribute->objectId().isValid())
            {
            // remove the pre-saved but bad attribute from database
            pNewAttribute->erase ();
            }
        else
            {
            delete pNewAttribute;
            }
        }

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::SaveTagsAsAttributeDefinitions
(
AcDbObjectId                blockId,
TransformCR                 transform,
ElementHandleR              tagSource
)
    {
    MSElementDescrCP    pCellDescr      = tagSource.GetElementDescrCP();
    bool                saveNestedTags  = (pCellDescr->el.ehdr.complex && CELL_HEADER_ELM != pCellDescr->el.ehdr.type);

    ElementAgenda       tags;
    UInt32              numAttrs = FindUniqueTagsOnElement (tags, tagSource, saveNestedTags);
    if (numAttrs < 1)
        return BSIERROR;

    AcDbSmartBlockTableRecordPointer acBlock (blockId, AcDb::kForWrite);
    if (Acad::eOk != acBlock.openStatus())
        return BSIERROR;

    AcDbObjectId        firstAttributeId;
    if (acBlock->hasAttributeDefinitions())
        {
        AcDbBlockTableRecordIterator*         pIterator;
        if (Acad::eOk == acBlock->newIterator (pIterator))
            {
            this->ReportProgress ();

            for (; !pIterator->done(); pIterator->step())
                {
                AcDbObjectId        entityId;
                if (Acad::eOk != pIterator->getEntityId (entityId))
                    continue;

                AcDbEntityPointer   pEntity (entityId, AcDb::kForRead);
                if (Acad::eOk != pEntity.openStatus())
                    continue;

                if (pEntity->isKindOf (AcDbAttributeDefinition::desc()))
                    {
                    firstAttributeId = entityId;
                    break;
                    }
                }
            delete pIterator;
            }
        }

    Transform                   currentTo, currentFrom, currentLocal;
    this->PushTransform (&currentTo, &currentFrom, &currentLocal, &transform);

    for each (ElemAgendaEntry tagEeh in tags)
        {
        this->ReportProgress ();

        if (!tagEeh.IsValid())
            continue;

        DgnTagDefinition        tagDef;

        if (SUCCESS == GetTagSetNameAndDefinition (NULL, &tagDef, tagEeh))
            {
            bool        definitionFound = false;

            // This is inefficient, but if we're going to allow a tag to be attached to an arbitrary shared cell then
            // we don't have any choice but to make sure that the definition exists here - and there are no
            // data structures available to find the definition with a matching cellname more efficiently.

            if (!firstAttributeId.isNull())
                {
                AcDbBlockTableRecordIterator*         pIterator;
                if (Acad::eOk == acBlock->newIterator (pIterator))
                    {
                    for (pIterator->seek (firstAttributeId); !definitionFound && !pIterator->done(); pIterator->step())
                        {
                        this->ReportProgress ();

                        AcDbObjectId        entityId;
                        if (Acad::eOk != pIterator->getEntityId (entityId))
                            continue;

                        AcDbEntityPointer   pEntity (entityId, AcDb::kForRead);
                        if (Acad::eOk != pEntity.openStatus())
                            continue;

                        AcDbAttributeDefinition*    pAttrDef;
                        if (NULL != (pAttrDef = AcDbAttributeDefinition::cast (pEntity)))
                            {
                            ACHAR*  tag = pAttrDef->tag();
                            if (NULL != tag && 0 == wcscmp (tag, tagDef.name))
                                definitionFound = true;
                            acutDelString (tag);
                            }
                        }
                    delete pIterator;
                    }
                }

            if (!definitionFound)
                {
                AcDbAttributeDefinition*      pDefinition = new AcDbAttributeDefinition();
                acBlock->appendAcDbEntity (pDefinition);
                this->AttributeDefinitionFromElement (pDefinition, tagEeh);
                pDefinition->recordGraphicsModified (false);
                pDefinition->close ();
                }
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Error Extracting Tag From Definition\n");
            }
        }

    this->PopTransform (&currentTo, &currentFrom, &currentLocal);

    return SUCCESS;
    }



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
ItemTypeP       ConvertToDgnContext::GetItemTypeByName (WCharCP name, bool addIfNotFound, bool* isAdded)
    {
    if (nullptr == name || 0 == name[0])
        return  nullptr;

    // create a new attrdef item type library if not already exists
    if (!m_attrdefItemtypeLibrary.IsValid())
        {
        // find the attrdef itemtype library from the file
        ItemTypeLibrariesPtr    itemtypeLibs = ItemTypeLibraries::Create (*m_dgnFile);
        if (!itemtypeLibs.IsValid())
            return  nullptr;

        bool    found = false;
        for (auto& itemtypeEntry : *itemtypeLibs.get())
            {
            m_attrdefItemtypeLibrary = itemtypeEntry.ObtainLibrary ();
            if (m_attrdefItemtypeLibrary.IsValid() && m_attrdefItemtypeLibrary->IsDwgAttributeDefinitionsLibrary())
                {
                found = true;
                break;
                }
            this->ReportProgress ();
            }

        if (!found)
            {
            // create a new ItemTypeLibrary
            m_attrdefItemtypeLibrary = ItemTypeLibrary::Create (SCHEMA_NAME_AttributeDefinition, *m_dgnFile);
            if (!m_attrdefItemtypeLibrary.IsValid())
                return  nullptr;

            m_attrdefItemtypeLibrary->SetIsDwgAttributeDefinitionsLibrary (true);
            }
        }

    if (nullptr != isAdded)
        *isAdded = false;

    ItemTypeP   itemType = m_attrdefItemtypeLibrary->GetItemTypeByName (name);
    if (nullptr != itemType)
        return  itemType;

    if (addIfNotFound)
        {
        itemType = m_attrdefItemtypeLibrary->AddItemType (name, false);
        if (nullptr != itemType && itemType->SetUpgradedTagSetName(name))
            {
            if (nullptr != isAdded)
                *isAdded = true;
            return  itemType;
            }
        }

    return  nullptr;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     AddStringProperty (ItemTypeP itemType, WCharCP propName, UInt32 propIndex, WCharCP propValue, bool readOnly)
    {
    CustomPropertyP     prop = itemType->AddProperty (propName);
    if (nullptr == prop)
        {
        DIAGNOSTIC_PRINTF ("Error adding attrdef %ls to item type def %ls\n", propName, itemType->GetName());
        return  false;
        }

    // get & set the default value
    AcString    acString;
    StripEncoding (acString, propValue, nullptr, nullptr);

    ECValue     ecValue;
    ecValue.SetString (acString.kwszPtr());

    prop->SetType (CustomProperty::Type::String);
    prop->SetDefaultValue (ecValue);
    prop->SetIsReadOnly (readOnly);
    prop->SetTagDefId (propIndex);

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SaveBlockAttrdefsAsItemTypedefs (AcDbBlockTableRecord* acBlock)
    {
    if (!acBlock->hasAttributeDefinitions())
        return  RealDwgIgnoreEntity;

    const ACHAR*    blockName = nullptr;
    if (Acad::eOk != acBlock->getName(blockName) || nullptr == blockName)
        return  RealDwgIgnoreEntity;

    AcDbBlockTableRecordIterator*   iterator;
    if (Acad::eOk != acBlock->newIterator(iterator))
        return  BlockIteratorFailure;

    WChar           itemtypeName[2048] = { 0 };
    if (acBlock->isAnonymous() || 0 == blockName[0])
        CreateTagSetNameWithId (itemtypeName, blockName, acBlock->objectId().handle());
    else
        wcscpy (itemtypeName, blockName);

    // if the item type has been created, skip from adding it
    bool            isNew = false;
    ItemTypeP       itemType = this->GetItemTypeByName (itemtypeName, true, &isNew);
    if (nullptr != itemType && !isNew)
        return  RealDwgSuccess;

    // use 1-based attrdef sequential index as "tagdef" index to get around duplicated attrdef names
    UInt32          attrdefIndex = 1;

    // add properties from attrdefs
    for (iterator->start(); !iterator->done(); iterator->step())
        {
        this->ReportProgress ();

        // only want attrdef entities
        AcDbObjectId    entityId;
        if (Acad::eOk != iterator->getEntityId(entityId) || !entityId.objectClass()->isDerivedFrom(AcDbAttributeDefinition::desc()))
            continue;

        // open this attrdef entity
        AcDbEntityPointer           acEntity (entityId, AcDb::kForRead);
        AcDbAttributeDefinition*    attrdef = nullptr;
        if (Acad::eOk != acEntity.openStatus() || nullptr == (attrdef = AcDbAttributeDefinition::cast(acEntity)))
            continue;

        // add a new property to the item type:
        ACHAR*      attrString = nullptr;
        if (attrdef->isMTextAttributeDefinition())
            {
            const AcDbMText*    mtext = attrdef->getMTextAttributeDefinitionConst ();
            if (nullptr != mtext)
                attrString = mtext->text ();
            }
        else
            {
            attrString = attrdef->textString ();
            }

        WString     stringValue(nullptr == attrString ? L"" : attrString);
        if (nullptr != attrString)
            acutDelString (attrString);

        AddStringProperty (itemType, attrdef->tagConst(), attrdefIndex, stringValue.GetWCharCP(), Adesk::kTrue == attrdef->isConstant());

        attrdefIndex++;
        }

    // DXF group code 70, i.e. acBlock->hasAttributeDefinitions(), can be bogus.
    if (isNew && 0 == itemType->GetPropertyCount())
        m_attrdefItemtypeLibrary->RemoveItemType (itemType->GetId());

    return  RealDwgSuccess;
    }


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          02/15
+===============+===============+===============+===============+===============+======*/
class           ConvertAttributesToItemTypes
{
private:
    struct IndexedAttrdef
        {
        UInt32                          m_index;
        AcDbAttributeDefinition*        m_attrdef;

        IndexedAttrdef (UInt32 i, AcDbAttributeDefinition* attrdef) : m_index(i), m_attrdef(attrdef) {;}
        };  // IndexedAttrdef

    EditElementHandleP                  m_outputElement;
    AcDbBlockReference*                 m_blockReference;
    ConvertToDgnContext*                m_toDgnContext;
    AcDbObjectIterator*                 m_attributeIterator;
    bvector<AcDbAttributeDefinition*>   m_attributeDefinitionList;
    bvector<struct IndexedAttrdef>      m_constantAttributeDefinitionList;
    ItemTypeP                           m_definedItemType;
    DgnECInstanceP                      m_definedItemInstance;
    bool                                m_isMInsert;
    bool                                m_visiblePerBlock;
    bool                                m_isBlockReferenceLayerFrozen;

public:
    // constructors & destructors
    ConvertAttributesToItemTypes (EditElementHandleR outElement, AcDbBlockReference* inBlockReference, bool minsert, ConvertToDgnContext* context)
        {
        m_outputElement = &outElement;
        m_blockReference = inBlockReference;
        m_isMInsert = minsert;
        m_toDgnContext = context;
        m_visiblePerBlock = false;
        m_attributeIterator = nullptr;
        m_attributeDefinitionList.clear ();
        m_constantAttributeDefinitionList.clear ();
        m_definedItemType = nullptr;
        m_definedItemInstance = nullptr;
        m_isBlockReferenceLayerFrozen = false;
        }

    ~ConvertAttributesToItemTypes ()
        {
        if (nullptr != m_attributeIterator)
            delete m_attributeIterator;

        for (auto& attrdef : m_attributeDefinitionList)
            attrdef->close ();
        }


private:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetTextProperty (WStringR propName, UInt32 propIndex, TextBlockR textBlock, bool& moreThan1Line, bool visible)
    {
    Caret       start = textBlock.Begin ();
    RunCP       run = start.GetCurrentRunCP ();
    if (moreThan1Line)
        {
        // this is from an mtext - get the first run having actual string content:
        while (nullptr != run && run->ContainsOnlyWhitespace())
            {
            if (BSISUCCESS == start.MoveToNextRun())
                run = start.GetCurrentRunCP ();
            else
                break;  // error
            }
        }
    // for an invisible attribute, all lines of texts will be set as a field
    if (!visible || nullptr == run)
        moreThan1Line = false;

    // set a string property from this attribute
    ECValue             propValue;
    propValue.SetString (textBlock.ToString().GetWCharCP());
    if (moreThan1Line)
        propValue.SetIsReadOnly (true);

    ECObjectsStatus     ecs = ECOBJECTS_STATUS_Error;
    CustomPropertyCP    prop = nullptr;
    if (nullptr != m_definedItemInstance)
        {
        if (nullptr != (prop = m_definedItemType->FindPropertyByTagDefId(propIndex)))
            propName = prop->GetInternalName ();

        ecs = m_definedItemInstance->SetValue (propName.GetWCharCP(), propValue);
        }

    if (ECOBJECTS_STATUS_Success != ecs)
        {
        DIAGNOSTIC_PRINTF ("Error setting item type property <%ls> to value <%ls>\n", propName.c_str(), nullptr == run ? L"???" : run->ToString().c_str());
        return  BadData;
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/17
+---------------+---------------+---------------+---------------+---------------+------*/
bool    NeedsToShiftByDescender (TextBlockCR textBlock)
    {
    static wchar_t  s_descenders[] = L"gjpqy";

    // check vertical justification
    TextElementJustification    just = textBlock.GetParagraphPropertiesForAdd().GetJustification ();
    if (TextElementJustification::LeftDescender != just && TextElementJustification::CenterDescender != just && TextElementJustification::RightDescender != just)
        return  false;

    // get & check the last line for descenders
    size_t  nLines = textBlock.GetLineCount (textBlock.Begin(), textBlock.End());
    if (nLines == 0)
        return  false;

    Caret   caret = textBlock.Begin ();
    for (size_t i = 0; i < nLines - 1; i++)
        caret.MoveToNextLine ();

    LineP   lastLine = caret.GetCurrentLineP ();
    if (nullptr == lastLine || lastLine->IsEmpty())
        return  false;

    for (size_t i = 0; i < lastLine->GetRunCount(); i++)
        {
        RunP    run = lastLine->GetRun (i);
        if (nullptr != run)
            {
            WString str = run->ToString ();
            if (WString::npos != str.find_first_of(s_descenders))
                return  false;
            }
        }
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateField (TextBlockR textBlock, WStringCR propName)
    {
    DgnElementECInstanceP   ecInstance = m_definedItemInstance->GetAsElementInstance ();
    if (nullptr == ecInstance)
        return  BadData;

    TextFieldPtr    field = TextField::CreateForElement (*ecInstance, propName.GetWCharCP(), nullptr, *m_toDgnContext->GetModel());
    if (!field.IsValid())
        {
        DIAGNOSTIC_PRINTF ("Failed creating a field from attribute \"%ls\", dropping to text.\n", propName.c_str());
        return  CantCreateItemType;
        }

    Caret       start = textBlock.Begin ();
    if (!textBlock.SetField(start, *field, textBlock.ToString().GetWCharCP()))
        return CantCreateItemType;

    /*-----------------------------------------------------------------------------------------------
    Now we have created a text field, but we have also effectively promoted a DTEXT to a textnode, hence 
    along with a known problem in TFS 786529: for a text node, when the vertical justification is descender, 
    MicroStation adjusts text vertical position based on the text content.  If there exists a descended 
    character, or if the text background is turned on, as a case for default text field display, the 
    bottom of the text box is the bottom of the descender, not the base line.  If there is no descended 
    character and background is turned off, the bottom of the box is the baseline, not the bottom.  
    In the latter case, we need to move the origin of the textnode up by the descender height to match 
    the DTEXT display in ACAD.
    -----------------------------------------------------------------------------------------------*/
    if (NeedsToShiftByDescender(textBlock))
        {
        // change vertical justification from descenter to baseline for all paragraphs:
        for (size_t i = 0; i < textBlock.GetParagraphCount(); i++)
            {
            ParagraphP  paragraph = textBlock.GetParagraph (i);
            if (nullptr != paragraph)
                {
                TextElementJustification    just = paragraph->GetJustification ();

                if (TextElementJustification::LeftDescender == just)
                    just = TextElementJustification::LeftBaseline;
                else if (TextElementJustification::CenterDescender == just)
                    just = TextElementJustification::CenterBaseline;
                else if (TextElementJustification::RightDescender == just)
                    just = TextElementJustification::RightBaseline;
                else
                    continue;

                paragraph->SetJustification (just);
                }
            }

        // get the descender ratio:
        double  descender = textBlock.GetRunPropertiesForAdd().GetFont().GetDescenderRatio ();
        double  textHeight = textBlock.GetNodeOrFirstRunHeight ();

        // get & move the user origin up by the descender height
        DPoint3d    userOrigin = textBlock.GetUserOrigin ();
        RotMatrix   matrix = textBlock.GetOrientation ();
        bool        isRotated = !matrix.IsIdentity ();

        if (isRotated)
            matrix.MultiplyTranspose (userOrigin);

        userOrigin.y += descender * textHeight;

        if (isRotated)
            matrix.Multiply (userOrigin);

        textBlock.SetUserOrigin (userOrigin);
        }
    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetTextElementHeaderFromAttribute (EditElementHandleR outText, AcDbEntityP acEntity, WCharCP attributeTag, bool needNewId, bool moreThan1Line)
    {
    m_toDgnContext->ElementHeaderFromEntity (outText, acEntity);

    /*---------------------------------------------------------------------------------------------------------------
    Unlike a tag element whose level goes by the target element, a field or text does not have such a special behavior.
    So when the block reference and an attribute are on different layers, we move the field/text to the cell instance's
    level when any of the conditions exists:

        1) the layer frozen status is different:
            a) both layers are frozen, we are good - don't bother to change the layer
            b) attribute's layer is frozen but block reference's layer is not(TFS620663) - don't change!
            c) block reference is frozen but attribute's layer is not - need to change the layer.
        2) attribute uses ByBlock color, or
        3) attribute is on layer 0, which implies layer-byblock

    Obviously this is not a right solution but it will get by most of the problems we otherwise may have.
    ---------------------------------------------------------------------------------------------------------------*/
    bool        isLayerFrozen = false;
    bool        isLayer0 = acEntity->database()->layerZero() == acEntity->layerId();
    if (!isLayer0)
        {
        AcDbLayerTableRecordPointer     attrLayer (acEntity->layerId(), AcDb::kForRead);
        if (Acad::eOk == attrLayer.openStatus())
            isLayerFrozen = attrLayer->isFrozen ();
        }

    if (isLayer0 || (!isLayerFrozen && m_isBlockReferenceLayerFrozen) || acEntity->color().isByBlock())
        {
        // change attribute's level, color and weight to those of block reference's:
        LevelId     level = m_toDgnContext->GetDgnLevel (m_blockReference->layerId());
        UInt32      color = m_toDgnContext->GetDgnColor (acEntity->color().isByBlock() ? m_blockReference->color() : acEntity->color());
        UInt32      weight = m_toDgnContext->GetDgnWeight (AcDb::kLnWtByBlock == acEntity->lineWeight() ? m_blockReference->lineWeight() : acEntity->lineWeight());
        double      lsScale = m_blockReference->linetypeScale ();
        double      transparency = m_toDgnContext->GetDgnTransparency (m_blockReference->transparency());

        m_toDgnContext->SetElementSymbology (outText, level, &color, nullptr, &weight, nullptr, nullptr, lsScale, transparency, DgnElementClass::Primary);
        }

    if (needNewId)
        outText.GetElementP()->ehdr.uniqueId = m_toDgnContext->GetAndIncrementNextId ();

    // if the attribute/attrdef orients to paperspace viewports, set text/node element view independent
    if (m_toDgnContext->IsOrientationMatchedToLayout (acEntity))
        StrokeAnnotationElm::SetCanOrientRefElemToMatchSheetLayout (outText);

    // A field does not support true multiline-text so it has been dropped to a text node - add an assoc point for roundtrip.
    if (moreThan1Line)
        SetTextnodeForAttributeRoundtrip (outText, m_toDgnContext->ElementIdFromObject(m_blockReference), attributeTag);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateItemTypeFieldOrDropToText (EditElementHandleR outText, AcDbEntityP acEntity, UInt32 propIndex)
    {
    // only allow attribute or attrdef entities
    AcDbAttribute*              acAttribute = AcDbAttribute::cast (acEntity);
    AcDbAttributeDefinition*    acAttrdef = AcDbAttributeDefinition::cast (acEntity);
    if (nullptr == acAttribute && nullptr == acAttrdef)
        return  NullObject;

    bool                isAttrdef = nullptr != acAttrdef;
    const ACHAR*        attributeTag = isAttrdef ? acAttrdef->tagConst() : acAttribute->tagConst ();
    WString             propName;

    // build an internal EC property name from attribute tag
    ECNameValidation::EncodeToValidName (propName, WString(attributeTag));

    // determine visibility of this attribute or attrdef entity:
    bool                visible = m_visiblePerBlock;
    // attribute may be visiable only if the attribute/attrdef entity is visible
    if (visible)
        visible = (isAttrdef ? acAttrdef->visibility() == AcDb::kVisible : acAttribute->visibility() == AcDb::kVisible);
    // check attribute/attrdef's on/off flag
    if (visible)
        visible = isAttrdef ? acAttrdef->isInvisible() == Adesk::kFalse : acAttribute->isInvisible() == Adesk::kFalse;

    // create TextBlock from the attribute value
    bool                moreThan1Line = isAttrdef ? acAttrdef->isMTextAttributeDefinition() : acAttribute->isMTextAttribute ();
    bool                isEmpty = false, hasFields = false;
    UInt32              color = m_toDgnContext->GetDgnColor (acEntity->color());
    RealDwgStatus       status = RealDwgSuccess;
    TextBlock           textBlock (*m_toDgnContext->GetModel());

    if (moreThan1Line)
        status = m_toDgnContext->ProcessMText (textBlock, nullptr, isAttrdef ? acAttrdef->getMTextAttributeDefinitionConst() : acAttribute->getMTextAttributeConst(), color, &hasFields, m_blockReference);
    else
        status = m_toDgnContext->ProcessText (textBlock, nullptr, nullptr, nullptr, nullptr, nullptr, true, &hasFields, isAttrdef ? AcDbText::cast(acAttrdef) : AcDbText::cast(acAttribute), m_blockReference);

    // allow empty properties in an item type
    if (RealDwgSuccess != status && !(isEmpty = (EmptyText == status)))
        return  status;

    // set the string property on the item type instance - an attrdef item type is read-only - don't bother to set it.
    if (nullptr != m_definedItemInstance && nullptr == acAttrdef)
        status = this->SetTextProperty (propName, propIndex, textBlock, moreThan1Line, visible);

    // don't create invisible fields or empty texts:
    if (!visible || isEmpty)
        return  status;

    // optionally create a field pointing to the string property
    if (nullptr != m_definedItemInstance && RealDwgSuccess == status && !moreThan1Line && !hasFields)
        this->CreateField (textBlock, propName);

    // TFS#1031466:[Escalation]This Block / Attributes shifted to display
    // This fix resolves the use case mentioned in the above TFS item. It may not work for other cases
    if (m_toDgnContext->IsOrientationMatchedToLayout (acEntity) && nullptr != m_outputElement && SHARED_CELL_ELM == m_outputElement->GetElementType ())
        {
        textBlock.SetJustification (TextElementJustification::CenterMiddle, ParagraphRange (textBlock.Begin (), textBlock.End ()));

        DPoint3d newTextOrigin;
        m_outputElement->GetDisplayHandler ()->GetTransformOrigin (*m_outputElement, newTextOrigin);
        textBlock.SetUserOrigin (newTextOrigin);
        }

    // create either a field or a plain text element
    if (BSISUCCESS == TextHandlerBase::CreateElement(outText, nullptr, textBlock))
        status = this->SetTextElementHeaderFromAttribute (outText, acEntity, attributeTag, nullptr != acAttrdef, moreThan1Line);

    // post process an mtext attribute originally containing fields
    if (hasFields)
        m_toDgnContext->AddPostProcessObject (acEntity);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            AppendTextsToOutput (MSElementDescrP textChain)
    {
    MSElementDescrP     cellElmdscr = m_outputElement->ExtractElementDescr ();
    if (nullptr != cellElmdscr)
        {
        cellElmdscr->AddToChain (textChain);
        m_outputElement->SetElementDescr (cellElmdscr, true, false, m_toDgnContext->GetModel());
        }
    else
        {
        // should never happen...
        textChain->Release ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FindAttributeDefinitionIndex (UInt32& defIndex, AcDbAttributeDefinition*& attrDef, bvector<AcDbAttributeDefinition*>& attrdefs, const ACHAR* tagName)
    {
    /*-----------------------------------------------------------------------------------------------------------
    Get the sequential index of the attrdef existed in the block definition.  This index will be used to retrieve 
    the item type property which can potentially have a duplicated property name.
    -----------------------------------------------------------------------------------------------------------*/
    defIndex = 0;

    for (bvector<AcDbAttributeDefinition*>::iterator iter = attrdefs.begin(); iter != attrdefs.end(); iter++)
        {
        attrDef = AcDbAttributeDefinition::cast(*iter);
        defIndex++;

        if (nullptr != attrDef && 0 == wcscmp(attrDef->tagConst(), tagName))
            {
            // found the attrdef - invalidate the entry so we will not come back again.
            *iter = 0;
            return  true;
            }
        }

    // duplicate attrdef's have been converted as item type properties with unique names - they are not missing defs
    CustomPropertyCP    prop = nullptr;
    if (nullptr != m_definedItemType && nullptr != (prop = m_definedItemType->GetPropertyByName(tagName)))
        {
        defIndex = prop->GetTagDefId ();
        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateItemTypeFieldsOrDropAttributes ()
    {
    RealDwgStatus       status = RealDwgSuccess;
    MSElementDescrP     textChain = nullptr, lastText = nullptr;

    // use a working copy of attrdef list so we can invalidate entries as we go on the attribute list as a way of handling dup attributes:
    bvector<AcDbAttributeDefinition*>   attrdefs = m_attributeDefinitionList;
    UInt32                              defIndex = 0;

    // process variable attributes
    for (m_attributeIterator->start(); !m_attributeIterator->done(); m_attributeIterator->step())
        {
        AcDbAttributePointer    acAttribute (m_attributeIterator->objectId(), AcDb::kForRead);

        if (Acad::eOk != acAttribute.openStatus())
            continue;

        const ACHAR*            attributeTag = acAttribute->tagConst();
        AcDbAttributeDefinition*    acAttrdef = nullptr;

        // find an attrdef index from the attrdef list - an effort to process duplicated attributes:
        if (this->FindAttributeDefinitionIndex(defIndex, acAttrdef, attrdefs, attributeTag))
            {
            //ADO-896165: Found case where constant flag is different in AcDbAttribute and AcDbAttributeDefinition
            //ACAD LIST(cmd) says attr def is constant but ACAD property windows shows Attrdef constant is No
            //both the cases/properties considerd below
            bool isConst = false;
            if (acAttrdef != nullptr)
                isConst = acAttribute->isConstant() && acAttrdef->isConstant();
            else
                isConst = acAttribute->isConstant();

            if (isConst)
                continue;

            // create a field from the variable attribute
            EditElementHandle   textnode;
            status = this->CreateItemTypeFieldOrDropToText (textnode, acAttribute, defIndex);

            if (RealDwgSuccess == status && textnode.IsValid())
                MSElementDescr::InitOrAddToChainWithTail (&textChain, &lastText, textnode.ExtractElementDescr());
            }

        m_toDgnContext->ReportProgress ();
        }

    // process constant attributes from attrdefs
    Transform       blockTransform;
    bool            transformInitialized = false;

    for (auto& indexedAttrdef : m_constantAttributeDefinitionList)
        {
        // create a field from the constant attrdef
        EditElementHandle   textnode;
        status = this->CreateItemTypeFieldOrDropToText (textnode, indexedAttrdef.m_attrdef, indexedAttrdef.m_index);

        if (RealDwgSuccess == status && textnode.IsValid())
            {
            // the text created is in block's coordinate system - transform it to the instance's coordinate system:
            if (!transformInitialized)
                {
                RealDwgUtil::TransformFromGeMatrix3d (blockTransform, m_blockReference->blockTransform());

                DPoint3d    translation;
                blockTransform.GetTranslation (translation);
                m_toDgnContext->GetTransformToDGN().Multiply (translation);
                blockTransform.SetTranslation (translation);

                transformInitialized = true;
                }

            textnode.GetHandler(MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (textnode, TransformInfo(blockTransform));

            // lock the text node as it represents a constant attribute:
            textnode.GetElementP()->ehdr.locked = true;

            MSElementDescr::InitOrAddToChainWithTail (&textChain, &lastText, textnode.ExtractElementDescr());
            }

        m_toDgnContext->ReportProgress ();
        }

    // schedule item type write
    if (nullptr != m_definedItemInstance)
        m_definedItemInstance->ScheduleWriteChanges (*m_outputElement);

    // add the fields/texts to the output cell to be checked in altogether:
    if (nullptr != textChain)
        this->AppendTextsToOutput (textChain);

    if (EmptyText == status)
        status = RealDwgSuccess;

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CreateItemTypeFieldsForMissingAttrdefs (MissingAttrdefBlock& blockAttrdefs)
    {
    MSElementDescrP     textChain = nullptr, lastText = nullptr;
    UInt32              numProps = 0;
    RealDwgStatus       status = RealDwgSuccess;

    // loop through the attributes with missing attrdefs we have previously collected:
    for (auto& attribProp : blockAttrdefs.m_attributePropertyList)
        {
        AcDbAttributePointer    acAttribute (attribProp.m_attributeId, AcDb::kForRead);
        if (Acad::eOk == acAttribute.openStatus())
            {
            // create a field from the attribute whose attrdef is missing:
            EditElementHandle   textnode;

            status = this->CreateItemTypeFieldOrDropToText (textnode, acAttribute, attribProp.m_attrdefIndex);

            if (RealDwgSuccess == status && textnode.IsValid())
                MSElementDescr::InitOrAddToChainWithTail (&textChain, &lastText, textnode.ExtractElementDescr());

            if (EmptyText == status)
                status = RealDwgSuccess;

            if (RealDwgSuccess == status)
                numProps++;
            }

        m_toDgnContext->ReportProgress ();
        }

    // re-write the cell element with the new item type:
    if (nullptr != m_definedItemInstance && numProps > 0)
        {
        status = CantCreateItemType;

        if (BSISUCCESS == m_definedItemInstance->ScheduleWriteChanges(*m_outputElement) &&
            BSISUCCESS == m_outputElement->ReplaceInModel(m_outputElement->GetElementRef()))
            status = RealDwgSuccess;
        }

    // add the fields/texts to the output cell to be checked in altogether:
    if (nullptr != textChain)
        {
        if (RealDwgSuccess == status)
            {
            // put the texts in the same graphic group of the cell:
            UInt32      graphicGroup = m_outputElement->GetElementCP()->hdr.dhdr.grphgrp;

            if (graphicGroup != 0 && m_toDgnContext->GetSettings().GraphicGroupAttributes())
                {
                for (MSElementDescrP next = textChain; nullptr != next; next = next->h.next)
                    {
                    EditElementHandle   nextEeh(next, false, false, m_toDgnContext->GetModel());
                    ElementPropertiesSetter::SetGraphicGroup (nextEeh, graphicGroup);
                    }
                }

            m_toDgnContext->LoadElementIntoCache (textChain);
            }
        else
            {
            // free elmdscr on failure
            textChain->Release ();
            }
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            GetItemTypeNameForMissingAttrdefs (WStringR itemtypeName, WCharCP blockName, const AcDbObjectId& objectId)
    {
    itemtypeName.Sprintf (L"%ls_%I64d", (blockName == nullptr) ? L"" : blockName, m_toDgnContext->ElementIdFromObjectId(objectId));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
size_t          CollectMissingAttrdefs (WCharCP blockName)
    {
    /*------------------------------------------------------------------------------------------------------
    This method collects attributes which do not have correspondent attrdefs in the block.  We cache them for
    the post process where we can add them into the attrdef library in one shot, as opposed to updating the
    attrdef library once per block.  Writing/reading schema has proven to be a very expensive operation as
    having been witnessed in TFS 263931.
    ------------------------------------------------------------------------------------------------------*/
    MissingAttrdefBlock     attrdefBlock;
    UInt32                  attrdefIndex = 1;

    // a block may not contain all attrdef's for attributes on a block reference - we will create a separate item type for them.
    for (m_attributeIterator->start(); !m_attributeIterator->done(); m_attributeIterator->step())
        {
        AcDbAttributePointer    acAttribute (m_attributeIterator->objectId(), AcDb::kForRead);
        if (Acad::eOk != acAttribute.openStatus() || acAttribute->isConstant() == Adesk::kTrue)
            continue;

        // does this attribute have an attrdef?
        const ACHAR*        attributeTag = acAttribute->tagConst ();
        if (nullptr == attributeTag || (nullptr != m_definedItemType && nullptr != m_definedItemType->GetPropertyByName(attributeTag)))
            continue;

        // missing attrdef - add it the missing def list
        ACHAR*              attributeString = nullptr;
        if (acAttribute->isMTextAttribute())
            {
            const AcDbMText*    mtext = acAttribute->getMTextAttribute ();
            if (nullptr != mtext)
                attributeString = mtext->text ();
            }
        else
            {
            attributeString = acAttribute->textString ();
            }

        AttributeProp       attribProp;
        attribProp.m_propertyName.assign (attributeTag);
        attribProp.m_propertyValue = WString (nullptr == attributeString ? L"" : attributeString);
        attribProp.m_attrdefIndex = attrdefIndex++;
        attribProp.m_attributeId = acAttribute->objectId ();

        attrdefBlock.m_attributePropertyList.push_back (attribProp);

        if (nullptr != attributeString)
            acutDelString (attributeString);

        m_toDgnContext->ReportProgress ();
        }

    size_t      numMissingAttrdefs = attrdefBlock.Count ();

    if (numMissingAttrdefs > 0)
        {
        attrdefBlock.m_blockRecordId = m_blockReference->blockTableRecord ();

        this->GetItemTypeNameForMissingAttrdefs (attrdefBlock.m_itemtypeName, blockName, m_blockReference->objectId());

        bpair <ElementId, MissingAttrdefBlock>  newBlockEntry (m_toDgnContext->ElementIdFromObject(m_blockReference), attrdefBlock);
        m_toDgnContext->GetMissingBlockAttrdefList().insert (newBlockEntry);
        }

    return  numMissingAttrdefs;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
ItemTypeP       GetItemTypeById (UInt32 itemtypeId)
    {
    ItemTypeLibraryP    itemtypeLib = m_toDgnContext->GetAttrdefItemTypeLibrary ();
    if (nullptr == itemtypeLib)
        return  nullptr;

    return itemtypeLib->GetItemTypeById (itemtypeId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            TransformTextnodes (MSElementDescrP& textnodeChain, TransformCR transform, ElementId targetId)
    {
    TransformInfo   moveText (transform);
    UInt32          count = 0;

    for (MSElementDescrP textElm = textnodeChain; nullptr != textElm; textElm = textElm->h.next)
        {
        // save the chain links
        MSElementDescrP     prev = textElm->h.previous;
        MSElementDescrP     next = textElm->h.next;

        textElm->h.previous = textElm->h.next = nullptr;

        EditElementHandle   eeh (textElm, true, false, m_toDgnContext->GetModel());
        if (eeh.IsValid())
            {
            DisplayHandlerP displayHandler = eeh.GetDisplayHandler ();
            if (nullptr == displayHandler)
                continue;

            displayHandler->ApplyTransform (eeh, moveText);
            displayHandler->ValidateElementRange (eeh, true);

            // add an assoc point for round trip purpose
            SetTextnodeForAttributeRoundtrip (eeh, targetId, nullptr);

            // get a new elmdscr - the old one from the copy list will be released:
            textElm = eeh.ExtractElementDescr ();
            }

        textElm->el.ehdr.uniqueId = 0;

        // update the chain links
        if (nullptr != prev)
            prev->h.next = textElm;
        textElm->h.previous = prev;
        if (nullptr != next)
            next->h.previous = textElm;
        textElm->h.next = next;
        if (0 == count++)
            textnodeChain = textElm;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            AssociateTextnodesToCellHeader (MSElementDescrP& textnodeChain, MSElementDescrP targetCell)
    {
    if (nullptr == targetCell)
        return;

    if (0 == targetCell->el.ehdr.uniqueId || INVALID_ELEMENTID == targetCell->el.ehdr.uniqueId)
        targetCell->el.ehdr.uniqueId = m_toDgnContext->GetAndIncrementNextId ();

    UInt32      count = 0;
    for (MSElementDescrP textnodeElm = textnodeChain; nullptr != textnodeElm; textnodeElm = textnodeElm->h.next)
        {
        MSElementDescrP     prev = textnodeElm->h.previous;
        MSElementDescrP     next = textnodeElm->h.next;

        textnodeElm->h.previous = textnodeElm->h.next = nullptr;

        EditElementHandle   eeh (textnodeElm, true, false, m_toDgnContext->GetModel());
        if (eeh.IsValid())
            {
            SetTextnodeForAttributeRoundtrip (eeh, targetCell->el.ehdr.uniqueId, nullptr);
            textnodeElm = eeh.ExtractElementDescr ();
            }

        if (nullptr != prev)
            prev->h.next = textnodeElm;
        textnodeElm->h.previous = prev;
        if (nullptr != next)
            next->h.previous = textnodeElm;
        textnodeElm->h.next = next;
        if (0 == count++)
            textnodeChain = textnodeElm;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   CopyTextnodesForMInsertInstances ()
    {
    // collect the text nodes created from the attributes of this minsert entity and appended at the end of the output cell element:
    MSElementDescrP     existingTextChain = m_outputElement->GetElementDescrP ();
    for (; nullptr != existingTextChain; existingTextChain = existingTextChain->h.next)
        {
        if (TEXT_NODE_ELM == existingTextChain->el.ehdr.type)
            break;
        }

    if (nullptr == existingTextChain || TEXT_NODE_ELM != existingTextChain->el.ehdr.type)
        return  RealDwgSuccess;

    // extact master cell origin
    DPoint3d            origin;
    if (BSISUCCESS != CellUtil::ExtractOrigin(origin, *m_outputElement))
        return  BadData;

    // now walk through the child cells anc copy the text chain with desired offset from the child cell origin:
    UInt32              count = 0;
    MSElementDescrP     newTextChain = nullptr, lastText = nullptr;
    MSElementDescrP     previousSaved = existingTextChain->h.previous;

    existingTextChain->h.previous = nullptr;

    for (ChildEditElemIter childCell(*m_outputElement, ExposeChildrenReason::Count); childCell.IsValid(); childCell = childCell.ToNext())
        {
        // skip the first child, which cooresponds to the original text chain
        if (count++ < 1)
            continue;

        DPoint3d        childOrigin;
        if (BSISUCCESS != CellUtil::ExtractOrigin(childOrigin, childCell))
            continue;

        DPoint3d        childOffset = childOrigin;
        childOffset.Subtract (origin);

        Transform       childTransform = Transform::FromIdentity ();
        childTransform.SetTranslation (childOffset);

        MSElementDescrP copyElmdscr = nullptr;
        if (BSISUCCESS != existingTextChain->Duplicate(&copyElmdscr, true, false))
            break;  // shouldn't happen

        ElementId       cellId = childCell.GetElementId ();
        if (0 == cellId || INVALID_ELEMENTID == cellId)
            cellId = m_toDgnContext->GetAndIncrementNextId ();
        childCell.GetElementP()->ehdr.uniqueId = cellId;

        this->TransformTextnodes (copyElmdscr, childTransform, cellId);

        // save the text node copy
        MSElementDescr::InitOrAddToChainWithTail (&newTextChain, &lastText, copyElmdscr);
        }

    if (nullptr != newTextChain)
        {
        MSElementDescrP     cellElmdscr = m_outputElement->GetElementDescrP ();
        
        if (CELL_HEADER_ELM == cellElmdscr->el.ehdr.type)
            {
            // associate existing text nodes to the first child shared cell
            this->AssociateTextnodesToCellHeader (existingTextChain, cellElmdscr->h.firstElem);

            // append existing and the copies of text nodes as children of the cell
            lastText = cellElmdscr->h.next = nullptr;
            cellElmdscr->AppendChild (&lastText, existingTextChain);
            cellElmdscr->AppendChild (&lastText, newTextChain);
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Unexpected element type from an MINSERT: %d, ID=%I64d - fields pulled out of the cell\n", cellElmdscr->el.ehdr.type, m_toDgnContext->ElementIdFromObject(m_blockReference));
            existingTextChain->h.previous = previousSaved;
            cellElmdscr->AddToChain (newTextChain);
            }
        }
    else
        {
        DIAGNOSTIC_PRINTF ("Unexpected empty item type fields from an MINSERT ID=%I64d\n", m_toDgnContext->ElementIdFromObject(m_blockReference));
        existingTextChain->h.previous = previousSaved;
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            InitializeVisibility ()
    {
    // check the layer frozen status of the block reference - if the block reference is on a frozen layer, attribites become invisible
    AcDbLayerTableRecordPointer     insertLayer (m_blockReference->layerId(), AcDb::kForRead);
    if (Acad::eOk == insertLayer.openStatus())
        m_isBlockReferenceLayerFrozen = insertLayer->isFrozen ();
    
    // attribute may be visible only if attmode=1 or 2 and the block is visible:
    m_visiblePerBlock = m_blockReference->visibility() == AcDb::kVisible && m_toDgnContext->GetDatabase()->attmode() != ATTRIBMODE_INVISIBLE;
    }


public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   Convert ()
    {
    if (nullptr == m_outputElement || !m_outputElement->IsValid() || nullptr == m_blockReference || nullptr == m_toDgnContext)
        return  NullObject;

    AcDbBlockTableRecordPointer acBlock (m_blockReference->blockTableRecord(), AcDb::kForRead);
    if (Acad::eOk != acBlock.openStatus())
        return  CantOpenObject;

    // get the block name which will be used as the basis for the item type name
    const ACHAR*        blockName = nullptr;
    if (Acad::eOk != acBlock->getName(blockName) || nullptr == blockName)
        return  BadData;

    bool                hasAttrdefs = acBlock->hasAttributeDefinitions ();

    // if neither the block nor the insert has attributes to follow, no need to go any futher
    m_attributeIterator = m_blockReference->attributeIterator ();
    if (nullptr == m_attributeIterator || (m_attributeIterator->done() && !hasAttrdefs))
        return  RealDwgSuccess;

    // a block may not have any attrdef
    AcDbBlockTableRecordIterator*   blockIterator = nullptr;
    if (hasAttrdefs && Acad::eOk == acBlock->newIterator(blockIterator))
        {
        UInt32          defIndex = 0;

        for (; !blockIterator->done(); blockIterator->step())
            {
            AcDbObjectId            entityId;
            if (Acad::eOk != blockIterator->getEntityId(entityId) || !entityId.isValid())
                continue;

            AcDbAttributeDefinition*    acAttrdef = nullptr;
            if (Acad::eOk == acdbOpenObject(acAttrdef, entityId, AcDb::kForRead))
                {
                m_attributeDefinitionList.push_back (acAttrdef);

                defIndex++;

                if (acAttrdef->isConstant())
                    m_constantAttributeDefinitionList.push_back (IndexedAttrdef(defIndex, acAttrdef));
                }
            }

        delete blockIterator;
        }

    this->InitializeVisibility ();

    // set item type name based on the uniqueness of the block name
    WString             itemtypeName;
    if (acBlock->isAnonymous() || 0 == blockName[0])
        this->GetItemTypeNameForMissingAttrdefs (itemtypeName, blockName, acBlock->objectId());
    else
        itemtypeName.assign (blockName);

    // save our attrdef schema it not already saved (a case if this is a nested block).
    ItemTypeLibraryP    itemtypeLib = m_toDgnContext->GetAttrdefItemTypeLibrary ();
    if (nullptr != itemtypeLib && !itemtypeLib->IsPersistent())
        itemtypeLib->Update (false);

    RealDwgStatus       status = CantCreateItemType;

    // get the attrdef item type that was created from the block definition
    m_definedItemType = m_toDgnContext->GetItemTypeByName (itemtypeName.GetWCharCP(), false);
    if (nullptr != m_definedItemType || !hasAttrdefs)
        {
        UInt32          definedId = nullptr == m_definedItemType ? 0 : m_definedItemType->GetId ();

        if (nullptr != m_definedItemType)
            {
            // attach the item type(s) to the cell header
            CustomItemHost      itemHost (*m_outputElement, true);
            DgnECInstancePtr    definedInstance;

            if (nullptr != m_definedItemType)
                {
                definedInstance = itemHost.ApplyCustomItem (*m_definedItemType);
                if (definedInstance.IsValid())
                    m_definedItemInstance = definedInstance.get ();
                }

            status = this->CreateItemTypeFieldsOrDropAttributes ();
            }
        }

    // find and cache attributes whose attrdefs are missing from the block definition - schedule saving them in PostConvert below.
    if (this->CollectMissingAttrdefs(blockName) > 0)
        m_toDgnContext->AddItemTypeHostForPostProcess (m_blockReference);

    // if for any reason an item type is not created for this insert entity, force dropping the attributes
    if (CantCreateItemType == status)
        {
        m_definedItemInstance = nullptr;

        status = this->CreateItemTypeFieldsOrDropAttributes ();
        }

    if (m_isMInsert)
        this->CopyTextnodesForMInsertInstances ();

    // put the cell + tags in a graphic group:
    m_toDgnContext->SetElementsInNextGraphicGroup (*m_outputElement);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   PostConvert (MissingAttrdefBlock& blockAttrdefs)
    {
    /*----------------------------------------------------------------------------------------------------
    In an effort to improve the performance of item type conversion we handle missing attrdefs in 3 steps:

        1) In the normal process, i.e. above Convert method, we cache attributes whose correspondent
           attrdefs are missing from the block definition.
        2) At the end of normal process, we create item types for all missing attrdefs. This allows us to
           re-write the attrdef library schema only once for all missing attrdefs.
        3) During the post process, this method gets called for each and every block reference we have 
           previously cached. The missing item types are attached to the cell and fields are created here.
    ----------------------------------------------------------------------------------------------------*/
    if (nullptr == m_outputElement || !m_outputElement->IsValid() || nullptr == m_blockReference || nullptr == m_toDgnContext || blockAttrdefs.Count() < 1)
        return  NullObject;
    
    this->InitializeVisibility ();

    RealDwgStatus       status = CantCreateItemType;
    m_definedItemType = m_toDgnContext->GetItemTypeByName (blockAttrdefs.m_itemtypeName.GetWCharCP(), false);

    if (nullptr != m_definedItemType)
        {
        // attach the item type to the cell header
        CustomItemHost      itemHost (*m_outputElement, true);

        if (nullptr != m_definedItemType)
            {
            DgnECInstancePtr    ecInstance = itemHost.ApplyCustomItem (*m_definedItemType);
            if (ecInstance.IsValid())
                {
                m_definedItemInstance = ecInstance.get ();
                status = this->CreateItemTypeFieldsForMissingAttrdefs (blockAttrdefs);
                }
            }
        }

    return  status;
    }

};  // ConvertAttributesToItemTypes


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::AddItemTypesForMissingAttrdefs ()
    {
    /*----------------------------------------------------------------------------------------------------------
    By now we should have collected all missing block attrdefs that need to be converted as item types and saved
    into our attrdef library.  This allows us to only save the library once for all missing attrdefs as opposed
    to updating the library as soon as we get missing attrdefs - an effort to avoid the expensive schema write,
    ----------------------------------------------------------------------------------------------------------*/
    if (this->GetSettings().AttributesAsTags() || m_missingBlockAttrdefList.empty())
        return  RealDwgSuccess;

    size_t      numAdded = 0;

    // loop through collected blocks of missing attrdefs
    for (T_AttrdefBlockList::const_iterator blockIter = m_missingBlockAttrdefList.begin(); blockIter != m_missingBlockAttrdefList.end(); blockIter++)
        {
        // try creating a new item type for the block reference on which the missing attrdefs were found:
        ElementId           cellId = blockIter->first;
        MissingAttrdefBlock attrdefBlock = blockIter->second;
        bool                isNew = false;
        ItemTypeP           itemType = this->GetItemTypeByName (attrdefBlock.m_itemtypeName.GetWCharCP(), true, &isNew);

        if (nullptr != itemType && isNew)
            {
            // create item type properties from attributes and add to the item type:
            for (auto& attribProp : attrdefBlock.m_attributePropertyList)
                AddStringProperty (itemType, attribProp.m_propertyName.GetWCharCP(), attribProp.m_attrdefIndex, attribProp.m_propertyValue.GetWCharCP(), false);

            numAdded++;
            }
        }

    // save all newly added item types to the attrdef library - this is an expensive operation!
    if (numAdded > 0 && m_attrdefItemtypeLibrary.IsValid() && m_attrdefItemtypeLibrary->Update(false))
        {
        DgnFileP        dgn = this->GetFile ();
        AcDbDatabaseP   dwg = this->GetDatabase ();
        if (nullptr == dwg || nullptr == dgn)
            return  BadData;

        // add the missing itemtype fields for each and every host cells
        for (auto& cellId : m_itemtypeHostsForPostProcess)
            {
            // get DGN cell
            ElementRefP     elementRef = dgn->FindByElementId (cellId, false);
            if (nullptr == elementRef)
                continue;

            EditElementHandle   eeh(elementRef);
            if (!eeh.IsValid())
                continue;

            // get DWG block reference
            AcDbObjectId    insertId;
            if (Acad::eOk != dwg->getAcDbObjectId (insertId, false, this->DBHandleFromElementId(cellId), 0))
                continue;
            
            AcDbBlockReferencePointer   insert(insertId, AcDb::kForRead);
            if (Acad::eOk != insert.openStatus())
                continue;

            auto    found = m_missingBlockAttrdefList.find (this->ElementIdFromObject(insert));
            if (found != m_missingBlockAttrdefList.end())
                {
                ConvertAttributesToItemTypes    attributesToItemTypes (eeh, insert, RealDwgUtil::IsValidMInsert(insert), this);
                if (RealDwgSuccess != attributesToItemTypes.PostConvert(found->second))
                    DIAGNOSTIC_PRINTF ("Failed converting missing attrdefs %ls to item type for INSERT ID=%I64d\n", found->second.m_itemtypeName.c_str(), eeh.GetElementId());
                }
            }
        return  RealDwgSuccess;
        }

    return  CantCreateItemType;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::AddItemTypeHostForPostProcess (AcDbObjectP acObject)
    {
    // save block reference ID for post adding missing fields
    AcDbObjectId    id = acObject->objectId ();
    if (id.isValid())
        m_itemtypeHostsForPostProcess.push_back (this->ElementIdFromObjectId(id));
    }


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          03/15
+===============+===============+===============+===============+===============+======*/
class           ConvertItemTypeToAttribute
{
private:
    AcDbBlockReference*                     m_blockReference;
    AcDbBlockTableRecord*                   m_blockTableRecord;
    WString                                 m_blockName;
    ElementHandleCP                         m_cellElement;
    ConvertFromDgnContext*                  m_fromDgnContext;
    bvector<AcDbAttributeDefinition*>       m_existingAttributeDefinitionList;
    ObjectIdArray                           m_unsavedExistingAttributes;
    bvector<ElementRefP>                    m_dependentTexts;
    bset<ElementRefP>                       m_dependentTextsProcessed;
    WString                                 m_elementIdSuffix;
    bool                                    m_isMInsertBlock;
    bool                                    m_allowEmptyProps;

public:
    ConvertItemTypeToAttribute (AcDbBlockReference* acBlockReference, ElementHandleCR cellElement, ConvertFromDgnContextR context)
        {
        m_blockReference = acBlockReference;
        m_blockTableRecord = nullptr;
        m_cellElement = &cellElement;
        m_fromDgnContext = &context;
        m_existingAttributeDefinitionList.clear ();
        m_unsavedExistingAttributes.clear ();
        m_dependentTexts.clear ();
        m_dependentTextsProcessed.clear ();
        m_elementIdSuffix.clear ();
        m_blockName.clear ();
        m_allowEmptyProps = context.GetSettings().AllowEmptyItemTypeProperties();

        AcDbMInsertBlock*   acMInsert = AcDbMInsertBlock::cast (acBlockReference);
        m_isMInsertBlock = nullptr != acMInsert && acMInsert->rows() > 0 && acMInsert->columns() > 0;
        }

    ~ConvertItemTypeToAttribute ()
        {
        for (auto& attrdef : m_existingAttributeDefinitionList)
            {
            if (!attrdef->isErased())
                attrdef->close ();
            }
        }

private:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/15
+---------------+---------------+---------------+---------------+---------------+------*/
UInt16          GetAttributeDefinitionIndex (CustomPropertyCR prop)
    {
    /*----------------------------------------------------------------------------------------------------------
    A property has a tagdef ID if the item type was either created from a DWG attrdef or upgraded from a tagset.
    When item type class name matches block name, we can treat the tagdefID as a unique ID for renaming purpose.
    Otherwise we cannot depend on tagdefID to track the name change.  Attaching an existing attrdef item type to
    a different shared cell element must be treated as a new item type to be added into the block def.

    If the property was created from a DWG attrdef, the tagdefID is a 1-based index.  0 is returned as an invalid
    value.
    ----------------------------------------------------------------------------------------------------------*/
    UInt16      tagdefId = prop.GetTagDefId ();
    if (0xFFFF == tagdefId)
        return  0;

    // a tagdefID is valid only within the same item type that was originally created from this block:
    if (m_blockName.CompareToI(prop.GetContainer().GetName()) == 0)
        return  tagdefId;

    return  0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DeduplicateAttributeTagName (WStringR tagName)
    {
    WString     newName = tagName;

    for (int suffixIndex = 1; suffixIndex < 1000; suffixIndex++)
        {
        auto    found = std::find_if (m_existingAttributeDefinitionList.begin(), m_existingAttributeDefinitionList.end(), [&](AcDbAttributeDefinition* arg) { return 0 == wcscmp(arg->tagConst(), newName.c_str()); });
        if (m_existingAttributeDefinitionList.end() == found)
            {
            tagName = newName;
            return  true;
            }

        WString::Sprintf (newName, L"%ls_%d", tagName.GetWCharCP(), suffixIndex);
        }

    DIAGNOSTIC_PRINTF ("Failed to deduplicate tag name for %ls\n", tagName.c_str());
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            OpenExistingAttributeDefinitions ()
    {
    AcDbBlockTableRecordIterator*   iterator;
    if (Acad::eOk == m_blockTableRecord->newIterator(iterator))
        {
        for (; !iterator->done(); iterator->step())
            {
            AcDbObjectId    entityId;
            if (Acad::eOk == iterator->getEntityId(entityId))
                {
                AcDbAttributeDefinition*    acAttrdef = nullptr;
                if (Acad::eOk == acdbOpenObject(acAttrdef, entityId, AcDb::kForWrite))
                    {
                    m_existingAttributeDefinitionList.push_back (acAttrdef);
                    }
                }
            }

        delete iterator;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            CollectExistingAttributes ()
    {
    // collect existing attributes on the block reference:
    m_unsavedExistingAttributes.clear ();

    AcDbObjectIterator*     attributeIter = m_blockReference->attributeIterator ();
    if (nullptr != attributeIter)
        {
        for (; !attributeIter->done(); attributeIter->step())
            m_unsavedExistingAttributes.push_back (attributeIter->objectId());

        delete attributeIter;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            CollectDependentTexts ()
    {
    // find text nodes that contain fields targeting to the cell
    ElementRefP     cellRef = m_cellElement->GetElementRef ();
    if (nullptr == cellRef)
        return;

    ChildElemIter   firstCell (*m_cellElement);
    ElementRefP     firstCellRef = firstCell.GetElementRef ();

    for (DependentElemRef dep = cellRef->GetFirstDependent(); nullptr != dep; dep = dep->GetNext())
        {
        ElementRefP     elemRef = dep->GetElementRef ();
        if (nullptr == elemRef || elemRef->IsDeleted())
            continue;

        switch (elemRef->GetElementType())
            {
            case TEXT_ELM:
                {
                // want text node
                ElementRefP     parent = elemRef->GetParentElementRef ();
                if (nullptr != parent && TEXT_NODE_ELM == parent->GetElementType())
                    elemRef = parent;
                //fall through
                }
            case TEXT_NODE_ELM:
                break;
            default:
                continue;
            }

        if (m_isMInsertBlock && cellRef == elemRef->GetParentElementRef())
            {
            // for an minsert, skip the texts that originally came as a copy of text fields:
            AssocPoint  assocPoint;
            AssociativePoint::InitOrigin (assocPoint, NULL);

            ElementRefP target = nullptr;
            DgnModelP   model = m_fromDgnContext->GetModel ();

            if (BSISUCCESS == AssociativePoint::ExtractPoint(assocPoint, ElementHandle(elemRef), 0, 1) &&
                BSISUCCESS == AssociativePoint::GetRoot(&target, nullptr, nullptr, nullptr, assocPoint, model, 0) &&
                firstCellRef != target)
                continue;
            }

        m_dependentTexts.push_back (elemRef);
        }

    // sort text nodes such that dup texts with lower ID's are used for the attrdef templates, preventing unnecessary change of original attrdefs:
    if (m_fromDgnContext->SavingChanges())
        std::sort (m_dependentTexts.begin(), m_dependentTexts.end(), [&](ElementRefP textnode1, ElementRefP textnode2){ return  textnode1->GetElementId() < textnode2->GetElementId(); });
    }
        
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            SetAttributeDefinitionHeader (AcDbAttributeDefinition* acAttrdef)
    {
    if (m_dependentTexts.empty())
        {
        // there is no dependent text attached to the cell, use the first graphic element in the cell
        m_fromDgnContext->UpdateEntitySymbologyAndLevelFromElement (acAttrdef, m_fromDgnContext->GetSymbologyTemplate(m_cellElement->GetElementDescrCP()));
        return;
        }
    
    // use the first dependent text as the symbology & level template element for the attrdef
    EditElementHandle       text0 (m_dependentTexts.front());
    m_fromDgnContext->UpdateEntitySymbologyAndLevelFromElement (acAttrdef, text0.GetElementDescrCP());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            GetAttrdefOriginFromTextOrigin (AcGePoint3d& attrdefOrigin, AcGePoint3d const& textOrigin)
    {
    // get the attrdef origin by subtracting attribute origin from the origin of the block reference:
    AcGeMatrix3d    blockMatrix = m_blockReference->nonAnnotationBlockTransform ();
    AcGeVector3d    baseOffset = textOrigin - m_blockReference->position();

    blockMatrix.invert ();
    baseOffset.transformBy (blockMatrix);

    attrdefOrigin.setToSum (m_blockTableRecord->origin(), baseOffset);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAttrdefFromAttribute (AcDbAttributeDefinition* acAttrdef, AcDbAttribute* acAttribute)
    {
    // copy attribute's properties to attrdef
    Acad::ErrorStatus   es = acAttrdef->setPropertiesFrom (acAttribute);
    if (Acad::eOk != es)
        return  EntityError;

    AcDbMText*          acMText = acAttribute->getMTextAttribute ();
    AcGePoint3d         attrdefOrigin;
    double              blockScale = m_blockReference->nonAnnotationScaleFactors().sy;
    if (nullptr != acMText)
        {
        // set mtext data
        this->GetAttrdefOriginFromTextOrigin (attrdefOrigin, acMText->location());

        double          textHeight = acMText->textHeight ();
        if (blockScale > TOLERANCE_ZeroScale)
            {
            // set text height to be the inverse scale of the block reference
            textHeight /= blockScale;
            acMText->setTextHeight (textHeight);
            }

        es = acMText->setLocation (attrdefOrigin);
        es = acAttrdef->setMTextAttributeDefinition (acMText);

        if (Acad::eOk != es)
            DIAGNOSTIC_PRINTF ("Error setting mtext origin for attrdef %ls [%ls]\n", acAttribute->tagConst(), acadErrorStatusText(es));

        delete acMText;
        }
    else
        {
        // set dtext data
        double      textHeight = acAttribute->height ();
        // set text height to be the inverse scale of the block reference
        if (blockScale > TOLERANCE_ZeroScale)
            textHeight /= blockScale;
        
        es = acAttrdef->setHeight (textHeight);
        es = acAttrdef->setWidthFactor (acAttribute->widthFactor());
        es = acAttrdef->setJustification (acAttribute->justification());
        es = acAttrdef->setHorizontalMode (acAttribute->horizontalMode());
        es = acAttrdef->setVerticalMode (acAttribute->verticalMode());
        es = acAttrdef->setTextString (acAttribute->textStringConst());
        es = acAttrdef->setTextStyle (acAttribute->textStyle());
        es = acAttrdef->setThickness (acAttribute->thickness());
        es = acAttrdef->setOblique (acAttribute->oblique());

        // use set the attrdef angle from attribute's relative rotation to the cell:
        double          angle = acAttribute->rotation() - m_blockReference->rotation();
        es = acAttrdef->setRotation (angle);

        this->GetAttrdefOriginFromTextOrigin (attrdefOrigin, acAttribute->position());
        es = acAttrdef->setPosition (attrdefOrigin);

        if (AcDb::kTextLeft != acAttrdef->horizontalMode() || AcDb::kTextBase != acAttrdef->verticalMode())
            {
            this->GetAttrdefOriginFromTextOrigin (attrdefOrigin, acAttribute->alignmentPoint());
            es = acAttrdef->setAlignmentPoint (attrdefOrigin);
            }

        es = acAttrdef->setInvisible (acAttribute->isInvisible());
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SaveAttributeDefinition (CustomPropertyCR prop, AcDbAttribute* acAttribute)
    {
    if (nullptr == acAttribute)
        return  RealDwgStatus::EntityError;

    AcDbAttributeDefinition*    acAttrdef = nullptr;
    UInt16                      tagdefId = this->GetAttributeDefinitionIndex (prop);
    const ACHAR*                tagName = acAttribute->tagConst ();

    // the input attribute should have the tag name properly set, but have a safeguard here anyway:
    if (nullptr == tagName || 0 == tagName[0])
        tagName = (const ACHAR*) prop.GetName ();

    // we used 1-base tagdef ID for attrdef's
    if (tagdefId > 0 && --tagdefId < m_existingAttributeDefinitionList.size())
        {
        // round trip DWG attrdef which may not have an unique name:
        acAttrdef = m_existingAttributeDefinitionList.at (tagdefId);
        // since the item type property has a valid tagdefId, allow renaming of attrdef:
        acAttrdef->setTag (acAttribute->tagConst());
        }
    else
        {
        // non-attrdef property which should have unique name - override the value of existing attrdef
        auto    found = std::find_if (m_existingAttributeDefinitionList.begin(), m_existingAttributeDefinitionList.end(), [&](AcDbAttributeDefinition* arg) { return 0 == wcscmp(arg->tagConst(), tagName); });
        if (m_existingAttributeDefinitionList.end() != found)
            acAttrdef = *found;
        }

    if (nullptr == acAttrdef)
        {
        acAttrdef = new AcDbAttributeDefinition ();
        this->SetAttributeDefinitionHeader (acAttrdef);
        acAttrdef->setTag (tagName);
        }

    // set the text part of attrdef from already saved attribute
    if (nullptr != acAttribute)
        this->SetAttrdefFromAttribute (acAttrdef, acAttribute);

    ECValue     propValue;
    if (prop.GetDefaultValue(propValue))
        acAttrdef->setTextString (propValue.ToString().c_str());

    acAttrdef->setConstant (prop.IsReadOnly());
    
    if (!acAttrdef->objectId().isValid())
        {
        m_blockTableRecord->appendAcDbEntity (acAttrdef);
        acAttrdef->close ();
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   AttributeFromTextBlock (AcDbAttribute* acAttribute, TextBlockR textBlock, EditElementHandleCR eeh)
    {
    AcDbMText*      acMText = new AcDbMText ();
    RealDwgStatus   status = m_fromDgnContext->MTextFromTextBlock (acMText, textBlock, eeh, false);

    if (RealDwgSuccess == status)
        status = ConvertItemTypeToAttribute::SetAcDbAttributeOrDropMText (acAttribute, acMText, *m_fromDgnContext);

    if (acMText->objectId().isValid())
        acMText->close ();
    else
        delete acMText;

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            HasSingleTextField (TextBlockCR textBlock)
    {
    // see if we can create a single line attribute from the text field:
    if (textBlock.GetParagraphCount() > 1)
        return false;

    ParagraphCP paragraph = textBlock.GetParagraph (0);
    if (nullptr != paragraph && paragraph->GetLineCount() > 1)
        return false;

    Caret       start = textBlock.Begin ();
    if (start.IsInField())
        {
        if (BSISUCCESS != start.MoveToNextRun() || start.IsAtEnd())
            return  true;

        RunCP   run = start.GetCurrentRunCP ();
        if (nullptr != run && run->ContainsOnlyWhitespace())
            return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId    FindExistingAttribute (WStringCR attributeTag, UInt16 tagdefId, bool invisibleOnly)
    {
    AcDbObjectIterator* attributeIter = m_blockReference->attributeIterator ();
    if (nullptr == attributeIter)
        return  AcDbObjectId::kNull;

    AcDbObjectId    foundId;
    UInt32          index = 0;

    for (attributeIter->start(); !attributeIter->done(); attributeIter->step())
        {
        // input tagdefId is a 1-based attrdef index we originally created
        index++;

        AcDbAttributePointer    acAttribute (attributeIter->objectId(), AcDb::kForRead);
        if (Acad::eOk == acAttribute.openStatus() && attributeTag.StartsWith(acAttribute->tagConst()))
            {
            // if we are looking for invisible only attribute, check that now
            if (invisibleOnly && acAttribute->isInvisible() == Adesk::kFalse && acAttribute->visibility() == AcDb::kVisible)
                continue;

            // pick up either the first attribute found or the one matching both name and index:
            if (foundId.isNull())
                foundId = acAttribute->objectId ();
            if (tagdefId == index)
                {
                foundId = acAttribute->objectId ();
                break;
                }
            }
        }

    delete attributeIter;

    return  foundId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus    CheckInAttribute (AcDbAttribute* acAttribute, ElementId attributeId = 0)
    {
    AcDbObjectId    idCheckedIn;

    if (nullptr != acAttribute && !acAttribute->objectId().isValid())
        {
        // if it is the first attribute we are appending to the block reference, check in the block reference first:
        if (!m_blockReference->objectId().isValid())
            idCheckedIn = m_fromDgnContext->AddEntityToCurrentBlock (m_blockReference, m_cellElement->GetElementId());

        idCheckedIn = m_fromDgnContext->AddAttributeToBlockReference (m_blockReference, acAttribute, attributeId);
        }

    return  idCheckedIn.isValid() ? RealDwgSuccess : NullObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            RemoveAttributeFromExistingList (AcDbObjectId savedAttrId)
    {
    // remove processed attribute object ID from existing attribute list:
    if (!m_fromDgnContext->SavingChanges() || !savedAttrId.isValid())
        return  false;

    auto    found = std::find_if (m_unsavedExistingAttributes.begin(), m_unsavedExistingAttributes.end(), [&](AcDbObjectId arg) { return arg == savedAttrId; });

    if (found != m_unsavedExistingAttributes.end())
        {
        // the saved attribute found in the unsaved list - remove it
        m_unsavedExistingAttributes.erase (found);
        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            DeleteUnsavedAttributesFromBlockReference ()
    {
    // at the end whatever is remained in the unsaved attribute list should be deleted from the block reference:
    for (auto& remainingId : m_unsavedExistingAttributes)
        {
        AcDbAttributePointer    remainingAttribute (remainingId, AcDb::kForWrite);
        if (Acad::eOk == remainingAttribute.openStatus())
            remainingAttribute->erase ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SaveAttribute (AcDbAttribute*& copyOut, CustomPropertyCR prop, UInt32 dupCount, TextBlockR textBlock, ElementRefP textElem)
    {
    EditElementHandle   eeh (textElem);
    if (!eeh.IsValid())
        return  MstnElementUnacceptable;

    ElementId           attributeId = textElem->GetElementId ();
    WString             attributeTag = prop.GetName ();
    UInt16              tagdefId = this->GetAttributeDefinitionIndex (prop);
    AcDbObjectId        objectId;

    if (m_fromDgnContext->SavingChanges())
        {
        objectId = m_fromDgnContext->ExistingObjectIdFromElementId (attributeId, AcDbAttribute::desc());
        if (!objectId.isValid() && (m_isMInsertBlock || !prop.IsReadOnly()))
            {
            /*------------------------------------------------------------------------------------------------------
            Updating text fields in model after a DWG file opened could have changed our the element ID's of our text
            nodes as a result of the minsert matrix cell being replaced(only text nodes changed).  Before fixing the 
            persistent text node in fields update in MiocroStation, workaround lost element ID's here by looking them
            up from the existing insert entity.  MInserts with attributes are rare in practice so performance is less 
            of our concern in this case.

            Another case needs this search is invisible variable attribute for which we did not create a text field when
            the file was opened.  After that, if a field linking to this property is added by a user, we have to find 
            and update the original attribute and change it to visible, instead of adding a new & duplicated attribute.
            -------------------------------------------------------------------------------------------------------*/
            bool        invisibleOnly = !m_isMInsertBlock;
            objectId = this->FindExistingAttribute (attributeTag, tagdefId, invisibleOnly);
            }
        }

    // only the first read-only prop gets to save as a constant attrdef - drop dups as texts:
    bool                isConstant = prop.IsReadOnly() && 0 == dupCount;
    bool                isNew = !objectId.isValid();

    // try open existing attribute or create a new one
    AcDbAttributePointer    acAttribute (objectId, AcDb::kForWrite);
    if (Acad::eOk != acAttribute.openStatus())
        {
        if (!isNew && !isConstant)
            return  CantOpenObject;
        if (Acad::eOk != acAttribute.create())
            return  OutOfMemoryError;
        }

    m_fromDgnContext->UpdateEntityPropertiesFromElement (acAttribute, eeh);

    // check in the attribute now, as otherwise adding annotation scale will incorrectly add it to current block:
    if (!isConstant)
        this->CheckInAttribute (acAttribute, attributeId);

    // swap entity visibility with attribute display mode
    AcDb::Visibility    visibility = acAttribute->visibility ();
    acAttribute->setVisibility (AcDb::kVisible);
    acAttribute->setInvisible (AcDb::kInvisible == visibility);

    // set the tag to a new attribute or update existing tag name which is neither the same nor from a dup tag name:
    if (isNew || !attributeTag.StartsWithI(acAttribute->tagConst()))
        {
        // deduplicate tag names based on fields, excluding minsert cells originally saved from DWG now saving back as DWG:
        if (dupCount > 0 && m_cellElement->GetElementRef() != textElem->GetParentElementRef())
            WString::Sprintf (attributeTag, L"%ls_%d", attributeTag.c_str(), dupCount);
        // dedupplicate tags names based on attrdefs if the item type was not originally created from this block
        if (m_blockName.CompareToI(prop.GetContainer().GetName()) != 0)
            this->DeduplicateAttributeTagName (attributeTag);

        acAttribute->setTag (attributeTag.c_str());
        }

    // set the text part of the attribute
    RealDwgStatus       status = RealDwgSuccess;
    if (this->HasSingleTextField(textBlock))
        status = m_fromDgnContext->DTextFromTextBlock (acAttribute, textBlock, eeh, false);
    else
        status = this->AttributeFromTextBlock (acAttribute, textBlock, eeh);

    if (RealDwgSuccess == status)
        {
        MSElementDescrCP    firstChild = eeh.GetElementDescrCP()->h.firstElem;
        if (!acAttribute->isMTextAttribute() && nullptr != firstChild)
            {
            // set level & symbology from the first text element
            m_fromDgnContext->UpdateEntitySymbologyFromDgn (acAttribute, firstChild->el.hdr.dhdr.symb.color, firstChild->el.hdr.dhdr.symb.weight, firstChild->el.hdr.dhdr.symb.style, firstChild->el.hdr.ehdr.level, &firstChild->el);
            m_fromDgnContext->UpdateEntityLevelFromElement (acAttribute, firstChild, CLASS_LAYER_None);
            }

        objectId = acAttribute->objectId ();
        if (!objectId.isValid() && !isConstant)
            status = NullObjectId;

        // attribute has been updated - remove it from our existing list:
        this->RemoveAttributeFromExistingList (objectId);

        if (RealDwgSuccess == status && nullptr == copyOut)
            {
            // return a copy of the attribute to create an attrdef:
            copyOut = new AcDbAttribute ();
            if (nullptr == copyOut)
                {
                status = OutOfMemoryError;
                }
            else if (Acad::eOk != copyOut->copyFrom(acAttribute))
                {
                delete copyOut;
                copyOut = nullptr;
                status = EntityError;
                }
            }
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SaveInvisibleAttributeDefinition (AcDbObjectId& attrdefId, AcString& attrdefTag, WStringCR stringValue, CustomPropertyCR prop)
    {
    WString         propName = prop.GetName ();
    UInt16          tagdefId = this->GetAttributeDefinitionIndex (prop);
    Adesk::Boolean  isConstant = prop.IsReadOnly ();

    attrdefTag.assign (propName.c_str());
    
    // first try to find an attrdef matching by both name and constant state:
    auto        found = std::find_if (m_existingAttributeDefinitionList.begin(), m_existingAttributeDefinitionList.end(), [&](AcDbAttributeDefinition* arg) { return 0 == wcscmp(arg->tagConst(), propName.c_str()) && arg->isConstant() == isConstant; });

    // if not found, then try it again only matching by name - we will reset its constant state:
    if (m_existingAttributeDefinitionList.end() == found)
        found = std::find_if (m_existingAttributeDefinitionList.begin(), m_existingAttributeDefinitionList.end(), [&](AcDbAttributeDefinition* arg) { return 0 == wcscmp(arg->tagConst(), propName.c_str()); });

    // if we were to allow attrdef name change, we must trust tagdefId of the item type using the block name:
    WCharCP     newTagName = nullptr;
    if (m_fromDgnContext->SavingChanges() && m_existingAttributeDefinitionList.end() == found && tagdefId > 0 && tagdefId <= m_existingAttributeDefinitionList.size())
        {
        found = m_existingAttributeDefinitionList.begin() + (tagdefId - 1);
        newTagName = prop.GetName ();
        }
    
    if (m_existingAttributeDefinitionList.end() != found)
        {
        // update existing attrdef
        (*found)->setTextString (stringValue.c_str());
        (*found)->setInvisible (Adesk::kTrue);
        (*found)->setConstant (isConstant);

        // allow renaming the tag
        if (nullptr != newTagName && 0 != newTagName[0])
            {
            // save old def name which will be used to find attribute
            attrdefTag.assign ((*found)->tagConst());
            // set renamed tag
            (*found)->setTag (newTagName);
            }

        // don't create a constant nor an empty attribute
        if (stringValue.empty() && !m_allowEmptyProps)
            attrdefId.setNull ();
        else
            attrdefId = (*found)->objectId ();

        return  RealDwgSuccess;
        }
    
    // no existing constant attrdef - create a new one:
    AcDbAttributeDefinitionPointer  acAttrdef;
    if (Acad::eOk != acAttrdef.create())
        return  OutOfMemoryError;

    // use the first dependent text element as a template for the text part of the attrdef:
    TextBlockPtr    textBlock;
    ElementHandle   firstText;
    if (!m_dependentTexts.empty() && (firstText = ElementHandle(m_dependentTexts.at(0))).IsValid())
        textBlock = TextBlock::Create (firstText);

    RealDwgStatus   status = RealDwgSuccess;
    if (textBlock.IsValid())
        status = m_fromDgnContext->DTextFromTextBlock (acAttrdef, *textBlock.get(), firstText, false);
    else
        status = this->SetDefaultTextPart (acAttrdef);
 
    AcGePoint3d     origin;
    this->GetAttrdefOriginFromTextOrigin (origin, acAttrdef->position());

    acAttrdef->setPosition (origin);
    acAttrdef->setTag (propName.c_str());
    acAttrdef->setTextString (stringValue.c_str());
    acAttrdef->setInvisible (Adesk::kTrue);
    acAttrdef->setConstant (isConstant);

    attrdefId = acAttrdef->objectId ();
    if (!attrdefId.isValid())
        {
        m_blockTableRecord->appendAcDbEntity (acAttrdef);
        attrdefId = acAttrdef->objectId ();
        acAttrdef->close ();
        }

    // a constant attrdef or an empty property does not need an attribute
    if (stringValue.empty() && !m_allowEmptyProps)
        attrdefId.setNull ();

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SaveInvisibleAttribute (AcDbObjectId attrdefId, AcString const& attrdefTag, WStringCR propName, WStringCR stringValue)
    {
    // if we are saving changes, try updating existing invisible attribute value
    AcDbObjectIterator*     attributeIter = m_blockReference->attributeIterator ();
    if (nullptr == attributeIter)
        return  OutOfMemoryError;

    AcDbAttributePointer    acAttribute;
    if (m_fromDgnContext->SavingChanges())
        {
        // find attribute by attrdef name
        for (; !attributeIter->done(); attributeIter->step())
            {
            if (Acad::eOk == acAttribute.open(attributeIter->objectId(), AcDb::kForWrite))
                {
                if (0 == attrdefTag.compareNoCase(acAttribute->tagConst()) && acAttribute->isInvisible())
                    break;
                acAttribute.close ();
                }
            }
        }

    if (Acad::eOk != acAttribute.openStatus())
        {
        // no existing attribute to be updated - add a new attribute
        if (Acad::eOk != acAttribute.create())
            {
            delete attributeIter;
            return  OutOfMemoryError;
            }

        AcDbAttributeDefinitionPointer  acAttrdef (attrdefId, AcDb::kForRead);
        if (Acad::eOk == acAttrdef.openStatus())
            {
            // set the new attribute from matching attrdef
            acAttribute->setAttributeFromBlock (acAttrdef, m_blockReference->blockTransform());
            }
        else if (!m_existingAttributeDefinitionList.empty())
            {
            // use the first existing attrdef as an attribute template
            acAttribute->setAttributeFromBlock (m_existingAttributeDefinitionList[0], m_blockReference->blockTransform());
            }
        else
            {
            // use the first existing attribute as a template for the new attribute
            attributeIter->start ();

            AcDbAttributePointer    firstAttribute (attributeIter->objectId(), AcDb::kForRead);
            if (Acad::eOk != firstAttribute.openStatus() || Acad::eOk != acAttribute->copyFrom(firstAttribute))
                this->SetDefaultTextPart (acAttribute);

            // place the new invisible attribute at the block reference origin
            acAttribute->setPosition (m_blockReference->position());
            }
        }

    acAttribute->setTag (propName.c_str());
    acAttribute->setTextString (stringValue.c_str());
    acAttribute->setInvisible (Adesk::kTrue);

    this->CheckInAttribute (acAttribute.object());
    this->RemoveAttributeFromExistingList (acAttribute->objectId());
    
    delete attributeIter;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetDefaultTextPart (AcDbText* acDText)
    {
    // a brand new attribute to be created from scratch:
    acDText->setPropertiesFrom (m_blockReference);

    AcDbObjectId    textstyleId = m_fromDgnContext->GetDatabase()->textstyle ();
    if (textstyleId.isValid())
        acDText->setTextStyle (textstyleId);

    double          height = m_fromDgnContext->GetDatabase()->textsize ();
    if (height <= TOLERANCE_TextHeight)
        height = 0.2;

    acDText->setHeight (height);
    acDText->setPosition (m_blockTableRecord->origin());

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SaveItemTypePropertyToDwg (CustomPropertyCR prop, DgnElementECInstanceCR ecInstance, bool saveAttrdefs)
    {
    if (prop.IsHidden())
        return  RealDwgSuccess;
    //Struct and hidden properties are not handled yet
    if (prop.IsArray() || CustomProperty::Type::Custom == prop.GetType())
        return  RealDwgSuccess;

    WString         className = prop.GetContainer().GetInternalName ();
    WString         propName = prop.GetInternalName ();
    AcDbAttribute*  attrTemplate = nullptr;
    RealDwgStatus   status = RealDwgSuccess;
    UInt32          fieldCount = 0, foundCount = 0;

    for (auto& textElem : m_dependentTexts)
        {
        TextBlockPtr    textBlock = TextBlock::Create (ElementHandle(textElem));
        if (textBlock.IsValid())
            {
            // only the whole string as a field
            TextFieldPtr    field = textBlock->GetField (textBlock->Begin());
            if (field.IsValid() && field->GetHandler().GetFieldType() == TextField::FieldType_Element)
                {
                // only if the item type name and property name both match we will save an attribute from the property:
                if (0 == className.CompareToI(field->GetClassName()) && 0 == propName.CompareTo(field->GetAccessor()))
                    {
                    if (m_dependentTextsProcessed.find(textElem) != m_dependentTextsProcessed.end())
                        {
                        // a duplicated property
                        foundCount++;
                        continue;
                        }
                    m_dependentTextsProcessed.insert (textElem);

                    status = this->SaveAttribute (attrTemplate, prop, fieldCount, *textBlock.get(), textElem);
                    fieldCount++;
                    }
                }
            }
        }

    // only save attrdef's once per block
    if (!saveAttrdefs)
        return  status;

    /*-----------------------------------------------------------------------------------------------------------
    If we are saving existing DWG file and if the property exists in the block definition, keep it as is - do not 
    attempt to change attrdef.  Other block references may still use it.
    -----------------------------------------------------------------------------------------------------------*/
    if (m_fromDgnContext->SavingChanges())
        {
        auto    found = std::find_if (m_existingAttributeDefinitionList.begin(), m_existingAttributeDefinitionList.end(), [&](AcDbAttributeDefinition* arg) { return 0 == wcscmp(arg->tagConst(), propName.c_str()); });
        if (found != m_existingAttributeDefinitionList.end())
            return  status;
        }

    if (fieldCount > 0)
        {
        /*-----------------------------------------------------------------------------------------------------
        At least one matching text field exists. Hopefully we also have created an attribute from it and now have
        a copy of it we can use as a template to create an attrdef.

        We want to exclude missing attrdefs as otherwise the block may accumulate too many unused attrdefs
        -----------------------------------------------------------------------------------------------------*/
        WString     displayClassName (prop.GetContainer().GetName());
        if (!displayClassName.EndsWith(m_elementIdSuffix.GetWCharCP()))
            status = this->SaveAttributeDefinition (prop, attrTemplate);

        if (nullptr != attrTemplate)
            delete attrTemplate;
        }
    else if (foundCount > 0)
        {
        /*-------------------------------------------------------------------------------
        A match exists, but has been previsouly processed, indicating a duplicate property
        -------------------------------------------------------------------------------*/
        DIAGNOSTIC_PRINTF ("A duplicate itemtype property %ls::%ls is ignored\n", className.c_str(), propName.c_str());
        }
    else
        {
        /*-----------------------------------------------------------------------------------------------------
        We get here because we have a missing text field linking to this property.  We need to have an invisible
        attrdef and/or attribute to represent this orphaned property:

            1. An editable property:
                    a) that has an empty value - just need an attrdef, no attribute needed
                    b) that has an actual value - need an invisible variable attribute to hold the value.
            2. A read-only property - need an invisible contant attribute definition to reserve the property.

        In all cases an attrdef is needed, so unless there already exists an attrdef, create a new attrdef first 
        then add an attribute as needed.
        -----------------------------------------------------------------------------------------------------*/
        WString         stringValue;
        if (ECOBJECTS_STATUS_Success != ecInstance.GetValueAsString(stringValue, propName.GetWCharCP(), false, 0))
            return  status;

        AcString        attrdefTag;
        AcDbObjectId    attrdefId;
        this->SaveInvisibleAttributeDefinition (attrdefId, attrdefTag, stringValue, prop);

        propName = prop.GetName ();

        if (attrdefId.isValid())
            this->SaveInvisibleAttribute (attrdefId, attrdefTag, propName, stringValue);
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbText::AcTextAlignment    AlignmentFromAttachmentPoint (AcDbMText::AttachmentPoint attachment)
    {
    switch (attachment)
        {
        case AcDbMText::kTopLeft:       return  AcDbText::kTextAlignmentTopLeft;
        case AcDbMText::kTopMid:
        case AcDbMText::kTopCenter:     return  AcDbText::kTextAlignmentTopCenter;
        case AcDbMText::kTopRight:      return  AcDbText::kTextAlignmentTopRight;

        case AcDbMText::kMiddleLeft:    return  AcDbText::kTextAlignmentMiddleLeft;
        case AcDbMText::kMiddleMid:
        case AcDbMText::kMiddleCenter:  return  AcDbText::kTextAlignmentMiddleCenter;
        case AcDbMText::kMiddleRight:   return  AcDbText::kTextAlignmentMiddleRight;

        case AcDbMText::kBottomLeft:    return  AcDbText::kTextAlignmentBottomLeft;
        case AcDbMText::kBottomMid:
        case AcDbMText::kBottomCenter:  return  AcDbText::kTextAlignmentBottomCenter;
        case AcDbMText::kBottomRight:   return  AcDbText::kTextAlignmentBottomRight;

        case AcDbMText::kBaseLeft:      return  AcDbText::kTextAlignmentLeft;
        case AcDbMText::kBaseMid:
        case AcDbMText::kBaseCenter:    return  AcDbText::kTextAlignmentCenter;
        case AcDbMText::kBaseRight:     return  AcDbText::kTextAlignmentRight;

        case AcDbMText::kTopAlign:
        case AcDbMText::kBaseAlign:
        case AcDbMText::kBottomAlign:
        case AcDbMText::kMiddleAlign:  return  AcDbText::kTextAlignmentAligned;

        case AcDbMText::kTopFit:
        case AcDbMText::kBaseFit:
        case AcDbMText::kBottomFit:
        case AcDbMText::kMiddleFit:     return  AcDbText::kTextAlignmentFit;
        }

    return  AcDbText::kTextAlignmentLeft;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
ItemTypeP       FindItemTypeInLibrary (ItemTypeLibraryP library, ECClassCR ecClass)
    {
    // find item type by internal name
    WStringCR   internalName = ecClass.GetName ();

    auto found = std::find_if(library->begin(), library->end(), [&](ItemTypeCR it) { return 0 == internalName.CompareTo(it.GetInternalName()); });

    return  library->end() == found ? nullptr : found;
    }


public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    SetAcDbAttributeOrDropMText (AcDbAttribute* acAttribute, AcDbMText* acMText, ConvertFromDgnContextR context)
    {
    if (nullptr == acAttribute || nullptr == acMText)
        return  NullObject;

    // convert mtext to dtext
    Acad::ErrorStatus   es = acAttribute->setHeight (acMText->textHeight());

    es = acAttribute->setTextStyle (acMText->textStyle());
    es = acAttribute->setJustification (ConvertItemTypeToAttribute::AlignmentFromAttachmentPoint(acMText->attachment()));
    es = acAttribute->setAlignmentPoint (acMText->location());
    es = acAttribute->setRotation (acMText->rotation());
    es = acAttribute->setNormal (acMText->normal());

    // if not already an mtext type, convert it now as otherwise setMTextAttributeConst will fail:
    if (!acAttribute->isMTextAttribute())
        es = acAttribute->convertIntoMTextAttribute ();

    // set alignment point after convertIntoMTextAttribute which seems to change our text location
    es = acAttribute->setAlignmentPoint (acMText->location());
    // base-left has no alignment point
    if (Acad::eOk != es)
        es = acAttribute->setPosition (acMText->location());

    // now copy rest of the mtext data to the attribute
    es = acAttribute->setMTextAttributeConst (acMText);

    if (Acad::eOk != es)
        {
        // replace attribute with a new mtext
        if (!acMText->objectId().isValid())
            context.AddEntityToCurrentBlock (acMText, 0);

        // wasting an ID, but we can't hand the ID over to an mtext which has a different owner
        if (acAttribute->objectId().isValid())
            acAttribute->erase ();

        return  ReplacedObjectType;
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   Convert ()
    {
    CustomItemHost      itemHost(*m_cellElement);
    DgnECInstanceVector instanceList;
    size_t              nInstances = itemHost.GetCustomItems (instanceList);

    if (0 == nInstances)
        return  RealDwgSuccess;

    AcDbObjectId                    blockId = m_blockReference->blockTableRecord ();
    AcDbBlockTableRecordPointer     acBlock (blockId, AcDb::kForWrite);
    if (Acad::eOk != acBlock.openStatus())
        return  CantCreateItemType;

    m_blockTableRecord = acBlock.object ();

    AcString            blockName;
    if (Acad::eOk == acBlock->getName(blockName))
        m_blockName.assign (blockName.kwszPtr());

    WString::Sprintf (m_elementIdSuffix, L"_%I64d", m_cellElement->GetElementId());

    bool                saveAttrdefs = !m_fromDgnContext->GetFileHolder().IsBlockSavedWithItemType (blockId);

    if (acBlock->hasAttributeDefinitions() && saveAttrdefs)
        this->OpenExistingAttributeDefinitions ();

    if (m_fromDgnContext->SavingChanges())
        this->CollectExistingAttributes ();

    this->CollectDependentTexts ();

    // convert all item types instantiated on this cell to attributes:
    for (auto& ecInstance : instanceList)
        {
        // find the item type library for this EC instance
        ECClassCR           ecClass = ecInstance->GetClass ();
        ItemTypeLibraryPtr  itemtypeLibrary = m_fromDgnContext->GetFileHolder().FindBySchemaNameWithMap ( ecClass.GetSchema ().GetName (), *m_fromDgnContext->GetFile () );

        if (itemtypeLibrary.IsValid())
            {
            // find the item type of this EC instance
            DgnElementECInstanceCP  elmInstance = ecInstance->GetAsElementInstance ();
            ItemTypeP               itemType = this->FindItemTypeInLibrary (itemtypeLibrary.get(), ecClass);

            if (nullptr != itemType && nullptr != elmInstance)
                {
                // loop through all properties in the item type
                for (ItemType::const_iterator propIter = itemType->begin(); propIter != itemType->end(); propIter++)
                    {
                    // save this property as attribute and/or attribute definition
                    this->SaveItemTypePropertyToDwg (*propIter, *elmInstance, saveAttrdefs);
                    }
                }
            }
        }

    // allow attributes to be deleted
    this->DeleteUnsavedAttributesFromBlockReference ();

    // we don't want to save item types to block def more than once
    if (saveAttrdefs)
        m_fromDgnContext->GetFileHolder().SetBlockSavedWithItemType (blockId);
    // mark the cell as saved with item types so we won't have to post process it for changed fielded texts
    if (m_fromDgnContext->SavingChanges())
        m_fromDgnContext->SetItemTypeTarget (m_cellElement->GetElementId(), true);

    return  RealDwgSuccess;
    }

};  // ConvertItemTypeToAttribute


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::SetItemTypeTarget (ElementId cellId, bool saved)
    {
    auto        ret = m_itemTypeTargetList.insert (bpair<ElementId, bool>(cellId, saved));
    if (!ret.second && saved)
        {
        // the target exists and we are marking it as processed, update the old status:
        auto&   oldEntry = *ret.first;
        oldEntry.second = saved;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::SaveItemTypeTargets ()
    {
    // force block references to be updated if any of it has not done so and any of its item type field element is changed.
    for (auto entry : m_itemTypeTargetList)
        {
        if (entry.second)
            continue;

        EditElementHandle   eeh (entry.first, m_model);
        if (eeh.IsValid())
            {
            AcDbBlockReferencePointer   acInsert (this->ExistingObjectIdFromElementId(eeh.GetElementId()), AcDb::kForWrite);
            if (Acad::eOk != acInsert.openStatus())
                {
                DIAGNOSTIC_PRINTF ("Failed opening item type block reference ID=%I64d\n", eeh.GetElementId());
                continue;
                }

            RealDwgStatus   status = this->UpdateObjectFromElement (acInsert, eeh);

            if (RealDwgSuccess != status)
                DIAGNOSTIC_PRINTF ("Failed saving item type block reference ID=%I64d\n", eeh.GetElementId());
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SetAttributDefinitionFromProperty (AcDbAttributeDefinition* acAttrdef, CustomPropertyCR prop, bvector<AcDbAttributeDefinition*> existingAttrdefs)
    {
    // if the atttrdef is new, use previous attdef as a template for text params
    if (!acAttrdef->objectId().isValid() && !existingAttrdefs.empty())
        acAttrdef->copyFrom (existingAttrdefs.back());

    acAttrdef->setTag (prop.GetName());
    acAttrdef->setConstant (prop.IsReadOnly());

    ECValue     ecValue;
    WCharCP     stringValue;
    if (prop.GetDefaultValue(ecValue) && nullptr != (stringValue = ecValue.GetString()))
        acAttrdef->setTextString (stringValue);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SaveRemainingItemtypeChangeToDwg ()
    {
    /*---------------------------------------------------------------------------------------------------------------
    This method attempts to save changes made in DWG Attribute Definition Item Type schema.  Hence it should be called
    only saving existing DWG file and should never reach here when saving a DGN file as a DWG file.
    
    ConvertItemTypeToAttribute::Convert should have processed all changes on shared cells and/or attributes, and had
    updated their attribute definitions as well.  However, DWG blocks with attrdefs that do NOT have instances have
    not been updated.  Changes made in the item type schema need to be saved into these blocks.

    To update these block definitions, we compare the DgnFile's DWG attrdef item type schema against the original schema
    we have created from opening the DWG file, find and update the corresponding block definition.
    ---------------------------------------------------------------------------------------------------------------*/
    AcDbBlockTablePointer   acBlockTable (this->GetDatabase()->blockTableId(), AcDb::kForRead);
    if (Acad::eOk != acBlockTable.openStatus())
        return  CantOpenObject;

    ItemTypeLibraryP        dwgOriginatedLib = m_pFileHolder->GetDwgOriginatedItemtypeLibrary ();
    if (nullptr == dwgOriginatedLib)
        return  RealDwgSuccess;

    ItemTypeLibraryPtr      dgnItemtypeLib = ItemTypeLibrary::FindByName (SCHEMA_NAME_AttributeDefinition, *m_dgnFile);
    if (!dgnItemtypeLib.IsValid() || dgnItemtypeLib->GetItemTypeCount() < 1)
        return  RealDwgSuccess;

    // check each DWG originated itemtype against edited item type and update attrdefs accordingly:
    for (ItemTypeLibrary::const_iterator iter = dwgOriginatedLib->begin(); iter != dwgOriginatedLib->end(); iter++)
        {
        ItemTypeCR          originalItemtype = *iter;
        WCharCP             itemtypeName = originalItemtype.GetName ();

        // if an item type is not found by ID, it could be deleted or replaced - try finding it by name:
        ItemTypeCP          editedItemtype = dgnItemtypeLib->GetItemTypeById (originalItemtype.GetId());
        if (nullptr == editedItemtype)
            editedItemtype = dgnItemtypeLib->GetItemTypeByName (itemtypeName);

        // compare the source item type with original DWG item type - skip if there is no change:
        if (nullptr != editedItemtype && editedItemtype->CompareTo(originalItemtype))
            continue;

        // there is item type change - open & check the corresponding block definition:
        AcDbBlockTableRecord*           acBlock = nullptr;
        AcDbBlockTableRecordIterator*   acEntityIter = nullptr;

        if (nullptr == itemtypeName || Acad::eOk != acBlockTable->getAt(itemtypeName, acBlock, AcDb::kForWrite) ||
            acBlock->isLayout() || acBlock->isFromExternalReference() || Acad::eOk != acBlock->newIterator(acEntityIter))
            {
            if (nullptr != acBlock && acBlock->objectId().isValid())
                acBlock->close ();
            continue;
            }

        // if the original item type not found in file, it has been deleted - remove all attrdefs from the block
        if (nullptr == editedItemtype)
            {
            for (; !acEntityIter->done(); acEntityIter->step())
                {
                AcDbAttributeDefinition*    acAttrdef = nullptr;
                AcDbObjectId                entityId;
                if (Acad::eOk == acEntityIter->getEntityId(entityId) && Acad::eOk == acdbOpenObject(acAttrdef, entityId, AcDb::kForWrite))
                    acAttrdef->erase ();
                }

            acBlock->close ();
            continue;
            }
        
        // collect and open all existing attrdefs in the block
        bvector<AcDbAttributeDefinition*>   existingAttrdefs;
        bvector<bool>                       foundList;
        for (; !acEntityIter->done(); acEntityIter->step())
            {
            AcDbObjectId    entityId;
            if (Acad::eOk != acEntityIter->getEntityId(entityId))
                continue;

            AcDbAttributeDefinition*    acAttrdef = nullptr;
            if (Acad::eOk == acdbOpenObject(acAttrdef, entityId, AcDb::kForWrite))
                {
                existingAttrdefs.push_back (acAttrdef);
                foundList.push_back (false);
                }
            }

        size_t      numAttrdefs = existingAttrdefs.size ();

        // walk through item type properties and check them againt existing attrdef list:
        for (auto propertyIter = editedItemtype->begin(); propertyIter != editedItemtype->end(); propertyIter++)
            {
            CustomPropertyCR    prop = *propertyIter;
            if (prop.IsHidden())
                continue;

            AcDbAttributeDefinition*    acAttrdef = nullptr;
            UInt16                      tagdefId = prop.GetTagDefId ();
            if (tagdefId > 0 && tagdefId <= numAttrdefs)
                {
                // found an existing attrdef matching tagdefId
                UInt16                  index = tagdefId - 1;

                acAttrdef = existingAttrdefs.at (index);
                if (nullptr != acAttrdef)
                    {
                    // update the attrdef from the EC property if changed
                    CustomPropertyCP    originalProp = originalItemtype.GetPropertyById (prop.GetId());
                    if (nullptr == originalProp || !originalProp->CompareTo(prop))
                        SetAttributDefinitionFromProperty (acAttrdef, prop, existingAttrdefs);

                    // mark this attrdef found
                    foundList[index] = true;
                    }
                }
            else
                {
                // no matching attrdef found from existing attrdefs by tagdefId - try finding it by name
                auto found = std::find_if (existingAttrdefs.begin(), existingAttrdefs.end(), [&](AcDbAttributeDefinition* at) { return 0 == wcscmp(at->tagConst(), prop.GetName()); });

                if (existingAttrdefs.end() == found)
                    {
                    // no attrdef matching the property - add a new attrdef to the block
                    acAttrdef = new AcDbAttributeDefinition ();
                    SetAttributDefinitionFromProperty (acAttrdef, prop, existingAttrdefs);
                    if (this->AddEntityToBlock(acBlock, acAttrdef, 0).isNull())
                        delete acAttrdef;
                    else
                        acAttrdef->close ();
                    }
                else
                    {
                    // found an existing attrdef matching the EC property name - update the attrdef from the property if changed:
                    CustomPropertyCP    originalProp = originalItemtype.GetPropertyById (prop.GetId());

                    if (nullptr == originalProp || !originalProp->CompareTo(prop))
                        SetAttributDefinitionFromProperty (*found, prop, existingAttrdefs);

                    // mark this attrdef found
                    size_t      index = found - existingAttrdefs.begin();
                    if (index < numAttrdefs)
                        foundList[index] = true;
                    }
                }
            }

        // close existing attrdefs that are found in the item type and delete those that are not found:
        for (size_t i = 0; i < numAttrdefs; i++)
            {
            if (foundList[i])
                existingAttrdefs[i]->close ();
            else
                existingAttrdefs[i]->erase ();
            }

        // save the block into the processed cache
        m_pFileHolder->SetBlockSavedWithItemType (acBlock->objectId());
        acBlock->close ();
        }
    
    return  RealDwgSuccess;
    }

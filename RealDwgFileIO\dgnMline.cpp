/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnMline.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

struct  MLineStyleParams
    {
    int                         nProfiles;
    MlineProfile                pProfiles[MULTILINE_MAX];
    int                         indices[MULTILINE_MAX];
    double                      originCapAngle;
    double                      endCapAngle;
    bool                        filled;
    UInt32                      fillColor;
    UInt32                      elementColor;
    Int32                       elementStyle;
    UInt32                      elementLevel;
    MultilineSymbologyPtr       originCapSymbology;
    MultilineSymbologyPtr       midCapSymbology;
    MultilineSymbologyPtr       endCapSymbology;
    MlineOffsetMode             offsetMode;
    bool                        colorByLayer;
    bool                        styleByLayer;
    double                      placementOffset;
    };


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtMultiline : public ToDwgExtension
{
private:
mutable ConvertFromDgnContextP  m_fromDgnContext;
mutable MultilineHandler*       m_mlineHandler;
mutable ElementHandleCP         m_mlineElement;
mutable MlineBreak const*       m_firstBreak;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                    GetProfileParamsFromDgnProfile
(
double*                         pOffset,
AcCmColor*                      pColor,
AcDbObjectId*                   pLinetypeId,
int                             profileIndex,
double                          justicationOffset,
MLineStyleParams*               pStyleParams
) const
    {
    const MlineProfile*         pProfile    = &pStyleParams->pProfiles[pStyleParams->indices[profileIndex]];
    unsigned int                color       = pProfile->symb.useColor ? pProfile->symb.color : pStyleParams->elementColor;
    int                         style       = pProfile->symb.useStyle ? pProfile->symb.style : pStyleParams->elementStyle;

   *pOffset = m_fromDgnContext->GetScaleFromDGN() * (pProfile->dist + justicationOffset);

    if (pStyleParams->colorByLayer)
        {
        //pColor->setColorMethod (AcCmEntityColor::kByLayer);
        pColor->setByLayer();
        }
    else
        {
        *pColor = m_fromDgnContext->GetColorFromDgn (color, 0);
        }

    if (pStyleParams->styleByLayer)
        {
        LevelHandle level = m_fromDgnContext->GetModel()->GetLevelCache().GetLevel (pStyleParams->elementLevel);
        AcDbObjectId layerId = m_fromDgnContext->SavingChanges() ? m_fromDgnContext->GetFileHolder().GetLayerByLevelHandle(level) : m_fromDgnContext->GetFileHolder().GetLayerByLevelId(level.GetLevelId());
        AcDbLayerTableRecordPointer pLayer (layerId, AcDb::kForRead);
        if (Acad::eOk == pLayer.openStatus())
            {
            *pLinetypeId = pLayer->linetypeObjectId();
            return;
            }
        }
    *pLinetypeId = m_fromDgnContext->GetLineTypeFromDgn (style);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsSameFillColor (MLineStyleParams const* dgnParams, AcDbMlineStyle const* dwgStyle) const
    {
    // don't duplicate the style for different fill colors if fill is not activated
    if (!dgnParams->filled && !dwgStyle->filled())
        return  true;

    UInt32      styleFillColor = dwgStyle->fillColor().colorIndex ();
    if (dgnParams->colorByLayer)
        return  AcCmEntityColor::kACIbyLayer == styleFillColor;

    return  m_fromDgnContext->GetColorIndexFromDgn(dgnParams->fillColor, -1) == styleFillColor;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsSameStartCap (MLineStyleParams const* dgnParams, AcDbMlineStyle const* dwgStyle) const
    {
    return  dgnParams->originCapSymbology->UseCapLine() == dwgStyle->startSquareCap() &&
            dgnParams->originCapSymbology->UseCapInnerArc() == dwgStyle->startInnerArcs() &&
            dgnParams->originCapSymbology->UseCapOuterArc() == dwgStyle->startRoundCap();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsSameEndCap (MLineStyleParams const* dgnParams, AcDbMlineStyle const* dwgStyle) const
    {
    return  dgnParams->endCapSymbology->UseCapLine() == dwgStyle->endSquareCap() &&
            dgnParams->endCapSymbology->UseCapInnerArc() == dwgStyle->endInnerArcs() &&
            dgnParams->endCapSymbology->UseCapOuterArc() == dwgStyle->endRoundCap();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsCapSuppressed (MultilineSymbology const* capSymbology) const
    {
    return !capSymbology->UseCapLine() && !capSymbology->UseCapInnerArc() && !capSymbology->UseCapOuterArc();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            HasStartCap (AcDbMlineStyle const* dwgStyle) const
    {
    return  dwgStyle->startSquareCap() || dwgStyle->startInnerArcs() || dwgStyle->startRoundCap();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            HasEndCap (AcDbMlineStyle const* dwgStyle) const
    {
    return  dwgStyle->endSquareCap() || dwgStyle->endInnerArcs() || dwgStyle->endRoundCap();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            CompareStartEndAngles (MLineStyleParams const* dgnParams, AcDbMlineStyle const* dwgStyle) const
    {
    /*--------------------------------------------------------------------------------------------------
    Apparently, while we can add breaks at the start/end points for an unfilled mline, we cannot seem to
    be able to do that for a filled mline.  The filled part does not appear to follow our line breaks
    unless the style has a 90 degrees angle.
    Before we can complately figure out how ACAD handles filled angles, we still have to create different 
    styles for filled mlines.
    --------------------------------------------------------------------------------------------------*/
    if (!dwgStyle->filled())
        return  true;

    double      styleStartAngle = dwgStyle->startAngle ();
    double      styleEndAngle = dwgStyle->endAngle ();
    if (Angle::NearlyEqualAllowPeriodShift(styleStartAngle, dgnParams->originCapAngle) && 
        Angle::NearlyEqualAllowPeriodShift(styleEndAngle, dgnParams->endCapAngle))
        return  true;

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            CompareStartEndCaps (MLineStyleParams const* dgnParams, AcDbMlineStyle const* dwgStyle) const
    {
    /*--------------------------------------------------------------------------------------------------
    Another effort to reduce unncessary duplicates of mline styles: DWG allows an mline to supress the
    cap of the start/end point.  Use this capability whenever possible.
    --------------------------------------------------------------------------------------------------*/
    bool        sameStartCap = this->IsSameStartCap (dgnParams, dwgStyle);
    bool        sameEndCap = this->IsSameEndCap (dgnParams, dwgStyle);

    // if start/end caps match, use the style
    if (sameStartCap && sameEndCap)
        return  true;

    bool        supressStartCap = this->IsCapSuppressed (dgnParams->originCapSymbology.get());
    bool        supressEndCap = this->IsCapSuppressed (dgnParams->endCapSymbology.get());

    // find the cases in which we can use the same style by applying cap supressions later on:
    if ((supressStartCap && sameEndCap) || (supressEndCap && sameStartCap) || (supressStartCap && supressEndCap))
        return  true;

    // all other cases will have to create a new style in order to maintain correct display
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        MLineStyleMatchesDgnParameters
(
AcDbMlineStyle*             pStyle,
MLineStyleParams*           pStyleParams
) const
    {
    if (pStyleParams->filled                                == pStyle->filled() &&
        pStyleParams->midCapSymbology->UseCapLine()         == pStyle->showMiters() &&
        pStyleParams->nProfiles                             == pStyle->numElements() &&
        this->CompareStartEndCaps(pStyleParams, pStyle) &&
        this->CompareStartEndAngles(pStyleParams, pStyle) &&
        this->IsSameFillColor(pStyleParams, pStyle))
        {
        double          justificationOffset = (MlineOffsetMode::ByCenter == pStyleParams->offsetMode) ? 0.0 : pStyleParams->placementOffset;
        for (int iProfile = 0; iProfile < pStyleParams->nProfiles; iProfile++)
            {
            double              offset, styleOffset;
            AcCmColor           color, styleColor;
            AcDbObjectId        lineTypeId, styleLineTypeId;

            this->GetProfileParamsFromDgnProfile (&offset, &color, &lineTypeId, iProfile, justificationOffset, pStyleParams);
            pStyle->getElementAt (iProfile, styleOffset, styleColor, styleLineTypeId);

            if ((offset != styleOffset) || (color != styleColor) || (lineTypeId != styleLineTypeId))
                return false;
            }
        return true;
        }
    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                AddMLineStyle
(
MLineStyleParams*           pStyleParams,
WCharCP                     pSuggestedName
) const
    {
    double                  startAngle  = Angle::AdjustToSweep (pStyleParams->originCapAngle, 0.0, Angle::TwoPi());
    double                  endAngle    = Angle::AdjustToSweep (pStyleParams->endCapAngle, 0.0, Angle::TwoPi());

    AcDbDatabase*           pDatabase   = m_fromDgnContext->GetDatabase();
    AcDbDictionaryPointer   pStyleDictionary (pDatabase->mLStyleDictionaryId (), AcDb::kForWrite);
    if (Acad::eOk != pStyleDictionary.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Failed to open MLStyle dictionary!\n");
        return  AcDbObjectId::kNull;
        }

    AcDbMlineStyle*         pStyle = new AcDbMlineStyle ();
    pStyle->initMlineStyle ();

    if (startAngle > msGeomConst_pi)
        startAngle -= msGeomConst_pi;

    if (endAngle > msGeomConst_pi)
        endAngle -= msGeomConst_pi;

    pStyle->setStartAngle (startAngle);
    pStyle->setEndAngle (endAngle);

    if (pStyleParams->colorByLayer)
        {
        AcCmColor color;
        //color.setColorMethod (AcCmEntityColor::kByLayer);
        color.setByLayer();
        pStyle->setFillColor (color);
        }
    else
        {
        pStyle->setFillColor (m_fromDgnContext->GetColorFromDgn (pStyleParams->fillColor, 0));
        }

    WString     name = WString(NULL == pSuggestedName ? L"UNNAMED" : pSuggestedName);
    m_fromDgnContext->ValidateMlineStyleName (name, pStyleDictionary);

    pStyle->setName (name.c_str());
    pStyle->setFilled           (pStyleParams->filled);
    pStyle->setShowMiters       (pStyleParams->midCapSymbology->UseCapLine());
    pStyle->setStartSquareCap   (pStyleParams->originCapSymbology->UseCapLine());
    pStyle->setStartInnerArcs   (pStyleParams->originCapSymbology->UseCapInnerArc());
    pStyle->setStartRoundCap    (pStyleParams->originCapSymbology->UseCapOuterArc());
    pStyle->setEndSquareCap     (pStyleParams->endCapSymbology->UseCapLine());
    pStyle->setEndInnerArcs     (pStyleParams->endCapSymbology->UseCapInnerArc());
    pStyle->setEndRoundCap      (pStyleParams->endCapSymbology->UseCapOuterArc());

    double              justificationOffset = (MlineOffsetMode::ByCenter == pStyleParams->offsetMode) ? 0.0 : pStyleParams->placementOffset;
    for (int i=0; i<pStyleParams->nProfiles; i++)
        {
        double          offset;
        AcCmColor       color;
        AcDbObjectId    lineTypeId;

        this->GetProfileParamsFromDgnProfile (&offset, &color, &lineTypeId, i, justificationOffset, pStyleParams);
        int     newIndex;
        pStyle->addElement (newIndex, offset, color, lineTypeId, false);
        }

    AcDbObjectId newObjectId;
    pStyleDictionary->setAt (name.c_str(), pStyle, newObjectId);
    pStyle->close ();

    return newObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                GetOrAddMultilineStyleFromDGNParameters
(
MLineStyleParams*           pStyleParams,
WCharCP                     pSuggestedName
) const
    {
    AcDbObjectId    lastUsedStyleId = m_fromDgnContext->GetLastUsedMlineStyleId();
    if (!lastUsedStyleId.isNull())
        {
        AcDbMlineStylePointer lastUsed (lastUsedStyleId, AcDb::kForRead);
        if (this->MLineStyleMatchesDgnParameters (lastUsed, pStyleParams))
            return lastUsedStyleId;
        }

    AcDbObjectId                styleObjectId;
    AcDbDatabase*               pDatabase = m_fromDgnContext->GetDatabase ();
    AcDbDictionary*             pStyleDictionary = NULL;
    if (Acad::eOk != acdbOpenObject(pStyleDictionary, pDatabase->mLStyleDictionaryId (), AcDb::kForRead))
        {
        DIAGNOSTIC_PRINTF ("Failed to open MLStyle dictionary!\n");
        return  AcDbObjectId::kNull;
        }

    AcDbDictionaryIterator*     pIterator   = pStyleDictionary->newIterator();
    for (; !pIterator->done(); pIterator->next())
        {
        if ( (NULL != pSuggestedName) && (0 != wcsncmp (pSuggestedName, pIterator->name(), wcslen (pSuggestedName))) )
            continue;

        AcDbMlineStylePointer   pStyle (pIterator->objectId(), AcDb::kForRead);
        if (Acad::eOk != pStyle.openStatus())
            continue;

        if (this->MLineStyleMatchesDgnParameters (pStyle, pStyleParams))
            {
            lastUsedStyleId = m_fromDgnContext->SetLastUsedMlineStyleId (pIterator->objectId());
            delete pIterator;
            pStyleDictionary->close ();
            return  lastUsedStyleId;
            }
        }
    delete pIterator;

    // close style dictionary to allow re-open at next step:
    pStyleDictionary->close ();

    return m_fromDgnContext->SetLastUsedMlineStyleId (this->AddMLineStyle(pStyleParams, pSuggestedName));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 UsesElementSymbology
(
const MlineSymbology  *pSymbology
)
    {
    return !pSymbology->useColor || !pSymbology->useStyle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                GetOrAddMultilineStyleFromDGNStyle
(
MLineStyleParams*           pStyleParams,
ElementId                   styleId,
double                      scale
) const
    {
    AcDbObjectId            styleObjectId;

    if ( (styleObjectId = m_fromDgnContext->ExistingObjectIdFromElementId (styleId)).isNull() )
        return styleObjectId;   // isNull.

    AcDbMlineStylePointer   pStyleObject (styleObjectId, AcDb::kForRead);
    if ( (Acad::eOk != pStyleObject.openStatus()) || !pStyleObject->isKindOf (AcDbMlineStyle::desc()) )
        return styleObjectId;   // isNull.

    // Use the style directly only if no overrides applied (TR# 133933).
    if (pStyleParams->filled                                == pStyleObject->filled() &&
        pStyleParams->midCapSymbology->UseCapLine()         == pStyleObject->showMiters() &&
        pStyleParams->nProfiles                             == pStyleObject->numElements() &&
        this->CompareStartEndCaps(pStyleParams, pStyleObject) &&
        this->CompareStartEndAngles(pStyleParams, pStyleObject))
        {
        // AutoCAD does not support symbology taken from the entity - so if any of these exist,
        // we have to find a matching style.  If none exist - assume this style matches.
        double              justificationOffset = (MlineOffsetMode::ByCenter == pStyleParams->offsetMode) ? 0.0 : pStyleParams->placementOffset;

        int iProfile;
        for (iProfile = 0; iProfile <pStyleParams->nProfiles; iProfile++)
            {
            double          offset, styleOffset;
            AcCmColor       color, styleColor;
            AcDbObjectId    lineTypeId, styleLineTypeId;

            pStyleObject->getElementAt (iProfile, styleOffset, styleColor, styleLineTypeId);
            this->GetProfileParamsFromDgnProfile (&offset, &color, &lineTypeId, iProfile, justificationOffset, pStyleParams);

            if (UsesElementSymbology (&pStyleParams->pProfiles[iProfile].symb) || (fabs (scale * styleOffset - offset) > 1.0E-8) )              // Offset test added as partial fix for TR# 167517
                break;
            }

        if (iProfile == pStyleParams->nProfiles)
            return styleObjectId;
        }

    // We are using style from symbology.
    return this->GetOrAddMultilineStyleFromDGNParameters (pStyleParams, pStyleObject->name());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        ResetMlineStartPoint
(
AcDbMline*                  pMline,
DPoint3d&                   startPoint
)
    {
    RecordingFiler          filer(100);
    filer.RecordData (pMline);

#if defined (REALDWG_FILER_DEBUG)
    filer.DumpList ("AcDbMline data fields from DWGOUT filing:");
    /*-----------------------------------------------------------------------------------
    AcDbMline data fields from DWGOUT filing:

      0: kDwgSoftPointerId, ffd01cf8 fd281cc0 AcDbBlockTableRecord
      1: kDwgUInt32,    0 (0x0)
      2: kDwgHardOwnershipId, NULL
      3: kDwgHardPointerId, ffd01c80 fd281cb8 AcDbLayerTableRecord
      4: kDwgHardPointerId, ffd01ca8 fd281c90 AcDbLinetypeTableRecord
      5: kDwgHardPointerId, ffd01e70 fd281e48 AcDbMaterial
      6: kDwgUInt8,    0 (0x0)
      7: kDwgBool false
      8: kDwgBool false
      9: kDwgUInt16,  256 (0x100)
     10: kDwgReal 1.000000
     11: kDwgUInt16,    0 (0x0)
     12: kDwgUInt8,   29 (0x1d)
     13: kDwgHardPointerId, ffd01cc0 fd281cf8 AcDbMlineStyle
     14: kDwgReal 1.000000
     15: kDwgInt8,    0 (0x0)
     16: AcGePoint3d, 7.619955, 9.298989, 29.470963             // start point (DXF 10,20,30)
     17: AcGeVector3d, 0.577350, 0.577350, 0.577350             // normal
     18: kDwgInt16,    1 (0x1)
     19: kDwgInt8,    0 (0x0)
     20: kDwgInt32,    0 (0x0)
    -----------------------------------------------------------------------------------*/
#endif
    FilerDataList&          dataList = filer.GetDataList ();

#if RealDwgVersion == 2009
    int                     startPointIndex = 16;
#else
    int                     startPointIndex = 19;
#endif
    Point3dFilerData*       pointData = dynamic_cast <Point3dFilerData *> (dataList[startPointIndex]);
    if (NULL == pointData)
        return BadDataSequence;

    pointData->SetValue (startPoint);

    Acad::ErrorStatus   es = filer.PlaybackData (pMline);
#if defined (REALDWG_FILER_DEBUG)
    if (Acad::eOk == es)
        {
        RecordingFiler check (100);
        check.RecordData (pMline);
        check.DumpList ("AcDbMline data after DWGIN filing:");
        }
#endif

    return (Acad::eOk == es) ? RealDwgSuccess : BadDataSequence;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbObjectP          InstantiateMultilineObject (AcDbObjectP existingObject)
    {
    // because AcDbMline::setStyle fails on existing object, we always create a new one:
    AcDbObjectP     mlineObject = new AcDbMline ();

    // if we are editing an existing object, use its handle
    AcDbMline*      existingMline = AcDbMline::cast (existingObject);
    if (NULL != existingMline)
        existingObject->handOverTo (mlineObject, true, false);

    return  mlineObject;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
MlineBreak const*       GetFirstBreakInMlineElement () const
    {
    MSElementCP         element = m_mlineElement->GetElementCP ();
    if (NULL == element)
        return  NULL;

    MlinePoint const*   firstPoint = (MlinePoint const*) (element->mline.profile + element->mline.nLines);

    return (MlineBreak const*)(firstPoint + element->mline.nPoints);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32          GetBreakNumberAt (UInt32 pointNumber) const
    {
    MSElementCP         element = m_mlineElement->GetElementCP ();
    if (NULL == element)
        return  0;

    MlinePoint const*   firstPoint = (MlinePoint const*) (element->mline.profile + element->mline.nLines);

    return  firstPoint[pointNumber].breakNo;
    }

/*--------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            HasJointBreaksAt (UInt32 endPointNo) const
    {
    MSElementCP         element = m_mlineElement->GetElementCP ();
    if (NULL == element)
        return  0;

    MlinePoint const*   firstPoint = (MlinePoint const*) (element->mline.profile + element->mline.nLines);
    if (NULL == firstPoint)
        return  0;

    // check this point for FROM_JOINT break
    if (firstPoint[endPointNo].nBreaks > 0)
        {
        MlineBreak const*   lineBreak = m_firstBreak + firstPoint[endPointNo].breakNo;
        for (UInt32 i = 0; i < firstPoint[endPointNo].nBreaks; i++, lineBreak++)
            {
            if (0 != (lineBreak->flags & MLBREAK_FROM_JOINT))
                return  true;
            }
        }

    // check the other end point for TO_JOINT break
    if (0 == endPointNo)
        endPointNo = element->mline.nPoints - 1;
    else
        endPointNo = 0;

    if (firstPoint[endPointNo].nBreaks > 0)
        {
        MlineBreak const*   lineBreak = m_firstBreak + firstPoint[endPointNo].breakNo;
        for (UInt32 i = 0; i < firstPoint[endPointNo].nBreaks; i++, lineBreak++)
            {
            if (0 != (lineBreak->flags & MLBREAK_TO_JOINT))
                return  true;
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
double          GetBreakDistanceFromMiter (DPoint3dCR miter, DPoint3dCR direction, DPoint3dCR perpendicular, double perpDistance) const
    {
    double      miterDot = miter.DotProduct (perpendicular);
    double      miterDistance = perpDistance / (0.0 == miterDot ? 1.0 : miterDot);
    double      breakDistance = miterDistance * direction.DotProduct (miter);

    return  breakDistance;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    07/98
+---------------+---------------+---------------+---------------+---------------+------*/
bool            MultiLineContainsUnsupportedLineStylesOrLevels (ElementHandleR mline) const
    {
    MSElementP  pNCElem = const_cast <MSElementP> (mline.GetElementCP());
    UInt32      nLines = m_mlineHandler->GetProfileCount (mline);
    LevelId     headerLevel = pNCElem->ehdr.level;

    if (nLines > 16)
        return true;

    for (UInt32 iLine = 0; iLine < nLines; iLine++)
        {
        MultilineProfilePtr     profile = m_mlineHandler->GetProfile (mline, iLine);
        if (profile.IsValid())
            {
            LevelId             level = profile->GetLevel ();
            if (profile->UsesLevel() && 0 != level && level != headerLevel)
                {
                // If all lines have same level, switch to that level and keep testing  (TR# 143462).
                for (UInt32 j = 0; j < nLines; j++)
                    {
                    MultilineProfilePtr     testProfile = m_mlineHandler->GetProfile (mline, j);
                    LevelId                 testLevel = testProfile->GetLevel ();
                    if (testLevel != level)
                        return true;
                    }

                pNCElem->ehdr.level = level;
                }

            LineStyleParamsP    styleParams;
            if (profile->UsesLinestyle() && NULL != (styleParams = profile->GetLinestyleParams()) && 1.0 != styleParams->scale)
                return true;

            UInt32              style = profile->GetLinestyle ();
            if (m_fromDgnContext->DropUnsupportedLineStyles() && m_fromDgnContext->UnsupportableLineStyle(style, level))
                return true;
            }
        }

    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   DropMline (ElementHandleCR mline, AcDbObjectP& acObject, AcDbObjectP existingObject) const
    {
    /*-----------------------------------------------------------------------------------
    Since Vancouver dropping an mline results in components including filled and patterned
    boundaries.  Therefore there is no need to get boundaries and create new fill and/or
    hatch entities as we have done prior to Vancouver.
    -----------------------------------------------------------------------------------*/
    DropGeometry    dropMline (DropGeometry::OPTION_Mlines);

    return  m_fromDgnContext->DropElementToDwg (acObject, existingObject, mline, dropMline);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   AddHatchEntity (ElementHandleCR mline) const
    {
    /*-----------------------------------------------------------------------------------
    An multiline may have more than one patterns so we try to drop them all to boundary
    only loops.  Then we add the patterns on them so we can save them to DWG one at a time.
    -----------------------------------------------------------------------------------*/
    IAreaFillPropertiesQuery*   areaQuery = dynamic_cast <IAreaFillPropertiesQuery*> (m_mlineHandler);
    if (NULL == areaQuery)
        return  RealDwgSuccess;

    UInt32                      nCreated = 0;
    PatternParamsPtr            params;
    bvector<DwgHatchDefLine>    hatchDefs;
    DPoint3d                    origin;

    for (int i = 0; areaQuery->GetPattern(mline, params, &hatchDefs, &origin, i); i++)
        {
        ElementAgenda   dropped;
        DropGeometry    dropMline (DropGeometry::OPTION_Mlines);
        DropGraphics    dropPattern (DropGraphics::OPTION_Patterns);

        dropPattern.SetPatternBoundary (DropGraphics::BOUNDARY_Only);
        dropPattern.SetPatternIndex (i);

        if (BSISUCCESS != DropToElementDrawGeom::DoDrop(mline, dropped, dropMline, dropPattern) || dropped.IsEmpty())
            continue;

        FOR_EACH (EditElementHandleR boundaryElem, dropped)
            {
            IAreaFillPropertiesEdit*    areaEdit = dynamic_cast <IAreaFillPropertiesEdit*> (&boundaryElem.GetHandler());
            if (NULL == areaEdit)
                continue;

            // add pattern to the boundary element
            if (!areaEdit->AddPattern (boundaryElem, *params, (0 != hatchDefs.size () ? &hatchDefs.front () : NULL), i))
                {
                DIAGNOSTIC_PRINTF ("Failed adding pattern on mline boundary! ID=%I64d\n", mline.GetElementId());
                continue;
                }

            // Create a new hatch entity and add it to database
            AcDbHatch*  newHatch = new AcDbHatch ();
            if (m_fromDgnContext->AddEntityToCurrentBlock(AcDbEntity::cast(newHatch), 0).isNull())
                {
                DIAGNOSTIC_PRINTF ("Failed adding mline hatch to database! ID=%I64d\n", mline.GetElementId());
                delete newHatch;
                continue;
                }
                
            // convert the new pattern element to the hatch entity
            if (RealDwgSuccess != m_fromDgnContext->HatchFromElement(newHatch, boundaryElem, NULL, true, false))
                {
                DIAGNOSTIC_PRINTF ("Failed creating hatch from mline boundary! ID=%I64d\n", mline.GetElementId());
                newHatch->erase ();
                }
            else
                {
                newHatch->close ();
                nCreated++;
                }
            }
        }

    return  nCreated > 0 ? RealDwgSuccess : BadHatch;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus       ToObject
(
ElementHandleR              elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&                acObject,           // The object created, if any.
AcDbObjectP                 existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR      context             // The context
) const override
    {
    m_mlineHandler = dynamic_cast <MultilineHandler*> (&elemHandle.GetHandler());
    if (NULL == m_mlineHandler)
        return  BadElementHandler;

    UInt32                  nPoints = m_mlineHandler->GetPointCount (elemHandle);
    if (nPoints == 0)
        return BadMlineStyle;

    m_fromDgnContext = &context;
    if (this->MultiLineContainsUnsupportedLineStylesOrLevels(elemHandle))
        return  this->DropMline (elemHandle, acObject, existingObject);

    m_mlineElement = &elemHandle;
    m_firstBreak = this->GetFirstBreakInMlineElement ();

    // build local style parameters for comparison purpose:
    MLineStyleParams        styleParams;
    memset (&styleParams, 0, sizeof(styleParams));

    styleParams.nProfiles = m_mlineHandler->GetProfileCount (elemHandle);
    if (styleParams.nProfiles == 0)
        return BadMlineStyle;

    // get the profiles frome the mline element:
    for (int iProfile=0; iProfile < styleParams.nProfiles; iProfile++)
        {
        MultilineProfilePtr profile = m_mlineHandler->GetProfile (elemHandle, iProfile);
        if (profile.IsValid())
            styleParams.pProfiles[iProfile] = profile->GetProfileCR ();
        }

    MSElementCP             element = elemHandle.GetElementCP ();
    styleParams.elementColor        = element->hdr.dhdr.symb.color;
    styleParams.elementStyle        = element->hdr.dhdr.symb.style;
    styleParams.elementLevel        = element->ehdr.level;
    styleParams.filled              = m_mlineHandler->GetSolidFill (elemHandle, &styleParams.fillColor, NULL);
    styleParams.colorByLayer        = styleParams.styleByLayer = false;
    styleParams.offsetMode          = m_mlineHandler->GetOffsetMode (elemHandle);
    styleParams.placementOffset     = m_mlineHandler->GetPlacementOffset (elemHandle);
    styleParams.originCapSymbology  = m_mlineHandler->GetMlineCapByIndex (elemHandle, MULTILINE_ORG_CAP);
    styleParams.midCapSymbology     = m_mlineHandler->GetMlineCapByIndex (elemHandle, MULTILINE_MID_CAP);
    styleParams.endCapSymbology     = m_mlineHandler->GetMlineCapByIndex (elemHandle, MULTILINE_END_CAP);
    styleParams.originCapAngle      = m_mlineHandler->GetOriginAngle (elemHandle);
    styleParams.endCapAngle         = m_mlineHandler->GetEndAngle (elemHandle);

#if defined (NEEDS_WORK_LEVEL_SYMBOLOGY)
    if (context.GetUseLevelSymbologyOverrides() && NULL != (layer = this.getLayer (context.GetFileHolder()))) )
        {
        colorByLayer = layer.getColorOverride();
        styleByLayer = layer.getStyleOverride();
        }
#endif

    // Sort the profiles by descending distance.
    for (int iProfile=0; iProfile < styleParams.nProfiles; iProfile++)
        styleParams.indices[iProfile] = iProfile;

    for (int iProfile=0; iProfile < styleParams.nProfiles; iProfile++)
        {
        double      minimumDistance = styleParams.pProfiles[styleParams.indices[iProfile]].dist;
        for (int jProfile = iProfile+1; jProfile < styleParams.nProfiles; jProfile++)
            {
            double      testDistance = styleParams.pProfiles[styleParams.indices[jProfile]].dist;

            if (testDistance > minimumDistance)
                {
                minimumDistance     = testDistance;

                int     saveIndex   = styleParams.indices[iProfile];
                styleParams.indices[iProfile] = styleParams.indices[jProfile];
                styleParams.indices[jProfile] = saveIndex;
                }
            }
        }

    // If there is a style attached to the element. use it first.
    ElementId               dgnStyleId = m_mlineHandler->GetStyleID (elemHandle);
    AcDbObjectId            dwgStyleId;
    if (0 == dgnStyleId || (dwgStyleId = this->GetOrAddMultilineStyleFromDGNStyle(&styleParams, dgnStyleId, m_mlineHandler->GetStyleScale(elemHandle))).isNull())
        dwgStyleId = this->GetOrAddMultilineStyleFromDGNParameters (&styleParams, NULL);

    /*-----------------------------------------------------------------------------------
    AcDbMline::setStyle can not change existing style.  It can set a new style only when
    dwgMline never has a style.  Autodesk showed no intention to fix this, and offered a
    workaround to create a new mline, set style and hand over old object ID to the new
    mline.  Since the problem occurs only with editing an existing multiline, we need to 
    do this only if there is an existing mline.
    -----------------------------------------------------------------------------------*/
    acObject = InstantiateMultilineObject (existingObject);

    AcDbMline*          dwgMline = AcDbMline::cast (acObject);
    Acad::ErrorStatus   es = dwgMline->setStyle (dwgStyleId);
    if (Acad::eOk != es)
        {
        DIAGNOSTIC_PRINTF ("Error setting mlinestyle ID=%I64d!  [%ls]\n", dgnStyleId, acadErrorStatusText(es));
        return  MissingMlineStyle;
        }

    AcDbMlineStylePointer   dwgMlinestyle (dwgStyleId, AcDb::kForRead);
    if (Acad::eOk != dwgMlinestyle.openStatus())
        return  MissingMlineStyle;

    RotMatrix   rotMatrix;
    context.GetLocalTransform().GetMatrix (rotMatrix);

    DPoint3d      normal = element->mline.zVector;
    rotMatrix.Multiply (normal, normal);
    if (rotMatrix.Determinant() < 0.0)
        normal.Scale (normal, -1.0);

    dwgMline->setNormal (RealDwgUtil::GeVector3dFromDPoint3d(normal));

    // Scale is only used if style was used directly - else style was derived from the scaled profiles (TR# 167517).
    double      scale = m_mlineHandler->GetStyleScale (elemHandle);
    dwgMline->setScale (context.ElementIdFromObjectId(dwgStyleId) == dgnStyleId ? fabs(scale) : 1.0);

    switch (styleParams.offsetMode)
        {
        case MlineOffsetMode::ByMax:   /* Place by maximum   */
            // when roundtrip the negative scale, which flips profiles, we have to flip the justification from top to bottom:
            dwgMline->setJustification ((Mline::MlineJustification)(scale < 0.0 ? Mline::kBottom : Mline::kTop));
            break;

        case MlineOffsetMode::ByMin:   /* Place by minimum   */
            // when roundtrip the negative scale, which flips profiles, we have to flip the justification from bottom to top:
            dwgMline->setJustification ((Mline::MlineJustification)(scale < 0.0 ? Mline::kTop : Mline::kBottom));
            break;

        case MlineOffsetMode::ByWork:
        case MlineOffsetMode::ByCenter:
            default:
            dwgMline->setJustification (Mline::kZero);
            break;
        }

    bool            isClosed = m_mlineHandler->IsClosed (elemHandle);
    // No stupid closure point for AutoCAD.
    if (isClosed)
        nPoints--;

    // Empty current vertices.
    AcGePoint3d     lastSeg;
    while (dwgMline->numVertices() > 0)
        dwgMline->removeLastSeg (lastSeg);

    /*---------------------------------------------------------------------------------------------------
    We used to check for start & end angles against those from the style.  If not matched, we'd create a 
    separate DWG style for each and every mline entity we create here.  As a result, mlines that are corner 
    joint can produce too many otherwise the same mline styles(TFS#43830).  To reduce such an unnecessary 
    style duplication, we allow an mline to use a style with different start or end angles, by using line
    breaks that correctly stroke the start/end geometry.  However, our mline tools do not seem to always
    create such breaks for us.  When an mline is corner joint at current end point and next start point, 
    there are no joint breaks created by the operation, so we will have to add line breaks to ensure profile 
    lines to be cut at desired locations.  On the other hand, when an mline is corner joint by an end-end or 
    a start-start point, joint breaks are created for us, and we just simply use them.
    ---------------------------------------------------------------------------------------------------*/
    bool    sameStartAngle = Angle::NearlyEqualAllowPeriodShift (styleParams.originCapAngle, dwgMlinestyle->startAngle());
    bool    sameEndAngle   = Angle::NearlyEqualAllowPeriodShift (styleParams.endCapAngle, dwgMlinestyle->endAngle());
    bool    addStartBreaks = false, addEndBreaks = false;

    Transform                           transformFromDGN = context.GetTransformFromDGN ();
    double                              scaleFromDGN = context.GetScaleFromDGN ();
    int                                 vertexIndex = 0;
    bvector<AcGeVoidPointerArray>       vertexSegments;

    for (UInt32 iPoint = 0; iPoint < nPoints; iPoint++)
        {
        MultilinePointPtr   mlinePoint = m_mlineHandler->GetPoint (elemHandle, iPoint);
        DPoint3d            point = mlinePoint->GetPoint ();
        UInt32              nPointBreaks = mlinePoint->GetNumBreaks ();
        UInt32              breakNumber = this->GetBreakNumberAt (iPoint);
        DPoint3d            previous, next;
        UInt16              flags = 0;
        bool                useAngle = false;
        double              angleByMline = 0.0, angleByStyle = 0.0;
        bool                is2ndFromLastPoint = iPoint == nPoints - 2;

        // Get previous point.
        if (0 == iPoint)    // First Point.
            {
            if (!isClosed)
                {
                MultilinePointPtr   nextPoint = m_mlineHandler->GetPoint (elemHandle, iPoint+1);
                next = nextPoint->GetPoint ();
                previous.sumOf (NULL, &point, 2.0, &next, -1.0);
                angleByStyle = dwgMlinestyle->startAngle ();
                angleByMline = styleParams.originCapAngle;
                useAngle = true;

                // if mline angles do not match its style and no breaks created for that, add breaks at the first point:
                if (!sameStartAngle && !this->HasJointBreaksAt(0))
                    addStartBreaks = true;
                if (is2ndFromLastPoint && !sameEndAngle && !this->HasJointBreaksAt(1))
                    addEndBreaks = true;
                }
            else
                {
                MultilinePointPtr   prevPoint = m_mlineHandler->GetPoint (elemHandle, nPoints-1);
                if (prevPoint.IsValid())
                    previous = prevPoint->GetPoint ();
                }
            }
        else
            {
            MultilinePointPtr   prevPoint = m_mlineHandler->GetPoint (elemHandle, iPoint-1);
            previous = prevPoint->GetPoint ();

            if (point.isEqual (&previous))
                {
                DIAGNOSTIC_PRINTF ("Ignoring degenerate multiline segment\n");
                continue;
                }

            // check if we may need to add breaks for the last segment; otherwise clear out the flags.
            if (!isClosed && is2ndFromLastPoint && !sameEndAngle && !this->HasJointBreaksAt(nPoints - 1))
                addEndBreaks = true;
            else
                addStartBreaks = addEndBreaks = false;
            }

        // Get Next Point.
        if (!isClosed && iPoint == nPoints - 1)
            {
            next.sumOf (NULL, &point, 2.0, &previous, -1.0);
            angleByStyle = dwgMlinestyle->endAngle ();
            angleByMline = styleParams.endCapAngle;
            useAngle = true;
            }
        else
            {
            MultilinePointPtr   nextPoint = m_mlineHandler->GetPoint (elemHandle, iPoint+1);
            next = nextPoint->GetPoint ();
            }

        transformFromDGN.Multiply (point);
        transformFromDGN.Multiply (previous);
        transformFromDGN.Multiply (next);

        DPoint3d        miterByMline, miterByStyle, direction, previousDirection, perpendicular, previousPerpendicular;

        direction.NormalizedDifference (next, point);
        perpendicular.NormalizedCrossProduct (normal, direction);

        if (useAngle)
            {
            miterByMline.SumOf (direction, cos(angleByMline), perpendicular, sin(angleByMline));
            miterByStyle.SumOf (direction, cos(angleByStyle), perpendicular, sin(angleByStyle));
            }
        else
            {
            previousDirection.NormalizedDifference (point, previous);
            previousPerpendicular.NormalizedCrossProduct (normal, previousDirection);

            miterByMline.SumOf (perpendicular, previousPerpendicular);
            miterByMline.Normalize ();
            miterByStyle = miterByMline;
            }

        /*------------------------------------------------------------------------------------------------------
        Set the segment parameters, i.e. the first set of DXF group code 41's in a mline.  This is from the DXF 
        documentation:

        The group code 41 parameterization is a list of real values, one real per group code 41. The list may 
        contain zero or more items.
            - The first group code 41 value is the distance from the segment vertex along the miter vector to 
                the point where the line element's path intersects the miter vector. 
            - The next group code 41 value is the distance along the line element's path from the point defined 
                by the first group 41 to the actual start of the line element. 
            - The next is the distance from the start of the line element to the first break (or cut) in the line 
                element.
            - The successive group code 41 values continue to list the start and stop points of the line element 
                in this segment of the mline.
        Linetypes do not affect group 41 lists.
        ------------------------------------------------------------------------------------------------------*/
        AcGeVoidPointerArray        segmentArray(10*styleParams.nProfiles*nPointBreaks);
        for (int jProfile=0; jProfile < styleParams.nProfiles; jProfile++)
            {
            AcGeDoubleArray*        mlSegment = new AcGeDoubleArray (10*nPointBreaks);
            const MlineProfile*     pProfile = styleParams.pProfiles + styleParams.indices[jProfile];

            double                  segmentDistance = pProfile->dist * scaleFromDGN;
            double                  miterDot = miterByStyle.DotProduct (perpendicular);
            double                  miterDistance = segmentDistance / (0.0 == miterDot ? 1.0 : miterDot);
            double                  breakOffset = miterDistance * direction.DotProduct (miterByStyle);

            // the 1st parameter: the offset from current vertex to the end of the miter vector:
            mlSegment->append (miterDistance);

            // the 2nd parameter: the distance from the miter point to the actual start point of current vertex along the segment.
            if (addStartBreaks)
                {
                // the start-angle differs from the style and no joint breaks were created for that - start the profile line at the mline miter intersection:
                double              breakByMline = this->GetBreakDistanceFromMiter (miterByMline, direction, perpendicular, segmentDistance);
                mlSegment->append (breakByMline - breakOffset);
                }
            else
                {
                // the start-angles match - start the point at the default miter intersection with the profilce line
                mlSegment->append (0.0);
                }

            // the rest of the parameters: the distances between the starts & the ends of cutout segment of the profile, under various conditions.
            if (0 != nPointBreaks)
                {
                // get input mline breaks and add them to DWG mline
                MlineBreak const*   pLineBreak  = m_firstBreak + breakNumber;

                for (UInt32 kBreak = 0; kBreak < nPointBreaks; kBreak++, pLineBreak++)
                    {
                    if (0 != ((int) pLineBreak->lineMask & (1 << styleParams.indices[jProfile])))
                        {
                        if (0 != (pLineBreak->flags & MLBREAK_FROM_JOINT))
                            {
                            mlSegment->setAt (1, pLineBreak->length * scaleFromDGN);
                            }
                        else if (0 != (pLineBreak->flags & MLBREAK_TO_JOINT))
                            {
                            mlSegment->append (pLineBreak->offset * scaleFromDGN - breakOffset);
                            }
                        else
                            {
                            double  breakDistance = pLineBreak->offset * scaleFromDGN - breakOffset;

                            mlSegment->append (breakDistance);
                            mlSegment->append (breakDistance + pLineBreak->length * scaleFromDGN);
                            }
                        }
                    }
                }

            if (addEndBreaks)
                {
                // the end-angle differs to style and no joint breaks created for that - create breaks from the end angle
                // Note that we are adding a to-joint break on the last segment from the current point to the last point.
                double              segmentLength = point.Distance (next);
                DPoint3d            endMiter = DPoint3d::FromSumOf (direction, cos(styleParams.endCapAngle), perpendicular, sin(styleParams.endCapAngle));
                double              endBreak = this->GetBreakDistanceFromMiter (endMiter, direction, perpendicular, segmentDistance);

                endBreak = segmentLength + endBreak - breakOffset;

                mlSegment->append (endBreak);
                }

            segmentArray.append (mlSegment);
            }

        /*-------------------------------------------------------------------------------
        Reset start point (DXF group code 10,20,30).  Vertices to be appended will base
        upon old start point.  TR 272354.
        -------------------------------------------------------------------------------*/
        if (0 == vertexIndex)
            ResetMlineStartPoint (dwgMline, point);

        /*-------------------------------------------------------------------------------
        Potential RealDWG 2009 Bug:
        Below call results in an eWasOpenForRead error from mline style dictionary at the
        end of file save when we assert all opened objects.  Chances are that the method
        appendSeg opens the mline style dictionary used by this mline, but somehow fails
        to close it.  The resultant DWG file seems fine and can be opened back with no
        problem.
        -------------------------------------------------------------------------------*/
        es = dwgMline->appendSeg (RealDwgUtil::GePoint3dFromDPoint3d (point));
        vertexSegments.push_back (segmentArray);
        vertexIndex++;
        }

    dwgMline->setClosedMline (0 != isClosed);                     // Needs to be done after vertices in DwgDirect 1.11
    for (int iVertex = 0; iVertex < vertexIndex; iVertex++)         // TR# 129369 - Segment parameters need to be set after vertices in DwgDirect 1.11
        es = dwgMline->setParametersAt (iVertex, vertexSegments[iVertex]);

    if (vertexIndex < 2)
        {
        DIAGNOSTIC_PRINTF ("Error extracting from multiline with less than two unique vertices\n");
        return InsufficientMlineVertices;
        }

    // Supress mline start/end caps as necessary
    if (!isClosed && this->IsCapSuppressed(styleParams.originCapSymbology.get()) && this->HasStartCap(dwgMlinestyle))
        dwgMline->setSupressStartCaps (true);
    if (!isClosed && this->IsCapSuppressed(styleParams.endCapSymbology.get()) && this->HasEndCap(dwgMlinestyle))
        dwgMline->setSupressEndCaps (true);

    this->AddHatchEntity (elemHandle);

    return RealDwgSuccess;
    }

};  // ToDwgExtMultiline

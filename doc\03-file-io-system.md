# File I/O System

## Overview

The RealDwgFileIO framework implements a sophisticated file I/O system that handles the complexities of reading and writing both DWG/DXF and DGN file formats. The system is designed to provide seamless integration with the Bentley DgnPlatform while leveraging Autodesk's RealDWG library.

## File Format Support

### Supported Formats
- **DWG**: AutoCAD Drawing files (various versions)
- **DXF**: Drawing Exchange Format files
- **DGN**: MicroStation Design files (V8 format)

### Version Compatibility
- DWG: Supports AutoCAD versions from R12 through current
- DXF: ASCII and Binary formats
- DGN: V8 format with full feature support

## File Type Detection and Validation

### File Type Registration

The framework registers file types with the DgnPlatform system:

```cpp
RDWG_EXPORTED DgnFileTypeP dwgFileIO_getFileType(int fileType)
{
    if (DgnFileFormatType::DWG == (DgnFileFormatType)fileType)
        return new RealDwgFileType();
    else if (DgnFileFormatType::DXF == (DgnFileFormatType)fileType)
        return new RealDxfFileType();
    
    return NULL;
}
```

### File Validation Process

#### DWG File Validation
```cpp
bool RealDwgFileType::ValidateFile(
    DgnFileFormatType *pFormat, 
    int *pMajorVersion, 
    int *pMinorVersion, 
    bool *pDefaultModelIs3D, 
    IThumbnailPropertyValuePtr* thumbnail,
    const WChar *pName)
{
    // 1. Check file extension
    // 2. Read file header
    // 3. Validate DWG signature
    // 4. Extract version information
    // 5. Determine 2D/3D default model
    // 6. Extract thumbnail if available
}
```

#### DXF File Validation
- Similar process but adapted for DXF format specifics
- Handles both ASCII and binary DXF files
- Validates DXF section structure

## File Loading Architecture

### Loading Process Flow

```
File Open Request
       ↓
File Type Validation
       ↓
Factory Creation
       ↓
RealDwgFileIO::LoadFile()
       ↓
FileHolder Creation
       ↓
AutoCAD Database Loading
       ↓
Model Index Creation
       ↓
Ready for Access
```

### Detailed Loading Implementation

```cpp
StatusInt RealDwgFileIO::LoadFile(
    DesignFileHeader* pHdr,
    bool* openedReadonly,
    StatusInt* rwStatus,
    DgnFileOpenMode openMode,
    DgnFileLoadContext* context)
{
    // 1. Validate parameters and file access
    if (DGNFILE_STATUS_Success != (status = m_fileHandle.TryOpen(
        fileName, openMode, true, openedReadonly, rwStatus)))
        return status;

    // 2. Create FileHolder for database management
    m_dwgFileHolder = new FileHolder((DgnFileP)GetDgnFile(), fileName);

    // 3. Load AutoCAD database
    if (SUCCESS != (status = m_dwgFileHolder->LoadAcadDatabase()))
        return status;

    // 4. Create model index
    if (SUCCESS != (status = LoadModelHeaders()))
        return status;

    return SUCCESS;
}
```

## Database Management

### AutoCAD Database Lifecycle

The FileHolder class manages the AutoCAD database:

```cpp
StatusInt FileHolder::LoadAcadDatabase()
{
    try {
        // Create new AutoCAD database
        m_database = new AcDbDatabase(false, true);
        
        // Load from file based on format
        Acad::ErrorStatus status;
        if (DgnFileFormatType::DXF == m_dgnFile->GetOriginalFormat())
            status = m_database->dxfIn(fileName, NULL);
        else
            status = m_database->readDwgFile(fileName, 
                AcDbDatabase::kForReadAndAllShare);
        
        // Validate loaded database
        if (Acad::eOk == status && this->IsCorruptedDwgFile())
            return DgnFileStatus::DWGOPEN_STATUS_BadFile;
            
        return (Acad::eOk == status) ? SUCCESS : BSIERROR;
    }
    catch (...) {
        return BSIERROR;
    }
}
```

### Database Cleanup

Automatic cleanup is handled in the FileHolder destructor:

```cpp
FileHolder::~FileHolder()
{
    // Clean up AutoCAD database
    if (m_database) {
        delete m_database;
        m_database = nullptr;
    }
    
    // Clean up symbology data
    if (m_dwgSymbologyData) {
        delete m_dwgSymbologyData;
        m_dwgSymbologyData = nullptr;
    }
    
    // Additional cleanup...
}
```

## Model Management

### Model Index Creation

The framework creates an index of all models in the file:

```cpp
StatusInt RealDwgFileIO::LoadModelHeaders()
{
    // Delegate to FileHolder
    return m_dwgFileHolder->IndexAcadModels();
}

void FileHolder::IndexAcadModels()
{
    // Iterate through AutoCAD layouts
    AcDbLayoutManager* layoutManager = 
        acdbHostApplicationServices()->layoutManager();
    
    // Process each layout as a model
    for (each layout) {
        RealDwgModelIndexItem modelItem;
        modelItem.SetModelId(nextModelId++);
        modelItem.SetModelName(layoutName);
        modelItem.SetBlockTableRecordId(blockId);
        m_models.push_back(modelItem);
    }
}
```

### Model Loading on Demand

Models are loaded into cache when accessed:

```cpp
StatusInt RealDwgFileIO::_CreateCacheAndLoadHeader(
    DgnModelP& cache, 
    ModelId modelIDtoRead)
{
    // Create DGN model cache
    cache = DgnModel::CreateModel(GetDgnFile(), modelIDtoRead, ...);
    
    // Load model content from AutoCAD database
    return m_dwgFileHolder->ReadModelIntoCache(cache, modelIDtoRead, true);
}
```

## File Saving Architecture

### Save Process Flow

```
Save Request
       ↓
Collect Changes
       ↓
Convert DGN → DWG
       ↓
Update AutoCAD Database
       ↓
Write to File
       ↓
Update File Headers
       ↓
Complete
```

### Implementation Details

```cpp
StatusInt RealDwgFileIO::_WriteChanges(
    bool doFullSave, 
    double timestamp, 
    DesignFileHeader* hdr)
{
    // 1. Save non-cache changes (styles, tables, etc.)
    if (SUCCESS != (status = SaveNonCacheChangesToDatabase(doFullSave)))
        return status;

    // 2. Save model changes
    DgnModelIterator modelIter = GetDgnFile()->CreateModelIterator();
    for (DgnModelP model : modelIter) {
        if (model->HasPendingChanges()) {
            status = SaveModelChangesToDatabase(model, model->GetModelId());
            if (SUCCESS != status)
                return status;
        }
    }

    // 3. Write database to file
    return WriteDatabaseToFile(exportFonts);
}
```

### Database Writing

```cpp
StatusInt FileHolder::WriteFile(
    WCharCP pFileName,
    DgnFileFormatType format,
    int dxfPrecision,
    bool useVersionFromSettings,
    WCharCP pProgressComment)
{
    // Determine save version
    AcDb::AcDbDwgVersion saveVersion = GetSaveVersion(useVersionFromSettings);
    
    // Write based on format
    Acad::ErrorStatus errorStatus = WriteFileAs(
        m_database, pFileName, format, saveVersion, dxfPrecision);
    
    return (Acad::eOk == errorStatus) ? SUCCESS : BSIERROR;
}
```

## File Locking and Concurrency

### Exclusive Locking

The framework implements file locking for write operations:

```cpp
bool RealDwgFileIO::_CanExclusiveLock()
{
    return true;  // Framework supports exclusive locking
}

// File handle manages OS-level locking
StatusInt status = m_fileHandle.TryOpen(
    fileName, openMode, true, openedReadonly, rwStatus);
```

### Read-Only Access

Files can be opened in read-only mode:
- No exclusive lock required
- Multiple readers supported
- Automatic fallback for locked files

## Error Handling

### File Access Errors

```cpp
// Common error conditions
enum FileErrors {
    DWGOPEN_STATUS_BadFile,      // Corrupted or invalid file
    DWGOPEN_STATUS_FileNotFound, // File doesn't exist
    DWGOPEN_STATUS_AccessDenied, // Permission denied
    DWGOPEN_STATUS_FileLocked,   // File locked by another process
    DWGOPEN_STATUS_OutOfMemory   // Insufficient memory
};
```

### Recovery Mechanisms

- Automatic retry for temporary failures
- Graceful degradation for partial corruption
- User notification for unrecoverable errors

## Performance Optimizations

### Lazy Loading

- Models loaded only when accessed
- Symbology data cached on first use
- Progressive loading for large files

### Memory Management

- Automatic cleanup of unused resources
- Smart pointer usage for reference counting
- Memory pool allocation for frequent operations

### Caching Strategy

- Symbol table caching
- Style definition reuse
- Conversion result caching

## Configuration Options

### Environment Variables

- `MS_DWG_SAVE_MULTIPROCESSING`: Enable multi-threaded saving
- Various debugging and logging options

### Settings Integration

The framework integrates with MicroStation's settings system:

```cpp
IDwgConversionSettings& settings = 
    MstnInterfaceHelper::Instance().GetSettings();

// Access various conversion settings
DwgFileVersion saveVersion = settings.GetDwgSaveVersion();
DwgOpenUnitMode unitMode = settings.GetDwgSaveUnitMode();
```

## File Format Specifics

### DWG Format Handling
- Binary format with complex structure
- Version-specific handling
- Object enabler support for custom entities

### DXF Format Handling
- Text-based format (ASCII) or binary
- Section-based structure
- Precision control for numeric values

### DGN Format Integration
- Native V8 format support
- Element-based structure
- Full symbology preservation

This file I/O system provides robust, high-performance access to multiple CAD file formats while maintaining data integrity and supporting advanced features like concurrent access and progressive loading.

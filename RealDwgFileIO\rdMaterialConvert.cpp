/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdMaterialConvert.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
/* Convert DWG materials to DGN materials
/*=================================================================================**//*/
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt ConvertToDgnContext::SaveMaterialsToDgn()
{
    // 尝试获取材质字典
    AcDbDictionary* materialDictionary = NULL;
    if (Acad::eOk != this->GetFileHolder().GetDatabase()->getMaterialDictionary(materialDictionary, AcDb::kForRead))
    {
        DIAGNOSTIC_PRINTF ("Failed opening material dictionary!\n");
        return CantCreateMaterial;
    }

    // 收集现有材质名称,用于截断和去重新创建的名称
    bvector<WString> materialNames;
    MaterialPaletteList paletteList;
    if (BSISUCCESS == MaterialManager::GetManagerR().GetPalettesInDgnLib (paletteList, *m_dgnFile))
    {
        // 遍历每个调色板
        for (auto& palette : paletteList)
        {
            MaterialSearchStatus status = MaterialSearchStatus::Success;
            MaterialList dgnMaterials;
            // 在调色板中查找材质
            if (BSISUCCESS == MaterialManager::GetManagerR().FindMaterialsInPalette(&status, dgnMaterials, *palette, *m_model, false) 
                && MaterialSearchStatus::Success == status)
            {
                // 遍历材质列表,收集有效的材质名称
                for (auto* material : dgnMaterials)
                {
                    WCharCP existingName = nullptr;
                    if (nullptr != material && nullptr != (existingName = material->GetName(nullptr)) && 0 != existingName[0])
                        materialNames.push_back (WString(existingName));
                }
            }
        }
    }

    // 遍历材质字典
    MaterialPtrList materialHolder;      // 用于保持材质对象的生命周期
    MaterialList dgnMaterialList;        // 用于构建最终的材质列表
    AcDbDictionaryIterator* iterator = materialDictionary->newIterator();
    for (; !iterator->done(); iterator->next())
    {
        AcDbObject* object;
        if (Acad::eOk == iterator->getObject(object, AcDb::kForRead))
        {
            AcDbMaterial* dwgMaterial = AcDbMaterial::cast(object);
            // 跳过 BYLAYER 和 BYBLOCK 材质
            if (NULL != dwgMaterial && 0 != _wcsicmp(dwgMaterial->name(), L"BYLAYER") && 0 != _wcsicmp(dwgMaterial->name(), L"BYBLOCK"))
            {
                // 创建新的 DGN 材质
                MaterialPtr dgnMaterial = Material::Create(*m_model);

                // 转换材质属性
                if (SUCCESS == this->ConvertMaterial(dgnMaterial.get(), dwgMaterial, materialNames))
                {
                    materialHolder.push_back(dgnMaterial);
                    dgnMaterialList.push_back(materialHolder.back().get());
                }
                else
                {
                    DIAGNOSTIC_PRINTF("Failed converting material %ls, ID=%I64d!\n", 
                        dwgMaterial->name(), this->ElementIdFromObjectId(dwgMaterial->objectId()));
                }
            }
            object->close();
        }
    }

    materialDictionary->close();

    // 将转换后的材质添加到模型中
    EditElementHandle eeh;
    BentleyStatus status = MaterialManager::GetManagerR().BuildEEHForMaterials(eeh, dgnMaterialList, m_dgnFile);
    if (BSISUCCESS == status)
        eeh.AddToModel();

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
}

[Rest of the file remains unchanged...]

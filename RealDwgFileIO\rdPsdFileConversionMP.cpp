/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSatFileConversionMP.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

#include <Mstn\RealDWG\rSatToPs.h>

#include <PSolid\parasolid_kernel.h>
#include <PSolid\PSolidCore.h>
#include <PSolidAcisInterop\PSolidAcisInterop.h>
#include <Mstn\ISessionMgr.h>

#include <fstream>

USING_NAMESPACE_BENTLEY_SQLITE
USING_NAMESPACE_BENTLEY_DGNPLATFORM

BEGIN_BENTLEY_NAMESPACE
namespace RealDwg {

bvector<EntityCollector> g_entityCollectors;

BeFileName RootPsdDir ()
    {
    BeFileName fol (_wgetenv (L"AppData"));
    fol.AppendToPath (L"Bentley").AppendToPath (L"MicroStation").AppendToPath (L"PsdToAcis");

    BeFileName::CreateNewDirectory (fol.c_str ());
    return fol;
    }

BeGuid NewPsdCachedGuid::Id ()
    {
    if (!m_guid.IsValid ())
        {
        BeSQLiteLib::Initialize (RootPsdDir ());
        m_guid.Create ();
        }
    return m_guid;
    }

PsdDirectory::PsdDirectory (IGuidPtr guid)
    :m_guid (guid)
    {}

IGuidPtr PsdDirectory::Guid ()
    {
    return m_guid;
    }

BeFileName PsdDirectory::SessionDir ()
    {
    BeFileName fol = RootPsdDir ().AppendToPath (m_guid->IdAsWString ().c_str ());
    BeFileName::CreateNewDirectory (fol.c_str ());
    return fol;
    }

bvector<BeFileName> PsdDirectory::FilesInSessionDir (std::function<bool (BeFileNameCR)> predicate)
    {
    BeFileListIterator beIt (SessionDir () + L"\\*", false);
    bvector<BeFileName> fileNames;
    BeFileName fileName;
    while (beIt.GetNextFileName (fileName) == SUCCESS)
        {
        if (predicate (fileName))
            fileNames.push_back (fileName);
        }

    return fileNames;
    }

bool PsdDirectory::AnyPsdFile (BeFileNameR psdFile)
    {
    auto files = FilesInSessionDir ([] (BeFileNameCR fileName)
        {
        return BeFileName::GetExtension (fileName.c_str ()).EqualsI (L"x_b");
        });
    if (!files.empty ())
        {
        psdFile = files[0];
        return true;
        }
    return false;
    }

bool PsdDirectory::HasPsdFiles ()
    {
    return 
        !FilesInSessionDir (
            [] (BeFileNameCR fileName)
            {
            return BeFileName::GetExtension (fileName.c_str ()).EqualsI (L"x_b");
            })
        .empty ();
    }

bool PsdDirectory::IsEndState ()
    {
    return
        !FilesInSessionDir (
            [] (BeFileNameCR fileName)
            {
            return BeFileName::GetFileNameAndExtension (fileName.c_str ()).EqualsI (L"EndFile.txt");
            })
        .empty ();
    }

HANDLE PsdMutexChild::Open (WStringCR mutexName)
    {
    return OpenMutexW (
        MUTEX_ALL_ACCESS,            // request full access
        FALSE,                       // handle not inheritable
        mutexName.c_str ()
    );
    }
bool PsdMutexChild::IsValid (HANDLE mutexH)
    {
    return mutexH != nullptr;
    }
DWORD PsdMutexChild::WaitToAcquire (HANDLE mutexH)
    {
    return WaitForSingleObject (
        mutexH,    // handle to mutex
        INFINITE);   // no time-out interval
    }
bool PsdMutexChild::ReleasePsdMutex (HANDLE mutexH)
    {
    return ReleaseMutex (mutexH);
    }
void PsdMutexChild::Close(HANDLE mutexH)
    {
    CloseHandle (mutexH);
    }

BeFileName PsdFileProcessing::ProcessingDir ()
        {
        BeFileName dir = m_psdDir.SessionDir ().AppendToPath (L"Processing");
        BeFileName::CreateNewDirectory (dir.c_str ());
        return dir;
        }

BeFileName PsdFileProcessing::MovedToProcessingDir (BeFileNameCR psdFile)
    {
    BeFileName newFile = ProcessingDir ().AppendToPath (BeFileName::GetFileNameAndExtension (psdFile.c_str ()).c_str ());
    if (BeFileNameStatus::Success != BeFileName::BeMoveFile (psdFile, newFile))
        {
        m_log->Log (L"Error: failed to move psd file ", psdFile.c_str (), L" to ", newFile.c_str ());
        return BeFileName (L"");
        }

    return newFile;
    }

void PsdFileProcessing::FindFileToProcess ()
    {
    m_fileToProcess = BeFileName (L"");

    BeFileName psdFile;
    if (!m_psdDir.AnyPsdFile (psdFile))
        return;

    m_fileToProcess = MovedToProcessingDir (psdFile);

    if (BeFileName::DoesPathExist (m_fileToProcess.c_str ()))
        m_notifyToProcess = true;
    }    
#if 0
void PsdFileProcessing::FindEntitiesToProcess ()
    {
    if (!g_entityCollectors.empty())
        {
        m_entitiesToProcess = g_entityCollectors.back();
        m_notifyToProcess = true;
        }
    else
        {
        m_entitiesToProcess = EntityCollector();
        m_notifyToProcess = false;
        }
    }
#endif
PsdFileProcessing::PsdFileProcessing (ILogPtr log, WStringCR guid, PsdMutexChildPtr mutex)
    :m_appends (log), m_log (log), m_psdDir (new WStringAsGuid (guid)), m_mutexName (guid), m_mutex (mutex)
    {}


void PsdFileProcessing::ReadTransforms (BeFileName& psdFileName, bvector<Transform>&  transformList, int& satVersion)
    {
    BeFileName transformsFile (psdFileName.c_str ());
    transformsFile.AppendExtension (L"txt");

    std::wifstream in;
    in.open (transformsFile.c_str ());
    if (in.good ())
        {
        int         size = 0;
        Transform   t;

        in >> satVersion;

        in >> size;

        for (int i = 0; i < size; ++i)
            {
            in >> t.form3d[0][0] >> t.form3d[0][1] >> t.form3d[0][2] >> t.form3d[0][3];
            in >> t.form3d[1][0] >> t.form3d[1][1] >> t.form3d[1][2] >> t.form3d[1][3];
            in >> t.form3d[2][0] >> t.form3d[2][1] >> t.form3d[2][2] >> t.form3d[2][3];
            transformList.push_back (t);
            }
        }
    in.close ();
    }

void PsdFileProcessing::OutputTransforms (BeFileName& psdFileName, bvector<Transform>&  transformList, int satVersion)
    {
    BeFileName transformsFile (psdFileName.c_str ());
    transformsFile.AppendExtension (L"txt");
    std::wofstream out;
    out.open (transformsFile.c_str ());
    out << satVersion << std::endl;
    out << transformList.size () << std::endl;

    for (TransformCR t : transformList)
        {
        out << t.form3d[0][0] << L" " << t.form3d[0][1] << L" " << t.form3d[0][2] << L" " << t.form3d[0][3] << L" " << std::endl;
        out << t.form3d[1][0] << L" " << t.form3d[1][1] << L" " << t.form3d[1][2] << L" " << t.form3d[1][3] << L" " << std::endl;
        out << t.form3d[2][0] << L" " << t.form3d[2][1] << L" " << t.form3d[2][2] << L" " << t.form3d[2][3] << L" " << std::endl;
        }
    out.close ();
    }

bool PsdFileProcessing::ProcessPsdFile ()
    {
    BeFileName satFileName =
        m_psdDir
        .SessionDir ()
        .AppendToPath (BeFileName::GetFileNameWithoutExtension (m_fileToProcess.c_str ()).c_str ())
        .AppendExtension (L"sat");

    // load x_b file
    bvector<PK_ENTITY_t>    entities;
    bvector<Transform>      transforms;
    int                     satVersion;

    if (SUCCESS != PSolidUtil::ReadEntities(m_fileToProcess.c_str (), entities, transforms, satVersion))
        return false;

    PSolidAcisInterop::XMTEntitiesToSATFile (satFileName.c_str (), &entities.front(), &transforms[0], entities.size(), StandardUnit::MetricMeters, satVersion, L"");

    return true;
    }





bool PsdFileProcessing::AppendToPathEnv ()
    {
    BeFileName psdDir = m_appends.CurrentDir ();
    psdDir.PopDir ();
    psdDir.AppendToPath (L"Psd");

    if (!BeFileName::DoesPathExist (psdDir.c_str ()))
        return true; //test environment

    return m_appends.AddToPath (psdDir);
    }
bool PsdFileProcessing::Start ()
    {
    if (!AppendToPathEnv ())
        return false;

    PSolidAcisInterop::StartAcisSession ();
    PSolidKernelManager::StartSession ();
    return true;
    }
bool PsdFileProcessing::Process ()
    {
    HANDLE psdMutex = m_mutex->Open (m_mutexName);

    if (!m_mutex->IsValid (psdMutex))
        {
        m_log->Log (L"Error: failed to open mutex");
        return false;
        }

    if (!Start ())
        return false;

    bool endState = false;

    do
        {
        DWORD dwWaitResult = m_mutex->WaitToAcquire (psdMutex);

        switch (dwWaitResult)
            {
            // The thread got ownership of the mutex
            case WAIT_OBJECT_0:
                {
                //FindEntitiesToProcess ();
                FindFileToProcess ();
                // Release ownership of the mutex object
                if (!m_mutex->ReleasePsdMutex (psdMutex))
                    {
                    m_log->Log (L"Error: failed to release mutex");
                    }

                if (m_notifyToProcess)
                    {
                    //MessageBoxW (nullptr, m_fileToProcess.c_str (), L"SAT file to process", MB_OK);
                    if (!ProcessPsdFile ())
                        m_log->Log (L"Error: failed to generate sat file ", m_fileToProcess.c_str () );
                    m_notifyToProcess = false;
                    }
                break;
                }

            // The thread got ownership of an abandoned mutex
            // The database is in an indeterminate state
            case WAIT_ABANDONED:
                {
                m_log->Log (L"Error: the thread got ownership of an abandoned mutex");
                return false;
                }
            }
        endState = m_psdDir.IsEndState () && !m_psdDir.HasPsdFiles ();
        }
    while (!endState);

    CloseHandle (psdMutex);

    return true;
    }

bool PsdFileConversionMP::CreateUniqueMutex()
    {
    if (m_psdMutex != nullptr)
        return true;

    if (nullptr == (m_psdMutex = CreateMutexW (
        nullptr,
        FALSE,
        m_psdDir->Guid ()->IdAsWString ().c_str ()
    )))
        return false;
    return true;
    }
PsdFileConversionMP::PsdFileConversionMP (int maxProcesses, PsdDirectoryPtr psdDir)
    :m_maxChildrenProcesses (maxProcesses), m_psdDir (psdDir)
    {
    BeAssert (m_maxChildrenProcesses >= 1);
    }
PsdFileConversionMP::~PsdFileConversionMP ()
    {
    //SignalEnd ();
    BeFileName::EmptyAndRemoveDirectory (m_psdDir->SessionDir ().GetWCharCP());
    if (m_psdMutex != nullptr)
        CloseHandle (m_psdMutex);
    }

void PsdFileConversionMP::CollectEntities (EntityCollector& entities)
    {
    g_entityCollectors.push_back (entities);
    }
BeFileName PsdFileConversionMP::NewPsdFileName ()
    {
    BeFileName tempDir (m_psdDir->SessionDir ().AppendToPath (L"temp"));
    BeFileName::CreateNewDirectory (tempDir.c_str ());

    BeFileName baseName ((L"psdFile" + std::to_wstring (m_psdId++)).c_str ());
    baseName.AppendExtension (L"x_b");

    BeFileName psdFileName (tempDir);
    psdFileName.AppendToPath (baseName);
    return psdFileName;
    }

BeFileName PsdFileConversionMP::OutputToPsdAsynch (BeFileName& psdFileName)
    {
    //move it asynchronously to the directory to be processed by children processes
    if (!CreateUniqueMutex ())
        return BeFileName (L"");

    HANDLE psdMutex = OpenMutexW (
        MUTEX_ALL_ACCESS,            // request full access
        FALSE,                       // handle not inheritable
        m_psdDir->Guid ()->IdAsWString ().c_str ()
    );

    if (psdMutex == nullptr)
        return BeFileName (L"");

    if (WAIT_OBJECT_0 != WaitForSingleObject (
        psdMutex,    // handle to mutex
        INFINITE     // no time-out interval
    ))
        return BeFileName (L"");

    WString    fileName = BeFileName::GetFileNameAndExtension (psdFileName.c_str ());
    BeFileName destFileName = m_psdDir->SessionDir ().AppendToPath (fileName.c_str ());
    bool res = BeFileNameStatus::Success == BeFileName::BeMoveFile (psdFileName.c_str (), destFileName.c_str ());

    return ReleaseMutex (psdMutex) && res ? destFileName : BeFileName (L"");
    }

BeFileName PsdFileConversionMP::CurrentDir () const
    {
    HMODULE hModule = GetModuleHandleW (NULL);
    WCHAR path[MAX_PATH];
    GetModuleFileNameW (hModule, path, MAX_PATH);
    return BeFileName (BeFileName::GetDirectoryName (path).c_str ());
    }

BeFileName PsdFileConversionMP::ExeFileName () const
    {
    BeFileName exeFileName =
        CurrentDir ()
        .AppendToPath (L"MdlSys")
        .AppendToPath (L"AsNeeded")
        .AppendToPath (L"Smartsolid")
        .AppendToPath (L"PsdToSatProcessing")
        .AppendExtension (L"exe");
    
    if (BeFileName::DoesPathExist (exeFileName.c_str ()))
        return exeFileName;

    //Used in test environment
    exeFileName =
        CurrentDir ()
        .AppendToPath (L"PsdToSatProcessing")
        .AppendExtension (L"exe");

    if (BeFileName::DoesPathExist (exeFileName.c_str ()))
        return exeFileName;
    return BeFileName (L"");
    }

WString PsdFileConversionMP::CmdLine ()
    {
    return m_psdDir->Guid ()->IdAsWString ();
    }

bool PsdFileConversionMP::SpawnPsdToSatChildProcess ()
    {
    // additional information
    PROCESS_INFORMATION pi;
    STARTUPINFOW si;

    // set the size of the structures
    ZeroMemory (&si, sizeof (si));
    si.cb = sizeof (si);
    ZeroMemory (&pi, sizeof (pi));

    WString cmdLine (L" " + CmdLine ());
    auto cmd = const_cast<LPWSTR> (cmdLine.c_str ());

    BOOL success =
        CreateProcessW (
            ExeFileName ().c_str (),
            cmd,
            NULL,                                           // Process handle not inheritable
            NULL,                                           // Thread handle not inheritable
            FALSE,                                          // Set handle inheritance to FALSE
            CREATE_SUSPENDED | CREATE_NO_WINDOW,            // The primary thread of the new process is created in a suspended state, and does not run until the ResumeThread function is called.
            NULL,                                           // Use parent's environment block
            NULL,                                           // Use parent's starting directory 
            &si,                                            // Pointer to STARTUPINFO structure
            &pi                                             // Pointer to PROCESS_INFORMATION structure (removed extra parentheses)
        );

    if (success != TRUE)
        return false;

    //We want the children processes to stop if the parent process is killed
    //cf https://blogs.msdn.microsoft.com/oldnewthing/20131209-00/?p=2433
    HANDLE hJob = CreateJobObjectW (nullptr, nullptr);

    JOBOBJECT_EXTENDED_LIMIT_INFORMATION info = { };
    info.BasicLimitInformation.LimitFlags =
        JOB_OBJECT_LIMIT_KILL_ON_JOB_CLOSE;

    SetInformationJobObject (
        hJob,
        JobObjectExtendedLimitInformation,
        &info,
        sizeof (info)
    );

    success = AssignProcessToJobObject (hJob, pi.hProcess);
    if (success == TRUE)
        {
        success = ResumeThread (pi.hThread) != (DWORD) -1;
        }
    if (success != TRUE)
        {
        TerminateProcess (pi.hProcess, 0);
        CloseHandle (pi.hProcess);
        CloseHandle (pi.hThread);
        return false;
        }

    m_pis.push_back ({ pi, hJob });
    return true;
    }

bool PsdFileConversionMP::SpawnPsdToSatChildrenProcesses ()
    {
    bool res = true;
    if (m_maxChildrenProcesses <= 0)
        return res;

    SYSTEM_INFO sysinfo;
    GetSystemInfo (&sysinfo);
    m_maxChildrenProcesses = min ((int) sysinfo.dwNumberOfProcessors, m_maxChildrenProcesses);
    //m_maxChildrenProcesses  = 1;
    while (--m_maxChildrenProcesses >= 0)
        res = SpawnPsdToSatChildProcess () && res;
    return res;
    }

BeFileName PsdFileConversionMP::EndFile ()
    {
    BeFileName sessionDir = m_psdDir->SessionDir ();
    return sessionDir.AppendToPath (L"EndFile").AppendExtension (L"txt");
    }

void PsdFileConversionMP::SignalEnd ()
    {
    BeFile myFile;
    myFile.Create (EndFile ().c_str ());
    }

bool PsdFileConversionMP::WaitForFinish ()
    {
    SignalEnd ();

    bool success = true;
    for (SpawnedInfo& spanwedInfo : m_pis)
        {
        auto& pi = spanwedInfo.m_pi;

        // Wait for the process to finish
        DWORD result = WaitForSingleObject (pi.hProcess, INFINITE);

        // Get and verify the exit code
        DWORD exitCode = -1;
        success = (TRUE == GetExitCodeProcess (pi.hProcess, &exitCode)) && (0 == exitCode) && success;

        // Close process and thread handles. 
        CloseHandle (pi.hProcess);
        CloseHandle (pi.hThread);

        CloseHandle (spanwedInfo.m_hJob);

        success = (WAIT_OBJECT_0 == result) && success;
        }

    CloseHandle (m_psdMutex);
    m_psdMutex = nullptr;

    return success;
    }

bvector<BeFileName> PsdFileConversionMP::SatFileNames ()
    {
    auto satfilesfilter = [] (BeFileNameCR fileName) -> bool
        {
        return BeFileName::GetExtension (fileName.c_str ()).EqualsI (L"sat");
        };

    return m_psdDir->FilesInSessionDir (satfilesfilter);
    }

UniqueLogPsdFileName::UniqueLogPsdFileName (WStringCR strGuid)
    :UniqueLogPsdFileName (new WStringAsGuid (strGuid))
    {}
UniqueLogPsdFileName::UniqueLogPsdFileName (IGuidPtr guid)
    :m_psdDir (guid)
    {}
BeFileName UniqueLogPsdFileName::FileName ()
    {
    return m_psdDir.SessionDir ().AppendToPath (m_guid.IdAsWString ().c_str ()).AppendExtension (L"txt");
    }

} // Ends RealDWG namespace
END_BENTLEY_NAMESPACE

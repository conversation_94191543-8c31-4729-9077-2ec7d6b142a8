#--------------------------------------------------------------------------------------
#
#     $Source: mstn/mdlapps/RealDwgFileIO/rdSatToPSTests.mke $
#
#  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
#
#--------------------------------------------------------------------------------------

appName = SatToPsTests
%include mdl.mki

consoleAppUnicode = 1
baseDir = $(_MakeFilePath)

#----------------------------------------------------------------------
#       Add include directories
#----------------------------------------------------------------------

# As of R24, interop includes parasolid_kernel.h directly.
dirToSearch=$(BuildContext)VendorAPI/Psolid
%include cincapnd.mki

#----------------------------------------------------------------------
#       Create output directories
#----------------------------------------------------------------------
always:
    !~@mkdir $(o)

MultiCompileDepends = $(_MakeFileSpec)

%include MultiCppCompileRule.mki

hFilesDependencies = $(baseDir)rdSatFileProcessing.h

#----------------------------------------------------------------------
#        All files
#----------------------------------------------------------------------

$(o)rdSatFileConversionMP.obj				: $(baseDir)rdSatFileConversionMP.cpp ${MultiCompileDepends} $(hFilesDependencies)

$(o)rdSatFileConversionMPTests.obj			: $(baseDir)rdSatFileConversionMPTests.cpp ${MultiCompileDepends} $(hFilesDependencies)

%include MultiCppCompileGo.mki

EXE_DEST     = $(o)
EXE_NAME     = $(appname)
EXE_OBJS     = $(MultiCompileObjectList)
LINKER_LIBRARIES  = $(ContextSubpartsLibs)BentleyAllocator.lib
LINKER_LIBRARIES  + $(ContextSubpartsLibs)Bentley.lib
LINKER_LIBRARIES  + $(ContextSubpartsLibs)BentleyGeom.lib
LINKER_LIBRARIES  + $(ContextSubpartsLibs)DgnPlatform.lib
LINKER_LIBRARIES  + $(ContextSubpartsLibs)BentleyGeomSerialization.lib
LINKER_LIBRARIES  + $(ContextSubpartsLibs)gtest.lib
LINKER_LIBRARIES  + $(ContextSubpartsLibs)BeSQLite.lib
LINKER_LIBRARIES  + $(ContextSubpartsLibs)pskernel.lib
LINKER_LIBRARIES  + advapi32.lib
LINKER_LIBRARIES  + shlwapi.lib
LINKER_LIBRARIES  + Rpcrt4.lib

LINKER_LIBRARIES_DELAYLOADED = $(ContextSubpartsLibs)mspsolid.lib \
	$(ContextSubpartsLibs)ToolscriptingLib.lib \
	$(ContextSubpartsLibs)PSolidAcisInterop.lib

EXE_TMP_DIR  = $(o)

LinkWarningsToErrorsOptions + -Ignore:4204  # Ignoring LNK4204 as it is trying to link objects.pdb (ideally we should remove the linkning of this pdb from compilation while consuming parts as LKGs)

%include $(sharedMki)linktool.mki

always:
    ~linkfile  "$(BuildContext)Delivery/$(appname)$(exeext)=$(o)$(appname)$(exeext)"

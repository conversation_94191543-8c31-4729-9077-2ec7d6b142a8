#--------------------------------------------------------------------------------------
#
#     $Source: mstn/mdlapps/RealDwgFileIO/VS2010Enums.py $
#
#  $Copyright: (c) 2013 Bentley Systems, Incorporated. All rights reserved. $
#
#--------------------------------------------------------------------------------------

# Dwg parts require their own headers to compile, since they aren't compatible with VC11
# enum classes.  This script updates all header files contained in the root specified in
# the first argument.

import os
import re
import sys
import string
import shutil

import timeit

oldRegexList = []
newStringList = []
fileList = []

#-----------------------------------------------------------------------------#
#                                               Justin <PERSON>           07/13
#-------+---------+---------+---------+---------+---------+---------+---------#
def revertClassesInFile(fileName):
    with open (fileName, 'r') as rFile:
        fileData = rFile.readlines()

    classRegex = re.compile(r'\benum\s+class\s+\w+\b')
    for lineNum in range(0, len(fileData)):
        nameRegex = re.search(classRegex, fileData[lineNum])
        if nameRegex and not isInComment(fileData[lineNum], nameRegex.group(0).split()[-1].rstrip('\n')):
            nameString = nameRegex.group(0).split()[-1].rstrip('\n')
            oldRegexList.append(re.compile(nameString.rstrip('\n') + r'::'))
            newStringList.append(nameString.rstrip('\n') + r'_')
            fileData[lineNum] = re.sub(re.compile(r'\benum\s+class\b'), 'enum', fileData[lineNum])
            bracketOffset = 1
            for lineOffset in range (1, len(fileData) - lineNum):
                if re.search(r'\{', fileData[lineNum + lineOffset]):
                    bracketOffset = lineOffset
                    break
            for memberLineNum in range(lineNum + bracketOffset, len(fileData)):
                currentLine = fileData[memberLineNum]
                if re.search(r'\w+', currentLine):
                    firstWord = re.search(r'\b\w+\b', currentLine).group(0)
                    if not isInComment(currentLine, firstWord):
                        fileData[memberLineNum] = re.sub(firstWord, nameString.rstrip('\n') + '_' + firstWord, currentLine)
                if re.search(r'\}', currentLine):
                    break

    with open (fileName, 'w') as wFile:
        for line in fileData:
            wFile.write(line)

#-----------------------------------------------------------------------------#
#                                               Justin Ying           07/13
#-------+---------+---------+---------+---------+---------+---------+---------#
def revertClassesInFileList():
    for file in fileList:
        revertClassesInFile(file)

#-----------------------------------------------------------------------------#
#                                               Justin Ying           07/13
#-------+---------+---------+---------+---------+---------+---------+---------#
def isInComment(line, word):
    if string.find(line, '//') == -1 or string.find(line, r'//') > string.find(line, word):
        return False
    else:
        return True

#-----------------------------------------------------------------------------#
#                                               Justin Ying           07/13
#-------+---------+---------+---------+---------+---------+---------+---------#
def replaceClassNames(fileName):
    with open (fileName, 'r') as rFile:
        fileData = rFile.readlines()
    with open (fileName, 'w') as wFile:
        for line in fileData:
            if '::' in line:
                for remapPair in range (0, len(newStringList)-1):
                    line = re.sub(oldRegexList[remapPair], newStringList[remapPair], line)
            wFile.write(line)

#-----------------------------------------------------------------------------#
#                                               Justin Ying           07/13
#-------+---------+---------+---------+---------+---------+---------+---------#
def replaceClassNamesInFileList():
    for file in fileList:
        replaceClassNames(file)

#-----------------------------------------------------------------------------#
#                                               Justin Ying           07/13
#-------+---------+---------+---------+---------+---------+---------+---------#
def initFileList(rootDir):
    for root, directoryNames, fileNames in os.walk(rootDir):
        for file in fileNames:
            if not file.endswith('.ini') and not file.endswith('.orig'):
                fileList.append(os.path.join(root, file))

#-----------------------------------------------------------------------------#
#                                               Justin Ying           07/13
#-------+---------+---------+---------+---------+---------+---------+---------#
# From Mital Vora (StackOverflow)
def copytree(src, dst, symlinks=False, ignore=None):
    if not os.path.exists(dst):
        os.makedirs(dst)
    for item in os.listdir(src):
        s = os.path.join(src, item)
        d = os.path.join(dst, item)
        if os.path.isdir(s):
            copytree(s, d, symlinks, ignore)
        else:
            shutil.copyfile(s, d)

#-----------------------------------------------------------------------------#
#                                               Justin Ying           07/13
#-------+---------+---------+---------+---------+---------+---------+---------#
def main():
    print "Arg1", sys.argv[1]
    print "Arg2", sys.argv[2]

    print "Copying files...".ljust(25),
    time = timeit.timeit("copytree(sys.argv[1], sys.argv[2])", setup="from __main__ import copytree", number=1)
    print "Done in {0:5.2f} seconds".format(time)

    print "Init FileList...".ljust(25),
    time = timeit.timeit("initFileList(sys.argv[2])", setup="from __main__ import initFileList", number=1)
    print "Done in {0:5.2f} seconds".format(time)

    print "Reverting files...".ljust(25),
    time = timeit.timeit("revertClassesInFileList()", setup="from __main__ import revertClassesInFileList", number=1)
    print "Done in {0:5.2f} seconds".format(time)

    print "Replacing class names...".ljust(25),
    time = timeit.timeit("replaceClassNamesInFileList()", setup="from __main__ import replaceClassNamesInFileList", number=1)
    print "Done in {0:5.2f} seconds".format(time)

#-----------------------------------------------------------------------------#
#                                               Justin Ying           07/13
#-------+---------+---------+---------+---------+---------+---------+---------#            
if __name__ == '__main__':
    main()
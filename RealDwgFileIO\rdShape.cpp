/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdShape.cpp $
|
|  $Copyright: (c) 2014 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtShape : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbShape*          acShape = AcDbShape::cast (acObject);
    RotMatrix           ecsMatrix;
    RealDwgUtil::RotMatrixFromArbitraryGeAxis (ecsMatrix, acShape->normal());

    // get and transform shape position, in both forms of AcGePoint3d and DPoint3d:
    AcGePoint3d         position = acShape->position ();
    DPoint3d            origin;
    RealDwgUtil::DPoint3dFromGePoint3d (origin, position);
    ecsMatrix.Multiply (origin);
    position = RealDwgUtil::GePoint3dFromDPoint3d (origin);

    // get text params
    TextSizeParam       textSize;
    RotMatrix           matrix;
    context.GetDgnTextTransformFromDwg (&textSize, &origin, &matrix, acShape->normal(), position, acShape->rotation(), acShape->size(), acShape->widthFactor());

    DPoint2d            fontSize = DPoint2d::From (textSize.size.width, textSize.size.height);
    DgnModelP           model = context.GetModel()->GetDgnModelP ();
    TextParamWide       textParams;
    context.GetDgnTextParamsFromDwg (&textParams, AcDb::kTextLeft, AcDb::kTextBottom, acShape->oblique(), false /* Vertical */, acShape->styleId());

    // set the symbol in shape code
    UInt8               shapeChars[2] = { 0 };
    shapeChars[0] = acShape->shapeNumber ();

    StatusInt   status = TextElemHandler::CreateElementFromShapeCodes (outElement, NULL, origin, matrix, fontSize, textParams, shapeChars, 1, model->Is3d(), *model);

    if (BSISUCCESS == status)
        context.ElementHeaderFromEntity (outElement, acShape);

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }
};  // ToDgnExtShape


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertFromDgnContext::SetAcDbShapeFromTextElement
(
AcDbShape*              acShape,
ElementHandleR          inElement
)
    {
    if (NULL == acShape)
        return  NullObject;

    TextBlock           textBlock(inElement);
    if (textBlock.IsEmpty())
        return  EmptyText;

    DgnTextStylePtr     dgnTextStyle = textBlock.GetTextStyle ();
    if (!dgnTextStyle.IsValid())
        return  CantCreateShapeEntity;

    AcDbObjectId        textStyleId = this->ExistingObjectIdFromElementId (dgnTextStyle->GetElementId());
    if (!textStyleId.isValid())
        return  CantCreateShapeEntity;

    // set text style
    if (textStyleId != acShape->styleId())
        {
        AcDbTextStyleTableRecordPointer     dwgTextstyle(textStyleId, AcDb::kForRead);
        Acad::ErrorStatus                   status = dwgTextstyle.openStatus ();
        /*-------------------------------------------------------------------------------
        Shape entity requires a shape text style.  In the event the style is replaced
        with a non-shape textstyle, return error to create a text/mtext instead.
        -------------------------------------------------------------------------------*/
        if (Acad::eOk == status && dwgTextstyle->isShapeFile())
            status = acShape->setStyleId (textStyleId);
        else
            return  CantCreateShapeEntity;
        }

    // set shape number, DXF group code 210
    Bentley::WString    string;
    textBlock.ToDText (string, GetModel (), false);
    if (string.size() > 1 || Acad::eOk != acShape->setShapeNumber(string.at(0)))
        return  CantCreateShapeEntity;

    /*-----------------------------------------------------------------------------------------------------
    Missing shape file results in the entire DXF file unable to open in either ACAD or RealDWG - TR 296209.
    DWG is a little better, at least it opens, although the shape does not display.  AcDbShape::name will
    call hostApp's findFile, which won't find it anyway if missing.  So just bail out for DXF creation.
    -----------------------------------------------------------------------------------------------------*/
    ACHAR*  shapeName = acShape->name ();
    if ((NULL == shapeName || 0 == shapeName) && DgnFileFormatType::DXF == this->GetFormat())
        return  CantCreateShapeEntity;
    else if (NULL != shapeName)
        acutDelString (shapeName);

    // set shape position
    DPoint3d            origin = DPoint3d::FromZero();
    if (BSISUCCESS != TextElemHandler::GetElementOrigin(inElement, origin))
        BeAssert (false && L"Failed in TextHandler::GetElementOrigin!");

    this->GetTransformFromDGN().Multiply (origin);
    acShape->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (origin));

    // set shape size
    RunPropertiesCP     runProperties = textBlock.GetRunProperties (0);
    DPoint2d            fontSize = runProperties->GetFontSize ();
    acShape->setSize (fontSize.y * this->GetScaleFromDGN());

    // update x-scale factor
    double  widthFactor = fontSize.y >= TOLERANCE_TextHeight ? fontSize.x / fontSize.y : 1.0;
    if (fabs(acShape->widthFactor() - widthFactor) > TOLERANCE_ZeroScale)
        acShape->setWidthFactor (widthFactor);

    // set shape orientation
    double              extrusionAngle;
    DPoint3d            extrusionDirection;
    RealDwgUtil::ExtractExtrusion (extrusionDirection, extrusionAngle, textBlock.GetOrientation());
    acShape->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (extrusionDirection));
    acShape->setRotation (extrusionAngle);

    return RealDwgSuccess;
    }

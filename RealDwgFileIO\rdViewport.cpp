/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdViewport.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    <PERSON>    06/98
+---------------+---------------+---------------+---------------+---------------+------*/
static DgnModelRefP         GetModelChildModelRefFromViewportChildModelRef (DgnModelRefP viewportChildModelRef)
    {
    int                     nPath;
    ElementId               pathArray[MAX_RefPathDepth];
    DgnModelRefP            modelChildModelRef = NULL, modelModelRef = NULL, parentModelRef = NULL, modelRef;

    for (nPath = 0, modelRef = viewportChildModelRef;
            NULL != modelRef && NULL != (parentModelRef = modelRef->GetParentModelRefP()) && NULL != parentModelRef->GetParentModelRefP();
                modelRef = parentModelRef)
        {
        DgnAttachmentCP     refFile = modelRef->AsDgnAttachmentCP ();
        pathArray[nPath++] = NULL == refFile ? 0 : refFile->GetElementId();
        }

    if (NULL == parentModelRef)
        return  NULL;

    DgnFileP        dgnFile = parentModelRef->GetDgnFileP ();
    if (NULL == dgnFile)
        return  NULL;

    ModelId         defaultModelID = dgnFile->GetDefaultModelId ();
    if (parentModelRef->GetModelId() == defaultModelID)
        modelModelRef = parentModelRef;
    else
        modelModelRef = dgnFile->FindLoadedModelById (defaultModelID);

    if (NULL == modelModelRef)
        return NULL;

    int             i;
    for (i=nPath-1, parentModelRef = modelModelRef; i >= 0; i--, parentModelRef = modelChildModelRef)
        {
        modelChildModelRef = DgnAttachment::FindByElementId (parentModelRef, pathArray[i]);
        if (NULL == modelChildModelRef)
            return NULL;
        }

    return modelChildModelRef;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    06/98
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       ConvertFromDgnContext::GetFrozenLayersFromViewportModelRef (ElementId* pFrozenLayerIds, DgnModelRefP pReferenceModelRef)
    {
    int                     nFrozenLayers   = 0;
    FileHolder&             fileHolder     = this->GetFileHolder();

    DgnModelRefP            pChildModelRef;
    ModelRefIterator        mrIterator (pReferenceModelRef, MRITERATE_Root | MRITERATE_PrimaryChildRefs, -1);
    for (pChildModelRef = mrIterator.GetFirst(); NULL != pChildModelRef; pChildModelRef = mrIterator.GetNext())
        {
        BitMaskCP           pFrozenBitMask = NULL;

        if (NULL != (pFrozenBitMask = pChildModelRef->GetEffectiveFrozenMask()))
            {
            int                     displayOn           = 1;
            BitMaskP                pDisplayBitMask     = NULL;
            DgnModelRefP            modelChildModelRef  = NULL;
            AcDbBlockTableRecord*   pXRefBlock          = NULL;

            if (!this->SavingChanges())
                {
                // Start with the global, per reference flags   (TR# 167999).
                BitMaskCP       levelBitMask = pChildModelRef->GetLevelCache().GetMask (LevelCache::MASKID_Display, *pChildModelRef);
                if (NULL == levelBitMask)
                    return  BSIERROR;

                pDisplayBitMask = BitMask::Clone (*levelBitMask);
                if (NULL == pDisplayBitMask)
                    return  BSIERROR;

                // If we're doing a view, and in the view masks.
                if (this->GetLevelDisplayView() >= 0)
                    {
                    BitMaskP    pViewDisplayBitMask = LayerFromLevel::ExtractByViewLevelDisplayMask (this->GetModel(), pChildModelRef, this->GetLevelDisplayView(), NULL, false);

                    if (NULL != pViewDisplayBitMask)
                        {
                        pDisplayBitMask->And (*pViewDisplayBitMask);
                        pViewDisplayBitMask->Free ();
                        }
                    }

                pDisplayBitMask->InvertAll ();
                pDisplayBitMask->Or (*pFrozenBitMask);
                pFrozenBitMask = pDisplayBitMask;
                }

            if (pChildModelRef != pReferenceModelRef)
                {
                displayOn = pChildModelRef->AsDgnAttachmentCP()->IsDisplayed ();

                modelChildModelRef = pReferenceModelRef->IsDgnAttachment() ? GetModelChildModelRefFromViewportChildModelRef (pChildModelRef) : pChildModelRef;

                AcDbObjectId            xRefBlockId;
                if (NULL == modelChildModelRef || (xRefBlockId = fileHolder.GetXRefBTRObjectIdByModelRef (modelChildModelRef->AsDgnAttachmentCP())).isNull())
                    continue;

                if (Acad::eOk != acdbOpenObject (pXRefBlock, xRefBlockId, AcDb::kForRead, false))
                    continue;
                }

            LevelCacheCR        levelCache = pChildModelRef->GetLevelCache ();
            for each (LevelHandle levelHandle in levelCache)
                {
                LevelId         levelId = levelHandle.GetLevelId ();

                // Don't VP Freeze the default level - this can turn off reference files.
                if (this->GetMSInsertLayer().isNull() && LEVEL_DEFAULT_LEVEL_ID == levelId)
                    continue;
                // When we do VP Freeze Only, there is no need to VP Freeze a level which is globally frozen or turned off
                if (VPFreeze_ViewportsOnly == this->GetSettings().GetViewportFreezeMode() && (levelHandle.GetFrozen() || !levelHandle.GetDisplay()))
                    continue;

                // if display off, add the layer to frozen layer list
                if (0 == displayOn || pFrozenBitMask->Test(levelId-1))
                    {
                    AcDbObjectId    layerId;

                    if (!(layerId = fileHolder.GetLayerByXRefAndLevelHandle(levelHandle, pXRefBlock, modelChildModelRef, this->SavingChanges())).isNull())
                        pFrozenLayerIds[nFrozenLayers++] = this->ElementIdFromObjectId (layerId);
                    }
                }

            if (NULL != pDisplayBitMask)
                pDisplayBitMask->Free ();

            if (NULL != pXRefBlock)
                pXRefBlock->close();
            }
        }

    return nFrozenLayers;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/10
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetRenderingPlot (MSElementP element, AcDbViewport::ShadePlotType shadePlotType, AcDbObjectId shadePlotId, ConvertToDgnContextR context)
    {
    if (AcDbViewport::kAsDisplayed == shadePlotType)
        return  BSIERROR;

    MSRefRenderingPlotLinkage   renderPlotLinkage;
    memset (&renderPlotLinkage, 0, sizeof renderPlotLinkage);

    renderPlotLinkage.header.primaryID = LINKAGEID_ReferenceRenderingPlot;
    renderPlotLinkage.header.user      = TRUE;

    int     linkageBytes = (sizeof(MSRefRenderingPlotLinkage) + 7) & ~7;
    LinkageUtil::SetWords (&renderPlotLinkage.header, static_cast<int>(linkageBytes/sizeof(short)));

    /*-----------------------------------------------------------------------------------
    Same workaround missing visual style support: prior to fully support visual style and
    rendering setting objects, try to use render mode.
    -----------------------------------------------------------------------------------*/
    switch (shadePlotType)
        {
        case AcDbViewport::kVisualStyle:
            {
            MSRenderMode    renderMode = RealDwgUtil::GetRenderModeFromVisualStyle (shadePlotId);
            if (MSRenderMode::HiddenLine == renderMode || MSRenderMode::SolidFill == renderMode || MSRenderMode::ConstantShade == renderMode)
                renderPlotLinkage.plotType = REF_PLOTTYPE_Hidden;
            else if (renderMode >= MSRenderMode::SmoothShade)
                renderPlotLinkage.plotType = REF_PLOTTYPE_Rendered;
            else
                renderPlotLinkage.plotType = REF_PLOTTYPE_Wireframe;
            
            renderPlotLinkage.elementId   = 0;
            break;
            }
        case AcDbViewport::kRenderPreset:
            renderPlotLinkage.plotType    = REF_PLOTTYPE_Rendered;
            renderPlotLinkage.elementId   = 0;
            break;
        default:
            renderPlotLinkage.plotType    = shadePlotType;
            renderPlotLinkage.elementId   = context.ElementIdFromObjectId (shadePlotId);
            break;
        }

    // we are creating a new element so there is no existing linkage yet.
    return Bentley::elemUtil_appendLinkage (element, &renderPlotLinkage.header);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/10
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            GetRenderingPlot (AcDbViewport::ShadePlotType& plotType, AcDbObjectId& shadePlotId, MSElementCP element, ConvertFromDgnContextR context)
    {
    MSRefRenderingPlotLinkage   renderPlotLinkage;
    if (NULL == linkage_extractFromElement(&renderPlotLinkage, element, LINKAGEID_ReferenceRenderingPlot, NULL, NULLFUNC, NULL))
        return  MDLERR_LINKAGENOTFOUND;

    plotType     = (AcDbViewport::ShadePlotType)renderPlotLinkage.plotType;
    shadePlotId  = AcDbObjectId::kNull;

    if (0 != renderPlotLinkage.elementId && INVALID_ELEMENTID != renderPlotLinkage.elementId &&
        Acad::eOk != context.GetFileHolder().GetDatabase()->getAcDbObjectId(shadePlotId, false, context.DBHandleFromElementId(renderPlotLinkage.elementId), 0))
        {
        // validate plot data
        shadePlotId = AcDbObjectId::kNull;
        if (REF_PLOTTYPE_UseDisplayStyle == renderPlotLinkage.plotType || REF_PLOTTYPE_UseRenderingStyle == renderPlotLinkage.plotType)
            plotType = AcDbViewport::kAsDisplayed;
        }

    return  SUCCESS;
    }


    
/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtViewport : public ToDgnExtension
{

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   11/08
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsOverallViewport (AcDbObjectId viewportObjectId, ConvertToDgnContextR context)
    {
    AcDbBlockTableRecordPointer pCurrentBlock (context.GetCurrentBlockId(), AcDb::kForRead);
    if (Acad::eOk != pCurrentBlock.openStatus())
        return false;

    AcDbObjectId    layoutId;
    if ( (layoutId = pCurrentBlock->getLayoutId()).isNull())
        return false;

    AcDbLayoutPointer pLayout (layoutId, AcDb::kForRead);
    if (Acad::eOk != pLayout.openStatus())
        return false;

#if defined (REALDWG_DIAGNOSTICS)
    const ACHAR*    blockName;
    pCurrentBlock->getName (blockName);
    const ACHAR*    layoutName;
    pLayout->getLayoutName (layoutName);
#endif

    AcDbObjectIdArray   viewports;
    viewports = pLayout->getViewportArray();
    if (viewports.length() > 0)
        return (viewports[0] == viewportObjectId);

    // if we get here, the viewport array is empty. That means it's never been initialized and the only
    // thing we can do is look through the block to find the first viewport.
    AcDbObjectId    firstViewportId = RealDwgUtil::GetFirstViewportFromPaperspace(pCurrentBlock);

    return firstViewportId == viewportObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 GetViewportOnCallback
(
AcDb::DxfCode               dxfGroupCode,
const void*                 dataIn,
void*                       userArg
)
    {
    // from AutoCAD 2009 DXF Reference
    if (90 == dxfGroupCode)
        {
        Int32*  value = (Int32*) dataIn;
        bool*   out   = (bool*)  userArg;
        *out = 0 == (*value & 0x20000);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 GetViewportOn (AcDbViewport* pViewport)
    {
    /*-----------------------------------------------------------------------------------
    DXF group code 90 is a new field, probably since R2006.  An older DWG does not have
    this group code, hence fails getting the on/off status, even if the viewport is in
    an active layout (TR 287523).  While we have not yet found a way to get old viewport's 
    on/off status for viewports that are not in active layout, we can at least get it 
    right for viewports, old and new, that are in the active layout.
    -----------------------------------------------------------------------------------*/
    AcDbLayoutManager*      layoutManager = RealDwgHostApp::Instance().layoutManager ();
    if (NULL != layoutManager)
        {
        AcDbObjectId        activeBlockId = layoutManager->getActiveLayoutBTRId ();
        if (activeBlockId.isValid() && pViewport->ownerId() == activeBlockId)
            return  pViewport->isOn();

        activeBlockId = RealDwgUtil::FindActiveLayoutBlockId(layoutManager, pViewport->database());
        if (activeBlockId.isValid() && pViewport->ownerId() == activeBlockId)
            return  pViewport->isOn();
        }

    bool    viewportOn = true;
    ExtractionFiler filer (GetViewportOnCallback, pViewport->database(), &viewportOn);
    filer.ExtractFrom (pViewport);
    return viewportOn;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            GetDynamicViewSettings (DynamicViewSettingsR dvSettings, MSRenderMode renderMode, ConvertToDgnContextR context) const
    {
    DisplayStyleCP  displayStyle = context.MapDisplayStyleFromRenderMode (renderMode);
    if (nullptr == displayStyle)
        return  false;
    
    DgnFileP        dgnFile = context.GetFile ();
    if (nullptr == dgnFile)
        return  false;

    ViewGroupPtr    viewgroup = dgnFile->GetViewGroups().FindByModelId (dgnFile->GetDefaultModelId(), false, false);
    if (!viewgroup.IsValid())
        return  false;

    dvSettings = viewgroup->GetViewInfo(0).GetDynamicViewSettings ();
    dvSettings.SetDisplayStyleIndex (displayStyle->GetIndex());
    dvSettings.SetFromParent (true);

    return  true;
    }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Mridul.Nahar                   08/2021
+---------------+---------------+---------------+---------------+---------------+------*/
void GetViewPortSymbologyOverride (ConvertToDgnContextR context, AcDbObjectId viewportObjectId, T_LevelViewportOverrideSymbologyMap & vpOverrideSymbologyMap) const
    {  

    AcDbDatabase*               database = context.GetFileHolder().GetDatabase();
    AcDbLayerTablePointer       layerTable(database->layerTableId(), AcDb::kForRead);


    AcDbLayerTableIterator*     layerIterator;

    if (Acad::eOk == layerTable->newIterator(layerIterator))
        {
        // need to collect hidden layers
        layerIterator->setSkipHidden(false);
        for (layerIterator->start(); !layerIterator->done(); layerIterator->step())
            {
            AcDbObjectId    layerId;
            if (Acad::eOk != layerIterator->getRecordId(layerId))
                continue;

            AcDbLayerTableRecordPointer layer(layerId, AcDb::kForRead);
            if (Acad::eOk != layer.openStatus())
                continue;
           
           
            ElementId           elementId = context.ElementIdFromObject(layer);
         
            bool iscolorOverride = false;
            Symbology vpOverrideSymbology;

            AcCmColor accmcolor = layer->color(viewportObjectId, iscolorOverride);
            UInt32 vpcolor = context.GetDgnColor(accmcolor);
            vpOverrideSymbology.color = vpcolor;

            bool isweightOverride = false;

            AcDb::LineWeight aclineweight = layer->lineWeight(viewportObjectId, isweightOverride);
            UInt32 vpLineWeight = context.GetDgnWeight(aclineweight);
            vpOverrideSymbology.weight = vpLineWeight;

            bool isStyleOverride = false;

            AcDbObjectId aclineTypeObjectId = layer->linetypeObjectId(viewportObjectId, isStyleOverride);
            Int32 linestyle = context.GetDgnStyle(aclineTypeObjectId);
            vpOverrideSymbology.style = linestyle;
            
            // if isoverride is present , add vpOverrideSymbology in to Map
            if (iscolorOverride || isweightOverride || isStyleOverride)
                vpOverrideSymbologyMap[elementId] = vpOverrideSymbology;
            }
        }


    delete layerIterator;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus       ToElement
(
AcDbObjectP                 acObject,
EditElementHandleR          outElement,
ConvertToDgnContextR        context
) const override
    {
    AcDbViewport*           pViewport = AcDbViewport::cast (acObject);
    double                  activeZ = 0.0, focalLength = 0.0;

    // Overall viewport is handled by creating a view element.
    if (IsOverallViewport (pViewport->objectId(), context))
        return RealDwgIgnoreEntity;

    double      scaleToDGN   = context.GetScaleToDGN();
    double      width        = scaleToDGN * pViewport->width();
    double      height       = scaleToDGN * pViewport->height();
    DPoint3d    center;
    RealDwgUtil::DPoint3dFromGePoint3d (center, pViewport->centerPoint());
    context.GetTransformToDGN().Multiply (center);

    // TR #130344 - Validate frontClip and backClip distance.
    if (pViewport->isFrontClipOn() && pViewport->isBackClipOn() && pViewport->frontClipDistance() < pViewport->backClipDistance())
        {
        DIAGNOSTIC_PRINTF ("Viewport with invalid clip detected - Clipping turned off");
        pViewport->upgradeOpen();
        pViewport->setFrontClipOff();
        pViewport->setBackClipOff();
        pViewport->downgradeOpen();
        }

    RotMatrix               rotation;
    DPoint3d                origin;
    DPoint3d                delta;
    DPoint3d                cameraPosition;
    context.CalculateDgnViewGeomFromDwgParams (rotation, origin, delta, activeZ, cameraPosition, focalLength,
                                     pViewport->backClipDistance(), pViewport->isBackClipOn(),
                                     pViewport->viewCenter(),       pViewport->frontClipDistance(),
                                     pViewport->isFrontClipOn(),    pViewport->viewHeight(),
                                     pViewport->lensLength(),       pViewport->isPerspectiveOn(),
                                     pViewport->viewTarget(),       pViewport->viewDirection(),
                                     pViewport->twistAngle(),       pViewport->viewHeight() * pViewport->width() / pViewport->height());


    ReferenceFileElm        refData;
    memset (&refData, 0, sizeof(refData));

    ElementId               clipElementId = context.ElementIdFromObjectId (pViewport->nonRectClipEntityId());
    LevelId                 levelId = context.GetDgnLevel (pViewport->layerId());

    refData.ehdr.type                   = REFERENCE_ATTACH_ELM;
    refData.ehdr.elementSize            = (int)sizeof(refData) / 2;
    refData.ehdr.attrOffset             = refData.ehdr.elementSize;
    refData.ehdr.uniqueId               = context.ElementIdFromObject (pViewport);
    refData.ehdr.level                  = levelId;
    refData.fileNumber                  = context.GetNextReferenceFileNumber ();
    refData.masterOrigin                = center;
    refData.transform                   = rotation;
    refData.scale                       = (0.0 == delta.x) ? 1.0 : width / delta.x ;
    refData.cameraPosition              = cameraPosition;
    refData.cameraFocalLength           = focalLength;

    refData.fd_opts.cameraOn            = pViewport->isPerspectiveOn();
    refData.fd_opts.display             = GetViewportOn (pViewport);    // Note:  The AcDbViewport::isOn method always returns false in RealDWG, because the viewport is "inactive".
    refData.fd_opts.displayBoundary     = (0 == clipElementId);         // Display the boundary if no clip element.
    refData.fd_opts.clipFront           = pViewport->isFrontClipOn();
    refData.fd_opts.clipBack            = pViewport->isBackClipOn();
    refData.fd_opts.viewport            = true;
    refData.fd_opts.rotateClipping      = true;
    refData.fd_opts.displayRasterRefs   = true;
    refData.fb_opts.snap_lock           = true;
    refData.fb_opts.locate_lock         = context.GetSettings().MapVPortLocateLockToDisplayUnlocked() ? !pViewport->isLocked() : true;
    refData.fb_opts.treatAsElement      = true;

    // VPorts may appear in modelSpace (TR# 129811) - but AutoCAD seems to implicitly treat these as off.
    if (context.GetCurrentBlockId() == acdbSymUtil()->blockModelSpaceId(pViewport->database()))
        refData.fd_opts.display = 0;

    double clipDistance                 = refData.scale * delta.z/2.0;
    refData.zFront                      = refData.fd_opts.clipFront ?  clipDistance :  1.0E8;
    refData.zBack                       = refData.fd_opts.clipBack  ? -clipDistance : -1.0E8;

    refData.nestDepth = DWG_REFERENCE_NEST_DEPTH;

    /*-----------------------------------------------------------------------------------
    According to AutDesk AcDbViewport::renderMode returns a hard coded k2DOptimized/0!
    They are obseleting renderMode and replaced it with visual style. Neither extraction
    filer nor recording filer works to get or set rendermode flag so there is no valid
    workaround for it until we support visual style.
    -----------------------------------------------------------------------------------*/
    MSRenderMode renderMode             = RealDwgUtil::GetRenderModeFromVisualStyle (pViewport->visualStyle());

    for (int iView = 0; iView < 8; iView++)
        refData.displayFlags[iView].renderMode = (UInt32)renderMode;

    DynamicViewSettings     dvSettings;
    bool                    hasDvs = this->GetDynamicViewSettings (dvSettings, renderMode, context);

    refData.refOrigin.scale (&delta, .5);
    bsiRotMatrix_multiplyTransposeDPoint3d (&rotation, &refData.refOrigin);         //rotation.multiplyTranspose (&refData.refOrigin);
    refData.refOrigin.add (&origin);

    DPoint2d  clipPoints[5];
    clipPoints[0].x = clipPoints[3].x = - width / 2.0;
    clipPoints[1].x = clipPoints[2].x = - clipPoints[0].x;
    clipPoints[0].y = clipPoints[1].y = - height / 2.0;
    clipPoints[2].y = clipPoints[3].y = - clipPoints[0].y;
    clipPoints[4]   = clipPoints[0];

    // AutoCAD's scale is not a "true" scale, but a scale between the file storage units.
    refData.fd_opts.scaleByUnits        = false;
#if defined (SCALE_BY_STORAGE_UNITS_TRUE_ON_VIEWPORTS)
    // This should not be necessary on viewports as the models in sheets in all models have
    // the same units - It causes problems when saving to DWG (TR# 124858) - so removed for 8.5.1 (RBB).
    refData.fd_opts.scaleByStorageUnits = true;
#endif
    double viewportAnnoScale = -1;
    if(!ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_SKIP_DWGANNOTATIONSCALE")) //ADO#664115
        {
        AcDbAnnotationScale*    annoscale = pViewport->annotationScale ();
        if (nullptr != annoscale)
            {
            /*----------------------------------------------------------------------------------------------
            When it comes to annotative objects in a viewport, ACAD compounds geometric and annotation scales,
            i.e. geomScale * annoScale.  In contrast, MicroStation applies annotation scale directly to an 
            annotative element and ignores geometric scale of the DgnAttachment, when the option useAnnotationScale 
            is turned on.  To fully support ACAD's annotation scale on viewports we'd need to, like ACAD, also 
            add an annoation scale on reference attachment, but we try not to do that as an additional scale
            will open a huge can of warms to us as well as to the users.
            
            While we do not have an explicit annotation scale for a reference attachment to fully support DWG 
            viewport annotation scale, we can make a particular yet a common case to work: when the effective 
            compound scale is the same as that for the overall viewport scale, the effect would be equivelent to
            a sheet model annotation scale of 1:1. In fact, ACAD appears to always set the overall viewport to 1:1.  
            So a user who wants to have his texts in a viewport to be displayed at the same size as those in the 
            layout, he must set geomScale * annoScale = 1.  This pratice makes sense and appears common among
            our users.  Even ACAD GUI automatically changes geomScale = 1/annoScale to help users to get this
            desired result.  Note that the annoScale referred here is MicroStation value which is an inverse
            of ACAD's value.

            When annotation scale is 1:1, above rule still applies, i.e. we can and should use the sheet model 
            annotation scale if geomScale is also 1:1.  TFS 569765.
            ----------------------------------------------------------------------------------------------*/
            double              scale = 1.0;

            if (!ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_ADD_DWGVIEWPORTSCALE"))
                {
                if (Acad::eOk == annoscale->getScale(scale) && scale > 0.0 && fabs(pViewport->customScale() / scale - 1.0) < TOLERANCE_ScaleRatio)
                    refData.fb_opts.useAnnotationScale = true;
                }
            else if (Acad::eOk == annoscale->getScale(scale) && scale > 0.0)
                    viewportAnnoScale = scale;

            delete annoscale;
            }
        }

    WChar     wLogicalChars[512];
    swprintf (wLogicalChars, L"%ls %d", REFERENCE_NAME_Viewport, context.GetFileHolder().GetAndIncrementNextAvailableViewportNumber());

    RotMatrix   idMatrix = RotMatrix::FromIdentity();
    if (SUCCESS != DgnAttachment::InitializeDgnAttachmentElementFromReferenceFileElm (outElement, refData, NULL, NULL, NULL, wLogicalChars,
                                5, clipPoints, (0 == clipElementId ? 0 : 1), &clipElementId, hasDvs ? &dvSettings : nullptr, &idMatrix, context.GetModel()))
        return CantCreateReference;

    bool        addedLinkage = false;
    MSElement   newElement;
    outElement.GetElementCP()->CopyTo (newElement);

    int                     nFrozenLayers;
    AcDbObjectIdArray       frozenLayers;

    pViewport->getFrozenLayerList (frozenLayers);
    if (0 != (nFrozenLayers = frozenLayers.length()))
        {
        ElementId   *pFrozenLayerIds = (ElementId *) _alloca (nFrozenLayers * sizeof(ElementId));

        for (int iLayer = 0; iLayer < nFrozenLayers; iLayer++)
            pFrozenLayerIds[iLayer] = context.ElementIdFromObjectId (frozenLayers.at (iLayer));

        if (SUCCESS == mdlLinkage_setElementIds (&newElement, pFrozenLayerIds, nFrozenLayers, DEPENDENCYAPPID_MicroStation, DEPENDENCYAPPVALUE_ReferenceFrozenLevel))
            addedLinkage = true;
        }

    if (SUCCESS == SetRenderingPlot (&newElement, pViewport->shadePlot(), pViewport->shadePlotId(), context))
        addedLinkage = true;

    if (addedLinkage)
        outElement.ReplaceElement (&newElement);

    linkage_setSymbologyLinkage (outElement, (int) SYMBOLOGY_LINKAGE_KEY_RefBound, true, true, true, true,
                                    context.GetDgnStyle (pViewport->linetypeId()), context.GetDgnWeight (pViewport->lineWeight()),
                                    context.GetDgnColor (pViewport->colorIndex()), levelId);
    
    // viewport symbology override
    T_LevelViewportOverrideSymbologyMap vpOverrideSymbologyMap;
    GetViewPortSymbologyOverride(context, pViewport->objectId(), vpOverrideSymbologyMap);

    if (outElement.IsValid())
        DgnAttachment::SetDWGViewportOverrideSymbology(vpOverrideSymbologyMap, outElement);

  
    /*This xattribute is going to persisted in the attachement*/
    if (outElement.IsValid())
        {
        DgnAttachment::SetAnnotationScaleFlag(outElement);
        if(viewportAnnoScale != -1)
            AnnotationScale::SetAsXAttribute(outElement, &viewportAnnoScale);
        }

    context.ElementHeaderFromEntity (outElement, pViewport);

    return RealDwgSuccess;
    }

};  // ToDgnExtViewport



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    06/98
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsRectangularBoundary
(
int                         nClipPoints,
DPoint2dCP                  pClipPoints
)
    {
    if (nClipPoints != 5)
        return false;

    int iPoint;
    for (iPoint = 0; iPoint < 4; iPoint++)
        {
        if (pClipPoints[iPoint].x != pClipPoints[iPoint+1].x &&
            pClipPoints[iPoint].y != pClipPoints[iPoint+1].y)
            break;
        }
    return 4 == iPoint;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      10/2005
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsValidViewportClipEntity (ElementHandleCR inElement)
    {
    DVec3d      normal;

    return RealDwgUtil::GetElementNormal (&normal, NULL, inElement) &&
           fabs (normal.z) > .95;       // AutoCAD requires parallel to the viewport.
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Mridul.Nahar                08/2021
+---------------+---------------+---------------+---------------+---------------+------*/
void ConvertFromDgnContext::SetViewportSymbologyOverride(DgnAttachmentP refP, AcDbObjectId viewportObjectId)
    {
    DgnModelRefP    rootDwgModelRef = LevelUtility::GetRootDwgModelRef(*refP);

    if (rootDwgModelRef == nullptr)
        return;

    T_LevelViewportOverrideSymbologyMap vpOverrideSymbologyMap;
    refP->GetDWGOverrideSymbologyMap(vpOverrideSymbologyMap);

    AcDbObjectId                overrideLayerId;
    LevelCacheR levelCache = rootDwgModelRef->GetLevelCacheR();
    for each (LevelHandle const & levelHandle in levelCache)
        {
        LevelDefinitionColor        colorDef = (levelHandle.GetOverrideColorOn()) ? levelHandle.GetOverrideColor() : levelHandle.GetByLevelColor();

        DgnModelP           colorModel = colorDef.GetDefinitionModel();      

        ElementId levelEntryElmId = levelHandle.GetElementId();
        overrideLayerId = this->ExistingObjectIdFromElementId(levelEntryElmId);



        if (!overrideLayerId.isNull() && !overrideLayerId.isErased())
            {
            AcDbLayerTableRecordPointer pLayer(overrideLayerId, AcDb::kForWrite);
            if (Acad::eOk != pLayer.openStatus())
                continue;

            const ACHAR* layerName;
            if (Acad::eOk != pLayer->getName(layerName))
                continue;
          
            // Save viewport level symbology override to pLayer
            T_LevelViewportOverrideSymbologyMap::iterator iter = vpOverrideSymbologyMap.find(levelEntryElmId);
            if (iter != vpOverrideSymbologyMap.end())
                {
                Symbology symb = iter->second;
                
                pLayer->setColor(this->GetColorFromDgn(symb.color, pLayer->color().colorIndex(), colorModel), viewportObjectId);
                
                AcDb::LineWeight  acweight = this->GetLineWeightFromDgn(symb.weight, pLayer->lineWeight());
                pLayer->setLineWeight(acweight, viewportObjectId);

                AcDbObjectId linetypeObjectId = this->GetLineTypeFromDgn(symb.style);
                pLayer->setLinetypeObjectId(linetypeObjectId, viewportObjectId);
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertFromDgnContext::SetAcDbViewportFromSelfReference
(
AcDbViewport*           acViewport,
ElementHandleCR         inElement
)
    {
    if (NULL == acViewport)
        return  NullObject;

    MSElementCP         pElem = inElement.GetElementCP ();
    DgnAttachmentP      dgnAttachment = DgnAttachment::FindByElementId (m_model, pElem->ehdr.uniqueId);
    if (NULL == dgnAttachment)
        return  CantAccessMstnElement;

    // Don't attempt to extract parameters if cache not loaded.
    if (NULL == dgnAttachment->GetDgnModelP() && this->SavingChanges())
        return RealDwgSuccess;

    if (!this->IsModelRefViewportAttachment (dgnAttachment))
        return RealDwgIgnoreElement;

    const ReferenceFileElm* pRefElement = &pElem->referenceFileElm;

    // unbias target origin by insertion base
    DPoint3d                masterOrigin = dgnAttachment->GetStoredMasterOrigin ();

    double                  zMin, zMax;
    DPoint3d                sheetCenter, origin, delta, originDelta;
    DRange3d                range;
    UInt32                  nClipPoints = 0;
    DPoint2d                clipPoints[MAX_REFCLIPPNTS];
    ElementId               clipEntityId = dgnAttachment->GetClipElementId ();
    EditElementHandle       clipElement;
    if (0 == clipEntityId)
        {
        // Use clip points from DgnAttachment object, instead of raw element as we used to use which may be out of synch w/fb_opts.synchVolumeWithSavedView. TR 337384.
        // Only use raw element's points should ReferenceFile contains no points (which should not occur).
        bvector<DPoint2d>  pointArray;
        dgnAttachment->GetClipPoints (pointArray);
        if (pointArray.empty())
            {
            for (int i = 0; i < pRefElement->nClipPoints; i++)
                pointArray.push_back (pRefElement->clipPoints[nClipPoints]);
            }

        // Clip points (or not clip) Ignore masks.
        for (nClipPoints=0; nClipPoints < pointArray.size() && pointArray[nClipPoints].x != DISCONNECT; )
            clipPoints[nClipPoints++] = pointArray[nClipPoints];

        if (pRefElement->fd_opts.rotateClipping)
            {
            RotMatrix   clipRMatrix = dgnAttachment->GetClipRotMatrix ();

            clipRMatrix.multiply (clipPoints, clipPoints, nClipPoints);
            }
        }
    else
        {
        // have clipping entity. Try to retrieve it so we can use its range.
        if (assoc_isFarElementId(clipEntityId, m_model))
            DependencyManagerLinkage::GetElementUsingFarPath (clipElement, clipEntityId, m_model, true);
        else
            clipElement.FindByID (clipEntityId, m_model);

        // if it doesn't work as a clipping element, ignore it.
        if (clipElement.IsValid() && !IsValidViewportClipEntity(clipElement))
            clipElement.Invalidate ();
        }

    // get range from clip points, clip descriptor, or range of cache.
    if (0 != nClipPoints)
        {
        range.InitFrom (clipPoints, nClipPoints, 0.0);
        }
    else if (clipElement.IsValid())
        {
        ScanRange       cacheRange = clipElement.GetElementCP()->hdr.dhdr.range;
        range.low.x  = cacheRange.xlowlim  - masterOrigin.x;
        range.low.y  = cacheRange.ylowlim  - masterOrigin.y;
        range.high.x = cacheRange.xhighlim - masterOrigin.x;
        range.high.y = cacheRange.yhighlim - masterOrigin.y;
        }
    else
        {
        DRange3d        cacheRange;

        if (BSISUCCESS == dgnAttachment->GetClipRange(&cacheRange, NULL, NULL, true, false, true))
            {
            range.low.x  = cacheRange.low.x - masterOrigin.x;
            range.low.y  = cacheRange.low.y - masterOrigin.y;
            range.high.x = cacheRange.high.x - masterOrigin.x;
            range.high.y = cacheRange.high.y - masterOrigin.y;
            }
        else
            {
            range.low.x = range.low.y   = -1.0E6;
            range.high.x = range.high.y =  1.0E6;
            }
        }

    bool            clipFront = ((pRefElement->zFront > pRefElement->zBack) && pRefElement->fd_opts.clipBack),
                    clipBack  = ((pRefElement->zFront > pRefElement->zBack) && pRefElement->fd_opts.clipFront);

    zMin = clipBack  ? pRefElement->zBack  :  -1.0E6;
    zMax = clipFront ? pRefElement->zFront :   1.0E6;

    sheetCenter.x = masterOrigin.x + (range.low.x + range.high.x)/2.0;
    sheetCenter.y = masterOrigin.y + (range.low.y + range.high.y)/2.0;
    sheetCenter.z = 0.0;

    delta.x = (range.high.x - range.low.x);
    delta.y = (range.high.y - range.low.y);
    delta.z = (zMax - zMin);

    originDelta.x = range.low.x;
    originDelta.y = range.low.y;
    originDelta.z = zMin;

    // The stored scale may differ from the actual scale if the units are different
    // between the sheet and the model (Gino's "Floorpan").
    double          refScale = dgnAttachment->GetDisplayScale ();

    // If the rotation matrix contained scale then we may have
    RotMatrix       rotMatrix = dgnAttachment->GetRotMatrix ();

    // AutoCAD viewports must be pure rotation - no mirror or scaling.
    // extract scaling first. -then square/normalize to get rid
    // of both mirroring and scaling.  - arbitrarily use xScaling
    // non-uniform scaling is discarded.
    double          xScale;
    DVec3d          column0;
    rotMatrix.getColumn (&column0, 0);
    if (1.0 != (xScale = column0.magnitude()))
        refScale *= xScale;

    rotMatrix.squareAndNormalizeColumns (&rotMatrix, 0, 1);

    if (0.0 != refScale)
        {
        double      inverseScale = 1.0 / refScale;

        delta.scale (inverseScale);
        originDelta.scale (inverseScale);
        }

    rotMatrix.multiplyTranspose (&originDelta);
    origin.sumOf (&pRefElement->refOrigin, &originDelta);

    double          scaleFromDGN     = this->GetScaleFromDGN();
    Transform       transformFromDGN = this->GetTransformFromDGN();

    ModelInfoCP     pModelInfo = dgnAttachment->GetModelInfoCP ();
    if (NULL != pModelInfo)
        {
        // If this modelRef is DWG then pass -1 to use the storage units - else use the save units.
        DwgOpenUnitMode unitMode = mdlModelRef_isTargetFormatDwgOrDxf(dgnAttachment) ? DWGOpenUnitMode_NotSetYet :
                                   this->GetDwgOpenUnitModeFromSaveUnitMode(this->GetSettings().GetDwgSaveUnitMode());

        double          scaleToDGN = 1.0;
        Transform       transformToDGN;

        this->GetTransformToDGNFromModelInfo (&transformToDGN, &scaleToDGN, NULL, *pModelInfo, dgnAttachment->GetDgnModelP(), unitMode);
        transformFromDGN.inverseOf (&transformToDGN);
        scaleFromDGN = 1.0 / scaleToDGN;
        }

    double          frontClipDistance, backClipDistance, viewHeight, viewWidth, viewTwist, lensLength;
    bool            frontClipEnabled, backClipEnabled, frontClipAtEye, perspectiveEnabled;
    AcGePoint2d     viewCenter;
    AcGePoint3d     viewTarget;
    AcGeVector3d    viewDirection;

    this->CalculateDwgViewParams (backClipDistance, backClipEnabled, frontClipDistance, frontClipEnabled, frontClipAtEye, viewCenter,
                                  viewHeight, viewWidth, lensLength, perspectiveEnabled, viewTarget, viewDirection, viewTwist,
                                  origin, delta, rotMatrix, pRefElement->fd_opts.cameraOn, pRefElement->cameraPosition, 
                                  pRefElement->cameraFocalLength, !clipFront, !clipBack, scaleFromDGN, transformFromDGN);

    if (pRefElement->fd_opts.display)
        {
        // turning on viewport display prerequisite database residency of the viewport entity:
        if (!acViewport->objectId().isValid())
            this->AddEntityToCurrentBlock (acViewport, pRefElement->ehdr.uniqueId);
        acViewport->setOn ();
        }
    else
        {
        acViewport->setOff ();
        }

    // Set sheet parameters.
    this->GetTransformFromDGN().Multiply (sheetCenter);
    acViewport->setCenterPoint (RealDwgUtil::GePoint3dFromDPoint3d (sheetCenter));
    acViewport->setWidth  ((0.0==delta.x ? 1.0 : delta.x) * refScale * this->GetScaleFromDGN());
    acViewport->setHeight ((0.0==delta.y ? 1.0 : delta.y) * refScale * this->GetScaleFromDGN());

    // Set view.
    acViewport->setViewCenter (viewCenter);
    acViewport->setViewTarget (viewTarget);
    acViewport->setViewDirection (viewDirection);
    acViewport->setViewHeight (viewHeight);
    acViewport->setTwistAngle (viewTwist);
    acViewport->setLensLength (lensLength);
    acViewport->setFrontClipDistance (frontClipDistance);
    acViewport->setBackClipDistance (backClipDistance);
    if (RealDwgUtil::GetRenderModeFromVisualStyle(acViewport->visualStyle()) != pRefElement->displayFlags[0].renderMode)
        this->SetVisualStyleFromRenderMode (acViewport, (MSRenderMode)pRefElement->displayFlags[0].renderMode, m_pFileHolder->GetDatabase());

    perspectiveEnabled                  ? acViewport->setPerspectiveOn()    : acViewport->setPerspectiveOff();
    frontClipAtEye                      ? acViewport->setFrontClipAtEyeOn() : acViewport->setFrontClipAtEyeOff(); // In 8.5 this was inverted to compensate for DwgDirect bug.
    if (this->GetSettings().MapVPortLocateLockToDisplayUnlocked())
        pRefElement->fb_opts.locate_lock ? acViewport->setUnlocked()        : acViewport->setLocked();

    frontClipEnabled                    ? acViewport->setFrontClipOn()      : acViewport->setFrontClipOff();
    backClipEnabled                     ? acViewport->setBackClipOn()       : acViewport->setBackClipOff();

    if (0 == clipEntityId)
        {
        if (0 != nClipPoints && ! IsRectangularBoundary (nClipPoints, clipPoints))
            {
            DPoint3d                clipOrigin;

            this->GetTransformFromDGN().Multiply (clipOrigin, masterOrigin);

            if (clipPoints[0].isEqual (&clipPoints[nClipPoints-1]))
                nClipPoints--;          // Discard closure point.

            AcDbPolylinePointer     pPolyline (new AcDbPolyline());

            for (UInt32 iPoint = 0; iPoint < nClipPoints; iPoint++)
                {
                AcGePoint2d     clipPoint;

                clipPoint.x = clipOrigin.x + clipPoints[iPoint].x * this->GetScaleFromDGN();
                clipPoint.y = clipOrigin.y + clipPoints[iPoint].y * this->GetScaleFromDGN();

                pPolyline->addVertexAt (iPoint, clipPoint);
                }
            pPolyline->setClosed (true);
            pPolyline->addPersistentReactor (acViewport->objectId());
            AcDbObjectId    polylineId = this->AddEntityToCurrentBlock (pPolyline, 0);
            // AcDbViewport::setNonRectClipEntityId won't work with clipping entity open:
            pPolyline.close ();
            acViewport->setNonRectClipEntityId (polylineId);
            acViewport->setNonRectClipOn ();
            }
        else
            {
            acViewport->setNonRectClipOff();
            }
        }
    else if (assoc_isFarElementId(clipEntityId, m_model) && clipElement.IsValid())
        {
        // TR #162751 - Handle far clipping by copying clip element to master.
        // Clear level to avoid having clone add new levels.
        ElementPropertiesSetter propSetter;
        propSetter.SetLevel (LEVEL_BYCELL);
        propSetter.Apply (clipElement);

        // now clone the clipper
        EditElementHandle   newClipper (clipElement, true);
        ElementCopyContext  copier (m_model);

        copier.SetWriteElements (false);
        copier.DoCopy (newClipper);

        AcDbObjectId        objectId;
        if (IsValidViewportClipEntity(newClipper) && (BSISUCCESS == (this->SaveElementToDatabase(newClipper, &objectId))))
            {
            AcDbEntityPointer pEntity (objectId, AcDb::kForWrite);
            if (Acad::eOk == pEntity.openStatus())
                {
                pEntity->addPersistentReactor (acViewport->objectId());
                // AcDbViewport::setNonRectClipEntityId won't work with clipping entity open:
                pEntity.close ();
                acViewport->setNonRectClipEntityId (objectId);
                acViewport->setNonRectClipOn ();
                }
            }
        }
     else
        {
        // Handle hooking an existing clipping up element postfacto as it may not exist yet.
        if (!acViewport->objectId().isValid())
            this->AddEntityToCurrentBlock (acViewport, pElem->ehdr.uniqueId);
        this->PostProcessingRequired (inElement, acViewport->objectId());
        }

    int                 nFrozenLayers;
    ElementId           frozenLayers[MAX_FrozenLayers];

    nFrozenLayers = mdlLinkage_getElementIds (frozenLayers, MAX_FrozenLayers, pElem, DEPENDENCYAPPID_MicroStation, DEPENDENCYAPPVALUE_ReferenceFrozenLevel);
    if (0 == nFrozenLayers)
        {
        // If the view visibility has been selected, turn off layers that are not on in the selected view.
        nFrozenLayers = this->GetFrozenLayersFromViewportModelRef (frozenLayers, dgnAttachment);
        }

    acViewport->thawAllLayersInViewport();
    if (0 != nFrozenLayers)
        {
        AcDbObjectId                frozenLayerId;
        AcDbObjectIdArray           frozenLayerArray;
        for (int iLayer = 0; iLayer < nFrozenLayers; iLayer++)
            {
//begin of KGS 11-18-04 Fix of TR#137620
//if layer resides in XREF it's status cannot be saved in main drawing if VISRETAIN 0
            frozenLayerId = this->ExistingObjectIdFromElementId(frozenLayers[iLayer]);

            if (!frozenLayerId.isNull() && !frozenLayerId.isErased())
                {
                AcDbLayerTableRecordPointer pLayer (frozenLayerId, AcDb::kForRead);
                if (Acad::eOk != pLayer.openStatus())
                    continue;

                const ACHAR* layerName;
                if (Acad::eOk != pLayer->getName (layerName))
                    continue;

                if (NULL != wcschr( layerName, '|'))
                    {
                    if( pLayer->database()->visretain() == true )
                        frozenLayerArray.append (frozenLayerId);
                    }
                else
                    {
                    frozenLayerArray.append (frozenLayerId);
                    }
                }
            }
//end of KGS 11-18-04 Fix of TR#137620
        acViewport->freezeLayersInViewport (frozenLayerArray);
        }

    SetViewportSymbologyOverride(dgnAttachment, acViewport->objectId());


    AcDbObjectId    layerId;
    LevelHandle     level = m_model->GetLevelCache().GetLevel (pElem->ehdr.level);
    if (level.IsValid())
        layerId = this->SavingChanges() ? m_pFileHolder->GetLayerByLevelHandle(level) : m_pFileHolder->GetLayerByLevelId(level.GetLevelId());

    if (layerId.isValid())
        {
        acViewport->setLayer (layerId);
        }
    else
        {
        // CR: 130019 - GM/Ghafari - If no level assigned and the clip element has a layer, assign the clip boundary layer.
        if (this->GetSettings().SetViewportLayerFromClipElement() && 0 != clipEntityId)
            {
            ElementHandle   clippingElement(clipEntityId, m_model);
            level = m_model->GetLevelCache().GetLevel (clippingElement.GetElementCP()->ehdr.level);
            if (clippingElement.IsValid())
                {
                layerId = this->SavingChanges() ? m_pFileHolder->GetLayerByLevelHandle(level) : m_pFileHolder->GetLayerByLevelId(level.GetLevelId());
                if (layerId.isValid())
                    acViewport->setLayer (layerId);
                }
            }
        }

    // set shade plot
    AcDbViewport::ShadePlotType     plotType;
    AcDbObjectId                    shadePlotId = AcDbObjectId::kNull;
    if (SUCCESS == GetRenderingPlot(plotType, shadePlotId, pElem, *this))
        acViewport->setShadePlot (plotType, shadePlotId);
    else if (AcDbViewport::kAsDisplayed != acViewport->shadePlot())
        acViewport->setShadePlot (AcDbViewport::kAsDisplayed, AcDbObjectId::kNull);

    /*-----------------------------------------------------------------------------------
    ACAD ignore weight & linetype on a viewport entity, but saving these should cause no
    harm.  Opening them can result different display, but we have been hornoring them
    from day one.  If that turns out to be an issue we can ignore weight and style at
    file open.
    -----------------------------------------------------------------------------------*/
    UInt32      dgnColor = 0, dgnWeight = 0;
    Int32       dgnStyle = 0;
    bool        overrideColor = false, overrideWeight = false, overrideStyle = false;

    if (SUCCESS == linkage_getSymbologyLinkage(&overrideColor, &overrideWeight, &overrideStyle, NULL,
                                               &dgnStyle, &dgnWeight, &dgnColor, NULL, SYMBOLOGY_LINKAGE_KEY_RefBound, inElement))
        {
        if (!overrideColor)
            dgnColor = 0;
        if (!overrideWeight)
            dgnWeight = 0;
        if (!overrideStyle)
            dgnStyle = 0;
        }

    /*------------------------------------------------------------------------------------------------------------
    DWG does not have use annotation scale on sheet, but does on viewports.  In theory we can find an effective 
    viewport annotation scale from our sheet or model annotation scale factor such that:
    
    if DgnAttachment uses sheet annotation scale
        viewport annotation scale X viewport custom scale = sheet annotation scale
    else, i.e. DgnAttachment uses model annotation scale
        viewport annotation scale X viewport custom scale = model annotation scale

    Above formula can create a viewport that looks correct in ACAD, but can be incorrect when the file is opened back 
    in MicroStation because we still do not have an annotation scale on DgnAttachment to truly support viewport annotation
    scale.  Only if annoScale X customScale = 1.0 we can display viewport annotation scale(see ToElement above).  
    So instead of creating viewports that ACAD can display but we cannot, we decide to not bother adding annotation 
    scales on viewports unless the sheet model uses scale 1:1.  In other words, we only support round tripping DWG 
    annotation scale in paperspace.  Another case we can support is when UseAnnotationScale is off and the default
    model's annotation scale mataches the viewport's annotation scale.

    Save off the annoation scale we added here, and we will later on iterate through model space annotative objects
    and add the scale if not supported.
    ------------------------------------------------------------------------------------------------------------*/
    bool        isSheetAnnotation = dgnAttachment->UseAnnotationScale ();
    DgnModelP   sourceModel = isSheetAnnotation ? m_model : m_dgnFile->FindLoadedModelById(m_dgnFile->GetDefaultModelId());
    if (nullptr != sourceModel)
        {
        double      modelAnnotationScale = m_model->GetModelInfo().GetAnnotationScaleFactor ();
        double      viewportCustomScale = acViewport->customScale ();
        if (modelAnnotationScale > TOLERANCE_ZeroScale && viewportCustomScale > TOLERANCE_ZeroScale)
            {
            // calculate effective annotation scale for the viewport based on aforementioned rule:
            double                  vpAnnotationScale = modelAnnotationScale / viewportCustomScale;
            // try rounding the floating number
            if (vpAnnotationScale > 1.0)
                vpAnnotationScale = floor (vpAnnotationScale * 100.0 + 0.5) / 100.0;

            // apply above rule to only suuport sheet model with scale 1:1 or default model with a scale that is the same for the viewport:
            if ((isSheetAnnotation && fabs(modelAnnotationScale - 1.0) < TOLERANCE_ZeroScale) ||
                (!isSheetAnnotation && fabs(modelAnnotationScale - vpAnnotationScale) < TOLERANCE_ZeroScale))
                {
                ScaleDefinition         scaleDef (vpAnnotationScale, true);
                AcDbAnnotationScale*    acAnnoscale = acViewport->annotationScale ();
                bool                    vpAlreadyHasScale = false;
                if (nullptr != acAnnoscale)
                    {
                    double              scaleFactor = 1.0;
                    if (Acad::eOk == acAnnoscale->getScale(scaleFactor) && fabs(scaleFactor - vpAnnotationScale) < TOLERANCE_AnnotationScale)
                        vpAlreadyHasScale = true;
                    delete acAnnoscale;
                    acAnnoscale = nullptr;
                    }
                if (!vpAlreadyHasScale && RealDwgSuccess == this->GetOrAddAnnotationScale(acAnnoscale, &scaleDef, true))
                    {
                    if (Acad::eOk == acViewport->setAnnotationScale(acAnnoscale))
                        this->GetFileHolder().AddViewportAnnotationScale (acAnnoscale);
                    else if (nullptr != acAnnoscale)
                        delete acAnnoscale;
                    }
                }
            }
        }

    this->UpdateEntitySymbologyFromDgn (acViewport, dgnColor, dgnWeight, dgnStyle, pElem->ehdr.level, pElem);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SetAcDbViewportPostProcess
(
AcDbViewport*               acViewport,
ElementHandleCR             inElement
)
    {
    if (NULL == acViewport || !inElement.IsValid())
        return  NullObject;

    EditElementHandle       clipElement;
    ElementId               clipEntityId = 0;
    AcDbObjectId            objectId;
    if (mdlLinkage_getClipElementIds (&clipEntityId, 1, inElement.GetElementCP()) > 0 &&
        SUCCESS == clipElement.FindByID(clipEntityId, m_model) &&
        IsValidViewportClipEntity(clipElement) &&
        ! (objectId = this->ExistingObjectIdFromElementId (clipEntityId)).isNull())
        {
        AcDbEntityPointer pEntity (objectId, AcDb::kForWrite);
        if (Acad::eOk == pEntity.openStatus())
            {
            pEntity->addPersistentReactor (acViewport->objectId());
            // AcDbViewport::setNonRectClipEntityId won't work with clipping entity open:

            pEntity.close ();
            acViewport->setNonRectClipEntityId (objectId);
            acViewport->setNonRectClipOn ();
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Error on viewport clip ID=%I64d: %ls.\n", clipEntityId, acadErrorStatusText(pEntity.openStatus()));
            }
        }
    else
        {
        DIAGNOSTIC_PRINTF ("Ignoring invalid viewport clip: %I64d\n", clipEntityId);
        }

    return RealDwgSuccess;
    }

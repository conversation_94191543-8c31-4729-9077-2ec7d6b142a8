/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rd2dPolyline.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/11
+===============+===============+===============+===============+===============+======*/
class    PolylineUtil
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/02
+---------------+---------------+---------------+---------------+---------------+------*/
static void         AppendVertex (AcDb2dPolyline* parent, DPoint3dCR point, double startWidth, double endWidth, double bulge)
    {
    AcGePoint3d     acPoint = RealDwgUtil::GePoint3dFromDPoint3d (point);
    AcDb2dVertex*   vertex = new AcDb2dVertex (acPoint, bulge, startWidth, endWidth, 0.0);

    vertex->setPropertiesFrom (parent);

    Acad::ErrorStatus status  = parent->appendVertex (vertex);
    if (Acad::eOk == status)
        {
        vertex->close ();
        }
    else
        {
        DIAGNOSTIC_PRINTF ("Error appending a new vertex to polyline!\n");
        delete vertex;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static void         RemoveAllVertices (AcDb2dPolyline* acPolyline)
    {
    AcDbObjectIterator* iterator = acPolyline->vertexIterator();
    for (; !iterator->done(); )
        {
        AcDbObjectId        vertexId = iterator->objectId ();
        AcDb2dVertex*       vertex = NULL;

        Acad::ErrorStatus status;
        if (Acad::eOk != (status = acPolyline->openVertex (vertex, vertexId, AcDb::kForWrite)))
            return;

        iterator->step();
        vertex->erase();
        vertex->close();
        }
    delete iterator;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
static int          CountVertices (AcDb2dPolyline* acPolyline, AcDb::Vertex2dType typeToSkip)
    {
    int                 count       = 0;
    AcDbObjectIterator* iterator   = acPolyline->vertexIterator();

    for (; !iterator->done(); iterator->step())
        {
        if ( (AcDb::Vertex2dType)(-1) == typeToSkip)
            count++;
        else
            {
            AcDbObjectId        vertexId = iterator->objectId ();
            AcDb2dVertex*       vertex = NULL;

            Acad::ErrorStatus status;
            if (Acad::eOk != (status = acPolyline->openVertex (vertex, vertexId, AcDb::kForRead)))
                continue;

            if (vertex->vertexType() != typeToSkip)
                count++;

            vertex->close();
            }
        }
    delete  iterator;

    return count;
    }

};  // PolylineUtil


/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExt2dPolyline : public ToDgnExtension, SetFromPolylineInfo
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SplinePolyline2dToElement
(
AcDb2dPolyline*         pPolyline,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const
    {
    DPoint3d                point;
    DPoint3dArray           pointArray;

    int                     degree          = (AcDb::k2dQuadSplinePoly == pPolyline->polyType()) ? 2 : 3;
    bool                    unflaggedVertex = false;
    bool                    first           = true;

    double                  startWidth      = 0.0;
    double                  endWidth        = 0.0;

    AcDbObjectIterator*     iterator = pPolyline->vertexIterator();
    for (; !iterator->done(); iterator->step())
        {
        AcDbObjectId        vertexId = iterator->objectId ();
        AcDb2dVertex*       vertex = NULL;

        Acad::ErrorStatus status;
        if (Acad::eOk != (status = pPolyline->openVertex (vertex, vertexId, AcDb::kForRead)))
            {
            delete iterator;
            return ErrorOpeningVertex;
            }

        if (AcDb::k2dSplineCtlVertex == vertex->vertexType())
            {
            DPoint3d        tmpPoint = {vertex->position().x, vertex->position().y, 0.0};
            pointArray.push_back (tmpPoint);
            }

        if (AcDb::k2dSplineFitVertex == vertex->vertexType())      // AutoCAD seems to take start/end width from first/last fit vertex.
            {
            if (first)
                {
                first = false;
                startWidth = vertex->startWidth();
                }
            endWidth = vertex->endWidth();
            }
        else if (AcDb::k2dVertex == vertex->vertexType())
            {
            unflaggedVertex = true;
            }
        vertex->close();
        }
    delete iterator;

    if (unflaggedVertex)
        {
        pointArray.clear();

        // Special case - Medusa spline - just take stroke points and unflagged vertex and use them.  (doesn't work for TR: 104338).
        degree = 1;
        iterator = pPolyline->vertexIterator();
        for (; !iterator->done(); iterator->step())
            {
            AcDbObjectId        vertexId = iterator->objectId ();
            AcDb2dVertex*       vertex = NULL;

            Acad::ErrorStatus status;
            if (Acad::eOk != (status = pPolyline->openVertex (vertex, vertexId, AcDb::kForRead)))
                {
                delete iterator;
                return ErrorOpeningVertex;
                }

            if (AcDb::k2dSplineCtlVertex != vertex->vertexType())
                pointArray.push_back (RealDwgUtil::DPoint3dFromGePoint3d (point, vertex->position()));

            vertex->close();
            }
        delete iterator;
        }

    auto normal = pPolyline->normal ();
    Transform           compositeTransform, extrusionTransform;
    if (NULL !=RealDwgUtil::GetExtrusionTransform (extrusionTransform, normal, pPolyline->elevation()))
        compositeTransform.InitProduct (context.GetTransformToDGN(), extrusionTransform);
    else
        compositeTransform = context.GetTransformToDGN();

    // AutoCAD duplicates middle pole to satisfy nPoles >= order
    if ( (3 == pointArray.size()) && (AcDb::k2dCubicSplinePoly == pPolyline->polyType()) )
        pointArray.insert (pointArray.begin()+1, pointArray[1]);

    RealDwgStatus   status;
    if (RealDwgSuccess != (status = context.CreateElementFromDwgSplineParamsAndTransform (outElement, degree, pointArray.size(), 0, (Adesk::kFalse != pPolyline->isClosed()),
                                                            false, &pointArray.front(), NULL, NULL, compositeTransform)))
        return status;

    if (0.0 != startWidth || 0.0 != endWidth)
        context.SetLineWidth (outElement, startWidth, endWidth, &normal);

    context.ElementHeaderFromEntity (outElement, pPolyline);
    context.ApplyThickness (outElement, pPolyline->thickness(), pPolyline->normal(), false);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley       03/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SimplePolyline2dToElement
(
AcDb2dPolyline*         acPolyline,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const
    {
    DPoint2dArray           pointArray;
    DPoint2dArray           widths;
    DoubleArray             bulges;
    bool                    bulgeFound = false;
    bool                    widthFound = false;

    AcDbObjectIterator*     iterator = acPolyline->vertexIterator();
    for (; !iterator->done(); iterator->step())
        {
        AcDbObjectId        vertexId = iterator->objectId ();
        AcDb2dVertex*       vertex = NULL;

        Acad::ErrorStatus status;
        if (Acad::eOk != (status = acPolyline->openVertex (vertex, vertexId, AcDb::kForRead)))
            return ErrorOpeningVertex;

        // ignore spline control points - unexpected in either a simple or a curve fitted polyline - TFS 8047.
        if (vertex->vertexType() == AcDb::k2dSplineCtlVertex)
            {
            vertex->close ();
            continue;
            }

        DPoint2d            point;
        pointArray.push_back (RealDwgUtil::DPoint2dFromGePoint3d (point, vertex->position()));

        point.x = vertex->startWidth();
        point.y = vertex->endWidth();
        widthFound |= (point.x != 0.0 || point.y != 0.0);
        widths.push_back (point);

        bulgeFound |= vertex->bulge() != 0.0;
        bulges.push_back (vertex->bulge());

        vertex->close ();
        }
    delete iterator;

    if (!widthFound)
        widths.clear();

    RealDwgStatus   status;
    if (RealDwgSuccess != (status = context.CreateElementFromPolylineParams (outElement, pointArray, bulgeFound ? &bulges : NULL, widthFound ? &widths : NULL, (Adesk::kFalse != acPolyline->isClosed()), false, acPolyline->normal(),
                                                                      acPolyline->elevation(), acPolyline->thickness(), acPolyline, context.GetTransformToDGN())))
        return status;

    if (acPolyline->isLinetypeGenerationOn())
        context.AddLineStyleContinuousLinkage (outElement);

    context.ElementHeaderFromEntity (outElement, acPolyline);
    context.ApplyThickness (outElement, acPolyline->thickness(), acPolyline->normal(), false);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/15
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   FitPolylineAsCurve (EditElementHandleR outElement, AcDb2dPolyline* polyline2d, ConvertToDgnContextR context) const
    {
    // complex chain does not apply line style param Non-Segment Mode across arc segments - fit it as a curve. AFAB case & TFS 8061.
    AcDbObjectIterator* iterator = polyline2d->vertexIterator ();
    DPoint3dArray       fitPoints;
    double              priorBulge = 0.0;
    DPoint3d            priorPoint;
    double              constantWidth = 0.0;
    bool                hasConstantWidth = false;

#if RealDwgVersion >= 2012
    hasConstantWidth = Acad::eOk == polyline2d->constantWidth(constantWidth) && constantWidth > TOLERANCE_ZeroSize;
#endif

    for (; !iterator->done(); iterator->step())
        {
        AcDbObjectPointer<AcDb2dVertex> vertex(iterator->objectId(), AcDb::kForRead);
        if (Acad::eOk != vertex.openStatus() || vertex->vertexType() == AcDb::k2dSplineCtlVertex)
            continue;

        if (!hasConstantWidth && (vertex->startWidth() > TOLERANCE_ZeroSize || vertex->endWidth() > TOLERANCE_ZeroSize))
            {
            // variable widths exist - bail out to create piecewise elements
            delete iterator;
            return  NotApplicable;
            }

        DPoint3d            point;
        RealDwgUtil::DPoint3dFromGePoint3d (point, vertex->position());

        // if bulged, insert a mid-arc point between prior point and this point
        if (fabs(priorBulge) > TOLERANCE_BulgeFactor)
            fitPoints.push_back (RealDwgUtil::ComputeBulgePoint(priorBulge, priorPoint, point));

        fitPoints.push_back (point);
            
        // set or remove current bulge
        if (fabs(vertex->bulge()) > TOLERANCE_BulgeFactor)
            priorBulge = vertex->bulge ();
        else
            priorBulge = 0.0;

        priorPoint = point;
        }

    // add end conditions if closed
    if (polyline2d->isClosed())
        {
        if (fabs(priorBulge) > TOLERANCE_BulgeFactor)
            fitPoints.push_back (RealDwgUtil::ComputeBulgePoint(priorBulge, priorPoint, fitPoints[0]));
        fitPoints.push_back (fitPoints[0]);
        }

    delete iterator;

    return  context.CreateElementFromCurveFitPolyline (outElement, fitPoints, 
                            polyline2d->normal(),
                            polyline2d->elevation(),
                            constantWidth,
                            polyline2d->thickness(),
                            polyline2d);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDb2dPolyline*         acPolyline = AcDb2dPolyline::cast (acObject);

    switch (acPolyline->polyType())
        {
        case    AcDb::k2dQuadSplinePoly:
        case    AcDb::k2dCubicSplinePoly:
            return SplinePolyline2dToElement (acPolyline, outElement, context);

        default:
        case   AcDb::k2dFitCurvePoly:
            if (context.IsPlinegenAsCurve() && NeedToFitPolylineAsCurve(acPolyline) && RealDwgSuccess == this->FitPolylineAsCurve(outElement, acPolyline, context))
                return  RealDwgSuccess;
        case   AcDb::k2dSimplePoly:
            return SimplePolyline2dToElement (acPolyline, outElement, context);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
* This method is called from the ToDwgExtension::ToObject methods of several elements.
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           VerticesFromPolylineInfo (AcDbObjectP acObject, PolylineInfoCR polylineInfo, ElementHandleR sourceElement, ConvertFromDgnContext& context) const override
    {
    AcDb2dPolyline* acPolyline = AcDb2dPolyline::cast (acObject);

    UInt32          numPoints  = (UInt32) polylineInfo.GetPointCount();

    if (numPoints < 2)
        return InvalidPolygonVertexCount;

    DPoint3dCP      points = polylineInfo.FirstPoint();
    DPoint2dCP      widths = polylineInfo.FirstWidth();
    double const*   bulges = polylineInfo.FirstBulge();
    bool            closed = polylineInfo.IsClosed();

    // remove the closure point
    if (numPoints > 2 && closed && points[0].IsEqual(points[numPoints-1], TOLERANCE_UORPointEqual))
        numPoints--;

    double  startWidth      = 0.0;
    double  endWidth        = 0.0;
    double  widthIncrement  = 0.0;
    if (NULL == widths)
        {
        startWidth      = polylineInfo.GetStartWidth();
        endWidth        = polylineInfo.GetEndWidth();
        widthIncrement  = (endWidth - startWidth) / (numPoints - 1);
        }

    // count vertices in the existing polyline..
    UInt32 currentNumVertices = PolylineUtil::CountVertices (acPolyline, (AcDb::Vertex2dType)(-1));

    if (numPoints == currentNumVertices)
        {
        AcDbObjectIterator* iterator = acPolyline->vertexIterator();
        double              width     = startWidth; 
        for (int iPoint = 0; !iterator->done(); iterator->step(), iPoint++)
            {
            AcDbObjectId        vertexId = iterator->objectId ();
            AcDb2dVertex*       vertex = NULL;

            Acad::ErrorStatus status;
            if (Acad::eOk != (status = acPolyline->openVertex (vertex, vertexId, AcDb::kForWrite)))
                return ErrorOpeningVertex;

            vertex->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (points[iPoint]));
            if (NULL == widths)
                {
                double nextWidth = width + widthIncrement;
                vertex->setStartWidth (width);
                vertex->setEndWidth (nextWidth);
                width = nextWidth;
                }
            else
                {
                vertex->setStartWidth (widths[iPoint].x);
                vertex->setEndWidth (widths[iPoint].y);
                }

            vertex->setBulge ((NULL != bulges) ? bulges[iPoint] : 0.0);
            vertex->close();
            }
        delete iterator;
        }
    else
        {
        PolylineUtil::RemoveAllVertices (acPolyline);

        double              segmentStartWidth = startWidth; 
        for (UInt32 iPoint = 0; iPoint < numPoints; iPoint++)
            {
            double segmentEndWidth;
            if (NULL != widths)
                {
                segmentStartWidth = widths[iPoint].x;
                segmentEndWidth   = widths[iPoint].y;
                }
            else
                {
                segmentEndWidth = segmentStartWidth + widthIncrement;
                }

            PolylineUtil::AppendVertex (acPolyline, points[iPoint], segmentStartWidth, segmentEndWidth, (NULL != bulges) ? bulges[iPoint] : 0.0);
            }
        }

    if ( (false != closed) != (Adesk::kFalse != acPolyline->isClosed()) )
        closed ? acPolyline->makeClosed() : acPolyline->makeOpen();

    return RealDwgSuccess;
    }

};  // ToDgnExt2dPolyline



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/11
+===============+===============+===============+===============+===============+======*/
class    Convert2dPolylineHelper
{
private:
        ElementHandleCP         m_inElementHandle;
        AcDb2dPolyline*         m_out2dPolyline;
        ConvertFromDgnContext*  m_fromDgnContext;
        

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
Adesk::Int16    FlagFromType (AcDb::Vertex2dType type)
    {
    if (type == AcDb::k2dSplineFitVertex)
        return 8;
    else if (type == AcDb::k2dSplineCtlVertex)
        return 16;
    else
        BeAssert (false && L"Unexpected 2dPolyline vertex type!");

    return 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool            AnyVerticesChanged (MSBsplineCurve* pCurve)
    {
    double      tol = TOLERANCE_Relative * bsiDPoint3d_getLargestCoordinate (pCurve->poles, pCurve->params.numPoles);
    bool        noneChanged = false;
    AcDbObjectIterator* iterator   = m_out2dPolyline->vertexIterator();
    for (int iVertex = 0; !iterator->done() && noneChanged; iterator->step())
        {
        AcDbObjectId        vertexId = iterator->objectId ();
        AcDb2dVertex*       vertex = NULL;

        Acad::ErrorStatus status;
        if (Acad::eOk != (status = m_out2dPolyline->openVertex (vertex, vertexId, AcDb::kForRead)))
            {
            DIAGNOSTIC_PRINTF ("Error opening a 2D polyline vertex\n");
            noneChanged = false;
            break;
            }
        else if (vertex->vertexType() != AcDb::k2dSplineFitVertex)
            {
            DPoint3d            currentPosition;
            RealDwgUtil::DPoint3dFromGePoint3d (currentPosition, vertex->position());
            noneChanged = currentPosition.IsEqual (pCurve->poles[iVertex], tol);
            iVertex++;
            }
        vertex->close();
        }
    delete  iterator;

    return !noneChanged;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   NewSplineFromPoles (MSBsplineCurve* pCurve, double startWidth, double endWidth)
    {
    PolylineUtil::RemoveAllVertices (m_out2dPolyline);

    // set to normal polyline.
    m_out2dPolyline->setPolyType (AcDb::k2dSimplePoly);

    // add poles as normal vertices.
    double              width       = startWidth;
    double              widthDelta  = (endWidth - startWidth) / (double) (pCurve->params.numPoles - 1);
    for (int iVertex = 0; iVertex < pCurve->params.numPoles; iVertex++, width + widthDelta)
        PolylineUtil::AppendVertex (m_out2dPolyline, pCurve->poles[iVertex], width, width + widthDelta, 0.0);

    // make closed if needed.
    if ( (0 != pCurve->params.closed) != (Adesk::kFalse != m_out2dPolyline->isClosed()) )
        pCurve->params.closed ? m_out2dPolyline->makeClosed() : m_out2dPolyline->makeOpen();

    // convert to spline - let RealDwg add the k2dSplineFitVertex's
    m_out2dPolyline->convertToPolyType ( (4 == pCurve->params.order) ? AcDb::k2dCubicSplinePoly : AcDb::k2dQuadSplinePoly);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* Compute the preset number of stroke points that should be stored for a spline
* frame polyline with the given characteristics.
* @bsimethod                                                    DavidAssaf      02/01
+---------------+---------------+---------------+---------------+---------------+------*/
int             ComputePolylineStrokePointCount (int numPoles, int order, bool closed)
    {
    // SPLINESEGS:
    // * ACAD global used in wacky formula for #strokes of displayed B-spline curves
    // * may be negative to indicate render betw/ stroke pts with arcs
    // * default value is 8
    int     splinesegs = abs (m_fromDgnContext->GetDatabase()->splinesegs());

    if (0 == splinesegs)
        splinesegs = 8;

    return  closed ? splinesegs * numPoles : splinesegs * (numPoles - order + 1) + 1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           Extract2dPolylineFromBCurve (MSBsplineCurveP pCurve, TransformCP pCompositeTransform)
    {
    bool                replaceStrokePoints = false;
    bool                splineFit           = false;
    double              startWidth          = 0.0;           // Needs work.
    double              endWidth            = 0.0;

    if (pCurve->rational)
        bsputil_unWeightPoles (pCurve->poles, pCurve->poles, pCurve->weights, pCurve->params.numPoles);

    pCompositeTransform->multiply (pCurve->poles, pCurve->poles, pCurve->params.numPoles);
    m_fromDgnContext->ExtractWidthFromElement (startWidth, endWidth, *m_inElementHandle);

    if ( (pCurve->params.order > 4) || pCurve->rational || (0 != pCurve->params.numKnots) )
        {
        // Not really compatible with AcDb2dPolyline.
        // If it has a start or end width, the best we can do is stroke the bspline to something close.
        // If no width, return BSIERROR and let it go through the failedObjectId path to create a bspline.
        if ( (startWidth < TOLERANCE_ZeroSize) && (endWidth < TOLERANCE_ZeroSize) )
            return CantCreateCurve;

        // It has width, need to stroke it.
        m_out2dPolyline->setPolyType (AcDb::k2dSimplePoly);

        PolylineUtil::RemoveAllVertices (m_out2dPolyline);
        DPoint3d    *pStrokePoints  = NULL;
        int         numStrokePoints = ComputePolylineStrokePointCount (pCurve->params.numPoles, pCurve->params.order, (0 != pCurve->params.closed));

        if (pCurve->rational)
            bsputil_weightPoles (pCurve->poles, pCurve->poles, pCurve->weights, pCurve->params.numPoles);

        if (SUCCESS == bspcurv_evaluateCurve (&pStrokePoints, NULL, &numStrokePoints, pCurve))
            {
            double  width = startWidth, widthDelta = (endWidth - startWidth) / (double) (numStrokePoints - 1);
            for (int iVertex = 0; iVertex < numStrokePoints; iVertex++, width += widthDelta)
                PolylineUtil::AppendVertex (m_out2dPolyline, pStrokePoints[iVertex], width, width + widthDelta, 0.0);
            }

        if ( (0 != pCurve->params.closed) != (Adesk::kFalse != m_out2dPolyline->isClosed()) )
            pCurve->params.closed ? m_out2dPolyline->makeClosed() : m_out2dPolyline->makeOpen();

        BSIBaseGeom::Free (pStrokePoints);
        }
    else
        {
        int         currentNumVertices = PolylineUtil::CountVertices (m_out2dPolyline, AcDb::k2dSplineFitVertex);
        if ( (pCurve->params.numPoles != currentNumVertices) || AnyVerticesChanged (pCurve) )
            return NewSplineFromPoles (pCurve, startWidth, endWidth);
        }

    return RealDwgSuccess;
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/11
+---------------+---------------+---------------+---------------+---------------+------*/
Convert2dPolylineHelper (AcDb2dPolyline* outPolyline, ElementHandleCR inElement, ConvertFromDgnContextR context)
    {
    m_out2dPolyline = outPolyline;
    m_inElementHandle = &inElement;
    m_fromDgnContext = &context;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    01/99
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDb2dPolylineFromCurve (CurveVectorCR curveVector)
    {
    DPoint3d    defaultNormal = DPoint3d::From (0, 0, 1);
    RealDwgUtil::DPoint3dFromGeVector3d (defaultNormal, m_out2dPolyline->normal());

    double      extrusionDistance = 0.0;
    m_fromDgnContext->GetEntityThicknessFromElementLinkage (extrusionDistance, defaultNormal, &defaultNormal, *m_inElementHandle);

    DPoint3d    normal, headerNormal;
    double      elevation;
    Transform   compositeTransform;
    if (SUCCESS != m_fromDgnContext->ExtractPolylineTransformFromElement(compositeTransform, headerNormal, normal, elevation, *m_inElementHandle, defaultNormal))
        return  WrongMstnElementType;

    if (extrusionDistance > 0.0 && defaultNormal.DotProduct(normal) < 0.0)
        extrusionDistance = -extrusionDistance;

    m_out2dPolyline->setNormal (RealDwgUtil::GeVector3dFromDPoint3d (headerNormal));
    m_out2dPolyline->setThickness (extrusionDistance);
    m_out2dPolyline->setElevation (elevation);

    RealDwgStatus   status;
    bool            closed, hasConstantWidth, continueLt;
    double          constantWidth = 0.0;
    DoubleArray     bulges;
    DPoint3dArray   points;
    DPoint2dArray   widths;

    status = m_fromDgnContext->ExtractLWPolylineFromElement (points, bulges, widths, hasConstantWidth, constantWidth, closed, continueLt,
                                                             *m_inElementHandle, normal, &compositeTransform, TOLERANCE_UORPointEqual, true);
    if (RealDwgSuccess == status)
        {
        size_t      nPoints = points.size ();
        size_t      nBulges = bulges.size ();
        size_t      nWidths = widths.size ();
        bool        hasWidths = nWidths > 0, hasBulges = nBulges > 0;

        // just count vertices.
        UInt32      currentNumVertices = PolylineUtil::CountVertices (m_out2dPolyline, (AcDb::Vertex2dType)(-1));
        if (nPoints == currentNumVertices)
            {
            AcDbObjectIterator* iterator = m_out2dPolyline->vertexIterator();
            for (int iPoint = 0; !iterator->done(); iterator->step(), iPoint++)
                {
                AcDbObjectId        vertexId = iterator->objectId ();
                AcDb2dVertex*       vertex = NULL;

                Acad::ErrorStatus   es;
                if (Acad::eOk != (es = m_out2dPolyline->openVertex(vertex, vertexId, AcDb::kForWrite)))
                    return ErrorOpeningVertex;

                vertex->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (points[iPoint]));
                vertex->setStartWidth (hasConstantWidth ? constantWidth : (hasWidths ? widths[iPoint].x : 0.0));
                vertex->setEndWidth   (hasConstantWidth ? constantWidth : (hasWidths ? widths[iPoint].y : 0.0));
                vertex->setBulge (hasBulges ? bulges[iPoint] : 0.0);

                vertex->close();
                }
            delete iterator;
            }
        else
            {
            PolylineUtil::RemoveAllVertices (m_out2dPolyline);

            for (UInt32 iPoint = 0; iPoint < nPoints; iPoint++)
                {
                PolylineUtil::AppendVertex (m_out2dPolyline, points[iPoint],
                                            hasConstantWidth ? constantWidth : (hasWidths ? widths[iPoint].x : 0.0),
                                            hasConstantWidth ? constantWidth : (hasWidths ? widths[iPoint].y : 0.0),
                                            hasBulges ? bulges[iPoint] : 0.0);
                }
            }

        closed ? m_out2dPolyline->makeClosed() : m_out2dPolyline->makeOpen();
        continueLt ? m_out2dPolyline->setLinetypeGenerationOn() : m_out2dPolyline->setLinetypeGenerationOff ();
        }
    else
        {
        MSBsplineCurve      curve;

        status = RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (curveVector.ToBsplineCurve(curve));

        if (RealDwgSuccess == status)
            {
            status = this->Extract2dPolylineFromBCurve (&curve, &compositeTransform);
            curve.ReleaseMem ();
            }
        }

    return status;
    }

};  // Convert2dPolylineHelper

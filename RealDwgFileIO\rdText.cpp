/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdText.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/


// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtText : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbText*           pText       = AcDbText::cast (acObject);
    const ACHAR*        textString  = pText->textStringConst();

    if (0 == *textString)
        return  EmptyText;

    TextSizeParam       textSize;
    DPoint3d            origin;
    DPoint2d            scale;
    RotMatrix           rMatrix;
    bool                useUserOrigin   = false;
    bool                fieldsPresent   = false;
    AcGePoint3d         userOrigin      = pText->alignmentPoint();

    TextBlock           textBlock (*context.GetModel()->GetDgnModelP());
    RealDwgStatus       status;
    if (RealDwgSuccess != (status = context.ProcessText (textBlock, &origin, &useUserOrigin, &rMatrix, &textSize, &scale, false, &fieldsPresent, pText)))
        return  status;

    if (textBlock.IsComplexText ())
        {
        textBlock.SetNodeNumber (context.GetFileHolder().GetAndIncrementNextAvailableTextNodeID ());

        /* if a node is created we always use the user origin */
        context.GetDgnTextTransformFromDwg (&textSize, &origin, &rMatrix,
                                               pText->normal(), userOrigin, pText->rotation(), pText->height(), pText->widthFactor());
        useUserOrigin = true;

        if (textBlock.IsValidUserOriginForDText ())
            textBlock.SetUserOrigin (origin);
        }

    textBlock.SetForceTextNodeFlag (fieldsPresent);

    TextBlockToElementResult    tbResult = TextHandlerBase::CreateElement (outElement, NULL, textBlock);
    if (TEXTBLOCK_TO_ELEMENT_RESULT_Empty == tbResult)
        {
        DIAGNOSTIC_PRINTF ("Ignoring empty text %I64d...\n", context.ElementIdFromObject(acObject));
        return  EmptyText;
        }
    if (!outElement.IsValid())
        return  CantCreateText;

    context.ElementHeaderFromEntity (outElement, pText);
    context.ApplyThickness (outElement, pText->thickness(), pText->normal(), false);

    if (fieldsPresent)
        {
        context.AddPostProcessObject (pText);
#if defined (REALDWG_DIAGNOSTICS_VERBOSE)
        RealDwgUtil::DumpField ("Original Field", pText);
#endif
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContextR context) const override
    {
    // Should only get here if the text contained fields.
    DgnModelP           model = context.GetModel ();
    EditElementHandle   textElement(context.ElementIdFromObject(acObject), model);

    if (textElement.IsValid())
        {
        // Convert field from dwg to dgn format
        if (!TextField::ConvertFieldsFromDWG(textElement))
            return  CantCreateFields;

        StatusInt   status = textElement.ReplaceInModel (textElement.GetElementRef());
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
        }

    return  RealDwgSuccess;
    }

};  // DgnExtText



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ConvertMIFToUnicodeString (AcString& stringInOut, AcDbText const* pText)
    {
    /*-----------------------------------------------------------------------------------
    In ACAD/RD 2012, if a text has an MIF override, its content is assumed to be converted
    as a multibyte string.  However, that assumption does not seem to be true for 
    ACAD/RealDWG 2009, which can incorrectly convert texts and also added the MIF xdata at
    load time.  So, we leave R2009 unchanged.  TR 330870.
    -----------------------------------------------------------------------------------*/
    bool                converted = false;
#if RealDwgVersion > 2009
    size_t              numChars = stringInOut.length ();
    if (numChars > 0)
        {
        LangCodePage    codepageOverride = LangCodePage::LatinI;
        RealDwgResBuf*  xData = static_cast <RealDwgResBuf*> (pText->xData(L"AcadStringInfo"));
        if (RealDwgXDataUtil::GetAnsiCodePageFromXData(&codepageOverride, xData) && LangCodePage::Unicode != codepageOverride)
            {
            WString     unicodeString;
            unicodeString.reserve(numChars + 1);

            if (BeStringUtilities::LocaleCharToWChar(unicodeString, stringInOut.utf8Ptr(), (UInt32)codepageOverride, numChars))
                {
                stringInOut.assign (unicodeString.c_str());
                converted = true;
                }
            }
        RealDwgResBuf::Free (xData);
        }
#endif

    return  converted;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
static double   GetDisplayRotationAngle (AcDbText const* acText, bool isAnnotative)
    {
    /*------------------------------------------------------------------------------------------------------
    The option "Match Layout Orientation" in text style suppresses DXF group code 50 for both annotative and 
    non-annotative texts, but ACAD still displays the non-annotative texts with the rotation angle. TFS#96837

    We need to get the angle by turning off paper orientation from a non-annotative text.
    ------------------------------------------------------------------------------------------------------*/
    double                      rotationAngle = acText->rotation ();
    if (isAnnotative)
        return  rotationAngle;

    AcDbPaperOrientationPE*     paperOrientationPE = ACRX_PE_PTR (acText, AcDbPaperOrientationPE);
    if (NULL != paperOrientationPE)
        {
        // workaround the lack of AcDbObject const* for AcDbPaperOrientationPE API's
        AcRxObject*             newText = acText->clone ();
        if (NULL != newText)
            {
            AcDbText*           copyText = AcDbText::cast (newText);
            if (paperOrientationPE->paperOrientation(copyText))
                {
                paperOrientationPE->setPaperOrientation (copyText, false);
                rotationAngle = copyText->rotation ();
                }

            delete newText;
            }
        }

    return  rotationAngle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::ProcessText
(
TextBlockR                  textBlock,
DPoint3dP                   pOrigin,
bool*                       pUseUserOrigin,
RotMatrixP                  pRotMatrix,
TextSizeParam*              pTextSize,
DPoint2dP                   pScale,
bool                        ignoreFields,
bool*                       pFieldsPresent,
AcDbText const*             pText,
AcDbObjectCP                parentObject
)
    {
    bool hasFields;
    if (NULL == pFieldsPresent)
        pFieldsPresent = &hasFields;
    
    AcString    rawStringContent (pText->textStringConst ());

    ConvertMIFToUnicodeString (rawStringContent, pText);

    AcString    stringContent;
    AcDbField*  pField = NULL;
    
    if (!ignoreFields && pText->hasFields() && (Acad::eOk == pText->getField (L"TEXT", pField, AcDb::kForRead)))
        {
        if (NULL != pFieldsPresent)
            *pFieldsPresent = true;
        
        AcString    acString = rawStringContent;
        this->GetFieldsString (stringContent, pField, acString);
        
        pField->close ();
        pField = NULL;
        }
    else
        {
        if (NULL != pFieldsPresent)
            *pFieldsPresent = false;
        
        stringContent = rawStringContent;
        }
    
    if (stringContent.isEmpty ())
        return EmptyText;

    // TFS 753830 - replace fractions that cause problems with ShxFont (do this after fields extraction):
    bool    promoteToMText = false;
    if (this->GetFileHolder().IsFileReadOnlyForPrinting())
        promoteToMText = RealDwgUtil::ReplaceFractionMarkups (stringContent, pText->textStyle());

    if (!promoteToMText && nullptr != pFieldsPresent && *pFieldsPresent)
        promoteToMText = true;
    
    // Apply rules to pick which origin to use, primary or user.
    // Right now, I am going to use the origin in the following cases: Left or Align horizontal mode or Base vertical mode. May need to adjust this based on cases. This seems to be working fine on all the cases
    // NOT_SURE: Account for other justification methods such as align, fit, etc.
    // For a DXF file it has a different set of rules. According to DXF format document, group codes 72 (horizontalMode) and 73 (verticalMode) decide which origin to be used. If either 72 or 73 has a none zero value, user origin is used and primary origin is calculated. This means that, only both values are zero, i.e. left-base justification, primary origin is used. Ideally this DXF rule should have been applied in DD, but ODA rejected my proposal.
    
    bool                isDxf           = (DgnFileFormatType::DXF == m_dgnFile->GetOriginalFormat ());
    TextSizeParam       textSize;
    DPoint3d            origin;
    AcGePoint3d         userOrigin      = pText->alignmentPoint ();
    AcGePoint3d         originalOrigin  = pText->position ();
    bool                useUserOrigin   = false;
    RotMatrix           rMatrix;
    // get annotation scale
    double              annoScale = 1.0;
    bool                isAnnotative = this->GetDisplayedAnnotationScale (annoScale, nullptr == parentObject ? pText : parentObject);
    // get effective rotation angle for display
    double              rotationAngle   = GetDisplayRotationAngle (pText, isAnnotative);

    /*-------------------------------------------------------------------------------------------
    Published rules of how justifications work on AcDbText taken from RealDWG API Rererences:

    If vertical mode is AcDb::kTextBase and horizontal mode is either AcDb::kTextLeft, AcDb::kTextAlign, 
        or AcDb::kTextFit, then the position point is the insertion point for the text object and, 
        for AcDb::kTextLeft, the alignment point (DXF group code 11) is automatically calculated 
        based on the other parameters in the text object. 

    For all other vertical and horizontal mode combinations, the alignment point is used as the 
        insertion point of the text and the position point is automatically calculated based on 
        the other parameters in the text object. 


    Art Cooney of Autodesk has provided below additional information, while attempted to find 
        the root cause of TFS 750329 for us:

    For horizontal modes AcDb::kTextAlign, AcDb::kTextMid, and AcDb::kTextFit, the vertical mode 
        value is not used at all.

    1) For horizontal mode AcDb::kTextAlign, the vector that specifies the direction to align the 
        text is from the position point to the alignment point, so both points matter.
    2) For horizontal mode AcDb::kTextMid, only the alignment point matters and that point is used 
        as the middle point (both horizontally and vertically) of the text string.  AutoCAD/RealDWG 
        will generate the position point.
    3) For horizontal mode AcDb::kTextFit, the text string is stretched or compressed in the 
        horizontal direction so that it fits between the position point and alignment point with 
        the text starting at the position point and ending at the alignment point.  So, again both 
        points matter.
    -------------------------------------------------------------------------------------------*/
    AcDb::TextHorzMode  horizontalMode = pText->horizontalMode ();
    AcDb::TextVertMode  verticalMode = pText->verticalMode ();

    if ((isDxf && AcDb::kTextLeft == horizontalMode && AcDb::kTextBase == verticalMode) ||
        (!isDxf && (AcDb::kTextLeft == horizontalMode || AcDb::kTextAlign == horizontalMode || AcDb::kTextBase == verticalMode)))
        {
        try
            {
            this->GetDgnTextTransformFromDwg(&textSize, &origin, &rMatrix, pText->normal(), originalOrigin, rotationAngle, pText->height(), pText->widthFactor());
            useUserOrigin = false;
            }
        catch (DiscardInvalidEntityException& invalidEntityError)
            {           
            DIAGNOSTIC_PRINTF("Discarding Invalid Entity: %ls \n", invalidEntityError.ErrorMessage());            
            return OriginOutOfRange;
            }

        }
    else if (AcDb::kTextMid == horizontalMode)
        {
        // apply Rule #2 above - force CC justification:
        this->GetDgnTextTransformFromDwg (&textSize, &origin, &rMatrix, pText->normal(), userOrigin, rotationAngle, pText->height(), pText->widthFactor());
        verticalMode = AcDb::kTextVertMid;
        useUserOrigin = true;
        }
    else
        {
        this->GetDgnTextTransformFromDwg (&textSize, &origin, &rMatrix, pText->normal (), userOrigin, rotationAngle, pText->height (), pText->widthFactor ());
        useUserOrigin = true;
        }
    
    DPoint2d    fontSize = DPoint2d::From (textSize.size.width, textSize.size.height);
    
    DPoint2d    scale;
    scale.y = pText->height ();
    scale.x = (scale.y * pText->widthFactor ());

    TextBlockPropertiesPtr          tbProps;
    ParagraphPropertiesPtr          paraProps;
    RunPropertiesPtr                runProps;
    bool                            bBackwards      = TO_BOOL (pText->isMirroredInX ());
    bool                            bUpsideDown     = TO_BOOL (pText->isMirroredInY ());
    AcDbTextStyleTableRecordPointer dwgTextStyle    (pText->textStyle (), AcDb::kForRead);
    DgnTextStylePtr                 dgnTextStyle;
    
    if ((Acad::eOk == dwgTextStyle.openStatus ()) && (dgnTextStyle = DgnTextStyle::GetByID (m_pFileHolder->GetTextStyleIndex ()->GetDgnId (pText->textStyle ().handle ()), *m_dgnFile)).IsValid ())
        { 
        /////
        // Fix for TFS#1061608
        // To address a performace issue, overrides will not be honored
        /////
        double dScaleFactor = (m_model->GetModelInfo().GetIsUseAnnotationScaleOn() ? m_model->GetModelInfo().GetAnnotationScaleFactor() : 1.0);

        tbProps = TextBlockProperties::Create(*m_model);
        tbProps->ApplyTextStyleNoHonorOverrides(dgnTextStyle, dScaleFactor);
      
        paraProps = ParagraphProperties::Create(*m_model);
        paraProps->ApplyTextStyleNoHonorOverrides(dgnTextStyle, dScaleFactor);

        runProps = RunProperties::Create(DgnFontManager::GetDefaultTrueTypeFont(), fontSize, *m_model);
        runProps->ApplyTextStyleNoHonorOverrides(dgnTextStyle, dScaleFactor);

       // force set text size
       // Calling ForceSetFontSize() to improve performance. It doesn't check the validity of the TextStyle ID (that RunProperties holds) by calling DgnTextStyle::GetByID which is an expensive call
       runProps->ForceSetFontSize (fontSize);

        Adesk::UInt8 flagBits = dwgTextStyle->flagBits ();
        
        // Values from documentation for flagBits function. Apparently no enum for this.
        bBackwards  |= (0 != (flagBits & 0x2)); // "second bit"
        bUpsideDown |= (0 != (flagBits & 0x4)); // "third bit"
        }
    else
        {
        tbProps     = TextBlockProperties::Create (*m_model);
        paraProps   = ParagraphProperties::Create (*m_model);
        runProps    = RunProperties::Create (DgnFontManager::GetDefaultTrueTypeFont (), fontSize, *m_model);
        }
    
    // set or remove text annotation scale
    if (isAnnotative)
        tbProps->SetAnnotationScale (annoScale);
    else
        tbProps->ClearAnnotationScale ();

    if (tbProps->IsBackwards () != bBackwards)
        tbProps->SetIsBackwards (bBackwards);
    
    if (tbProps->IsUpsideDown () != bUpsideDown)
        tbProps->SetIsUpsideDown (bUpsideDown);

    if (verticalMode < AcDb::kTextBase || horizontalMode < AcDb::kTextLeft || verticalMode > AcDb::kTextTop || horizontalMode > AcDb::kTextFit)
        {
        // Out of bounds error
        paraProps->SetJustification (TextElementJustification::LeftBaseline);
        }
    else if (horizontalMode == AcDb::kTextFit)
        {
        // According to DXF document, group code 72 is 5 for fit between points IF 73 is 0. However, ACAD seems always ignores 73 if 72 is 5.  Such example is seen in DWG produced by Polaris as in TR 222668.
        // See above confirmation by Art: verticalMode is indeed ignored by ACAD & RealDWG!
        tbProps->SetIsFitted (true);
        paraProps->SetJustification (TextElementJustification::RightBaseline); // Preserve secondary text loc
        }
    else
        {
        TextElementJustification    just = static_cast<TextElementJustification> (s_justTable[verticalMode][horizontalMode]);
        paraProps->SetJustification (just);
        }
    
    // local oblique angle always overrides text style - style only serves as a default oblique value, TR74414, TFS129717.
    // check oblique angle before turning it on: an angle near 360 degrees as in TFS 14667 is the same as 0.
    double      obliqueAngle = pText->oblique ();
    if (fabs(obliqueAngle) > MIN_AngleRadians && fabs(obliqueAngle) < (msGeomConst_2pi - MIN_AngleRadians))
        {
        runProps->SetIsItalic (true);
        runProps->SetCustomSlantAngle (obliqueAngle);
        }
    else
        {
        runProps->SetIsItalic (false);
        runProps->SetCustomSlantAngle (0.0);
        }
    
    // an italic TTF is always slanted: the text is slanted by the TTF's default italic value (0=obliqueAngle) or by obliqueAngle.
    if (Acad::eOk == dwgTextStyle.openStatus ())
        {
        Adesk::Boolean  bold            = false;
        Adesk::Boolean  italic          = false;
        Charset         charset = Charset::kUndefinedCharset;
        FontPitch       pitch = FontPitch::kDefault;
        FontFamily      family = FontFamily::kDoNotCare;
        ACHAR*          typeface        = NULL;
        
        dwgTextStyle->font (typeface, bold, italic, charset, pitch, family);
        acutDelString (typeface);
        
        if (italic)
            runProps->SetIsItalic (true);
        }
    
    // TR 80380: check to see if the width scale is off the chart ie > 50x the height if it is try and calculate a new width based on the characters in the text and the primary and secondary text locations. After the Japanese 02 release this processing should be done for all fitted text.
    if (tbProps->IsFitted () && (RealDwgUtil::MSVersionFromAcVersion (m_pFileHolder->GetDatabase ()->originalFileVersion ()) < DwgFileVersion_13))
        fixupBadDataForFittedText (userOrigin, originalOrigin, scale, stringContent, *tbProps, *paraProps, *runProps, pText->widthFactor (), *this);

    if (tbProps->IsVertical ())
        {
        // NEEDS_WORK: Do I need to do anything on the way out.
        // Need to remap justifications
        TextElementJustification just = TextElementJustification::LeftTop;
        getMicroStationJustificationFromAutoCADVerticalAlignmentMode (just, horizontalMode);
        
        paraProps->SetJustification (just);
        }

    textBlock.SetType (TEXTBLOCK_TYPE_DwgDText);
    textBlock.SetProperties (*tbProps);
    textBlock.SetParagraphPropertiesForAdd (*paraProps);
    textBlock.SetRunPropertiesForAdd (*runProps);

    if (promoteToMText)
        textBlock.FromMText (stringContent.kwszPtr (), DwgContextForTextBlock(this, NULL, pText), this->GetScaleToDGN ());
    else
        textBlock.FromDText (stringContent.kwszPtr ());
    
    textBlock.PerformLayout ();

    // NEEDS_WORK: I had some code for vertical text here. Check that out also.
    if (useUserOrigin)
        textBlock.SetUserOrigin (origin);
    else
        textBlock.SetTextAutoCADOrigin (origin);

    textBlock.SetOrientation (rMatrix);

    if (NULL != pOrigin)        { *pOrigin        = origin;         }
    if (NULL != pScale)         { *pScale         = scale;          }
    if (NULL != pRotMatrix)     { *pRotMatrix     = rMatrix;        }
    if (NULL != pTextSize)      { *pTextSize      = textSize;       }
    if (NULL != pUseUserOrigin) { *pUseUserOrigin = useUserOrigin;  }
    
    return RealDwgSuccess;
    }

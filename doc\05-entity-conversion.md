# Entity Conversion

## Overview

The RealDwgFileIO framework provides comprehensive entity conversion between DWG/DXF and DGN formats. Each entity type has specialized conversion logic implemented through extension classes that handle the specific requirements and nuances of converting between the different geometric representations.

## Entity Conversion Architecture

### Extension-Based Conversion

The framework uses an extension pattern where each entity type has dedicated conversion methods:

```
DWG Entity Types          DGN Element Types
     ↓                           ↑
ToDgnExtension  ←→  ToDwgExtension
     ↓                           ↑
DGN Elements             DWG Entities
```

### Supported Entity Types

#### Basic Geometric Entities
- **Line** (`rdLine.cpp` ↔ `dgnLinear.cpp`)
- **Point** (`rdPoint.cpp` ↔ `dgnLinear.cpp`)
- **Circle** (`rdCircle.cpp` ↔ `dgnArcs.cpp`)
- **Arc** (`rdArc.cpp` ↔ `dgnArcs.cpp`)
- **Ellipse** (`rdEllipse.cpp` ↔ `dgnArcs.cpp`)

#### Complex Geometric Entities
- **Polyline** (`rdPolyline.cpp` ↔ `dgnLinear.cpp`)
- **2D Polyline** (`rd2dPolyline.cpp`)
- **3D Polyline** (`rd3dPolyline.cpp`)
- **Spline** (`rdSpline.cpp` ↔ `dgnLinear.cpp`)

#### Surface and Solid Entities
- **Solid** (`rdSolid.cpp` ↔ `dgnSolids.cpp`)
- **Face** (`rdFace.cpp`)
- **Trace** (`rdTrace.cpp`)
- **Mesh** (`rdPolyFaceMesh.cpp`, `rdPolygonMesh.cpp` ↔ `dgnMesh.cpp`)
- **SubD Mesh** (`rdSubDMesh.cpp`)
- **Surface** (`rdSurface.cpp`)

#### Text and Annotation
- **Text** (`rdText.cpp` ↔ `dgnTexts.cpp`)
- **MText** (`rdMText.cpp` ↔ `dgnTexts.cpp`)
- **Dimension** (`rdDimension.cpp` ↔ `dgnDimension.cpp`)
- **Leader** (`rdLeader.cpp`)
- **Multi-Leader** (`rdMultiLeader.cpp`)

#### Complex Objects
- **Block Reference** (`rdBlockReference.cpp` ↔ `dgnCells.cpp`)
- **Hatch** (`rdHatch.cpp`)
- **Image** (`rdImage.cpp` ↔ `dgnImage.cpp`)
- **Multiline** (`rdMline.cpp` ↔ `dgnMline.cpp`)

## DWG to DGN Conversion

### Line Conversion Example

**File**: `rdLine.cpp`

```cpp
class LineExtension : public ToDgnExtension
{
public:
    StatusInt ConvertToDgn(
        AcDbEntity* pEntity,
        DgnElementP& element,
        ConvertToDgnContext* context,
        DgnModelP model) override
    {
        AcDbLine* pLine = AcDbLine::cast(pEntity);
        if (!pLine)
            return BSIERROR;

        // Get line geometry
        AcGePoint3d startPoint, endPoint;
        pLine->getStartPoint(startPoint);
        pLine->getEndPoint(endPoint);

        // Convert to DGN coordinates
        DPoint3d dgnStart = ConvertPoint(startPoint);
        DPoint3d dgnEnd = ConvertPoint(endPoint);

        // Create DGN line element
        LineStringElement lineElement;
        DPoint3d points[2] = { dgnStart, dgnEnd };
        lineElement.SetVertices(points, 2);

        // Map symbology
        MapSymbology(pLine, &lineElement, context);

        // Create element
        element = lineElement.CreateElement(model);
        return SUCCESS;
    }
};
```

### Circle Conversion Example

**File**: `rdCircle.cpp`

```cpp
class CircleExtension : public ToDgnExtension
{
public:
    StatusInt ConvertToDgn(
        AcDbEntity* pEntity,
        DgnElementP& element,
        ConvertToDgnContext* context,
        DgnModelP model) override
    {
        AcDbCircle* pCircle = AcDbCircle::cast(pEntity);
        if (!pCircle)
            return BSIERROR;

        // Get circle properties
        AcGePoint3d center = pCircle->center();
        double radius = pCircle->radius();
        AcGeVector3d normal = pCircle->normal();

        // Convert to DGN
        DPoint3d dgnCenter = ConvertPoint(center);
        DVec3d dgnNormal = ConvertVector(normal);

        // Create DGN ellipse (circle is ellipse with equal axes)
        EllipseElement ellipseElement;
        ellipseElement.SetCenter(dgnCenter);
        ellipseElement.SetPrimaryAxis(radius);
        ellipseElement.SetSecondaryAxis(radius);
        ellipseElement.SetNormal(dgnNormal);

        // Map symbology
        MapSymbology(pCircle, &ellipseElement, context);

        element = ellipseElement.CreateElement(model);
        return SUCCESS;
    }
};
```

### Block Reference Conversion

**File**: `rdBlockReference.cpp`

Block references (AutoCAD) convert to cell instances (DGN):

```cpp
StatusInt ConvertBlockReference(
    AcDbBlockReference* pBlockRef,
    DgnElementP& element,
    ConvertToDgnContext* context,
    DgnModelP model)
{
    // Get block definition
    AcDbObjectId blockId = pBlockRef->blockTableRecord();
    AcDbBlockTableRecord* pBlockRecord;
    blockId.safeOpenObject(pBlockRecord, AcDb::kForRead);

    // Get or create corresponding DGN cell
    DgnCellHeaderP cellHeader = GetOrCreateCell(pBlockRecord, context);

    // Get transformation
    AcGeMatrix3d transform = pBlockRef->blockTransform();
    Transform dgnTransform = ConvertMatrix(transform);

    // Create cell instance
    CellInstanceElement cellInstance;
    cellInstance.SetCellHeader(cellHeader);
    cellInstance.SetTransform(dgnTransform);

    // Handle attributes if present
    ProcessAttributes(pBlockRef, &cellInstance, context);

    element = cellInstance.CreateElement(model);
    return SUCCESS;
}
```

## DGN to DWG Conversion

### Line Element Conversion

**File**: `dgnLinear.cpp`

```cpp
class LineElementExtension : public ToDwgExtension
{
public:
    StatusInt ConvertToDwg(
        DgnElementP element,
        AcDbEntity*& pEntity,
        ConvertFromDgnContext* context) override
    {
        LineStringElementP lineElement = 
            dynamic_cast<LineStringElementP>(element);
        if (!lineElement)
            return BSIERROR;

        // Get line vertices
        bvector<DPoint3d> vertices;
        lineElement->GetVertices(vertices);

        if (vertices.size() < 2)
            return BSIERROR;

        if (vertices.size() == 2) {
            // Simple line
            AcDbLine* pLine = new AcDbLine();
            pLine->setStartPoint(ConvertPoint(vertices[0]));
            pLine->setEndPoint(ConvertPoint(vertices[1]));

            // Map symbology
            MapSymbology(lineElement, pLine, context);

            pEntity = pLine;
        } else {
            // Polyline
            AcDbPolyline* pPolyline = new AcDbPolyline();
            for (size_t i = 0; i < vertices.size(); ++i) {
                pPolyline->addVertexAt(i, 
                    AcGePoint2d(vertices[i].x, vertices[i].y));
            }

            // Map symbology
            MapSymbology(lineElement, pPolyline, context);

            pEntity = pPolyline;
        }

        return SUCCESS;
    }
};
```

### Ellipse Element Conversion

**File**: `dgnArcs.cpp`

```cpp
StatusInt ConvertEllipseElement(
    EllipseElementP ellipseElement,
    AcDbEntity*& pEntity,
    ConvertFromDgnContext* context)
{
    // Get ellipse properties
    DPoint3d center = ellipseElement->GetCenter();
    double primaryAxis = ellipseElement->GetPrimaryAxis();
    double secondaryAxis = ellipseElement->GetSecondaryAxis();
    DVec3d normal = ellipseElement->GetNormal();

    if (fabs(primaryAxis - secondaryAxis) < 1e-6) {
        // Circle
        AcDbCircle* pCircle = new AcDbCircle();
        pCircle->setCenter(ConvertPoint(center));
        pCircle->setRadius(primaryAxis);
        pCircle->setNormal(ConvertVector(normal));

        pEntity = pCircle;
    } else {
        // Ellipse
        AcDbEllipse* pEllipse = new AcDbEllipse();
        pEllipse->setCenter(ConvertPoint(center));
        
        // Calculate major and minor axes
        AcGeVector3d majorAxis, minorAxis;
        if (primaryAxis > secondaryAxis) {
            majorAxis = AcGeVector3d(primaryAxis, 0, 0);
            minorAxis = AcGeVector3d(0, secondaryAxis, 0);
        } else {
            majorAxis = AcGeVector3d(0, primaryAxis, 0);
            minorAxis = AcGeVector3d(secondaryAxis, 0, 0);
        }

        pEllipse->set(ConvertPoint(center), ConvertVector(normal),
                     majorAxis, secondaryAxis / primaryAxis);

        pEntity = pEllipse;
    }

    return SUCCESS;
}
```

## Symbology Mapping

### Color Conversion

```cpp
StatusInt MapSymbology(
    AcDbEntity* pDwgEntity,
    DgnElementP dgnElement,
    ConvertToDgnContext* context)
{
    // Map color
    AcCmColor dwgColor = pDwgEntity->color();
    UInt32 dgnColorIndex;
    
    if (dwgColor.isByACI()) {
        // AutoCAD Color Index
        dgnColorIndex = context->GetDgnSymbologyData()->
            GetColorIndex(dwgColor.colorIndex(), context);
    } else if (dwgColor.isByRGB()) {
        // RGB color
        RgbColorDef rgbColor;
        rgbColor.red = dwgColor.red();
        rgbColor.green = dwgColor.green();
        rgbColor.blue = dwgColor.blue();
        
        dgnColorIndex = context->GetDgnSymbologyData()->
            GetColorIndex(&rgbColor, context);
    }

    dgnElement->SetColorIndex(dgnColorIndex);

    // Map line style
    AcDbObjectId linetypeId = pDwgEntity->linetypeId();
    UInt32 dgnLineStyleId = MapLineStyle(linetypeId, context);
    dgnElement->SetLineStyleId(dgnLineStyleId);

    // Map line weight
    AcDb::LineWeight dwgWeight = pDwgEntity->lineWeight();
    UInt32 dgnWeight = ConvertLineWeight(dwgWeight);
    dgnElement->SetWeight(dgnWeight);

    return SUCCESS;
}
```

### Line Style Mapping

```cpp
UInt32 MapLineStyle(
    AcDbObjectId linetypeId,
    ConvertToDgnContext* context)
{
    // Check cache first
    UInt32 cachedStyle = context->GetCachedLineStyle(linetypeId);
    if (cachedStyle != INVALID_LINESTYLE_ID)
        return cachedStyle;

    // Get AutoCAD linetype
    AcDbLinetypeTableRecord* pLinetype;
    linetypeId.safeOpenObject(pLinetype, AcDb::kForRead);

    // Convert to DGN line style
    LineStyleParams params;
    ConvertLinetypeToParams(pLinetype, params);

    // Create or find matching DGN line style
    UInt32 dgnLineStyleId = context->GetDgnSymbologyData()->
        FindOrCreateLineStyle(params);

    // Cache the mapping
    context->CacheLineStyleMapping(linetypeId, dgnLineStyleId);

    pLinetype->close();
    return dgnLineStyleId;
}
```

## Complex Entity Handling

### Hatch Conversion

**File**: `rdHatch.cpp`

Hatch entities require special handling due to their complexity:

```cpp
StatusInt ConvertHatch(
    AcDbHatch* pHatch,
    DgnElementP& element,
    ConvertToDgnContext* context,
    DgnModelP model)
{
    // Get hatch boundaries
    int numLoops = pHatch->numLoops();
    bvector<DPoint3d> boundaryPoints;

    for (int i = 0; i < numLoops; ++i) {
        // Process each boundary loop
        ProcessHatchLoop(pHatch, i, boundaryPoints, context);
    }

    // Get hatch pattern
    HatchPatternType patternType = pHatch->patternType();
    
    if (patternType == kPreDefined) {
        // Convert to DGN pattern
        PatternParams params;
        ConvertHatchPattern(pHatch, params);

        // Create DGN shape with pattern
        ShapeElement shapeElement;
        shapeElement.SetVertices(boundaryPoints);
        shapeElement.SetPattern(params);

        element = shapeElement.CreateElement(model);
    } else {
        // Solid fill - create filled shape
        ShapeElement shapeElement;
        shapeElement.SetVertices(boundaryPoints);
        shapeElement.SetFilled(true);

        element = shapeElement.CreateElement(model);
    }

    return SUCCESS;
}
```

### Text Conversion

**File**: `rdText.cpp` and `rdMText.cpp`

Text entities have different handling for single-line and multi-line text:

```cpp
StatusInt ConvertText(
    AcDbText* pText,
    DgnElementP& element,
    ConvertToDgnContext* context,
    DgnModelP model)
{
    // Get text properties
    WString textString = pText->textString();
    AcGePoint3d position = pText->position();
    double height = pText->height();
    double rotation = pText->rotation();

    // Create DGN text element
    TextElement textElement;
    textElement.SetText(textString.c_str());
    textElement.SetOrigin(ConvertPoint(position));
    textElement.SetHeight(height);
    textElement.SetRotation(rotation);

    // Map text style
    AcDbObjectId textStyleId = pText->textStyle();
    UInt32 dgnTextStyleId = MapTextStyle(textStyleId, context);
    textElement.SetTextStyleId(dgnTextStyleId);

    element = textElement.CreateElement(model);
    return SUCCESS;
}
```

## Error Handling and Fallbacks

### Unsupported Entities

```cpp
StatusInt HandleUnsupportedEntity(
    AcDbEntity* pEntity,
    DgnElementP& element,
    ConvertToDgnContext* context,
    DgnModelP model)
{
    // Log warning
    context->LogWarning(L"Unsupported entity type: %s", 
                       pEntity->isA()->name());

    // Create proxy element or skip
    if (context->GetSettings().CreateProxyElements()) {
        // Create a proxy element with entity information
        element = CreateProxyElement(pEntity, model);
        return SUCCESS;
    }

    return BSIERROR;  // Skip entity
}
```

### Geometry Validation

```cpp
StatusInt ValidateGeometry(
    const GeometryData& geometry,
    ConvertToDgnContext* context)
{
    // Check for degenerate geometry
    if (geometry.IsDegenerate()) {
        context->LogWarning(L"Degenerate geometry detected");
        return BSIERROR;
    }

    // Check coordinate ranges
    if (!geometry.IsWithinValidRange()) {
        context->LogWarning(L"Geometry outside valid coordinate range");
        return BSIERROR;
    }

    return SUCCESS;
}
```

## Performance Optimization

### Batch Processing

- Group similar entity types for batch conversion
- Minimize database queries
- Cache frequently used data

### Memory Management

- Reuse temporary objects
- Pool allocation for geometry data
- Automatic cleanup of conversion artifacts

### Conversion Caching

- Cache identical geometry conversions
- Reuse symbology mappings
- Share common style definitions

This entity conversion system provides comprehensive support for translating between the rich geometric and annotation capabilities of both DWG/DXF and DGN formats while maintaining high fidelity and performance.

/*---------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgDgnExtension.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+----------------------------------------------------------------------*/
// Put Autodesk #include files first (they are included from rDwgInternal.h). This is to avoid a problem we encountered where the vtable of AcDbleader was wrong when their include files were
//   after all of ours. We finally discovered that the "intersectWith" methods declared in Autodesk's dblead.h were not overriding the base class methods (and they do not use the "override" keyword
//   in their include files, so it took us a while to figure it out). It turned out to be because in the base class they had arguments listed as "AcDb::Intersect intType," while in dblead.h they had
//   just declared the argument as  "AcDb::Intersect," with no argument name. But, in dloadbsi.h we had #define intType (*intTypeP) so the base class looked like it took a pointer, while the
//   AcDbLeader derived class didn't. That vtable misalignment caused crashes, etc., on all of the vtable entries that followed in AcDbLeader.h
#include    "rDwgInternal.h"
#include    "rDwgFileIO.h"
#include    <Mstn\RealDwg\rDwgUtil.h>
#include    <Mstn\RealDwg\rDwgDefs.h>
#include    <Mstn\RealDwg\DwgPlatformHost.h>
#include    <Mstn\RealDwg\rDwgConvertEvents.h>

// RTLONG is defined both by parasolid_tokens.h and RealDWG's adscodes.h. 
#undef  RTLONG

#include    <shlwapi.h>
#include    <algorithm>     // sort

#include    <Bentley\Bentley.h>
#include    <Bentley\BeFileListIterator.h>
#include    <Bentley\BeTextFile.h>
#include    <DgnPlatform\Tools\BitMask.h>
#include    <DgnPlatform\Tools\ostime.fdf>
#include    <DgnPlatform\Tools\fileutil.h>
#include    <DgnPlatform\Tools\varichar.h>
#include    <DgnPlatform\DependencyManagerLinkage.h>
#include    <DgnPlatform\DgnAttachment.h>
#include    <DgnPlatform\mdlModelRef.h>
#include    <DgnPlatform\DgnRscFont.h>
#include    <DgnPlatform\GradientSettings.h>
#include    <DgnPlatform\LineStyleManager.h>
#include    <DgnPlatform\NamedGroup.h>
#include    <DgnPlatform\DgnHandlersAPI.h>
#include    <DgnPlatform\Note.h>
#include    <DgnPlatform\AssocGeom.h>
#include    <DgnPlatform\RegionUtil.h>
#include    <DgnPlatform\SchemaElementHandler.h>    // Bentley internal only
#include    <DgnPlatform\ViewportDrawnCellHandler.h>
#include    <DgnPlatform\ElementUtil.h>
#include    <DgnPlatform\LightElementHandlers.h>
#include    <DgnPlatform\HUDMarker.h>
#include    <DgnPlatform\MdlTextStyleInternal.h>
#include    <DgnPlatform\XGraphicsElementCreator.h>
#include    <DgnPlatform\StandardSurfaceHandler.h>
#include    <DgnPlatform\DgnFileIO\dgnitemindex.h>
#include    <DgnPlatform\DgnFileIO\DgnLinkage.h>
#include    <DgnPlatform\dgnole.h>
#include    <Mtg\GpaApi.h>
#include    <Vu\VuApi.h>
#include    <PSolid\parasolid_kernel.h>
#include    <PSolid\PSolidCoreApi.h>
#include    <PSolidAcisInterop\PSolidAcisInterop.h>
#include    <RasterCore\RasterCoreApi.h>
#include    <Raster\RasterExt\rlUtilRaster.fdf>
#include    <RmgrTools\Tools\rmgrstrl.h>
#include    <BeSQLite\BeSQLite.h>
#include    <DgnView\IViewManager.h>
#include    <DgnView\DrawContext.h>

#include    <Mstn\MdlApi\filtertable.fdf>
#include    <Mstn\MdlApi\mselmdsc.fdf>
#include    <Mstn\MdlApi\mselemen.fdf>
#include    <Mstn\MdlApi\msdgnmodelref.fdf>
#include    <MsjInternal\Ustn\mielemen.fdf>
#include    <MsjInternal\Ustn\mileveltable.fdf>
#include    <MsjInternal\Ustn\mifiltertable.fdf>
#include    "../../../PPModules/MultiPointPlacement/PublicApi/MstnPlacementPoint.h"
#include    "../../../PPModules/Interchange/PublicAPI/Interchange/pdfLib/pdfLib.h"
// Note: This source file directly contains only the framework for our RealDwg protocol extension.
//       It #includes the source files for each entity's protocol extension. These are then all
//       compiled together. The advantage of that is that all of the methods are thus inline, so
//       we don't have to maintain a .h file that contains all the method names, etc.

USING_NAMESPACE_BENTLEY_DGNPLATFORM
USING_NAMESPACE_BENTLEY_ECOBJECT

BEGIN_BENTLEY_NAMESPACE

namespace RealDwg {

#include "rdModelIndexItem.cpp"
#include "rdResbuf.cpp"
#include "rdXDataUtil.cpp"
#include "rdExtractionFiler.cpp"
#include "rdRecordingFiler.cpp"
#include "rDwgBaseContext.cpp"
#include "rDwgToDgnContext.cpp"
#include "rDwgFromDgnContext.cpp"
#include "rDwgWorldDraw.cpp"
#include "rdUtil.cpp"


// settings and table elements are not processed by having ToDgnExtension on their AcDbObjects
// and ToDwgExtensions on their element handlers. Instead we use the respective RealDwg and DgnPlatform
// APIs to convert DgnTextStyles to/from AcDbTextStyles, etc. Therefore, we #include them here, before
// the definition of the extensions. These .cpp files often implement methods on ConvertToDgnContext and
// ConvertFromDgnContext.

#include "rdTextStyleConvert.cpp"
#include "rdLineStyleConvert.cpp"
#include "rdDimStyleConvert.cpp"
#include "rdViewConvert.cpp"
#include "rdLayerConvert.cpp"
#include "rdMlineStyleConvert.cpp"
#include "rdUcsConvert.cpp"
#include "rdMaterialConvert.cpp"
#include "rdBrepConvert.cpp"

/*---------------------------------------------------------------------------------**//**
* default ToElementPostProcess does nothing.
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToDgnExtension::ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContextR context) const
    {
    // default version does nothing.
    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* default FromElementDeleteObject erases the object from the database.
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToDgnExtension::FromElementDeleteObject (AcDbObjectP acObject, AcDbObjectIdArray& idsToPurge, ConvertFromDgnContextR context) const
    {
    // default version simply deletes the (opened) acObject.
    if (NULL != acObject)
        return  Acad::eOk == acObject->erase() ? RealDwgSuccess : CantEraseObject;

    return RealDwgSuccess;
    }

/*=================================================================================**//**
* This is the protocol extensions for entities for which we haven't specified a ToDgnExtension.
* @bsiclass                                                     Barry.Bentley   09/08
+===============+===============+===============+===============+===============+======*/
class   ToDgnExtensionDefault : public ToDgnExtension
{
public:
virtual     RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR eeh, ConvertToDgnContextR context) const override
    {
    AcRxClass*  desc = acObject->isA();
    const ACHAR* objectName = desc->name();

    if (desc == AcDbPlaceHolder::desc())
        return NoConversionMethod;

    if (desc == AcDbTableStyle::desc())
        return NoConversionMethod;

    if (desc == AcDbVisualStyle::desc())
        return NoConversionMethod;

    if (0 == wcscmp (objectName, L"AcDbScale"))
        return NoConversionMethod;

    if (0 == wcscmp (objectName, L"AcDbDictionaryVar"))
        return NoConversionMethod;

    if (0 == wcscmp (objectName, L"AcDbFieldList"))
        return NoConversionMethod;

    // if it's an entity, try world drawing it - we might have its object enabler.
    AcDbEntity* pEntity;
    if (NULL != (pEntity = AcDbEntity::cast (acObject)))
        {
        // if an entity is known to be drawn differently per viewport, create an element for ViewportDrawnCellHandler:
        if (RealDwgSuccess == context.DrawViewportDependentEntity(pEntity, eeh))
            return  RealDwgSuccess;
        // all other cases fall back to create default cells
        return context.WorldDrawToElements (pEntity, eeh);
        }

    DIAGNOSTIC_PRINTF ("Ignoring object of type %ls\n", objectName);

    return NoConversionMethod;
    }
};



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/09
+---------------+---------------+---------------+---------------+---------------+------*/
static  bool                AddLineElement
(
ElementAgendaR              lineChain,
DSegment3dR                 lineSeg,
ConvertToDgnContextR        context
)
    {
    BentleyStatus       status;
    EditElementHandle   eeh;
    if (BSISUCCESS != (status = LineHandler::CreateLineElement (eeh, NULL, lineSeg, context.GetThreeD(), *context.GetModel())))
        return false;

    lineChain.Insert (eeh);
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/09
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        CreateProxyBoundingBox
(
EditElementHandleR          eeh,
AcDbProxyEntity*            pProxy,
ConvertToDgnContextR        context
)
    {
    AcDbExtents     extents;
    if (Acad::eOk != pProxy->getGeomExtents(extents))
        return  CantCreateProxyBoundingBox;

    DPoint3d    minPoint, maxPoint;
    RealDwgUtil::DPoint3dFromGePoint3d (minPoint, extents.minPoint());
    RealDwgUtil::DPoint3dFromGePoint3d (maxPoint, extents.maxPoint());

    context.GetTransformToDGN().multiply (&minPoint);
    context.GetTransformToDGN().multiply (&maxPoint);

    ElementAgenda   lineChain;
    DSegment3d      lineSeg;

    // line 1
    lineSeg.point[0] = lineSeg.point[1] = minPoint;
    lineSeg.point[1].x = maxPoint.x;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 2
    lineSeg.point[1] = minPoint;
    lineSeg.point[1].y = maxPoint.y;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 3
    lineSeg.point[1] = minPoint;
    lineSeg.point[1].z = maxPoint.z;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 4
    lineSeg.point[0] = lineSeg.point[1] = maxPoint;
    lineSeg.point[1].x = minPoint.x;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 5
    lineSeg.point[1] = maxPoint;
    lineSeg.point[1].y = minPoint.y;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 6
    lineSeg.point[1] = maxPoint;
    lineSeg.point[1].z = minPoint.z;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 7
    lineSeg.point[0] = lineSeg.point[1] = minPoint;
    lineSeg.point[0].y = lineSeg.point[1].y = maxPoint.y;
    lineSeg.point[1].x = maxPoint.x;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 8
    lineSeg.point[1].x = minPoint.x;
    lineSeg.point[1].y = maxPoint.y;
    lineSeg.point[1].z = maxPoint.z;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 9
    lineSeg.point[0] = lineSeg.point[1] = minPoint;
    lineSeg.point[0].x = lineSeg.point[1].x = maxPoint.x;
    lineSeg.point[1].y = maxPoint.y;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 10
    lineSeg.point[1].x = maxPoint.x;
    lineSeg.point[1].y = minPoint.y;
    lineSeg.point[1].z = maxPoint.z;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 11
    lineSeg.point[0] = lineSeg.point[1] = minPoint;
    lineSeg.point[0].z = lineSeg.point[1].z = maxPoint.z;
    lineSeg.point[1].y = maxPoint.y;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // line 12
    lineSeg.point[1].x = maxPoint.x;
    lineSeg.point[1].y = minPoint.y;
    lineSeg.point[1].z = maxPoint.z;
    if (!AddLineElement (lineChain, lineSeg, context))
        return  CantCreateProxyBoundingBox;

    // wrap the bounding box in proxy cell and lock it:
    RealDwgStatus status;
    if (RealDwgSuccess != (status = context.CreateProxyCell (eeh, lineChain, pProxy)))
        return  status;

    eeh.GetElementP()->hdr.ehdr.locked = true;
    return  RealDwgSuccess;
    }

/*=================================================================================**//**
* This is the protocol extensions for entities that were converted to proxies.
* @bsiclass                                                     Barry.Bentley   09/08
+===============+===============+===============+===============+===============+======*/
class   ToDgnExtProxyEntity : public ToDgnExtensionDefault
{
public:
virtual     RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContext& context) const override
    {
    AcDbProxyEntity*    pProxy = AcDbProxyEntity::cast (acObject);

    // if it is an RTEXT, its raw data should have been collected via our substitute RTEXT OE
    if (pProxy != nullptr)
        {
        const ACHAR* name = pProxy->originalClassName();
        if (name != nullptr && wcsncmp(name, L"RText", 5) == 0 && context.CreateElementFromRText(outElement, pProxy) == RealDwgSuccess)
            return  RealDwgSuccess;
        }

    switch (context.GetSettings().GetProxyShowMode())
        {
        case ProxyObject_Suppressed:     // do not show proxy
            context.AddMissingObjectEnabler (acObject, false);
            return  RealDwgSuccess;

        case ProxyObject_BoundingBox:     // show bounding box
            context.AddMissingObjectEnabler (acObject, false);
            return  CreateProxyBoundingBox (outElement, pProxy, context);

        default:    // show proxy image
            break;
        }

    RealDwgStatus status = context.WorldDrawToElements (pProxy, outElement);
    context.AddMissingObjectEnabler (acObject, RealDwgSuccess == status);

    return  status;
    }
};

/*=================================================================================**//**
* This is the protocol extensions for objects that were converted to proxies.
* @bsiclass                                                     Don.Fu          05/09
+===============+===============+===============+===============+===============+======*/
class    ToDgnExtProxyObject : public ToDgnExtensionDefault
{
public:
virtual     RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContext& context) const override
    {
    context.AddMissingObjectEnabler (acObject, false);
    return NoConversionMethod;
    }
};


// these files have the "ToDgnExtension" methods for DWG objects.
#include "rdLine.cpp"
#include "rdPoint.cpp"
#include "rdPolyline.cpp"
#include "rd2dPolyline.cpp"
#include "rd3dPolyline.cpp"
#include "rdMText.cpp"
#include "rdRText.cpp"
#include "rdAttribute.cpp"
#include "rdBlock.cpp"
#include "rdBlockReference.cpp"
#include "rdImage.cpp"
#include "rdSolid.cpp"
#include "rdAcisData.cpp"
#include "rdMline.cpp"
#include "rdText.cpp"
#include "rdDimension.cpp"
#include "rdLeader.cpp"
#include "rdHatch.cpp"
#include "rdCircle.cpp"
#include "rdArc.cpp"
#include "rdEllipse.cpp"
#include "rdShape.cpp"
#include "rdFace.cpp"
#include "rdTrace.cpp"
#include "rdPolyFaceMesh.cpp"
#include "rdPolygonMesh.cpp"
#include "rdSubDMesh.cpp"
#include "rdSpline.cpp"
#include "rdViewport.cpp"
#include "rdWipeout.cpp"
#include "rdDictionary.cpp"
#include "rdXRecord.cpp"
#include "rdGroup.cpp"
#include "rdRegApp.cpp"
#include "rdOle2Frame.cpp"
#include "rdSurface.cpp"
#include "rdLight.cpp"
#include "rdField.cpp"
#include "rdMultiLeader.cpp"
#include "rdUnderlay.cpp"
#include "rdSection.cpp"
#include "rdTable.cpp"
#include "rdFcf.cpp"
#include "rdPointCloud.cpp"

/*---------------------------------------------------------------------------------**//**
* This method is after we have #included all the source files that define the protocol
*      extensions for individual entities so that we can access those classes without
*      requiring a .h file.
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToDgnExtension::Initialize ()
    {
    acrxRegisterService (L"DgnExtension");
    ToDgnExtension::rxInit();
    acrxBuildClassHierarchy();

    // must load these image dll's before adding raster and wipeout extensions:
    static ACHAR    moduleName[100];
    ::wsprintf (moduleName, L"acISMobj%d.dbx", ACADV_RELMAJOR);
    acrxLoadModule (moduleName, 0);
    // AcDbLight depends on this module
    acrxLoadModule (L"AcSceneOE.dbx", 0);
    // AcDbPointCloudEx depends on this module
    acrxLoadModule (L"AcDbPointCloudObj.dbx", 0);
    acrxLoadModule(L"AcGeomentObj.dbx", 0);
    // demand loading problems arises from release to release
#if RealDwgVersion == 2009
    acrxLoadModule (L"AcWipeoutObj17.dbx", 0);
#elif (RealDwgVersion >= 2010 && RealDwgVersion <= 2012)
    acrxLoadModule (L"AcWipeoutObj18.dbx", 0);
#elif RealDwgVersion >= 2015 && RealDwgVersion <= 2016
    // Somehow RealDWG2016 does not even attempt to load Inventor OE - TFS421548!
    acrxLoadModule (L"AcIdViewObj.dbx", 0);
#endif
    // explicit load our substitute OE to extract RTEXT data
    acrxLoadModule (L"RText.dbx", 0);

    AcRxClass*  dgnExtClass = ToDgnExtensionDefault::desc();

    AcDbObject::desc()->addX                (dgnExtClass, new ToDgnExtensionDefault());
    AcDbLine::desc()->addX                  (dgnExtClass, new ToDgnExtLine());
    AcDbRay::desc()->addX                   (dgnExtClass, new ToDgnExtRay());
    AcDbXline::desc()->addX                 (dgnExtClass, new ToDgnExtXLine());
    AcDbPoint::desc()->addX                 (dgnExtClass, new ToDgnExtPoint());
    AcDbPolyline::desc()->addX              (dgnExtClass, new ToDgnExtPolyline());
    AcDb2dPolyline::desc()->addX            (dgnExtClass, new ToDgnExt2dPolyline());
    AcDb3dPolyline::desc()->addX            (dgnExtClass, new ToDgnExt3dPolyline());
    AcDbSolid::desc()->addX                 (dgnExtClass, new ToDgnExtSolid());
    AcDb3dSolid::desc()->addX               (dgnExtClass, new ToDgnExt3dSolid());
    AcDbBody::desc()->addX                  (dgnExtClass, new ToDgnExtBody());
    AcDbRegion::desc()->addX                (dgnExtClass, new ToDgnExtRegion());
    AcDbMline::desc()->addX                 (dgnExtClass, new ToDgnExtMline());
    AcDbText::desc()->addX                  (dgnExtClass, new ToDgnExtText());
    AcDbMText::desc()->addX                 (dgnExtClass, new ToDgnExtMText());
    AcDbBlockTableRecord::desc()->addX      (dgnExtClass, new ToDgnExtBlock());
    AcDbBlockReference::desc()->addX        (dgnExtClass, new ToDgnExtBlockReference());
    AcDbAttribute::desc()->addX             (dgnExtClass, new ToDgnExtAttribute());
    AcDbAttributeDefinition::desc()->addX   (dgnExtClass, new ToDgnExtAttributeDefinition());
    AcDbDimension::desc()->addX             (dgnExtClass, new ToDgnExtDimension());
    AcDbLeader::desc()->addX                (dgnExtClass, new ToDgnExtLeader());
    AcDbMLeader::desc()->addX               (dgnExtClass, new ToDgnExtMLeader());
    AcDbHatch::desc()->addX                 (dgnExtClass, new ToDgnExtHatch());
    AcDbCircle::desc()->addX                (dgnExtClass, new ToDgnExtCircle());
    AcDbArc::desc()->addX                   (dgnExtClass, new ToDgnExtArc());
    AcDbEllipse::desc()->addX               (dgnExtClass, new ToDgnExtEllipse());
    AcDbShape::desc()->addX                 (dgnExtClass, new ToDgnExtShape());
    AcDbFace::desc()->addX                  (dgnExtClass, new ToDgnExtFace());
    AcDbTrace::desc()->addX                 (dgnExtClass, new ToDgnExtTrace());
    AcDbPolyFaceMesh::desc()->addX          (dgnExtClass, new ToDgnExtPolyFaceMesh());
    AcDbPolygonMesh::desc()->addX           (dgnExtClass, new ToDgnExtPolygonMesh());
    AcDbSubDMesh::desc()->addX              (dgnExtClass, new ToDgnExtSubDMesh());
    AcDbSpline::desc()->addX                (dgnExtClass, new ToDgnExtSpline());
    AcDbViewport::desc()->addX              (dgnExtClass, new ToDgnExtViewport());
    AcDbWipeout::desc()->addX               (dgnExtClass, new ToDgnExtWipeout());
    AcDbRasterImage::desc()->addX           (dgnExtClass, new ToDgnExtRasterAttachment());
    AcDbProxyEntity::desc()->addX           (dgnExtClass, new ToDgnExtProxyEntity());
    AcDbProxyObject::desc()->addX           (dgnExtClass, new ToDgnExtProxyObject());
    AcDbDictionary::desc()->addX            (dgnExtClass, new ToDgnExtDictionary());
    AcDbXrecord::desc()->addX               (dgnExtClass, new ToDgnExtXrecord());
    AcDbGroup::desc()->addX                 (dgnExtClass, new ToDgnExtGroup());
    AcDbOle2Frame::desc()->addX             (dgnExtClass, new ToDgnExtOle2Frame());
    AcDbSurface::desc()->addX               (dgnExtClass, new ToDgnExtSurface());
    AcDbLight::desc()->addX                 (dgnExtClass, new ToDgnExtLight());
    AcDbDgnReference::desc()->addX          (dgnExtClass, new ToDgnExtDgnUnderlay());
    AcDbPdfReference::desc()->addX          (dgnExtClass, new ToDgnExtPdfUnderlay());
    AcDbDwfReference::desc()->addX          (dgnExtClass, new ToDgnExtDwfUnderlay());
    AcDbTable::desc()->addX                 (dgnExtClass, new ToDgnExtTable());
    AcDbFcf::desc()->addX                   (dgnExtClass, new ToDgnExtFcf());
    AcDbPointCloudEx::desc()->addX          (dgnExtClass, new ToDgnExtPointCloudEx());
 
    return RealDwgSuccess;
    }


//--------------------------------------------------------------------------------------
// From here down are methods for the ToDwgExtension, which is a Dgn Handler extension.
// The default implementation is here.
//--------------+---------------+---------------+---------------+---------------+-------

// these files contain the "ToDwgExtension" methods for DGN elements.
#include "dgnMesh.cpp"
#include "dgnLinear.cpp"
#include "dgnArcs.cpp"
#include "dgnCone.cpp"
#include "dgnSolids.cpp"
#include "dgnCells.cpp"
#include "dgnMline.cpp"
#include "dgnTexts.cpp"
#include "dgnDimension.cpp"
#include "dgnImage.cpp"
#include "dgnExtElement.cpp"
#include "dgnNonGraphics.cpp"

// define the members of the ToDwgExtension
ELEMENTHANDLER_EXTENSION_DEFINE_MEMBERS (ToDwgExtension)

/*=================================================================================**//**
* This is the handler extension for non-graphics elements for which we haven't specified a ToDwgExtension.
* @bsiclass                                                     Barry.Bentley   01/10
+===============+===============+===============+===============+===============+======*/
struct ToDwgExtensionIgnore : public ToDwgExtension
{
public:

virtual     RealDwgStatus   ToObject (ElementHandleR eh, AcDbObjectP& acObject, AcDbObjectP existingObject, ConvertFromDgnContext& context) const override
    {
    // there exist some type-107 & 66 handlers that do not extend the default nongraphic handlers as they should!!
    switch (eh.GetElementType())
        {
        case EXTENDED_NONGRAPHIC_ELM:
            {
            ToDwgExtExtendedNonGraphics     type107;
            return  type107.ToObject (eh, acObject, existingObject, context);
            }
        case MICROSTATION_ELM:
            {
            ToDwgExtType66      type66;
            return  type66.ToObject (eh, acObject, existingObject, context);
            }
        }

#if defined (REALDWG_DIAGNOSTICS)
    HandlerR    handler = eh.GetHandler();

    WString typeName;
    eh.GetHandler().GetTypeName (typeName, 100);
    printf ("Unimplemented nongraphic handler type \"%ls\" for ElementType=%d & ID=%I64d...\n", typeName.c_str(), eh.GetElementType(), eh.GetElementId());
#endif

    return NoConversionMethod;
    }
};

/*=================================================================================**//**
* This is the handler extension for elements for which we haven't specified a ToDwgExtension.
* @bsiclass                                                     Barry.Bentley   01/10
+===============+===============+===============+===============+===============+======*/
struct ToDwgExtensionDefault : public ToDwgExtension
{
public:

virtual     RealDwgStatus   ToObject (ElementHandleR eh, AcDbObjectP& acObject, AcDbObjectP existingObject, ConvertFromDgnContext& context) const override
    {
#if defined (REALDWG_DIAGNOSTICS)
    HandlerR    handler = eh.GetHandler();

    WString typeName;
    handler.GetTypeName (typeName, 100);
    printf ("Ignoring object of type %ls, ElementType=%d, Id=%I64d\n", typeName.c_str(), eh.GetElementType(), eh.GetElementId());
#endif

    return NoConversionMethod;
    }
};

/*---------------------------------------------------------------------------------**//**
* default FromElementPostProcess does nothing.
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToDwgExtension::ToObjectPostProcess (ElementHandleR inElement, AcDbObjectP acObject, ConvertFromDgnContextR context) const
    {
    // default version does nothing.
    return RealDwgSuccess;
    }



/*---------------------------------------------------------------------------------**//**
* This method is after we have #included all the source files that define the ToDwg
*      extensions for element handlers so we can access those classes without
*      requiring a .h file.
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToDwgExtension::Initialize ()
    {
    // put the default on Handler, so it is called for everything we haven't registered one for.
    ToDwgExtension::RegisterExtension (Handler::GetInstance(), *new ToDwgExtensionIgnore());
    ToDwgExtension::RegisterExtension (DisplayHandler::GetInstance(), *new ToDwgExtensionDefault());
    ToDwgExtension::RegisterExtension (LineHandler::GetInstance(), *new ToDwgExtLine());
    ToDwgExtension::RegisterExtension (LineStringHandler::GetInstance(), *new ToDwgExtLineString());
    ToDwgExtension::RegisterExtension (PointStringHandler::GetInstance(), *new ToDwgExtPointString());
    ToDwgExtension::RegisterExtension (ComplexStringHandler::GetInstance(), *new ToDwgExtComplexString());
    ToDwgExtension::RegisterExtension (ComplexShapeHandler::GetInstance(), *new ToDwgExtComplexShape());    
    ToDwgExtension::RegisterExtension (ShapeHandler::GetInstance(), *new ToDwgExtShape());
    ToDwgExtension::RegisterExtension (ConeHandler::GetInstance(), *new ToDwgExtCone());
    ToDwgExtension::RegisterExtension (SurfaceOrSolidHandler::GetInstance(), *new ToDwgExtSurfaceOrSolid());
    ToDwgExtension::RegisterExtension (SolidHandler::GetInstance(), *new ToDwgExtSolid());
    ToDwgExtension::RegisterExtension (SurfaceHandler::GetInstance(), *new ToDwgExtSurface());
    ToDwgExtension::RegisterExtension (BSplineSurfaceHandler::GetInstance(), *new ToDwgExtBSplineSurface());
    ToDwgExtension::RegisterExtension (MultilineHandler::GetInstance(), *new ToDwgExtMultiline());
    ToDwgExtension::RegisterExtension (TextElemHandler::GetInstance(), *new ToDwgExtTextElement());
    ToDwgExtension::RegisterExtension (TextNodeHandler::GetInstance(), *new ToDwgExtTextNode());
    ToDwgExtension::RegisterExtension (TagElementHandler::GetInstance(), *new ToDwgExtTagElement());
    ToDwgExtension::RegisterExtension (SharedCellDefHandler::GetInstance(), *new ToDwgExtSharedCellDefinition());
    ToDwgExtension::RegisterExtension (SharedCellHandler::GetInstance(), *new ToDwgExtSharedCellInstance());
    ToDwgExtension::RegisterExtension (Type2Handler::GetInstance(), *new ToDwgExtType2Cell());
    ToDwgExtension::RegisterExtension (GroupedHoleHandler::GetInstance(), *new ToDwgExtGroupHole());
    ToDwgExtension::RegisterExtension (AssocRegionCellHeaderHandler::GetInstance(), *new ToDwgExtAssocRegion());
    ToDwgExtension::RegisterExtension (NoteCellHeaderHandler::GetInstance(), *new ToDwgExtNoteCell());
    ToDwgExtension::RegisterExtension (OleCellHeaderHandler::GetInstance(), *new ToDwgExtOle2Frame());
    ToDwgExtension::RegisterExtension (BrepCellHeaderHandler::GetInstance(), *new ToDwgExtBrepElement());
    ToDwgExtension::RegisterExtension (StandardSurfaceBaseHandler::GetInstance(), *new ToDwgExtStandardSurfaceBase());
    ToDwgExtension::RegisterExtension (SweptSurfaceHandler::GetInstance(), *new ToDwgExtSweptSurface());
    ToDwgExtension::RegisterExtension (ExtrudedSurfaceHandler::GetInstance(), *new ToDwgExtExtrudedSurface());
    ToDwgExtension::RegisterExtension (RevolvedSurfaceHandler::GetInstance(), *new ToDwgExtRevolvedSurface());
    ToDwgExtension::RegisterExtension (LoftedSurfaceHandler::GetInstance(), *new ToDwgExtLoftedSurface());
    ToDwgExtension::RegisterExtension (PlaneSurfaceHandler::GetInstance(), *new ToDwgExtPlaneSurface());
    ToDwgExtension::RegisterExtension (PointLightHandler::GetInstance(), *new ToDwgExtLight());
    ToDwgExtension::RegisterExtension (DistantLightHandler::GetInstance(), *new ToDwgExtLight());
    ToDwgExtension::RegisterExtension (SpotLightHandler::GetInstance(), *new ToDwgExtLight());
    ToDwgExtension::RegisterExtension (RefAttachHandler::GetInstance(), *new ToDwgExtReferenceAttachment());
    ToDwgExtension::RegisterExtension (DimensionHandler::GetInstance(), *new ToDwgExtDimension());
    ToDwgExtension::RegisterExtension (ArcHandler::GetInstance(), *new ToDwgExtArc());
    ToDwgExtension::RegisterExtension (EllipseHandler::GetInstance(), *new ToDwgExtEllipse());
    ToDwgExtension::RegisterExtension (CurveHandler::GetInstance(), *new ToDwgExtSpline());
    ToDwgExtension::RegisterExtension (BSplineCurveHandler::GetInstance(), *new ToDwgExtSpline());
    ToDwgExtension::RegisterExtension (MeshHeaderHandler::GetInstance(), *new ToDwgExtMeshHeader());
    ToDwgExtension::RegisterExtension (RasterFrameHandler::GetInstance(), *new ToDwgExtRasterAttachment());
    ToDwgExtension::RegisterExtension (Type9Handler::GetInstance(), *new ToDwgExtType9());
    ToDwgExtension::RegisterExtension (Type66Handler::GetInstance(), *new ToDwgExtType66());
    ToDwgExtension::RegisterExtension (DgnStoreHdrHandler::GetInstance(), *new ToDwgExtDgnStore());
    ToDwgExtension::RegisterExtension (SchemaElementHandler::GetInstance(), *new ToDwgExtSchemaElement());
    ToDwgExtension::RegisterExtension (NamedGroupHandler::GetInstance(), *new ToDwgExtNamedGroup());
    ToDwgExtension::RegisterExtension (ExtendedElementHandler::GetInstance(), *new ToDwgExtExtendedElement());
    ToDwgExtension::RegisterExtension (ExtendedNonGraphicsHandler::GetInstance(), *new ToDwgExtExtendedNonGraphics());
    ToDwgExtension::RegisterExtension (SectionClipElementHandler::GetInstance(), *new ToDwgExtSectionClip());
    ToDwgExtension::RegisterExtension (TextTableHandler::GetInstance(), *new ToDwgExtTextTable());
    ToDwgExtension::RegisterExtension (ViewElementHandler::GetInstance(), *new ToDwgExtViewElement());
    ToDwgExtension::RegisterExtension (ACSHandler::GetInstance(), *new ToDwgExtAcsElement());
    ToDwgExtension::RegisterExtension (ParametricCellHandler::GetInstance(), *new ToDwgExtParametricCell());
    ToDwgExtension::RegisterExtension (IModelXGraphicsHandler::GetInstance(), *new ToDwgExtIModelXGraphics());

    return RealDwgSuccess;
    }


}   // end RealDwg namespace

END_BENTLEY_NAMESPACE




/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/transkit/realDwgFileIOMsg.r $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include        <Mstn\MdlApi\rscdefs.r.h>
#include        "rDwgFileIDs.h"

MessageList MSGLISTID_RealDwgFileIOErrors =
{
    {
    {REALDWGMESSAGE_FatalErrorShutDown,                     "A fatal error has occurred. $[_USTN_PRODUCT_NAME] must shut down! RealDWG error message: %ls."},
    {REALDWGMESSAGE_CorruptionDetected,                     "Found a corrupt object %ls with entity handle %x and reported status %ls."},
    }
};

MessageList MSGLISTID_RealDwgMessageCenterBrief =
{
    {
    {REALDWGMESSAGECENTER_InsufficientSeedResolution,       "The resolution setting for the seed file is too low for %ls." },
    {REALDWGMESSAGECENTER_ExcessiveSeedResolution,          "The resolution setting for the seed file is too high for %ls."},
    {REALDWGMESSAGECENTER_EntitiesOffDesignPlaneIgnored,    "An entity (%ls) outside of the $[_USTN_PRODUCT_NAME] Design plane was ignored."},
    {REALDWGMESSAGECENTER_EntitiesOffDesignPlaneDiscarded,  "An entity (%ls) outside of the $[_USTN_PRODUCT_NAME] Design plane was discarded."},
    {REALDWGMESSAGECENTER_EntitiesOffDesignPlaneFixed,      "An entity (%ls) outside of the $[_USTN_PRODUCT_NAME] Design plane was fixed!"},
    {REALDWGMESSAGECENTER_PatternCellTruncated,             "The pattern cell %ls is too complex to store as a DWG Hatch" },
    {REALDWGMESSAGECENTER_ReplacedLinetype,                 "Replaced a line type with BYLAYER due to error: %ls."},
    {REALDWGMESSAGECENTER_LoadingUserDBX,                   "Loading %ls..."},
    {REALDWGMESSAGECENTER_FailueLoadingDBX,                 "Failed loading module %ls!"},
    {REALDWGMESSAGECENTER_UnmatchingCodepage,               "The NLS files are not found to convert text entities from the DWG codepage %ls!"},
    {REALDWGMESSAGECENTER_OldVersionAEC,                    "Incompatible version of AEC objects!"},
    {REALDWGMESSAGECENTER_LinetypeIgnored,                  "Line type \"%ls\" may have incomplete data!"},
    {REALDWGMESSAGECENTER_RasterAttachmentDiscarded,        "Raster Attachment:%ls was not converted"},
    {REALDWGMESSAGECENTER_DwgSettingsNotFound,              "User DWG settings not found!"},
    {REALDWGMESSAGECENTER_NotSupported,                     "%ls is not currently supported!"},
    {REALDWGMESSAGECENTER_DgnLinestyleRscNotFound,          "Unable to find line style %ls from a line style resource file!"},
    {REALDWGMESSAGECENTER_DwgFileNeedsRecovery,             "DWG file needs recovery!"},
    }
};

MessageList MSGLISTID_RealDwgMessageCenterDetaild =
{
    {
    {REALDWGMESSAGECENTER_InsufficientSeedResolution,       "The seed file resolution for is too low to accurately represent the geometry \
in a DWG File: %ls.  The seed file units are ignored and an appropriate resolution is used instead.  This usually indicates a mismatch \
between the seed file units and the units selected for the DWG file.  Increase the seed file resolution to avoid this warning."},
    {REALDWGMESSAGECENTER_ExcessiveSeedResolution,          "The seed file resolution for is too high to fit the geometry in a DWG File.  \
The seed file units are ignored and an appropriate resolution is used instead.  This usually indicates a mismatch between the seed file \
units and the units selected for the DWG file.  Increase the seed file resolution to avoid this warning."},
    {REALDWGMESSAGECENTER_EntitiesOffDesignPlaneIgnored,    "An entity (%ls) outside of the $[_USTN_PRODUCT_NAME] Design plane was ignored.  \
This is typically caused by entities with invalid coordinates.  These entities may cause invalid \"Zoom Extents\" within AutoCAD. - In order \
to resolve this, select \"Discard invalid entities\" from the Advanced section of the Dwg Open Settings dialog, reopen and save the file."},
    {REALDWGMESSAGECENTER_EntitiesOffDesignPlaneDiscarded,  "An entity (%ls) outside of the $[_USTN_PRODUCT_NAME] Design plane was Discarded."},
    {REALDWGMESSAGECENTER_EntitiesOffDesignPlaneFixed,      "An entity (ID=%ls) is outside of the $[_USTN_PRODUCT_NAME] Design plane and the bad coordinate(s) is(are) reset to 0!"},
    {REALDWGMESSAGECENTER_PatternCellTruncated,             "The pattern cell %ls is too complex to store as a DWG Hatch - the pattern \
definition has been truncated and may appear incomplete. It will be necessary to use simpler pattern cells in order to save these to DWG." },
    {REALDWGMESSAGECENTER_ReplacedLinetype,                 "DWG line type with handle value=%ls can not be opened. A common cause to \
this failure is an invalid SHX font.  All elements using this line style are replaced with line style BYLEVEL."},
    {REALDWGMESSAGECENTER_LoadingUserDBX,                   "Attempted to load a DBX module, %ls, which is specified in configuration variable MS_DWG_OBJECTDBX."},
    {REALDWGMESSAGECENTER_FailueLoadingDBX,                 "RealDWG has attempted to load module %ls, but has consequently failed. It is likely that the ObjectDBX module is invalid."},
    {REALDWGMESSAGECENTER_UnmatchingCodepage,               "The NLS files are required to correctly convert text entities for file %ls. \
Text entities have been converted using the default system codepage, but the result may not be correct. Please check the Regional Settings \
in the Windows Control Panel and install missing NLS files if you believe the DWG codepage to be correct."},
    {REALDWGMESSAGECENTER_OldVersionAEC,                    "File %ls was last saved with an earlier version of an AEC product. Saving this DWG file \
will update AEC objects to a newer version, which is incompatible with the version of the software that have originally created them."},
    {REALDWGMESSAGECENTER_LinetypeIgnored,                  "This is a DWG line type that appears to be originally imported from a DGN file \"%ls\".  Definition of the original DGN line style cannot be accessed."},
    {REALDWGMESSAGECENTER_RasterAttachmentDiscarded,        "This raster file format is not supported in DWG work mode"},
    {REALDWGMESSAGECENTER_DwgSettingsNotFound,              "The default DWG settings are applied instead!"},
    {REALDWGMESSAGECENTER_NotSupported,                     "Underlay reference %ls is not loaded!"},
    {REALDWGMESSAGECENTER_DgnLinestyleRscNotFound,          "A DWG linetype that was originated from a DGN line style from the resource file %ls cannot be found or is invalid."},
    {REALDWGMESSAGECENTER_DwgFileNeedsRecovery,             "$[_USTN_PRODUCT_NAME] is unable to open corrupt DWG file, %ls! However, the file may be recovered by a program that has a DWG recovery utility."},
    }
};

MessageList MSGLISTID_RealDwgFileIOMisc =
{
    {
    {REALDWGMESSAGE_FILEPROP_Client,                        "Client"},
    {REALDWGMESSAGE_FILEPROP_Manager,                       "Manager"},
    {REALDWGMESSAGE_STATUS_LoadingDBX,                      "Loading module %ls..."},
    {REALDWGMESSAGE_USING_VERSION,                          "Using RealDWG %d "},
    {REALDWGMESSAGE_PROXY_ClassName,                        "Class Name"},
    {REALDWGMESSAGE_PROXY_DXFName,                          "DXF Name"},
    {REALDWGMESSAGE_PROXY_ApplicationName,                  "Application Name"},
    {REALDWGMESSAGE_PROGRESS_Writing,                       "Writing %ls..."},
    }
};

MessageList MSGLISTID_RealDwgPresetLights =
{
    {
    {REALDWGMESSAGE_PRESETLIGHT_D65White,                   "D65 White"},
    {REALDWGMESSAGE_PRESETLIGHT_Fluorescent,                "Fluorescent"},
    {REALDWGMESSAGE_PRESETLIGHT_CoolWhite,                  "Cool White"},
    {REALDWGMESSAGE_PRESETLIGHT_WhiteFluorescent,           "White Fluorescent"},
    {REALDWGMESSAGE_PRESETLIGHT_DaylightFluorescent,        "Daylight Fluorescent"},
    {REALDWGMESSAGE_PRESETLIGHT_Incandescent,               "Incandescent"},
    {REALDWGMESSAGE_PRESETLIGHT_Xenon,                      "Xenon"},
    {REALDWGMESSAGE_PRESETLIGHT_Halogen,                    "Halogen"},
    {REALDWGMESSAGE_PRESETLIGHT_Quartz,                     "Quartz"},
    {REALDWGMESSAGE_PRESETLIGHT_MetalHalide,                "MetalHalide"},
    {REALDWGMESSAGE_PRESETLIGHT_Mercury,                    "Mercury"},
    {REALDWGMESSAGE_PRESETLIGHT_PhosphorMercury,            "Phosphor Mercury"},
    {REALDWGMESSAGE_PRESETLIGHT_HighPressureSodium,         "HighPressure Sodium"},
    {REALDWGMESSAGE_PRESETLIGHT_LowPressureSodium,          "LowPressure Sodium"},
    {REALDWGMESSAGE_PRESETLIGHT_Custom,                     "Custom"},
    }
};

MessageList MSGLISTID_RealDwgObjectTypes =
{
    {
    {REALDWGMESSAGE_OBJECTTYPE_PdfUnderlay,                 "PDF Underlay"},
    {REALDWGMESSAGE_OBJECTTYPE_DwfUnderlay,                 "DWF Underlay"},
    {REALDWGMESSAGE_OBJECTTYPE_DgnUnderlay,                 "DGN Underlay"},
    }
};

# Core Components

## Overview

The RealDwgFileIO framework consists of several core components that work together to provide comprehensive DWG/DXF and DGN file format support. This document details the key classes and their responsibilities.

## Primary Classes

### 1. RealDwgFileIO

**File**: `rDwgFileIO.h/.cpp`

The main file I/O handler class that inherits from `DgnPlatform::DgnFileIO`.

#### Key Responsibilities:
- File format validation and detection
- File loading and saving coordination
- Model management and indexing
- Integration with DgnPlatform file system

#### Key Methods:
```cpp
class RealDwgFileIO : public DgnPlatform::DgnFileIO
{
public:
    // File operations
    StatusInt LoadFile(DesignFileHeader* pHdr, bool* openedReadonly, 
                      StatusInt* rwStatus, DgnFileOpenMode openMode, 
                      DgnFileLoadContextP context);
    
    StatusInt _WriteChanges(bool doFullSave, double timestamp, 
                           DesignFileHeader* hdr);
    
    // Model operations
    StatusInt GetModelReader(DgnModelReader** ppStream, DgnModelP pCache);
    StatusInt _CreateCacheAndLoadHeader(DgnModelP& cache, ModelId modelIDtoRead);
    StatusInt DeleteModel(ModelId modelID);
    
    // File holder management
    FileHolder* GetDwgFileHolder();
    StatusInt InitializeFileHolderFromSeedFile();
};
```

#### Key Members:
- `m_dwgFileHolder`: Manages AutoCAD database
- `m_format`: File format (DWG/DXF)
- `m_dgnSeedFileName`: Template DGN file path
- `m_dwgSeedFileName`: Template DWG file path

### 2. FileHolder

**File**: `rDwgFileHolder.h/.cpp`

Central class that manages the relationship between AutoCAD database and DGN file.

#### Key Responsibilities:
- AutoCAD database lifecycle management
- Model indexing and organization
- Symbology data coordination
- File I/O operations

#### Key Methods:
```cpp
class FileHolder
{
public:
    // Database operations
    StatusInt LoadAcadDatabase();
    StatusInt LoadAcadDatabaseFromSeedDwgFile(DgnModelP defaultModel);
    StatusInt WriteFile(WCharCP pFileName, DgnFileFormatType format, 
                       int dxfPrecision, bool useVersionFromSettings, 
                       WCharCP pProgressComment);
    
    // Model management
    StatusInt ReadModelIntoCache(DgnModelP model, ModelId modelId, 
                                bool loadDictionary);
    bool AddModel(ModelId modelId, DgnModelType modelType, 
                 WCharCP modelName, int sheetTabOrder = -1);
    
    // Symbology access
    DwgSymbologyData* GetDwgSymbologyData();
    DgnSymbologyData* GetDgnSymbologyData();
};
```

#### Key Members:
- `m_database`: AutoCAD database instance
- `m_dgnFile`: Associated DGN file
- `m_models`: Array of model index items
- `m_dwgSymbologyData`: DWG symbology mapping
- `m_dgnSymbologyData`: DGN symbology mapping

### 3. File Type Classes

#### RealDwgFileType
**File**: `rDwgFileIO.h/.cpp`

Factory class for DWG file format support.

```cpp
struct RealDwgFileType : public DgnPlatform::DgnFileType
{
    RealDwgFileType() : DgnFileType(DgnFileFormatType::DWG) {}
    
    DgnFileIOP Factory(DgnFileP pFile) override;
    bool ValidateFile(DgnFileFormatType *pFormat, int *pMajorVersion, 
                     int *pMinorVersion, bool *pDefaultModelIs3D, 
                     IThumbnailPropertyValuePtr*, const WChar *pName) override;
    void GetCapabilities(DgnFileCapabilities *cap) override;
};
```

#### RealDxfFileType
**File**: `rDwgFileIO.h/.cpp`

Factory class for DXF file format support.

```cpp
struct RealDxfFileType : public DgnPlatform::DgnFileType
{
    RealDxfFileType() : DgnFileType(DgnFileFormatType::DXF) {}
    
    DgnFileIOP Factory(DgnFileP pFile) override;
    bool ValidateFile(...) override;
    void GetCapabilities(DgnFileCapabilities *cap) override;
};
```

## Conversion Context Classes

### 1. ConvertToDgnContext

**File**: `rDwgToDgnContext.cpp`

Handles conversion from DWG/DXF to DGN format.

#### Key Responsibilities:
- DWG entity enumeration and conversion
- Symbology mapping from DWG to DGN
- Model structure creation
- Progress reporting

#### Key Methods:
```cpp
class ConvertToDgnContext : public BaseConvertContext
{
public:
    StatusInt ConvertDwgModelToDgn(DgnModelP model, ModelId modelId);
    StatusInt ProcessDwgEntity(AcDbEntity* pEntity, DgnModelP model);
    StatusInt MapDwgSymbologyToDgn(AcDbEntity* pEntity, DgnElementP element);
};
```

### 2. ConvertFromDgnContext

**File**: `rDwgFromDgnContext.cpp`

Handles conversion from DGN to DWG/DXF format.

#### Key Responsibilities:
- DGN element enumeration and conversion
- Symbology mapping from DGN to DWG
- AutoCAD database population
- Style and table management

#### Key Methods:
```cpp
class ConvertFromDgnContext : public BaseConvertContext
{
public:
    StatusInt ConvertDgnModelToDwg(DgnModelP model, ModelId modelId);
    StatusInt ProcessDgnElement(DgnElementP element);
    StatusInt SaveNonCacheChanges(bool fullSave, DgnFileP dgnFile);
    
    // Style conversion methods
    StatusInt SaveDgnTextStylesToDatabase();
    StatusInt SaveDgnLineStylesToDatabase();
    StatusInt SaveDgnDimensionStylesToDatabase();
    StatusInt SaveDgnLayersToDatabase();
};
```

### 3. ConvertFromDgnMultiProcessingContext

**File**: `rDwgFromDgnContext.cpp`

Multi-threaded version of DGN to DWG conversion for improved performance.

## Symbology Management Classes

### 1. BaseSymbologyData

**File**: `rDwgSymbologyData.cpp`

Base class for symbology mapping between formats.

#### Key Responsibilities:
- Color table management
- Line style mapping
- Weight conversion
- Index management

### 2. DwgSymbologyData

Specialized symbology data for DWG format specifics.

### 3. DgnSymbologyData

Specialized symbology data for DGN format specifics.

## Table Index Classes

### 1. SignedTableIndex

**File**: `rDwgTableIndex.cpp`

Provides bidirectional mapping between DGN IDs and AutoCAD handles.

#### Key Methods:
```cpp
class SignedTableIndex
{
public:
    AcDbHandle GetDBHandle(Int32 dgnId);
    Int32 GetDgnId(AcDbHandle dbHandle);
    StatusInt AddEntry(Int32 dgnId, AcDbHandle dbHandle, bool originatedInDwg);
    void RemoveEntry(Int32 dgnId);
};
```

### 2. LayerTableIndex

Specialized table index for layer management.

## Model Index Classes

### RealDwgModelIndexItem

**File**: `rdModelIndexItem.cpp`

Represents a single model in the conversion system.

#### Key Members:
- Model ID and name
- Block table record ID
- Model type (2D/3D)
- Sheet tab order

## Host Integration Classes

### 1. DwgPlatformHost

**File**: `DwgPlatformHost.cpp`

Provides platform-specific host services for the RealDWG library.

#### Key Responsibilities:
- Progress reporting
- Error handling
- Resource management
- Application integration

### 2. DwgMstnHost

**File**: `filehandler/DwgMstnHost.h/.cpp`

MicroStation-specific host implementation.

## Utility Classes

### 1. RealDwgUtil

Provides various utility functions for the framework.

### 2. RealDwgException

Custom exception class for framework-specific errors.

## Component Relationships

```
RealDwgFileIO
    ├─ FileHolder
    │   ├─ AcDbDatabase (AutoCAD)
    │   ├─ DgnFile (Bentley)
    │   ├─ DwgSymbologyData
    │   ├─ DgnSymbologyData
    │   └─ RealDwgModelIndexItem[]
    ├─ ConvertToDgnContext
    │   └─ ToDgnExtension (per entity type)
    ├─ ConvertFromDgnContext
    │   └─ ToDwgExtension (per element type)
    └─ Table Indexes
        ├─ SignedTableIndex
        └─ LayerTableIndex
```

## Initialization and Lifecycle

### 1. Framework Initialization
```cpp
// Called during module load
RealDwgFileIO::Initialize();
```

### 2. File Type Registration
```cpp
// Register file types with DgnPlatform
DgnFileTypeP dwgFileIO_getFileType(int fileType);
```

### 3. File Opening Sequence
1. File validation via ValidateFile()
2. Factory creation via Factory()
3. File loading via LoadFile()
4. Database initialization
5. Model index creation

### 4. Conversion Process
1. Context creation
2. Entity/element enumeration
3. Individual conversions
4. Symbology mapping
5. Database/file writing

This component architecture provides a clean separation of concerns while maintaining tight integration between the various subsystems required for high-fidelity file format conversion.

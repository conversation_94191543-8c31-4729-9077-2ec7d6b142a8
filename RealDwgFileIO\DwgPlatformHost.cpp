/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/DwgPlatformHost.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
// windows includes
#define UNICODE
#include    <windows.h>
#include    <stdarg.h>      // va_list

// DgnPlatform includes
#include    <Dgnplatform\DgnplatformApi.h>
#include    <Dgnplatform\DgnplatformLib.h>
#include    <DgnPlatform\Tcb\tcb.r.h>       // SolorTime
#include    <DgnPlatform\Tools\fileutil.h>
#include    <RmgrTools\Tools\rscdefs.r.h>
#include    <RmgrTools\Tools\RscFileManager.h>

#ifndef BUILD_DWGPLATFORM
#include    <MsjInternal\Ustn\findacad.fdf>
#endif

// RealDWG includes
#include    <dbapserv.h>
#include    <dbsymtb.h>

USING_NAMESPACE_BENTLEY_DGNPLATFORM

#include    <Mstn\RealDwg\DwgPlatformHost.h>
#include    <Mstn\RealDwg\rDwgUtil.h>
#include    <Mstn\RealDwg\rDwgDefs.h>
#include    "rDwgFileIds.h"


BEGIN_BENTLEY_NAMESPACE
namespace RealDwg {

static  WChar               s_lastShxFontName[1024];
static  DwgPlatformHost*    s_dwgPlatformHostInstance;
static  RscFileHandle       s_dwgPlatformRscFileHandle;

DLLLOCAL_ATTRIBUTE RscFileManager::DllRsc*  s_dwgPlatformResources;

void realDwg_initECProvider();

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgPlatformHost::Initialize (DwgPlatformHost& host, bool registerFiletype, bool addFileECProvider)
    {
    s_dwgPlatformHostInstance = &host;
    s_dwgPlatformRscFileHandle = NULLRSC;
    s_dwgPlatformResources = NULL;
    s_lastShxFontName[0] = 0;

    static WChar    s_langDir[MAX_PATH] = {0};
    static WChar    s_moduleName[] = L"RealDwgFileIO.dll";
    if (::GetModuleFileName (::GetModuleHandle(s_moduleName), s_langDir, MAX_PATH) != 0)
        {
        s_dwgPlatformResources = new RscFileManager::DllRsc (BeFileName(s_langDir));
        if (NULL != s_dwgPlatformResources)
            s_dwgPlatformRscFileHandle = s_dwgPlatformResources->GetRscFileHandle ();
        }

    if (registerFiletype)
        dwgFileIO_addFileTypes ();

    if (addFileECProvider)
        realDwg_initECProvider ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
DwgPlatformHost&    DwgPlatformHost::Instance ()
    {
    if (NULL == s_dwgPlatformHostInstance)
        s_dwgPlatformHostInstance = new DwgPlatformHost ();

    return  *s_dwgPlatformHostInstance;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
DwgPlatformHost::DwgPlatformHost ()
    {
    m_realDwgRegistryRootKey = NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
DwgPlatformHost::~DwgPlatformHost ()
    {
    s_dwgPlatformHostInstance = NULL;
    if (NULL != s_dwgPlatformResources)
        delete s_dwgPlatformResources;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
RscFileHandle   DwgPlatformHost::GetRscFileHandle ()
    {
    return  s_dwgPlatformRscFileHandle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgPlatformHost::_FatalError (WCharCP format, ...)
    {
    WString     rscString;

    if (SUCCESS == RmgrResource::LoadWString(rscString, s_dwgPlatformRscFileHandle, MSGLISTID_RealDwgFileIOErrors, REALDWGMESSAGE_FatalErrorShutDown))
        {
        va_list     varArgs;
        WString     errMessage;

        va_start (varArgs, format);
        WString::Sprintf (errMessage, rscString.c_str(), format, varArgs);
        va_end (varArgs);

        OutputMessageAlert  alertOption = ConfigurationManager::IsVariableDefined (L"MS_NO_OPEN_ALERTS") ? OutputMessageAlert::None : OutputMessageAlert::Dialog;

        NotificationManager::OutputMessage (NotifyMessageDetails(OutputMessagePriority::Error, errMessage.c_str(), NULL, 0, alertOption));
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgPlatformHost::_Alert (WCharCP message)
    {
    DIAGNOSTIC_PRINTF ("RealDWG Alert: %ls\n", message);
    NotificationManager::OutputMessage (NotifyMessageDetails(OutputMessagePriority::Warning, message, NULL, 0, OutputMessageAlert::None));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     FindFontFile(WCHAR* fontOut, int nLength, const WCHAR* fontIn, DwgPlatformHost::AcadFileType hint)
    {
    bool        isShx = DwgPlatformHost::ACADFILE_CompiledShapeFile == hint;
    WString     fontName ((WCharCP)fontIn);

    // trim spaces in font name - TFS 174231
    fontName.Trim ();

    size_t      dot= fontName.find_last_of (L'.');
    if (WString::npos != dot)
        {
        if (DwgPlatformHost::ACADFILE_FontFile == hint)
            {
            // This font could be either an shx or a ttf - check for file extension.
            WString ext = fontName.substr (dot + 1, fontName.length() - dot - 1);
            isShx = 0 == ext.compare(L"shx");
            }
        fontName.erase (dot);
        }

    DgnFontPtr fontWanted = isShx ? DgnFontManager::GetShxFont(fontName.c_str(), NULL, false) : DgnFontManager::GetTrueTypeFont(fontName.c_str(), NULL, false);

    // if input font name contains a path that does not exist, remove the path and try it again:
    if (fontWanted.IsNull())
        {
        size_t  backSlash = fontName.find_last_of (L'\\');
        if (WString::npos != backSlash)
            {
            fontName = fontName.substr (backSlash + 1, fontName.length() - backSlash - 1);
            fontWanted = isShx ? DgnFontManager::GetShxFont(fontName.c_str(), NULL, false) : DgnFontManager::GetTrueTypeFont(fontName.c_str(), NULL, false);
            }
        }

    WString     fileName;
    if (fontWanted.IsValid())
        {
        fontWanted->GetFontFileName (fileName);
        }
    else if (isShx && 0 == _wcsicmp(s_lastShxFontName, fontName.c_str()))
        {
        /*-------------------------------------------------------------------------------
        A RealDWG/AutoCAD problem:

        The input name fontIn can be an shx for text or an shx for shape.  There is no
        way we can tell which type of the shx it is looking for.  If we return a wrong
        type shx, the caller would enter into an infinite loop calling findFile until a
        right type of shx is returned.  ACAD does that by popping up a dialog box forcing
        user to manually pick a right file.  We are left with few choice.  AutoDesk offers
        no help on how to tell an shx for text vs one for shape, neither they have a way
        to stop the infinite loop!

        As a workaround, here we track and check for shx font name RealDWG has previously
        called for.  That shx file cannot be found, so we had returned our fallback shx
        file for text.  If now findFile is calling us for the same shx font again, we
        assume the shx font we had returned last time to be of a wrong type, so we now
        return an shx font for shape instead.  Not a reliable check but it seems to have
        stopped RealDWG's infinite loop of calling this method.  Ltypeshp.shx is the only
        shx file for shape delivered with ACAD and RealDWG.
        -------------------------------------------------------------------------------*/
        DgnFontPtr shxFont = DgnFontManager::FindSystemFont (L"ltypeshp.shx", DgnFontFilter::Shx);
        if (shxFont.IsValid())
            shxFont->GetFontFileName (fileName);

        s_lastShxFontName[0] = 0;
        }
    else
        {
        DgnFontR   fallbackFont = isShx ? DgnFontManager::GetDefaultShxFont() : DgnFontManager::GetDefaultTrueTypeFont();
        if (!fallbackFont.IsValid())
            return  false;

        fallbackFont.GetFontFileName (fileName);

        if (isShx)
            RealDwgUtil::TerminatedStringCopy (s_lastShxFontName, fontName.c_str(), _countof (s_lastShxFontName));
        }

    if (NULL != fontOut && nLength > 0)
        RealDwgUtil::TerminatedStringCopy (fontOut, fileName.c_str(), nLength);

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     SearchAcadPaths (WCHAR* fullPathOut, int numChars, const WCHAR* filenameIn)
    {
#ifndef BUILD_DWGPLATFORM
    // search through ACAD path list found in registry.
    WString     searchPaths;
    if (RealDwgSuccess == mdlFile_getAcadPrimarySearchDir(searchPaths))
        {
        size_t      semicolon = searchPaths.find (L';');
        size_t      startFrom = 0, pathSize = semicolon;

        while (WString::npos != semicolon)
            {
            WString     path = searchPaths.substr (startFrom, pathSize);
            if (!path.empty())
                {
                WCHAR   *filePart = NULL, *ext = NULL;

                size_t  result = SearchPath (path.c_str(), filenameIn, ext, (DWORD)numChars, fullPathOut, &filePart);

                if (result > 0 && result < numChars)
                    return  true;
                }

            startFrom += pathSize + 1;
            semicolon = searchPaths.find (L';', startFrom);
            pathSize = semicolon - startFrom;
            }
        }
#endif

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/17
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     SearchReferencePaths (WCHAR* fullPathOut, int numChars, WCharCP filenameIn, AcDbDatabaseP dwg)
    {
    // if it's an absolute path and exists, return it as is:
    if (BeFileName::DoesPathExist(filenameIn))
        {
        wcsncpy_s (fullPathOut, numChars, filenameIn, _TRUNCATE);
        return  true;
        }

    // if it's a relative path and exists, resolve to full path and return it:
    BeFileName  found;
    if (filenameIn[0] == L'.' && dwg != nullptr)
        {
        const ACHAR*  masterFile = nullptr;
        if (Acad::eOk == dwg->getFilename(masterFile) && nullptr != masterFile)
            {
            if (BSISUCCESS == BeFileName::ResolveRelativePath(found, filenameIn, masterFile) && BeFileName::DoesPathExist(found.c_str()))
                {
                wcsncpy_s (fullPathOut, numChars, found.c_str(), _TRUNCATE);
                return  true;
                }
            }
        }

    // search MS_RFDIR
    WString name = BeFileName::GetFileNameAndExtension (filenameIn);
    if (BSISUCCESS == ::util_findFile(nullptr, &found, name.c_str(), L"MS_RFDIR", filenameIn, OPEN_FOR_READ))
        {
        wcsncpy_s (fullPathOut, numChars, found.c_str(), _TRUNCATE);
        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   DwgPlatformHost::_FindFile (WCHAR* fullPathOut, int numChars, const WCHAR* filenameIn, AcDbDatabase* pDb, DwgPlatformHost::AcadFileType hint)
    {
    // mostly copied from Autodesk example. Should probably be changed to use our findFile function.
    WCHAR*   pExtension;
    switch (hint)
        {
        case DwgPlatformHost::ACADFILE_FontFile:
        case DwgPlatformHost::ACADFILE_CompiledShapeFile:
        case DwgPlatformHost::ACADFILE_TrueTypeFontFile:
            {
            pExtension = DwgPlatformHost::ACADFILE_CompiledShapeFile == hint ? L".shx" : L".ttf";
            if (FindFontFile(fullPathOut, numChars, filenameIn, hint))
                return  RealDwgSuccess;
            break;
            }

#ifndef BUILD_DWGPLATFORM
        case DwgPlatformHost::ACADFILE_FontMapFile:
            {
            pExtension = L".fmp";
            BeFileName fmpPath;
            if (RealDwgSuccess != mdlFile_getAcadFontmapFile (fmpPath))
                {
                wcsncpy_s (fullPathOut, numChars, fmpPath, _TRUNCATE);
                return  RealDwgSuccess;
                }
            break;
            }

        case DwgPlatformHost::ACADFILE_PatternFile:
            {
            BeFileName patPath;
            if (0 == wcsicmp (filenameIn, L"acad.pat"))
                {
                pExtension = L".pat";
                if (RealDwgSuccess == mdlFile_getAcadPatFile(patPath))
                    {
                    wcsncpy_s (fullPathOut, numChars, patPath, _TRUNCATE);
                    return  RealDwgSuccess;
                    }
                }
            else
                {
                WChar   outFilePath[1024] = { 0 };
                if (RealDwgSuccess == RealDwgUtil::GetPatternFilePath (outFilePath, _countof (outFilePath), filenameIn))
                    {
                    wcsncpy_s (fullPathOut, numChars, outFilePath, _TRUNCATE);
                    struct __stat64 statBuf;
                    if (0 == _wstat64 (fullPathOut, &statBuf))
                        return RealDwgSuccess;
                    }

                /*-----------------------------------------------------------------------
                We did not find the requested pattern name or pattern file in above attempt.
                If it is a pattern name (ex. AR-SAND), we can try ACAD's or user's pattern
                file, because if the pattern name exists in that file, RealDWG can find and
                use it.  But if the requested is an explicit pattern file (ex. acdb.pat),
                we do not want to simply replace it with acad.pat.
                -----------------------------------------------------------------------*/
                WString     extension;
                BeFileName::ParseName (NULL, NULL, NULL, &extension, filenameIn);
                if (extension.empty() && RealDwgSuccess == mdlFile_getAcadPatFile(patPath))
                    {
                    wcsncpy_s (fullPathOut, numChars, patPath, _TRUNCATE);
                    return RealDwgSuccess;
                    }
                DIAGNOSTIC_PRINTF ("Failed to find pattern file %ls\n", filenameIn);

                return FileNotFound;
                }
            break;
            }
#endif

        case DwgPlatformHost::ACADFILE_ARXApplication:
            pExtension = L".dbx";
            break;

        case DwgPlatformHost::ACADFILE_XRefDrawing:
            pExtension = L".dwg";
            if (SearchReferencePaths(fullPathOut, numChars, filenameIn, pDb))
                return  RealDwgSuccess;
            break;

        case DwgPlatformHost::ACADFILE_EmbeddedImageFile:
        default:
            {
            pExtension = L"";
            if (SearchAcadPaths(fullPathOut, numChars, filenameIn))
                return  RealDwgSuccess;
            break;
            }
        }
    WCHAR*  filePart;
    DWORD   result;
    result = SearchPath(NULL, filenameIn, pExtension, numChars, fullPathOut, &filePart);
    if (result && result < (DWORD)numChars)
        return RealDwgSuccess;
    else
        return FileNotFound;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
WCHAR*          DwgPlatformHost::_GetAlternateFontName ()
    {
    static WCHAR*  s_defaultFontName = NULL;

    if (NULL == s_defaultFontName)
        {
        WString fontFileName;
        DgnFontManager::GetDefaultShxFont().GetFontFileName (fontFileName);
        s_defaultFontName = wcsdup (fontFileName.c_str());
        }
    return s_defaultFontName;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
void            DwgPlatformHost::_ReportMessage (ReportMessageType messageType, ...)
    {
    va_list                 varArgs;
    va_start (varArgs, messageType);

    switch (messageType)
        {
        case REPORTMESSAGE_ReadOnlyForced:
            {
            wchar_t*        fileName = va_arg (varArgs, wchar_t*);
            NotificationManager::OutputMessage (NotifyMessageDetails(OutputMessagePriority::Info, L"File opens read only!", NULL == fileName ? L"??" : fileName, 0, OutputMessageAlert::None));
            break;
            }
        case REPORTMESSAGE_DgnSeedFileNotFound:
            {
            wchar_t*        fileName = va_arg (varArgs, wchar_t*);
            NotificationManager::OutputMessage (NotifyMessageDetails(OutputMessagePriority::Info, L"DGN seed file not found!", NULL == fileName ? L"??" : fileName, 0, OutputMessageAlert::None));
            break;
            }
        case REPORTMESSAGE_BigfontInitFileMissing:
            {
            wchar_t*        fileName = va_arg (varArgs, wchar_t*);
            wchar_t*        fontName = va_arg (varArgs, wchar_t*);

            WString         str2(L"file: ");
            if (NULL != fileName)
                str2 += WString(fileName);

            str2 += WString (L"font: ");
            if (NULL != fontName)
                str2 += WString(fileName);
                
            NotificationManager::OutputMessage (NotifyMessageDetails(OutputMessagePriority::Warning, L"Bigfont init fie not found for exporting a bigfont SHX!", str2.GetWCharCP(), 0, OutputMessageAlert::None));
            break;
            }
        }        

    va_end (varArgs);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
bool            DwgPlatformHost::_GetDwgDefaultDisplayStyle (WStringR displayStyleName, DisplayStylePtr& displayStyle)
    {
    displayStyleName = L"DWG Wireframe";
    return true;
    }

}   // Ends RealDWG namespace

END_BENTLEY_NAMESPACE

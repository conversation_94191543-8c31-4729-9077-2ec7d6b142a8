#--------------------------------------------------------------------------------------
#
#     $Source: mstn/mdlapps/RealDwgFileIO/rdSatToPSProcessing.mke $
#
#  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
#
#--------------------------------------------------------------------------------------

appName = PsdToSatProcessing

%include mdl.mki
%include corelibs.mki

#----------------------------------------------------------------------
#       Create output directories
#----------------------------------------------------------------------
always:
    !~@mkdir $(o)
	~@mkdir $(solidSmart)

baseDir = $(_MakeFilePath)

# As of R24, interop includes parasolid_kernel.h directly.
dirToSearch=$(BuildContext)VendorAPI/Psolid
%include cincapnd.mki

#----------------------------------------------------------------------
#       Compile obj files
#----------------------------------------------------------------------
MultiCompileDepends = $(_MakeFileSpec)
%include MultiCppCompileRule.mki

$(o)rdSatFileConversionMP.obj				: $(baseDir)rdSatFileConversionMP.cpp ${MultiCompileDepends} $(hFilesDependencies)

$(o)rdPsdFileConversionMP.obj				: $(baseDir)rdPsdFileConversionMP.cpp ${MultiCompileDepends} $(hFilesDependencies)

$(o)rdPsdFileProcessing.obj					: $(baseDir)rdPsdFileProcessing.cpp ${MultiCompileDepends} #$(dependencies)

%include MultiCppCompileGo.mki

#--------------------------------------------------------------------------------------
#		Linking
#--------------------------------------------------------------------------------------
EXE_DEST		= $(o)
EXE_TMP_DIR		= $(o)
EXE_NAME        = $(appName)
EXE_LOPT1		= -ENTRY:wmainCRTStartup

EXE_OBJS		 = $(MultiCompileObjectList)

LINKER_LIBRARIES  = \
    $(geomExportLib) \
    $(DgnPlatformExportLib) \
	$(BuildContext)SubParts/Libs/BentleyAllocator.lib \
    $(BuildContext)SubParts/Libs/pskernel.lib \
	$(BuildContext)SubParts/Libs/BeSQLite.lib \
	user32.lib

LINKER_LIBRARIES_DELAYLOADED = \
	$(BuildContext)SubParts/Libs/PSolidAcisInterop.lib \
    $(BuildContextSubPartsLib)$(PSolidCoreLib)         \
        $(BuildContext)SubParts/Libs/Bentley.lib

%include $(sharedMki)linktool.mki

$(BuildContext)Delivery/$(appname).exe:
    ~linkfile $(BuildContext)Delivery/$(appname).exe=$(o)$(appname)$(exeext)

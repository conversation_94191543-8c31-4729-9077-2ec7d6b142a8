/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdPolygonMesh.cpp $
|
|  $Copyright: (c) 2014 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtPolygonMesh : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/02
+---------------+---------------+---------------+---------------+---------------+------*/
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbPolygonMesh*    acMesh = AcDbPolygonMesh::cast (acObject);

    if (acMesh->mSize() <= 0 || acMesh->nSize() <= 0)
        return InvalidPolygonMesh;

    MSBsplineSurface            surface;        // initially zero
    memset (&surface, 0, sizeof(surface));
    switch (acMesh->polyMeshType())
        {
        case AcDb::kQuadSurfaceMesh:
            surface.uParams.order = surface.vParams.order = 3;
            break;

        case AcDb::kCubicSurfaceMesh:
            surface.uParams.order = surface.vParams.order = 4;
            break;

        case AcDb::kBezierSurfaceMesh:
            surface.uParams.order = acMesh->nSize();
            surface.vParams.order = acMesh->mSize();
            break;

        case AcDb::kSimpleMesh:
        default:
            return CreateMeshElementFromPolygonMeshEntity (acMesh, outElement, context);
        }

    /*
    AutoCAD closure doesn't mean periodic.  (At least in the Bezier case) it
    means that along a rule line in the closed direction, interpolate the first
    and last points and connect the gap with a straight line.  For us, closure
    means periodic, so ours renders differently (the right way).

    We also ignore AutoCAD's mesh densities (rule lines), as they're funky.
    */

    // per-element polygon/surface display flags set according to SPLFRAME global
    if (context.GetDatabase()->splframe())
        surface.display.polygonDisplay = 1; // only (!) show ctrl polygon
    else
        surface.display.curveDisplay = 1;   // only show surface

    surface.uParams.closed = acMesh->isNClosed () ? 1 : 0;
    surface.uParams.numPoles = acMesh->nSize();

    surface.vParams.closed = acMesh->isMClosed () ? 1 : 0;
    surface.vParams.numPoles = acMesh->mSize();

    surface.uParams.numRules = surface.vParams.numPoles;
    surface.vParams.numRules = surface.uParams.numPoles;

    if (surface.uParams.numPoles < surface.uParams.order)
        surface.uParams.order = surface.uParams.numPoles;

    if (surface.vParams.numPoles < surface.vParams.order)
        surface.vParams.order = surface.vParams.numPoles;


    if (SUCCESS != bspsurf_allocateSurface (&surface))
        return OutOfMemoryError;

    bspknot_computeKnotVector (surface.uKnots, &surface.uParams, NULL);
    bspknot_computeKnotVector (surface.vKnots, &surface.vParams, NULL);

    int                     iPole = 0;
    int                     totalPoles = surface.uParams.numPoles * surface.vParams.numPoles;
    AcDbObjectIterator*     pIterator = acMesh->vertexIterator();
    for (; !pIterator->done(); pIterator->step())
        {
        AcDbObjectId            vertexId = pIterator->objectId ();
        AcDbEntityPointer       pEntity (vertexId, AcDb::kForRead);
        AcDbPolygonMeshVertex*  pVertex = AcDbPolygonMeshVertex::cast (pEntity.object());
        if (AcDb::k3dFitVertex != pVertex->vertexType())
            RealDwgUtil::DPoint3dFromGePoint3d (surface.poles[iPole++], pVertex->position());
        }
    delete pIterator;

    RealDwgStatus           status = CantCreateSurface;
    if (iPole == totalPoles)
        {
        context.GetTransformToDGN().Multiply (surface.poles, totalPoles);

        // this used to be bspsurf_createOneSurfaceFromDescrPool, which is deprecated in Vancouver.
        if (BSPLINE_STATUS_Success == BSplineSurfaceHandler::CreateBSplineSurfaceElement(outElement, NULL, surface, *context.GetModel()))
            {
            context.ElementHeaderFromEntity (outElement, acMesh);
            status = RealDwgSuccess;
            }
        }

    bspsurf_freeSurface (&surface);

    return status;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               CreateMeshElementFromPolygonMeshEntity
(
AcDbPolygonMesh*            acMesh,
EditElementHandleR          outElement,
ConvertToDgnContextR        context
) const
    {
    int                     numAcVertsPerRow = acMesh->nSize();   // AutoCAD data is N-major
    int                     numVertexPerRow = numAcVertsPerRow;
    if (acMesh->isNClosed ())
        numVertexPerRow++;      // dgnPolyface will replicate start vertex at end of each row.
    PolyfaceHeaderPtr       dgnPolyface = PolyfaceHeader::CreateQuadGrid (numVertexPerRow);
    BlockedVectorDPoint3dR  pointVector = dgnPolyface->Point ();
    BlockedVectorIntR       indexVector = dgnPolyface->PointIndex ();

    DPoint3d                v0;
    AcDbObjectIterator*     pIterator = acMesh->vertexIterator();

    for (int iVertex=0; !pIterator->done(); pIterator->step(), iVertex++)
        {
        AcDbObjectId            vertexId = pIterator->objectId ();
        AcDbEntityPointer       pEntity (vertexId, AcDb::kForRead);
        if (Acad::eOk != pEntity.openStatus())
            return  CantOpenObject;

        AcDbPolygonMeshVertex*  acVertex = AcDbPolygonMeshVertex::cast (pEntity.object());
        DPoint3d                tmpPoint;
        pointVector.push_back (RealDwgUtil::DPoint3dFromGePoint3d(tmpPoint, acVertex->position()));

        // additional last column if closed
        if (acMesh->isNClosed())
            {
            if (0 == iVertex % numAcVertsPerRow)
                RealDwgUtil::DPoint3dFromGePoint3d (v0, acVertex->position());
            else if (0 == (iVertex+1) % numAcVertsPerRow)
                pointVector.push_back (v0);
            }
        }
    delete pIterator;

    // additional last row if closed
    if (acMesh->isMClosed())
        {
        for (int iVertex=0; iVertex < numVertexPerRow; iVertex++)
            pointVector.push_back (pointVector.at(iVertex));
        }

    dgnPolyface->Transform (context.GetTransformToDGN());

    BentleyStatus   status = MeshHeaderHandler::CreateMeshElement(outElement, NULL, *dgnPolyface, context.GetThreeD(), *context.GetModel());
    if (SUCCESS == status)
        context.ElementHeaderFromEntity (outElement, acMesh);

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }


};  // ToDgnExtPolygonMesh

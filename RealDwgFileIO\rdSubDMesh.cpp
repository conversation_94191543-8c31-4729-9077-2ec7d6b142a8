/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSubDMesh.cpp $
|
|  $Copyright: (c) 2013 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          11/13
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtSubDMesh : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbSubDMesh*           acSubDMesh = AcDbSubDMesh::cast (acObject);
    if (NULL == acSubDMesh)
        return  EntityError;

    Acad::ErrorStatus       es = Acad::eOk;
    bool                    isWatertight = true;
    if (Acad::eOk != (es = acSubDMesh->isWatertight(isWatertight)) || !isWatertight)
        DIAGNOSTIC_PRINTF ("SubDivision Mesh ID=%I64d is NOT watertight?  [%ls]\n", context.ElementIdFromObject(acObject), acadErrorStatusText(es));

    Adesk::Int32            subDivisionLevel = 0;
    if (Acad::eOk != (es = acSubDMesh->subdLevel(subDivisionLevel)))
        {
        subDivisionLevel = 0;
        DIAGNOSTIC_PRINTF ("Failed getting SubDivisionMesh level from ID=%I64d.  [%ls]\n", context.ElementIdFromObject(acObject), acadErrorStatusText(es));
        }

    AcArray<Adesk::Int32>   acFaces;
    AcGePoint3dArray        acVertices;
    if (Acad::eOk != (es = acSubDMesh->getSubDividedFaceArray(acFaces)) || acFaces.length() < 1 || 
        Acad::eOk != (es = acSubDMesh->getSubDividedVertices(acVertices)) || acVertices.length() < 3)
        {
        DIAGNOSTIC_PRINTF ("Ignoring bad SubDivisionMesh ID=%I64d.  [%ls]\n", context.ElementIdFromObject(acObject), acadErrorStatusText(es));
        return  EntityError;
        }

    PolyfaceHeaderPtr       dgnPolyface = PolyfaceHeader::CreateVariableSizeIndexed ();
    if (!dgnPolyface.IsValid())
        return  OutOfMemoryError;

    BlockedVectorDPoint3dR  pointArray = dgnPolyface->Point ();
    BlockedVectorIntR       indexArray = dgnPolyface->PointIndex ();

    // point array
    UInt32                  numVertices = acVertices.length ();
    DPoint3d                point;
    for (UInt32 i = 0; i < numVertices; i++)
        pointArray.push_back (RealDwgUtil::DPoint3dFromGePoint3d(point, acVertices.at(i)));

    // face array
    UInt32                  numEntries = acFaces.length ();
    for (UInt32 i = 0; i < numEntries;)
        {
        // add a face
        bvector<int>        faceVertices;
        UInt32              numFaceVerts = acFaces.at (i++);

        // a face vertex is a 1-based index.
        for (UInt32 j = 0; j < numFaceVerts; j++)
            faceVertices.push_back (acFaces.at(i++) + 1);

        dgnPolyface->AddIndexedFacet (faceVertices);
        }

    // vertex normal array
    AcGeVector3dArray        acNormals;
    if (Acad::eOk == (es = acSubDMesh->getSubDividedNormalArray(acNormals)) && acNormals.length() == numVertices)
        {
        BlockedVectorDVec3dR    normalArray = dgnPolyface->Normal ();
        DVec3d                  normal;
        for (UInt32 i = 0; i < numVertices; i++)
            normalArray.push_back (RealDwgUtil::DVec3dFromGeVector3d(normal, acNormals.at(i)));
        }

    // get color overrides
    // Needs work by Adesk - AcDbSubDMesh::getVertexColorArray seems to always return an empty array!
    AcArray<AcCmEntityColor>    acColors;
    if (Acad::eOk == (es = acSubDMesh->getVertexColorArray(acColors)) && acColors.length() == numVertices)
        {
        BlockedVectorIntR       colorIndexArray = dgnPolyface->ColorIndex ();
        BlockedVectorUInt32R    colorTableArray = dgnPolyface->ColorTable ();

        colorIndexArray.SetTags (1, 1, MESH_ELM_TAG_VERTEX_TO_INT_COLOR_INDICES, MESH_ELM_INDEX_FAMILY_BY_VERTEX, MESH_ELM_INDEX_FAMILY_NONE, true);
        colorTableArray.SetTags (1, 1, MESH_ELM_TAG_TABLE_COLOR, MESH_ELM_INDEX_FAMILY_NONE, MESH_ELM_TAG_VERTEX_TO_INT_COLOR_INDICES, true);

        int                     cachedIndex = 0, colorArrayTail = 0, colorArrayInverse[257];

        // init inverse lookup table
        for (UInt32 i = 0; i < 257; i++)
            colorArrayInverse[i] = -1;

        for (UInt32 i = 0; i < numVertices; i++)
            {
            UInt32              dgnColorIndex = context.GetDgnColor (acColors.at(i));
            if (dgnColorIndex <= 255)
                {
                // add new color index to the colorArray
                cachedIndex = colorArrayInverse[dgnColorIndex];
                if (cachedIndex < 0)
                    {
                    cachedIndex = colorArrayInverse[dgnColorIndex] = colorArrayTail;
                    colorTableArray.push_back ((int)dgnColorIndex); // insert @ colorArrayTail
                    colorArrayTail++;
                    }

                // add 1-based index into the colorArray
                colorIndexArray.push_back (cachedIndex + 1);
                }
            else
                {
                colorTableArray.push_back ((int)dgnColorIndex); // insert @ colorArrayTail
                colorArrayTail++;
                colorIndexArray.push_back (colorArrayTail);
                }
            }
        }

    dgnPolyface->Transform (context.GetTransformToDGN(), false);

    BentleyStatus  status = MeshHeaderHandler::CreateMeshElement (outElement, NULL, *dgnPolyface, context.GetThreeD(), *context.GetModel());

    if (BSISUCCESS == status)
        context.ElementHeaderFromEntity (outElement, acSubDMesh);

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

};  // ToDgnExtSubDMesh


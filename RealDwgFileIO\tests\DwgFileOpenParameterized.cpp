/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/tests/DwgFileOpenParameterized.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include "DwgUnitTestsPCH.h"

USING_NAMESPACE_DGNPLATFORM_DGNHISTORY

BeFileName DwgFileOpenParameterized::m_reportFolder;

BeFileName DwgGlobals::BaseDir ()
    {
    BeFileName basePath = BeFileName (_wgetenv (L"SEEDFULLPATH"));
    if (WString::IsNullOrEmpty (basePath.c_str ()))
        {
        wprintf (L"Failed get data path for SEEDFULLPATH \n");
        return BeFileName ();
        }

    basePath
        .AppendToPath (L"workflow")
        .AppendToPath (L"DWG")
        .AppendToPath (L"Open")
        .AppendToPath (L"DWGOpen");

    EXPECT_TRUE (BeFileName::DoesPathExist (basePath.c_str ()));
    return basePath;
    }

bvector<BeXmlNodeP> const& XmlParamsSingleton::Params ()
    {
    static bvector<BeXmlNodeP> s_xmlItems;
    if (!s_xmlItems.empty ())
        return s_xmlItems;

    BeFileName xmlPath = DwgGlobals::BaseDir ()
        .AppendToPath (L"DwgFiles")
        .AppendExtension (L"xml");

    EXPECT_TRUE (BeFileName::DoesPathExist (xmlPath.c_str ())) << L"xml file doesn't exist: " << xmlPath.c_str ();

    BeXmlStatus xmlStatus;
    static BeXmlDomPtr s_xmlDom = BeXmlDom::CreateAndReadFromFile (xmlStatus, xmlPath);

    static BeXmlDom::IterableNodeSet s_nodes;
    s_xmlDom->SelectNodes (s_nodes, "/DwgFiles/Item", nullptr);

    for (auto& xmlItem : s_nodes)
        s_xmlItems.push_back (xmlItem);
    return s_xmlItems;
    }

BeFileName DwgFileOpenParameterized::FullFileNameFromXml (BeXmlNodeP xmlNode)
    {
    WString stringValue;
    if (nullptr == xmlNode || BEXML_Success != xmlNode->GetAttributeStringValue (stringValue, "DwgFile"))
        {
        EXPECT_TRUE (false) << L"Failed to read DgnFile attribute in xml node \n";
        return BeFileName ();
        }
    return DwgGlobals::BaseDir().AppendToPath (stringValue.c_str ());
    }

BeFileName DwgFileOpenParameterized::DgnFileToSaveSingleProcess (BeXmlNodeP xmlNode)
    {
    WString stringValue;
    if (nullptr == xmlNode || BEXML_Success != xmlNode->GetAttributeStringValue (stringValue, "SaveAsDgn"))
        {
        //It won't be saved to any dgn file
        return BeFileName ();
        }

    stringValue.ReplaceAll (L".dgn", L"");
    stringValue += L"SingleProcess";
    return DwgGlobals::BaseDir ().AppendToPath (stringValue.c_str ()).AppendExtension (L"dgn");
    }

BeFileName DwgFileOpenParameterized::DgnFileToSaveMultiProcess (BeXmlNodeP xmlNode)
    {
    WString stringValue;
    if (nullptr == xmlNode || BEXML_Success != xmlNode->GetAttributeStringValue (stringValue, "SaveAsDgn"))
        {
        //It won't be saved to any dgn file
        return BeFileName ();
        }

    stringValue.ReplaceAll (L".dgn", L"");
    stringValue += L"MultiProcess";
    return DwgGlobals::BaseDir ().AppendToPath (stringValue.c_str ()).AppendExtension (L"dgn");
    }

size_t CountAllElmCollection (DgnFilePtr dgnFile)
    {
    size_t count = 0;
    for each (DgnModelP model in dgnFile->GetLoadedModelsCollection ())
        {
        for each (PersistentElementRefP const& elemRef in model->GetElementsCollection ())
            {
            if (elemRef)
                {
                //to avoid warning C4189: 'elemRef': local variable is initialized but not referenced
                }
            count++;
            }
        }
    return count;
    }

void DwgFileOpenParameterized::Compare (DgnFilePtr dgnSingle, DgnFilePtr dgnMulti, DgnECFileDiff& diffs)
    {
    m_notifyFailure = true;

    ASSERT_TRUE (dgnSingle.IsValid () && dgnMulti.IsValid ());

    ECCompareTypes flags = ECCompareTypes::Models | ECCompareTypes::Elements | ECCompareTypes::Instances;
    ECCompareLevel compType = COMPARELEVEL_Effective;
    DgnECFileComparer comparer (*dgnSingle, *dgnMulti, flags, ECCompareContext (compType, IGNOREFLAG_None, ECCOMPARE_DEFAULT_TOLERANCE, ECCOMPARE_DEFAULT_TOLERANCE, *ECClassCompareCache::Create ()));

    ASSERT_TRUE (comparer.Compare (&diffs));

    //Compare the 2 opened dwg files
    /*for each (DgnModelP modelSingle in dgnSingle->GetLoadedModelsCollection ())
        {
        for each (PersistentElementRefP const& elemSingle in modelSingle->GetElementsCollection ())
            {
            bool found = false;

            for each (DgnModelP modelMulti in dgnMulti->GetLoadedModelsCollection ())
                {
                if (found)
                    break;
                for each (PersistentElementRefP const& elemMulti in modelMulti->GetElementsCollection ())
                    {
                    CompareElements comparison;
                    if (comparison.areEqual (elemSingle, elemMulti))
                        {
                        found = true;
                        break;
                        }
                    }
                }

            ASSERT_TRUE (found) << L"Element in single processing dwg file not found in multiprocessing dwg file";
            }
        }

    ASSERT_EQ (CountAllElmCollection (dgnSingle), CountAllElmCollection (dgnMulti));*/

    m_notifyFailure = false;
    }

static bool printDiff (IECDiff& diff, WCharCP name, WCharCP xmlFileName, WCharCP status)
    {
    BeXmlDomPtr dom = BeXmlDom::CreateEmpty ();
    dom->AddNewElement ("ComparisonResult", NULL, NULL);
    auto& root = *dom->GetRootElement ();
    root.AddAttributeStringValue ("Type", name);
    root.AddAttributeStringValue ("Status", status);
    if (!diff.ToXml (root))
        {
        printf ("\nERROR: Failed to write diffs to XML\n\n");
        return false;
        }

    WString xml;
    dom->ToString (xml, (BeXmlDom::ToStringOption)(BeXmlDom::TO_STRING_OPTION_Formatted | BeXmlDom::TO_STRING_OPTION_OmitByteOrderMark | BeXmlDom::TO_STRING_OPTION_Indent));

    if (!WString::IsNullOrEmpty (xmlFileName))
        {
        BeFileStatus fileStatus;
        BeTextFilePtr file = BeTextFile::Open (fileStatus, xmlFileName, TextFileOpenType::Write, TextFileOptions::None, TextFileEncoding::CurrentLocale);
        if (file.IsValid () && TextFileWriteStatus::Success == file->PutLine (xml.c_str (), false))
            {
            wprintf (L"XML output written to %ls\n", xmlFileName);
            return true;
            }
        else
            {
            wprintf (L"Failed to write XML output to %ls\n", xmlFileName);
            return false;
            }
        }
    else
        {
        wprintf (L"%ls", xml.c_str ());
        return true;
        }
    }

BeFileName DwgFileOpenParameterized::ReportFolder ()
    {
    if (WString::IsNullOrEmpty (m_reportFolder.c_str ()))
        {
        std::time_t t = std::time (0);   // get time now
        std::tm* now = std::localtime (&t);
        WString timeFolder ((std::to_wstring (now->tm_mday) + L"th " + std::to_wstring (now->tm_hour) + L"h " + std::to_wstring (now->tm_min) + L"min " + std::to_wstring (now->tm_sec) + L"sec").c_str ());

        m_reportFolder =
            DwgGlobals::BaseDir ()
            .AppendToPath (timeFolder.c_str ());
        BeFileName::CreateNewDirectory (m_reportFolder.c_str ());
        }
    return m_reportFolder;
    }

void DwgFileOpenParameterized::ListenToFailure (BeFileNameCR dwgFileName, IECDiff& diffs)
    {
    if (m_notifyFailure)
        {
        std::wcout << L"Failure with dwg file: " << dwgFileName.c_str () << std::endl;

        BeFileName reportFile =
            ReportFolder ()
            .AppendToPath ((
                BeFileName::GetFileNameWithoutExtension (dwgFileName.c_str ()) + L"Diff").c_str ())
            .AppendExtension (L"xml");

        printDiff (diffs, L"FileCompare", reportFile.c_str (), L"FAIL");
        }
    }

TEST_P (DwgFileOpenParameterized, OpenAllXml)
    {
    BeXmlNodeP param = GetParam ();

    BeFileName dwgFileName (FullFileNameFromXml (param));

    DwgFileOpenParams paramMulti {
        true,
        DgnFileToSaveMultiProcess (param),
        DgnFileOpenMode::ReadOnly
        };

    DgnFilePtr dgnMulti = DwgFileOpen (paramMulti).OpenDwgFile (dwgFileName);
    ASSERT_TRUE (dgnMulti.IsValid ());

    DwgFileOpenParams paramSingle {
        false,
        DgnFileToSaveSingleProcess (param),
        DgnFileOpenMode::ReadOnly
        };

    DgnFilePtr dgnSingle = DwgFileOpen (paramSingle).OpenDwgFile (dwgFileName);
    ASSERT_TRUE (dgnSingle.IsValid ());

    DgnECFileDiff diffs;
    Compare (dgnSingle, dgnMulti, diffs);

    ListenToFailure (dwgFileName, diffs);
    }

// --gtest_filter=DwgFileOpenP/DwgFileOpenParameterized.OpenAllXml/n


/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdRecordingFiler.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*--------------------------------------------------------------------------------------+
|
| The intent of this collection of classes is to allow us to set members of an ObjectARX
| object while converting from DGN to DWG. It is needed to set members of an object for
| which ObjectARX does not provide methods.
|
| The caller creates an instance of RecordingFiler, which is a subset of AcDbDwgFiler.
| Then RecordingFiler's RecordData method is called, which calls ObjectARX object's dwgOutFields
| method.  The RecordingFiler stores a list of data that it gets by overriding all of the
| WriteXXX methods of AcDbDwgFiler. Each entry in the list is a type-specific subclass of
| FilerData.
|
| After the data is read, the caller filters through it to find the entry that corresponds
| to the data that needs to be changed. It can be difficult to locate the data, because
| the only information available is the sequence of data types (and the values).
|
| After the data bas been modified as required, the RecordingFiler's PlaybackData method
| called. That calls dwgInFields, and the overrides of AcDbDwgFilers' ReadXXX methods
| are used to set all of the data in the element, in the exact order that it was filed out
| in the RecordData step.
|
| Note: Initially, I tried to make this use a subclass of AcDbDxfFiler, because that provides
| far more context information about what data is used for in the AcDbObject, but I
| discovered that DxfInField does not stick to the published interface, and instead static
| casts to some internal class and all kinds of bad things happened (subsequently confirmed
| by Art Cooney at Autodesk).
|
+--------------------------------------------------------------------------------------*/
#if RealDwgVersion <= 2009
typedef long            RDwgSeekType;
typedef Adesk::UInt32   RDwgRWByteSize;
#else
typedef Adesk::Int64    RDwgSeekType;
typedef Adesk::UIntPtr  RDwgRWByteSize;
#endif

/*=================================================================================**//**
* @bsiclass   Abstract base class for our FilerData subclasses
+===============+===============+===============+===============+===============+======*/
struct  FilerData
{
virtual void    Dump (int sequenceNum) = 0;
virtual ~FilerData () {}
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct  ObjectIdFilerData : FilerData
{
bool        m_ok;
ads_name    m_adsName;
ObjectIdFilerData (AcDbObjectId objectId)
    {
    m_adsName[0] = m_adsName[1] = 0;
    m_ok = (Acad::eOk == acdbGetAdsName (m_adsName, objectId));
    }
void                DumpObjectId (int sequenceNum, CharCP typeString)
    {
    const ACHAR *entityName = L"None";
    ACHAR       handle[] = L"BadHandle";
    if (m_ok)
        {
        AcDbObjectId    objectId;
        if (Acad::eOk == acdbGetObjectId (objectId, m_adsName))
            {
            entityName   = objectId.objectClass()->name();
            objectId.handle().getIntoAsciiBuffer(handle);
            }
        }

    if (m_ok)
        printf ("%3d: %hs, %4x %4x %ls, %ls\n", sequenceNum, typeString, m_adsName[0], m_adsName[1], handle, entityName);
    else
        printf ("%3d: %hs, NULL\n", sequenceNum, typeString);
    }

Acad::ErrorStatus   ReadData (AcDbObjectId* objectId)
    {
    BeAssert (nullptr != objectId && L"RecordingFiler error - null object ID!");

    // if we didn't successfully get the data, can't write it.
    if (!m_ok)
        return Acad::eOk;

    Acad::ErrorStatus status = acdbGetObjectId (*objectId, m_adsName);
    if (Acad::eOk != status)
        {
        BeAssert (false && L"RecordingFiler error - object ID appears invalid!");
        }
    return status;
    }

Acad::ErrorStatus   SetValue (AcDbObjectId objectId)
    {
    Acad::ErrorStatus status;
    m_ok = (Acad::eOk == (status = acdbGetAdsName (m_adsName, objectId)));
    return status;
    }

Acad::ErrorStatus   GetValue (AcDbObjectId& objectId)
    {
    if (!m_ok)
        return Acad::eNullObjectId;

    return acdbGetObjectId (objectId, m_adsName);
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct HardOwnershipIdFilerData : ObjectIdFilerData
{
HardOwnershipIdFilerData (AcDbObjectId objectId) : ObjectIdFilerData (objectId) {}
virtual void        Dump (int sequenceNum) override { DumpObjectId (sequenceNum, "kDwgHardOwnershipId"); }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct SoftOwnershipIdFilerData : ObjectIdFilerData
{
SoftOwnershipIdFilerData (AcDbObjectId objectId) : ObjectIdFilerData (objectId) {}
virtual void        Dump (int sequenceNum) override { DumpObjectId (sequenceNum, "kDwgSoftOwnershipId"); }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct HardPointerIdFilerData : ObjectIdFilerData
{
HardPointerIdFilerData (AcDbObjectId objectId) : ObjectIdFilerData (objectId) {}
virtual void        Dump (int sequenceNum) override { DumpObjectId (sequenceNum, "kDwgHardPointerId"); }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct SoftPointerIdFilerData : ObjectIdFilerData
{
SoftPointerIdFilerData (AcDbObjectId objectId) : ObjectIdFilerData (objectId) {}
virtual void        Dump (int sequenceNum) override { DumpObjectId (sequenceNum, "kDwgSoftPointerId"); }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct IntFilerData : FilerData
{
UInt64         m_intData;
IntFilerData (UInt64 data) { m_intData = data; }
void                DumpIntData (int sequenceNum, CharCP typeString) { printf ("%3d: %hs, %4I64d (0x%x)\n", sequenceNum, typeString, m_intData, m_intData); }
void                SetValue (UInt64 data) { m_intData = data; }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct HandleFilerData : IntFilerData
{
HandleFilerData (const AcDbHandle& data) : IntFilerData (RealDwgUtil::CastDBHandle (data)) {}
virtual void        Dump (int sequenceNum) override { DumpIntData (sequenceNum, "kDwgHandle"); }
Acad::ErrorStatus   ReadData (AcDbHandle* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read AcDbHandle!");
        return  Acad::eInvalidInput;
        }
    *output = (AcDbHandle) m_intData;
    return Acad::eOk;
    }
};

#if RealDwgVersion > 2009
/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct Int64FilerData : IntFilerData
{
Int64FilerData (Adesk::Int64 data) : IntFilerData (data) {}
virtual void        Dump (int sequenceNum) override { DumpIntData (sequenceNum, "kDwgInt64"); }
Adesk::Int64        GetValue () { return (Adesk::Int64) m_intData; }
Acad::ErrorStatus   ReadData (Adesk::Int64* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read Adesk::Int64!");
        return  Acad::eInvalidInput;
        }
    *output = (Adesk::Int64) m_intData;
    return Acad::eOk;
    }
};
#endif

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct Int32FilerData : IntFilerData
{
Int32FilerData (Adesk::Int32 data) : IntFilerData (data) {}
virtual void        Dump (int sequenceNum) override { DumpIntData (sequenceNum, "kDwgInt32"); }
Adesk::Int32        GetValue () { return (Adesk::Int32) m_intData; }
Acad::ErrorStatus   ReadData (Adesk::Int32* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read Adesk::Int32!");
        return  Acad::eInvalidInput;
        }
    *output = (Adesk::Int32) m_intData;
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct Int16FilerData : IntFilerData
{
Int16FilerData (Adesk::Int16 data) : IntFilerData (data) {}
virtual void        Dump (int sequenceNum) override { DumpIntData (sequenceNum, "kDwgInt16"); }
Adesk::Int16        GetValue () { return (Adesk::Int16) m_intData; }
Acad::ErrorStatus   ReadData (Adesk::Int16* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read Adesk::Int16!");
        return  Acad::eInvalidInput;
        }
    *output = (Adesk::Int16) m_intData;
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct Int8FilerData : IntFilerData
{
Int8FilerData (Adesk::Int8 data) : IntFilerData (data) {}
virtual void        Dump (int sequenceNum) override { DumpIntData (sequenceNum, "kDwgInt8"); }
Acad::ErrorStatus   ReadData (Adesk::Int8* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read Adesk::Int8!");
        return  Acad::eInvalidInput;
        }
    *output = (Adesk::Int8) m_intData;
    return Acad::eOk;
    }
};

#if RealDwgVersion > 2009
/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct UInt64FilerData : IntFilerData
{
UInt64FilerData (Adesk::UInt64 data) : IntFilerData (data) {}
virtual void        Dump (int sequenceNum) override { DumpIntData (sequenceNum, "kDwgUInt64"); }
Adesk::UInt64       GetValue () { return (Adesk::UInt64) m_intData; }
Acad::ErrorStatus   ReadData (Adesk::UInt64* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read Adesk::UInt64!");
        return  Acad::eInvalidInput;
        }
    *output = (Adesk::UInt64) m_intData;
    return Acad::eOk;
    }
};
#endif

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct UInt32FilerData : IntFilerData
{
UInt32FilerData (Adesk::UInt32 data) : IntFilerData (data) {}
virtual void        Dump (int sequenceNum) override { DumpIntData (sequenceNum, "kDwgUInt32"); }
Adesk::UInt32       GetValue () { return (Adesk::UInt32) m_intData; }
Acad::ErrorStatus   ReadData (Adesk::UInt32* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read Adesk::UInt32!");
        return  Acad::eInvalidInput;
        }
    *output = (Adesk::UInt32) m_intData;
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct UInt16FilerData : IntFilerData
{
UInt16FilerData (Adesk::UInt16 data) : IntFilerData (data) {}
virtual void        Dump (int sequenceNum) override { DumpIntData (sequenceNum, "kDwgUInt16"); }
Adesk::UInt16       GetValue () { return (Adesk::UInt16) m_intData; }
Acad::ErrorStatus   ReadData (Adesk::UInt16* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read Adesk::UInt16!");
        return  Acad::eInvalidInput;
        }
    *output = (Adesk::UInt16) m_intData;
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct UInt8FilerData : IntFilerData
{
UInt8FilerData (Adesk::UInt8 data) : IntFilerData (data) {}
virtual void        Dump (int sequenceNum) override { DumpIntData (sequenceNum, "kDwgUInt8"); }
Adesk::UInt8        GetValue () { return (Adesk::UInt8) m_intData; }
Acad::ErrorStatus   ReadData (Adesk::UInt8* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read Adesk::UInt8!");
        return  Acad::eInvalidInput;
        }
    *output = (Adesk::UInt8) m_intData;
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct BooleanFilerData : FilerData
{
bool    m_boolData;
BooleanFilerData (Adesk::Boolean data) {m_boolData = (0 != data) ? true : false; }
virtual void    Dump (int sequenceNum) override { printf ("%3d: kDwgBoolean %hs\n", sequenceNum, m_boolData ? "true" : "false"); }
void                SetValue (bool newValue) { m_boolData = newValue; }
bool                GetValue () { return m_boolData; }
Acad::ErrorStatus   ReadData (Adesk::Boolean* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read Adesk::Boolean!");
        return  Acad::eInvalidInput;
        }
    *output = m_boolData;
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct BoolFilerData : FilerData
{
bool    m_boolData;
BoolFilerData (bool data) { m_boolData = data; }
virtual void        Dump (int sequenceNum) override { printf ("%3d: kDwgBool %hs\n", sequenceNum, m_boolData ? "true" : "false"); }
void                SetValue (bool newValue) { m_boolData = newValue; }
bool                GetValue () { return m_boolData; }
Acad::ErrorStatus   ReadData (bool* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read a bool!");
        return  Acad::eInvalidInput;
        }
    *output = m_boolData;
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct DoubleFilerData : FilerData
{
double m_doubleData;
DoubleFilerData (double data) {m_doubleData = data; }
virtual void        Dump (int sequenceNum) override { printf ("%3d: kDwgReal %f\n", sequenceNum, m_doubleData); }
void                SetValue (double newValue) { m_doubleData = newValue; }
double              GetValue () { return m_doubleData; }
Acad::ErrorStatus   ReadData (double* output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read a double!");
        return  Acad::eInvalidInput;
        }
    *output = m_doubleData;
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct AddressFilerData : FilerData
{
const void *        m_address;
AddressFilerData (const void *data) {m_address = data; }
virtual void        Dump (int sequenceNum) override { printf ("%3d: Address %p\n", sequenceNum, m_address); }
void                SetValue (const void* newValue) { m_address = newValue; }
const void *        GetValue () { return m_address; }
Acad::ErrorStatus   ReadData (void ** output)
    {
    if (nullptr == output)
        {
        BeAssert (false && L"RecordingFiler error - caller passed nullptr to read a void*!");
        return  Acad::eInvalidInput;
        }
    *output = (void*) m_address;
    return Acad::eOk;
    }
};


/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct ACharFilerData : FilerData
{
AcString*    m_string;
ACharFilerData (const ACHAR* data) {m_string = new AcString (data); }
virtual void        Dump (int sequenceNum) override { printf ("%3d: kDwgString(ACHAR) %ls\n", sequenceNum, m_string->kwszPtr()); }
void                SetValue (const ACHAR* newValue) { m_string->assign (newValue); }
const ACHAR*        GetValue () { return nullptr == m_string ? nullptr : m_string->kwszPtr(); }

Acad::ErrorStatus   ReadData (ACHAR ** output)
    {
    ACHAR*  newString;
    Acad::ErrorStatus status = acutNewString (m_string->kwszPtr(), newString);
    if (Acad::eOk == status)
        *output = newString;

    return status;
    }

Acad::ErrorStatus   ReadData (AcString& output)
    {
    output.assign (m_string->kwszPtr());
    return Acad::eOk;
    }

virtual ~ACharFilerData () { delete m_string; }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct StringFilerData : FilerData
{
AcString*    m_string;
StringFilerData (const AcString& val) {m_string = new AcString (val); }
virtual void        Dump (int sequenceNum) override { printf ("%3d: kDwgString(AcString) %ls\n", sequenceNum, m_string->kwszPtr()); }
void                SetValue (const ACHAR* newValue) { m_string->assign (newValue); }
const ACHAR*        GetValue () { return nullptr == m_string ? nullptr : m_string->kwszPtr(); }

Acad::ErrorStatus   ReadData (AcString& output)
    {
    output.assign (m_string->kwszPtr());
    return Acad::eOk;
    }

Acad::ErrorStatus   ReadData (ACHAR** output)
    {
    ACHAR*  newString;
    Acad::ErrorStatus status = acutNewString (m_string->kwszPtr(), newString);
    if (Acad::eOk == status)
        *output = newString;
    return Acad::eOk;
    }

virtual ~StringFilerData () { delete m_string; }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct ThreeDoubleFilerData : FilerData
{
double  m_data[3];
ThreeDoubleFilerData (double double1, double double2, double double3) { m_data[0] = double1; m_data[1] = double2; m_data[2] = double3; }
void                Dump3Doubles (int sequenceNum, CharCP typeString) { printf ("%3d: %hs, %f, %f, %f\n", sequenceNum, typeString, m_data[0], m_data[1], m_data[2]); }
Acad::ErrorStatus   ReadData (double* double1, double* double2, double* double3)
    {
    *double1 = m_data[0];
    *double2 = m_data[1];
    *double3 = m_data[2];
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct Point3dFilerData : ThreeDoubleFilerData
{
Point3dFilerData (const AcGePoint3d& point) : ThreeDoubleFilerData (point.x, point.y, point.z) {}
virtual void        Dump (int sequenceNum) override { Dump3Doubles (sequenceNum, "AcGePoint3d"); }
const AcGePoint3d   GetValue () { return AcGePoint3d(m_data[0], m_data[1], m_data[2]); }
void                SetValue (const DPoint3d& point) { m_data[0] = point.x; m_data[1] = point.y; m_data[2] = point.z; }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct Point2dFilerData : ThreeDoubleFilerData
{
Point2dFilerData (const AcGePoint2d& point) : ThreeDoubleFilerData (point.x, point.y, 0.0) {}
Point2dFilerData (double x, double y) : ThreeDoubleFilerData (x, y, 0.0) {}
virtual void        Dump (int sequenceNum) override { Dump3Doubles (sequenceNum, "AcGePoint2d"); }
const AcGePoint2d   GetValue () { return AcGePoint2d(m_data[0], m_data[1]); }
void                SetValue (const DPoint3d& point) { m_data[0] = point.x; m_data[1] = point.y; m_data[2] = 0.0; }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct Vector3dFilerData : ThreeDoubleFilerData
{
Vector3dFilerData (const AcGeVector3d& point) : ThreeDoubleFilerData (point.x, point.y, point.z) {}
const AcGeVector3d  GetValue () { return AcGeVector3d(m_data[0], m_data[1], m_data[2]); }
void                SetValue (const DVec3d& vector) { m_data[0] = vector.x; m_data[1] = vector.y; m_data[2] = vector.z; }
virtual void        Dump (int sequenceNum) override { Dump3Doubles (sequenceNum, "AcGeVector3d"); }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct Vector2dFilerData : ThreeDoubleFilerData
{
Vector2dFilerData (const AcGeVector2d& point) : ThreeDoubleFilerData (point.x, point.y, 0.0) {}
virtual void        Dump (int sequenceNum) override { Dump3Doubles (sequenceNum, "AcGeVector2d"); }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct Scale3dFilerData : ThreeDoubleFilerData
{
Scale3dFilerData (const AcGeScale3d& scale) : ThreeDoubleFilerData (scale.sx, scale.sy, scale.sz) {}
virtual void        Dump (int sequenceNum) override { Dump3Doubles (sequenceNum, "AcGeScale3d"); }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct BinaryDataFilerData : FilerData
{
RDwgRWByteSize  m_dataSize;
void*           m_data;
BinaryDataFilerData (const void* data, RDwgRWByteSize length)
    {
    m_data = acdbAlloc (m_dataSize = length);
    if (nullptr == m_data)
        {
        BeAssert (false && L"RecordingFiler error - allocating 0 size of memory!");
        return;
        }
    memcpy (m_data, data, m_dataSize);
    }

virtual ~BinaryDataFilerData ()
    {
    if (NULL != m_data)
        {
        acdbFree (m_data);
        m_data = NULL;
        }
    }
virtual void        DumpBinaryData (int sequenceNum, CharCP typeString)
    {
    printf ("%3d: %hs, %d bytes\n", sequenceNum, typeString, m_dataSize);
    }

RDwgRWByteSize      GetSize () { return m_dataSize; }
const void*         GetValue () { return m_data; }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct ByteFilerData : BinaryDataFilerData
{
ByteFilerData (const void* data, RDwgRWByteSize length) : BinaryDataFilerData (data, length) {}
virtual void        Dump (int sequenceNum) override { DumpBinaryData (sequenceNum, "Byte"); }
Acad::ErrorStatus   ReadData (void *data, RDwgRWByteSize length)
    {
    if (m_dataSize != length)
        {
        BeAssert (false && L"RecordingFiler error - unexpected binary data size!");
        return Acad::eFilerError;
        }
    memcpy (data, m_data, length);
    return Acad::eOk;
    }
Acad::ErrorStatus   UpdateData (const void* dataIn, RDwgRWByteSize length)
    {
    if (length <= 0)
        return Acad::eFilerError;
    acdbFree (m_data);
    m_data = acdbAlloc (m_dataSize = length);
    if (nullptr == m_data)
        {
        BeAssert (false && L"RecordingFiler error - allocating 0 size of memory!");
        return  Acad::eOutOfMemory;
        }
    memcpy (m_data, dataIn, m_dataSize);
    return Acad::eOk;
    }
};

/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
struct BChunkFilerData : BinaryDataFilerData
{
BChunkFilerData (const void* data, RDwgRWByteSize length) : BinaryDataFilerData (data, length) {}
virtual void        Dump (int sequenceNum) override { DumpBinaryData (sequenceNum, "BChunk"); }
Acad::ErrorStatus   ReadData (ads_binary* output)
    {
    if (m_dataSize >= 32768);
        {
        BeAssert (false && L"RecordingFiler error - binary data size too large!");
        return Acad::eFilerError;
        }
    // clen is a short as of RealDwg 2010. Seems wrong.
    output->clen = (short) m_dataSize;
    output->buf  = (char*)acdbAlloc (m_dataSize);
    memcpy (output->buf, m_data, m_dataSize);

    return Acad::eOk;
    }
};




/*=================================================================================**//**
* @bsiclass
+===============+===============+===============+===============+===============+======*/
class FilerDataList : public bvector<FilerData*>
{
public:
~FilerDataList ()
    {
    for each (FilerData* filerData in *this)
        delete filerData;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   GetBinaryAcValueAt (AcValue& value, UInt32& index)
    {
    BinaryDataFilerData*    binData = dynamic_cast <BinaryDataFilerData*> (this->at(index));
    if (NULL == binData)
        return  BadDataSequence;

    value.set (binData->GetValue(), (DWORD) binData->GetSize());

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   GetAcValueAt (AcValue* value, UInt32& index)
    {
    /*-----------------------------------------------------------------------------------
    Retrieve an AcValue, and/or get the end index of reading it.  When input value==NULL
    this method is used to move filer index over an AcValue.  For speed consideration,
    not all data checks are performed.

    The input index always points to the start of AcValue to be read.  This index will be
    updated at the end of AcValue reading.  It will thus point to the next filer entry
    right after AcValue.

    The preset data type of AcValue may not match the stored data type(kString stored as
    kBinary for instance), at least prior to R2017.
    -----------------------------------------------------------------------------------*/
    UInt32FilerData*     uIntData = NULL;

    // AcValue flags integer:
    Int32FilerData*     intData = dynamic_cast <Int32FilerData*> (this->at(index++));
    if (NULL == intData)
        return  BadDataSequence;

    UInt32      valueFlags = intData->GetValue ();

    // get data type:
    if (NULL == (intData = dynamic_cast <Int32FilerData*> (this->at(index++))))
        return  BadDataSequence;

    AcValue::DataType   dataType = (AcValue::DataType) intData->GetValue ();
    if (NULL != value)
        value->reset (dataType);

    // AcValue flags 0x1 indicates no data present (new in R2017??)
    if (0 == (valueFlags & 0x1))
        this->ParseAcValueData (value, dataType, index);

    // unit type
    if (NULL != value &&
        NULL != (intData = dynamic_cast <Int32FilerData *> (this->at(index))))
        {
        value->setUnitType (AcValue::UnitType(intData->GetValue()));
        }
    index++;

    // finish off of AcValue by string format & string value
    if (nullptr != value)
        {
        ACharFilerData*     charsData = dynamic_cast <ACharFilerData *> (this->at(index));
        if (nullptr != charsData)
            value->setFormat (charsData->GetValue());
        if (nullptr != (charsData = dynamic_cast <ACharFilerData *> (this->at(index))))
            value->set (charsData->GetValue());
        }
    index += 2;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ParseAcValueData (AcValue* value, AcValue::DataType dataType, UInt32& index)
    {
    Int32FilerData*     intData = nullptr;
    UInt32FilerData*    uIntData = nullptr;

    switch (dataType)
        {
        case AcValue::kLong:
            if (NULL != value)
                {
                intData = dynamic_cast <Int32FilerData *> (this->at(index));
                if (NULL == intData)
                    {
                    if (NULL == (uIntData = dynamic_cast <UInt32FilerData*> (this->at(index))))
                        return  BadDataSequence;

                    value->set ((long)uIntData->GetValue());
                    }
                else
                    {
                    value->set (intData->GetValue());
                    }
                }
            index++;
            break;

        case AcValue::kDouble:
            if (NULL != value)
                {
                DoubleFilerData*    doubleData = dynamic_cast <DoubleFilerData*> (this->at(index));
                if (NULL == doubleData)
                    return  BadDataSequence;
                value->set (doubleData->GetValue());
                }
            index++;
            break;

        case AcValue::kGeneral:     // start from R21
            if (nullptr != value)
                {
                if (RealDwgSuccess != this->GetBinaryAcValueAt(*value, index))
                    return  BadDataSequence;
                }
            index++;
            break;

        case AcValue::kString:
            if (nullptr != value)
                {
                ACharFilerData*     charsData = nullptr;
                StringFilerData*    stringData = dynamic_cast <StringFilerData*> (this->at(index));
                if (nullptr != stringData)
                    value->set (stringData->GetValue());
                else if (nullptr != (charsData = dynamic_cast<ACharFilerData*>(this->at(index))))
                    value->set (charsData->GetValue());
                else
                    return  BadDataSequence;
                }
            index++;
            break;

        case AcValue::kDate:
            if (NULL == (uIntData = dynamic_cast <UInt32FilerData *> (this->at(index++))))
                return  BadDataSequence;

            if (NULL != value)
                {
                if (uIntData->GetValue() > 0)
                    {
                    if (RealDwgSuccess != this->GetBinaryAcValueAt(*value, index))
                        return  BadDataSequence;
                    }
                else
                    {
                    IntFilerData*           int64Data = dynamic_cast <IntFilerData*> (this->at(index));
                    if (NULL == int64Data)
                        return  BadDataSequence;
                    value->set ((__time64_t&)int64Data->m_intData);
                    }
                }
            index++;
            break;

        case AcValue::kPoint:
            if (NULL == (uIntData = dynamic_cast <UInt32FilerData *> (this->at(index++))))
                return  BadDataSequence;

            if (NULL != value)
                {
                Point2dFilerData*   point2dData = dynamic_cast <Point2dFilerData*> (this->at(index));
                if (NULL == point2dData)
                    {
                    if (RealDwgSuccess != this->GetBinaryAcValueAt(*value, index))
                        return  BadDataSequence;
                    }
                else
                    {
                    value->set (point2dData->GetValue());
                    }
                }
            index++;
            break;

        case AcValue::k3dPoint:
            if (NULL == (uIntData = dynamic_cast <UInt32FilerData *> (this->at(index++))))
                return  BadDataSequence;

            if (NULL != value)
                {
                Point3dFilerData*   point3dData = dynamic_cast <Point3dFilerData*> (this->at(index));
                if (NULL == point3dData)
                    {
                    if (RealDwgSuccess != this->GetBinaryAcValueAt(*value, index))
                        return  BadDataSequence;
                    }
                else
                    {
                    value->set (point3dData->GetValue());
                    }
                }
            index++;
            break;

        case AcValue::kObjectId:
            if (NULL != value)
                {
                AcDbObjectId        objectId;
                ObjectIdFilerData*  objectIdData = dynamic_cast <ObjectIdFilerData*> (this->at(index));
                if (NULL == objectIdData || Acad::eOk != objectIdData->GetValue(objectId))
                    return  BadDataSequence;
                value->set (objectId);
                }
            index++;
            break;

        case AcValue::kBuffer:
        case AcValue::kResbuf:
          break;

        case AcValue::kUnknown:
            if (NULL == (intData = dynamic_cast <Int32FilerData *> (this->at(index))) &&
                NULL == (uIntData = dynamic_cast <UInt32FilerData *> (this->at(index))))
                return  BadDataSequence;
            index++;
            break;
        }

    return  RealDwgSuccess;
    }

};  // FilerDataList

typedef void (*PFRecordingFilerCallback)
(
FilerData*          newData,
FilerDataList&      priorData,
void*               userData
);


/*=================================================================================**//**
* Main RecordingFiler class.
* @bsiclass                                                     Barry.Bentley   02/09
+===============+===============+===============+===============+===============+======*/
class RecordingFiler : public AcDbDwgFiler
{
private:
Acad::ErrorStatus           m_status;
FilerDataList               m_dataList;
UInt32                      m_currentPlaybackPos;
AcDb::FilerType             m_filerType;

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
RecordingFiler (size_t expectedMembers)
    {
    m_status                = Acad::eOk;
    m_currentPlaybackPos    = 0;
    m_filerType             = AcDb::kBagFiler;
    m_dataList.reserve (expectedMembers);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
~RecordingFiler ()
    {
    }

/*--------------------------------------------------------------------------------------+
|
| Our Methods
|
+--------------------------------------------------------------------------------------*/
private:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus               AddData (FilerData* newData)
    {
    m_dataList.push_back (newData);
    return Acad::eOk;
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus               RecordData (AcDbObjectCP pObject)
    {
    return pObject->dwgOutFields (this);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus               PlaybackData (AcDbObjectP pObject)
    {
    return  pObject->dwgInFields (this);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
FilerDataList&                  GetDataList()
    {
    return m_dataList;     // m_dataList;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                            DumpList (CharCP message)
    {
    int  iMember = 0;
    printf ("%hs\n", message);
    for each (FilerData* filerData in m_dataList)
        {
        filerData->Dump (iMember++);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                            RemoveEntryAt (UInt32 entryNum)
    {
    BeAssert (entryNum < m_dataList.size() && L"RecordingFiler error - attempt removing size larger than actual data");
    m_dataList.erase (m_dataList.begin()+13);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                            InsertEntryAt (UInt32 entryNum, FilerData* entry)
    {
    BeAssert (entryNum <= m_dataList.size() && L"RecordingFiler error - attempt inserting size larger than actual size!");
    m_dataList.insert (m_dataList.begin()+entryNum, entry);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                            DeleteEntryRange (UInt32 deleteStart, UInt32 numToDelete)
    {
    m_dataList.erase (m_dataList.begin() + deleteStart, m_dataList.begin() + deleteStart + numToDelete);
    }

/*--------------------------------------------------------------------------------------+
|
| AcDbDwgFiler Write Methods
|
+--------------------------------------------------------------------------------------*/

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual  Acad::ErrorStatus      filerStatus() const override
    {
    FILERDEBUG_PRINTF ("filerStatus\n");
    return m_status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual  AcDb::FilerType        filerType() const override
    {
    FILERDEBUG_PRINTF ("filerType\n");
    return m_filerType;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual  Acad::ErrorStatus      dwgVersion (AcDb::AcDbDwgVersion& version, AcDb::MaintenanceReleaseVersion& maintVer) const override
    {
    FILERDEBUG_PRINTF ("dwgVersion\n");
    return __super::dwgVersion (version, maintVer);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                    setFilerStatus (Acad::ErrorStatus newStatus) override
    {
    FILERDEBUG_PRINTF ("setFilerStatus\n");
    m_status = newStatus;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus               SetFilerStatus (Acad::ErrorStatus newStatus)
    {
    // this is our internal one that sets the status and returns that value
    BeAssert (Acad::eOk == newStatus);
    return (m_status = newStatus);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                    resetFilerStatus() override
    {
    FILERDEBUG_PRINTF ("resetFilerStatus\n");
    m_status = Acad::eOk;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       writeHardOwnershipId (const AcDbHardOwnershipId& ownerId) override
    {
    FILERDEBUG_PRINTF ("writeHardOwnershipId\n");
    FilerData* filerData = new HardOwnershipIdFilerData (ownerId);
    return AddData (filerData);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       writeSoftOwnershipId (const AcDbSoftOwnershipId& ownerId) override
    {
    FILERDEBUG_PRINTF ("writeSoftOwnershipId\n");
    FilerData* filerData = new SoftOwnershipIdFilerData (ownerId);
    return AddData (filerData);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       writeHardPointerId (const AcDbHardPointerId& ownerId) override
    {
    FILERDEBUG_PRINTF ("writeHardPointerId\n");
    FilerData* filerData = new HardPointerIdFilerData (ownerId);
    return AddData (filerData);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       writeSoftPointerId (const AcDbSoftPointerId& ownerId) override
    {
    FILERDEBUG_PRINTF ("writeSoftPointerId\n");
    FilerData* filerData = new SoftPointerIdFilerData (ownerId);
    return AddData (filerData);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       writeInt8 (Adesk::Int8 val) override
    {
    FILERDEBUG_PRINTF ("writeInt8\n");
    FilerData* filerData = new Int8FilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeString (const ACHAR* val) override
    {
    FILERDEBUG_PRINTF ("writeString1\n");
    FilerData* filerData = new ACharFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeString (const AcString& val) override
    {
    FILERDEBUG_PRINTF ("writeString2\n");
    FilerData* filerData = new StringFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeBChunk (const ads_binary& val) override
    {
    FILERDEBUG_PRINTF ("writeBChunk\n");
    FilerData* filerData = new BChunkFilerData (val.buf, val.clen);
    return Acad::eInvalidInput;
    }

virtual Acad::ErrorStatus       writeBytes(const void *data, RDwgRWByteSize size) override
    {
    FILERDEBUG_PRINTF ("writeBytes\n");
    FilerData* filerData = new ByteFilerData (data, size);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeAcDbHandle (const AcDbHandle& val) override
    {
    FILERDEBUG_PRINTF ("writeAcDbHandle\n");
    FilerData* filerData = new HandleFilerData (val);
    return AddData (filerData);
    }

#if RealDwgVersion > 2009
virtual Acad::ErrorStatus       writeInt64 (Adesk::Int64 val)  override
    {
    FILERDEBUG_PRINTF ("writeInt64\n");
    FilerData* filerData = new Int64FilerData (val);
    return AddData (filerData);
    }
#endif

virtual Acad::ErrorStatus       writeInt32 (Adesk::Int32 val)  override
    {
    FILERDEBUG_PRINTF ("writeInt32\n");
    FilerData* filerData = new Int32FilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeInt16 (Adesk::Int16 val) override
    {
    FILERDEBUG_PRINTF ("writeInt16\n");
    FilerData* filerData = new Int16FilerData (val);
    return AddData (filerData);
    }

#if RealDwgVersion > 2009
virtual Acad::ErrorStatus       writeUInt64 (Adesk::UInt64 val) override
    {
    FILERDEBUG_PRINTF ("writeUInt64\n");
    FilerData* filerData = new UInt64FilerData (val);
    return AddData (filerData);
    }
#endif

virtual Acad::ErrorStatus       writeUInt32 (Adesk::UInt32 val) override
    {
    FILERDEBUG_PRINTF ("writeUInt32\n");
    FilerData* filerData = new UInt32FilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeUInt16 (Adesk::UInt16 val) override
    {
    FILERDEBUG_PRINTF ("writeUInt16\n");
    FilerData* filerData = new UInt16FilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeUInt8 (Adesk::UInt8 val) override
    {
    FILERDEBUG_PRINTF ("writeUInt8\n");
    FilerData* filerData = new UInt8FilerData (val);
    return AddData (filerData);
    }

#if defined(Adesk_Boolean_is_bool) && RealDwgVersion < 2017
#error
virtual Acad::ErrorStatus       writeInt (int val) override
    {
    FILERDEBUG_PRINTF ("writeInt\n");
    return AddResBuf (RealDwgResBuf::CreateInt (code, val));
    }
#endif

virtual Acad::ErrorStatus       writeBoolean (Adesk::Boolean val) override
    {
    FILERDEBUG_PRINTF ("writeBoolean\n");
    FilerData* filerData = new BooleanFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeBool (bool val) override
    {
    FILERDEBUG_PRINTF ("writeBool\n");
    FilerData* filerData = new BoolFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeDouble (double val) override
    {
    FILERDEBUG_PRINTF ("writeDouble\n");
    FilerData* filerData = new DoubleFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writePoint2d (const AcGePoint2d& val) override
    {
    FILERDEBUG_PRINTF ("writePoint2d\n");
    FilerData* filerData = new Point2dFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writePoint3d (const AcGePoint3d& val) override
    {
    FILERDEBUG_PRINTF ("writePoint3d\n");
    FilerData* filerData = new Point3dFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeVector2d (const AcGeVector2d& val) override
    {
    FILERDEBUG_PRINTF ("writeVector2d\n");
    FilerData* filerData = new Vector2dFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeVector3d (const AcGeVector3d& val) override
    {
    FILERDEBUG_PRINTF ("writeVector3d\n");
    FilerData* filerData = new Vector3dFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeScale3d (const AcGeScale3d& val) override
    {
    FILERDEBUG_PRINTF ("writeScale3d\n");
    FilerData* filerData = new Scale3dFilerData (val);
    return AddData (filerData);
    }

virtual Acad::ErrorStatus       writeAddress (const void* val) override
    {
    FILERDEBUG_PRINTF ("writeAddress\n");
    FilerData* filerData = new AddressFilerData (val);
    return AddData (filerData);
    }

/*--------------------------------------------------------------------------------------+
|
| AcDbDwgFiler Read Methods
|
+--------------------------------------------------------------------------------------*/

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readHardOwnershipId (AcDbHardOwnershipId* output) override
    {
    FILERDEBUG_PRINTF ("readHardOwnershipId\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    HardOwnershipIdFilerData*   thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <HardOwnershipIdFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readSoftOwnershipId (AcDbSoftOwnershipId* output) override
    {
    FILERDEBUG_PRINTF ("readSoftOwnershipId\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    SoftOwnershipIdFilerData*   thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <SoftOwnershipIdFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readHardPointerId (AcDbHardPointerId* output)  override
    {
    FILERDEBUG_PRINTF ("readHardPointerId\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    HardPointerIdFilerData*     thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <HardPointerIdFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus               readSoftPointerId (AcDbSoftPointerId* output) override
    {
    FILERDEBUG_PRINTF ("readSoftPointerId\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    SoftPointerIdFilerData*     thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <SoftPointerIdFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readString (ACHAR ** output) override
    {
    FILERDEBUG_PRINTF ("readString(ACHAR)\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    // entry may be chars or a string - TFS 569776.
    FilerData*          filerData = m_dataList[m_currentPlaybackPos++];
    StringFilerData*    stringFilerData = nullptr;
    ACharFilerData*     thisFilerData = nullptr;
    if (nullptr == (thisFilerData = dynamic_cast <ACharFilerData*> (filerData)) &&
        nullptr == (stringFilerData = dynamic_cast <StringFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    if (nullptr != thisFilerData)
        return SetFilerStatus (thisFilerData->ReadData (output));
    else if (nullptr != stringFilerData)
        return SetFilerStatus (stringFilerData->ReadData (output));
    else
        return  SetFilerStatus (Acad::eFilerError);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readString (AcString &output) override
    {
    FILERDEBUG_PRINTF ("readString(AcString)\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    ACharFilerData*             acharFilerData;
    StringFilerData*            stringFilerData;
    if (nullptr != (stringFilerData = dynamic_cast <StringFilerData*> (filerData)))
        return SetFilerStatus (stringFilerData->ReadData (output));
    else if (nullptr != (acharFilerData = dynamic_cast <ACharFilerData*> (filerData)))
        return SetFilerStatus (acharFilerData->ReadData (output));

    return SetFilerStatus (Acad::eFilerError);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readAcDbHandle (AcDbHandle* output) override
    {
    FILERDEBUG_PRINTF ("readAcDbHandle\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    HandleFilerData*            thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <HandleFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

#if RealDwgVersion > 2009
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readInt64 (Adesk::Int64* output) override
    {
    FILERDEBUG_PRINTF ("readInt64\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    Int64FilerData*             thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <Int64FilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readInt32 (Adesk::Int32* output) override
    {
    FILERDEBUG_PRINTF ("readInt32\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    Int32FilerData*             thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <Int32FilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readInt16 (Adesk::Int16* output) override
    {
    FILERDEBUG_PRINTF ("readInt16\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    Int16FilerData*             thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <Int16FilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readInt8 (Adesk::Int8 * output) override
    {
    FILERDEBUG_PRINTF ("readInt8\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    Int8FilerData*              thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <Int8FilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

#if RealDwgVersion > 2009
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readUInt64 (Adesk::UInt64* output) override
    {
    FILERDEBUG_PRINTF ("readUInt64\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    UInt64FilerData*            thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <UInt64FilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readUInt32 (Adesk::UInt32* output) override
    {
    FILERDEBUG_PRINTF ("readUInt32\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    UInt32FilerData*            thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <UInt32FilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readUInt16 (Adesk::UInt16* output) override
    {
    FILERDEBUG_PRINTF ("readUInt16\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    UInt16FilerData*            thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <UInt16FilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readUInt8 (Adesk::UInt8* output) override
    {
    FILERDEBUG_PRINTF ("readUInt8\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    UInt8FilerData*             thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <UInt8FilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

#if defined(Adesk_Boolean_is_bool) && RealDwgVersion < 2017
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readInt (int* output) override
    {
    FILERDEBUG_PRINTF ("readInt\n");
    return SetFilerStatus (Acad::eFilerError);
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readBoolean (Adesk::Boolean* output) override
    {
    FILERDEBUG_PRINTF ("readBoolen\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    BooleanFilerData*           thisFilerData;
    BoolFilerData*              altFilerData;

    // I ran across a case where they wrote a Boolean, but read a Bool, so I had
    //  to add the else if clause. That case was on a dwgFileIn of an AcDbHatch with a Polyline
    //  loop, reading the isClosed data.
    if (NULL != (thisFilerData = dynamic_cast <BooleanFilerData*> (filerData)))
        return SetFilerStatus (thisFilerData->ReadData (output));
    else if (NULL != (altFilerData = dynamic_cast <BoolFilerData*> (filerData)))
        {
        *output = altFilerData->GetValue ();
        return SetFilerStatus (Acad::eOk);
        }

    return SetFilerStatus (Acad::eFilerError);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readBool (bool* output) override
    {
    FILERDEBUG_PRINTF ("readBool\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    BoolFilerData*              thisFilerData;
    BooleanFilerData*           altFilerData;

    // same problem - now with spline rational & periodic booleans using RealDWG 2016 - they must have an unmatching dwgInFields vs dwgOutFields!
    if (nullptr != (thisFilerData = dynamic_cast <BoolFilerData*> (filerData)))
        {
        return SetFilerStatus (thisFilerData->ReadData (output));
        }
    else if (nullptr != (altFilerData = dynamic_cast <BooleanFilerData*> (filerData)))
        {
        *output = altFilerData->GetValue ();
        return SetFilerStatus (Acad::eOk);
        }

    return SetFilerStatus (Acad::eFilerError);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readDouble (double* output) override
    {
    FILERDEBUG_PRINTF ("readDouble\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    DoubleFilerData*            thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <DoubleFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readPoint2d (AcGePoint2d* output) override
    {
    FILERDEBUG_PRINTF ("readPoint2d\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    Point2dFilerData*           thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <Point2dFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    if (nullptr == output)
        {
        BeAssert (false && L"RecoringFiler error - caller passed nullptr to read a 2D point!");
        return  Acad::eInvalidInput;
        }

    double zVal;
    return SetFilerStatus (thisFilerData->ReadData (&output->x, &output->y, &zVal));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readPoint3d (AcGePoint3d* output) override
    {
    FILERDEBUG_PRINTF ("readPoint3d\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    Point3dFilerData*           thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <Point3dFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    if (nullptr == output)
        {
        BeAssert (false && L"RecoringFiler error - caller passed nullptr to read a 3D point!");
        return  Acad::eInvalidInput;
        }

    return SetFilerStatus (thisFilerData->ReadData (&output->x, &output->y, &output->z));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readVector2d (AcGeVector2d* output) override
    {
    FILERDEBUG_PRINTF ("readVector2d\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    Vector2dFilerData*          thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <Vector2dFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    if (nullptr == output)
        {
        BeAssert (false && L"RecoringFiler error - caller passed nullptr to read a 2D vector!");
        return  Acad::eInvalidInput;
        }

    double zVal;
    return SetFilerStatus (thisFilerData->ReadData (&output->x, &output->y, &zVal));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readVector3d (AcGeVector3d* output) override
    {
    FILERDEBUG_PRINTF ("readVector3d\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    Vector3dFilerData*          thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <Vector3dFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    if (nullptr == output)
        {
        BeAssert (false && L"RecoringFiler error - caller passed nullptr to read a 3D vector!");
        return  Acad::eInvalidInput;
        }
    return SetFilerStatus (thisFilerData->ReadData (&output->x, &output->y, &output->z));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readScale3d (AcGeScale3d* output) override
    {
    FILERDEBUG_PRINTF ("readScale3d\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    Scale3dFilerData*           thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <Scale3dFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    if (nullptr == output)
        {
        BeAssert (false && L"RecoringFiler error - caller passed nullptr to read a 3D scale!");
        return  Acad::eInvalidInput;
        }
    return SetFilerStatus (thisFilerData->ReadData (&output->sx, &output->sy, &output->sz));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readBytes (void *buffer, RDwgRWByteSize size) override
    {
    FILERDEBUG_PRINTF ("readBytes\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    ByteFilerData*              thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <ByteFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    if (nullptr == buffer)
        {
        BeAssert (false && L"RecoringFiler error - caller passed nullptr to read a binary buffer!");
        return  Acad::eInvalidInput;
        }

    return SetFilerStatus (thisFilerData->ReadData (buffer, size));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readBChunk (ads_binary * output) override
    {
    FILERDEBUG_PRINTF ("readBytes\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    BChunkFilerData*              thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <BChunkFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    if (nullptr == output)
        {
        BeAssert (false && L"RecoringFiler error - caller passed nullptr to read a binary chunk!");
        return  Acad::eInvalidInput;
        }
    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       readAddress (void ** output) override
    {
    FILERDEBUG_PRINTF ("readAddress\n");
    BeAssert ((Acad::eOk == m_status) && (m_currentPlaybackPos < m_dataList.size()));
    if (m_status != Acad::eOk)
        return m_status;

    FilerData*                  filerData = m_dataList[m_currentPlaybackPos++];
    AddressFilerData*              thisFilerData;
    if (NULL == (thisFilerData = dynamic_cast <AddressFilerData*> (filerData)))
        return SetFilerStatus (Acad::eFilerError);

    if (nullptr == output)
        {
        BeAssert (false && L"RecoringFiler error - caller passed nullptr to read an address!");
        return  Acad::eInvalidInput;
        }
    return SetFilerStatus (thisFilerData->ReadData (output));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       seek (RDwgSeekType offset, int method) override
    {
    assert (false);
    return SetFilerStatus (Acad::eFilerError);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RDwgSeekType            tell () const override  { assert (false); return 0; }

};


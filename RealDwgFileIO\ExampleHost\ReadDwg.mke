#*--------------------------------------------------------------------------------------+
#
#     $Source: mstn/mdlapps/RealDwgFileIO/ExampleHost/ReadDwg.mke $
#
#  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
#
#--------------------------------------------------------------------------------------*/
PolicyFile=$(_MakeFilePath)DwgExampleHost.mki

consoleAppUnicode = 1

appName             = ReadDwg
baseDir             = $(_MakeFilePath)
SubpartLibs         = $(BuildContext)SubParts/Libs/
ContextDeliveryDir  = $(BuildContext)Delivery/
binDest             = $(OutputRootDir)build/DwgExampleHost/
o                   = $(binDest)ReadDwg/
productDir          = $(OutputRootDir)Product/DwgExampleHost/
%if !defined (RealDwgVersion)
RealDwgVersion      = 2023
%endif

%include mdl.mki

always:
    !~@mkdir $(o)

MultiCompileDepends=$(_MakeFileSpec)
%include MultiCppCompileRule.mki

$(o)DgnPlatformHosts$(oext)         : $(baseDir)DgnPlatformHosts.cpp $(baseDir)DgnPlatformHosts.h ${MultiCompileDepends}

$(o)ReadDwg$(oext)                  : $(baseDir)$(appName).cpp $(baseDir)DwgExampleHost.h ${MultiCompileDepends}

%include MultiCppCompileGo.mki
appObjects =% $(MultiCompileObjectList)

EXE_DEST            = $(binDest)
EXE_NAME            = $(appName)
EXE_OBJS            = $(appObjects)
EXE_TMP_DIR         = $(o)
EXE_SYMB_DEST       = $(o)

LINKER_LIBRARIES    =   $(SubpartLibs)Bentley.lib                           \
                        $(SubpartLibs)BentleyAllocator.lib                  \
                        $(SubpartLibs)DgnPlatform.lib                       \
                        $(SubpartLibs)DwgDgnIO$(RealDwgVersion).lib         \
                        $(ContextDeliveryDir)DwgExampleHost.lib

LINKER_LIBRARIES_DELAYLOADED =\
%if RealDwgVersion >= 2017
                        $(SubpartLibs)AcPal.lib                             \
%endif
                        $(SubpartLibs)PSolidCore.lib                        \
                        $(SubpartLibs)PSolidAcisInterop.lib                 \
                        $(SubpartLibs)RasterCore.lib

%include $(SharedMki)linktool.mki

$(ContextDeliveryDir)$(appName).exe : $(binDest)$(appName).exe
    $(LinkFirstDepToFirstTarget)

## Copy transeed.dgn to the root folder of the sample program for testing purpose
%include $(SrcMstnPlatform)privmki/miscdevdirs.mki
%iffile $(miscDevWorkspaceSystem)seed/transeed.dgn
$(productDir)transeed.dgn           : $(miscDevWorkspaceSystem)seed/transeed.dgn
    $(CopyFirstDepToFirstTarget)
%endif

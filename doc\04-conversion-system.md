# Conversion System

## Overview

The RealDwgFileIO framework implements a sophisticated bidirectional conversion system between DWG/DXF and DGN formats. The conversion system is built around context objects that manage the conversion process and extension objects that handle specific entity types.

## Conversion Architecture

### Core Conversion Components

```
┌─────────────────────────────────────────┐
│           Conversion Contexts           │
│  ┌─────────────────┐ ┌─────────────────┐│
│  │ ConvertToDgn    │ │ ConvertFromDgn  ││
│  │ Context         │ │ Context         ││
│  │ (DWG → DGN)     │ │ (DGN → DWG)     ││
│  └─────────────────┘ └─────────────────┘│
├─────────────────────────────────────────┤
│           Extension System              │
│  ┌─────────────────┐ ┌─────────────────┐│
│  │ ToDgnExtension  │ │ ToDwgExtension  ││
│  │ (per entity)    │ │ (per element)   ││
│  └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────┘
```

## Base Conversion Context

### BaseConvertContext

**File**: `rDwgBaseContext.cpp`

Provides common functionality for all conversion contexts:

```cpp
class BaseConvertContext
{
protected:
    FileHolder*         m_pFileHolder;
    DgnModelP          m_modelRef;
    ModelId            m_modelId;
    DgnFileFormatType  m_format;
    bool               m_is3dModel;
    
public:
    // Common conversion utilities
    StatusInt MapSymbology(...);
    StatusInt HandleProgress(...);
    StatusInt ReportError(...);
    
    // Symbology access
    DwgSymbologyData* GetDwgSymbologyData();
    DgnSymbologyData* GetDgnSymbologyData();
};
```

## DWG to DGN Conversion

### ConvertToDgnContext

**File**: `rDwgToDgnContext.cpp`

Handles conversion from DWG/DXF format to DGN format.

#### Key Responsibilities:
- Enumerate AutoCAD entities
- Convert entities to DGN elements
- Map DWG symbology to DGN
- Handle model structure
- Manage conversion progress

#### Conversion Process:

```cpp
StatusInt ConvertToDgnContext::ConvertDwgModelToDgn(
    DgnModelP model, 
    ModelId modelId)
{
    // 1. Initialize conversion context
    Initialize(model, modelId);
    
    // 2. Get AutoCAD block table record
    AcDbBlockTableRecord* pBlockRecord = GetBlockRecord(modelId);
    
    // 3. Iterate through all entities
    AcDbBlockTableRecordIterator* pIterator;
    pBlockRecord->newIterator(pIterator);
    
    for (pIterator->start(); !pIterator->done(); pIterator->step()) {
        AcDbEntity* pEntity;
        if (Acad::eOk == pIterator->getEntity(pEntity, AcDb::kForRead)) {
            // Convert individual entity
            ProcessDwgEntity(pEntity, model);
            pEntity->close();
        }
    }
    
    delete pIterator;
    return SUCCESS;
}
```

#### Entity Processing:

```cpp
StatusInt ConvertToDgnContext::ProcessDwgEntity(
    AcDbEntity* pEntity, 
    DgnModelP model)
{
    // 1. Get entity type
    AcRxClass* pClass = pEntity->isA();
    
    // 2. Find appropriate ToDgnExtension
    ToDgnExtension* pExtension = FindExtension(pClass);
    if (!pExtension)
        return BSIERROR;  // Unsupported entity type
    
    // 3. Convert entity using extension
    DgnElementP element = nullptr;
    StatusInt status = pExtension->ConvertToDgn(
        pEntity, element, this, model);
    
    // 4. Add element to model if successful
    if (SUCCESS == status && element) {
        model->AddElement(element);
    }
    
    return status;
}
```

## DGN to DWG Conversion

### ConvertFromDgnContext

**File**: `rDwgFromDgnContext.cpp`

Handles conversion from DGN format to DWG/DXF format.

#### Key Responsibilities:
- Enumerate DGN elements
- Convert elements to AutoCAD entities
- Map DGN symbology to DWG
- Manage AutoCAD database
- Handle styles and tables

#### Conversion Process:

```cpp
StatusInt ConvertFromDgnContext::ConvertDgnModelToDwg(
    DgnModelP model, 
    ModelId modelId)
{
    // 1. Initialize conversion context
    Initialize(model, modelId);
    
    // 2. Process non-graphical data first
    SaveNonCacheChanges(false, model->GetDgnFile());
    
    // 3. Get AutoCAD block table record
    AcDbBlockTableRecord* pBlockRecord = GetOrCreateBlockRecord(modelId);
    
    // 4. Iterate through DGN elements
    DgnElementIteratorP iterator = model->CreateElementIterator();
    
    for (DgnElementP element : *iterator) {
        ProcessDgnElement(element, pBlockRecord);
    }
    
    return SUCCESS;
}
```

#### Element Processing:

```cpp
StatusInt ConvertFromDgnContext::ProcessDgnElement(
    DgnElementP element)
{
    // 1. Get element handler
    DgnElementHandlerP handler = element->GetHandler();
    
    // 2. Find appropriate ToDwgExtension
    ToDwgExtension* pExtension = FindExtension(handler);
    if (!pExtension)
        return BSIERROR;  // Unsupported element type
    
    // 3. Convert element using extension
    AcDbEntity* pEntity = nullptr;
    StatusInt status = pExtension->ConvertToDwg(
        element, pEntity, this);
    
    // 4. Add entity to AutoCAD database
    if (SUCCESS == status && pEntity) {
        AddEntityToDatabase(pEntity);
    }
    
    return status;
}
```

### Non-Cache Changes

The framework handles styles, tables, and other non-graphical data:

```cpp
StatusInt ConvertFromDgnContext::SaveNonCacheChanges(
    bool fullSave, 
    DgnFileP dgnFile)
{
    // Save in specific order due to dependencies
    
    // 1. Text styles (required by other styles)
    SaveDgnTextStylesToDatabase();
    
    // 2. Line styles (required by layers)
    SaveDgnLineStylesToDatabase();
    
    // 3. Layers and references
    SaveXRefsAndLayersToDatabase(dgnFile);
    SaveLayerFiltersToDatabase();
    
    // 4. Dimension styles
    SaveDgnDimensionStylesToDatabase();
    
    // 5. Multiline styles
    SaveDgnMultilineStylesToDatabase();
    
    // 6. Named views
    SaveDgnNamedViewsToDatabase();
    
    // 7. Materials
    SaveDgnMaterialsToDatabase();
    
    // 8. Registered applications
    SaveRegisteredApplicationsToDatabase();
    
    return SUCCESS;
}
```

## Multi-Processing Support

### ConvertFromDgnMultiProcessingContext

**File**: `rDwgFromDgnContext.cpp`

Provides multi-threaded conversion for improved performance:

```cpp
class ConvertFromDgnMultiProcessingContext : public ConvertFromDgnContext
{
public:
    // Override element processing for parallel execution
    StatusInt ProcessDgnElement(DgnElementP element) override;
    
private:
    // Thread management
    std::vector<std::thread> m_workerThreads;
    std::queue<DgnElementP> m_elementQueue;
    std::mutex m_queueMutex;
};
```

#### Activation:
Multi-processing is enabled via environment variable:
```cpp
if (ConfigurationManager::IsVariableDefinedAndTrue(L"MS_DWG_SAVE_MULTIPROCESSING"))
    context = new ConvertFromDgnMultiProcessingContext(...);
else
    context = new ConvertFromDgnContext(...);
```

## Extension System

### ToDgnExtension Interface

Defines the interface for converting DWG entities to DGN elements:

```cpp
class ToDgnExtension
{
public:
    virtual StatusInt ConvertToDgn(
        AcDbEntity* pEntity,
        DgnElementP& element,
        ConvertToDgnContext* context,
        DgnModelP model) = 0;
        
    virtual bool CanConvert(AcRxClass* pClass) = 0;
};
```

### ToDwgExtension Interface

Defines the interface for converting DGN elements to DWG entities:

```cpp
class ToDwgExtension
{
public:
    virtual StatusInt ConvertToDwg(
        DgnElementP element,
        AcDbEntity*& pEntity,
        ConvertFromDgnContext* context) = 0;
        
    virtual bool CanConvert(DgnElementHandlerP handler) = 0;
};
```

### Extension Registration

Extensions are registered during framework initialization:

```cpp
// In rDwgDgnExtension.cpp
void RegisterExtensions()
{
    // Register DWG to DGN extensions
    RegisterToDgnExtension(new LineExtension());
    RegisterToDgnExtension(new CircleExtension());
    RegisterToDgnExtension(new ArcExtension());
    // ... more extensions
    
    // Register DGN to DWG extensions
    RegisterToDwgExtension(new LineElementExtension());
    RegisterToDwgExtension(new EllipseElementExtension());
    RegisterToDwgExtension(new ArcElementExtension());
    // ... more extensions
}
```

## Symbology Conversion

### Color Mapping

```cpp
StatusInt BaseConvertContext::MapColor(
    UInt32 sourceColor,
    UInt32& targetColor,
    bool dwgToDgn)
{
    if (dwgToDgn) {
        // DWG to DGN color mapping
        targetColor = GetDgnSymbologyData()->GetColorIndex(
            sourceColor, this);
    } else {
        // DGN to DWG color mapping
        targetColor = GetDwgSymbologyData()->GetColorIndex(
            sourceColor, this);
    }
    
    return SUCCESS;
}
```

### Line Style Mapping

```cpp
StatusInt BaseConvertContext::MapLineStyle(
    UInt32 sourceLineStyle,
    UInt32& targetLineStyle,
    bool dwgToDgn)
{
    if (dwgToDgn) {
        targetLineStyle = GetDgnSymbologyData()->RemapLineStyleId(
            sourceLineStyle, GetDwgSymbologyData(), this);
    } else {
        targetLineStyle = GetDwgSymbologyData()->RemapLineStyleId(
            sourceLineStyle, GetDgnSymbologyData(), this);
    }
    
    return SUCCESS;
}
```

## Progress Reporting

### Progress Management

```cpp
class ConversionProgress
{
private:
    AcDbProgressMeter* m_progressMeter;
    UInt32 m_totalElements;
    UInt32 m_processedElements;
    
public:
    void Initialize(UInt32 totalElements);
    void UpdateProgress(UInt32 elementsProcessed);
    void SetMessage(WCharCP message);
    void Complete();
};
```

## Error Handling

### Conversion Errors

```cpp
enum ConversionErrors
{
    CONVERSION_SUCCESS = 0,
    CONVERSION_UNSUPPORTED_ENTITY,
    CONVERSION_SYMBOLOGY_ERROR,
    CONVERSION_GEOMETRY_ERROR,
    CONVERSION_MEMORY_ERROR,
    CONVERSION_CANCELLED_BY_USER
};
```

### Error Recovery

- Continue processing on non-critical errors
- Log errors for user review
- Provide fallback conversions where possible
- Graceful degradation for complex entities

## Performance Optimization

### Batch Processing

- Group similar operations
- Minimize database transactions
- Cache frequently used data

### Memory Management

- Reuse conversion contexts
- Pool allocation for temporary objects
- Automatic cleanup of resources

### Conversion Caching

- Cache conversion results for identical entities
- Reuse symbology mappings
- Share common geometry data

This conversion system provides robust, high-fidelity translation between DWG/DXF and DGN formats while maintaining performance and extensibility for custom entity types.

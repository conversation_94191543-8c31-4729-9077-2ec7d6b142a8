/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdWipeout.cpp $
|
|  $Copyright: (c) 2014 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/


// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtWipeout : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbWipeout*        wipeout = AcDbWipeout::cast (acObject);
    AcGePoint3dArray    clipArray;
    wipeout->getVertices(clipArray);

    int                 nPoints = clipArray.length();
    DPoint3dP           pPoints = (DPoint3d *) _alloca (sizeof (DPoint3d) * nPoints);

    for (int iPoint=0; iPoint<nPoints; iPoint++)
        RealDwgUtil::DPoint3dFromGePoint3d (pPoints[iPoint], clipArray[iPoint]);

    RealDwgStatus       status = context.CreateElementFromVertices (outElement, pPoints, nPoints, true, context.GetTransformToDGN());
    if (RealDwgSuccess != status)
        return  status;

    IAreaFillPropertiesEdit* areaObj = dynamic_cast <IAreaFillPropertiesEdit*> (&outElement.GetHandler());
    if (!areaObj)
        return CantFillObject;

    context.ElementHeaderFromEntity (outElement, wipeout);

    // set the fill color
    UInt32              fillColor = DWG_COLOR_Background255;
    bool                alwaysFill = true;
    if (!areaObj->AddSolidFill(outElement, &fillColor, &alwaysFill))
        return CantFillObject;

    // default wipeout frame color to be the same as background (i.e. frameless), unless WIPEOUT/FRAMES is set to on with a different effective color.
    UInt32              frameColor = fillColor;
    if (context.DisplayWipeoutFrames())
        {
        if (IsFrameColorSameAsBackgroundColor(wipeout, context))
            frameColor = context.GetRemappedForegroundColorIndex ();
        else
            return  RealDwgSuccess; // keep the frame color set by ElementHeaderFromEntity
        }

    // set the frame color, frameless or outlined in our remapped color.
    ElementPropertiesSetter propertySetter;
    propertySetter.SetColor (frameColor);

    propertySetter.Apply (outElement);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/07
+---------------+---------------+---------------+---------------+---------------+------*/
bool                IsFrameColorSameAsBackgroundColor
(
AcDbWipeout*            wipeout,
ConvertToDgnContextR    context
) const
    {
    /*-----------------------------------------------------------------------------------
    Since we use 255 for background color, white foreground color lost its own color index
    and turned to be the same as background color.  Use remapped 255 index for frame color
    to workaround this short handed problem.
    We need to find the effective DGN color including BYLEVEL.  For BYBLOCK, the effective
    colors are by shared cell instances.  There may not be a unique color to use.
    -----------------------------------------------------------------------------------*/
    UInt32          frameColor = context.GetDgnColor (wipeout->entityColor());

    if (COLOR_BYLEVEL == frameColor)
        {
        AcDbLayerTableRecordPointer layer (wipeout->layerId(), AcDb::kForRead);
        if (Acad::eOk == layer.openStatus())
            frameColor = context.GetDgnColor (layer->entityColor());
        }

    return  0 == frameColor || DWG_COLOR_Default == frameColor || DWG_COLOR_Background255 == frameColor;
    }

};  // ToDgnExtWipout


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SetAcDbWipeoutFromPoints (AcDbWipeout* acWipeout, DPoint3dP points, size_t numPoints)
    {
    if (NULL == acWipeout || NULL == points || numPoints < 3)
        return  CantCreateWipeout;

    // remove the closure point if any
    if (points[0].IsEqual(points[numPoints-1], TOLERANCE_UORPointEqual))
        numPoints--;
    if (numPoints < 3)
        return  CantCreateWipeout;

    RotMatrix       matrix, inverseMatrix;
    AcGePoint3d     geOrigin;
    AcGeVector3d    geU, geV;
    DPoint3d        normalPoint;
    DVec3d          u, v, z, normal;

    bsiGeom_planeThroughPointsTol (&normal, &normalPoint, points, (int)numPoints, 1.0E-6);

    acWipeout->getOrientation (geOrigin, geU, geV);
    z.NormalizedCrossProduct (RealDwgUtil::DVec3dFromGeVector3d(u, geU), RealDwgUtil::DVec3dFromGeVector3d(v, geV));

    normal.Normalize();
    if (normal.DotProduct (z) < 0.0)
        normal.Scale (-1.0);

    if (!z.IsEqual (normal, 1.0E-8))
        {
        matrix.InitFrom1Vector (normal, 2, false);
        matrix.GetColumns (u, v, z);
        }
    else
        {
        u.Normalize();
        v.Normalize();
        matrix.InitFrom2Vectors (u, v);
        }
    inverseMatrix.InverseOf (matrix);
    inverseMatrix.Multiply (points, points, (int)numPoints);

    DRange3d        range;
    range.InitFrom (points, (int) numPoints);

    DPoint3d        delta;
    delta.DifferenceOf (range.high, range.low);

    double          maxSize = delta.x > delta.y ? delta.x : delta.y;
    DPoint2d        origin;

    origin.x = range.low.x + maxSize/2.0;
    origin.y = range.low.y + maxSize/2.0;
    u.Scale (maxSize);
    v.Scale (maxSize);

    AcGePoint2dArray    clipPoints;
    for (size_t iPoint = 0; iPoint < numPoints; iPoint++)
        {
        AcGePoint2d     newPoint;

        newPoint.x = (points[iPoint].x - origin.x) / maxSize;
        newPoint.y = (origin.y - points[iPoint].y) / maxSize;
        clipPoints.append (newPoint);
        }

    // make sure points to form a closed loop
    if (clipPoints.first() != clipPoints.last())
        clipPoints.append (clipPoints[0]);

    matrix.Multiply (range.low);

    acWipeout->setDisplayOpt (AcDbRasterImage::kClip, true);
    if (Acad::eOk != acWipeout->setClipBoundary(AcDbRasterImage::kPoly, clipPoints))
        {
        /*-----------------------------------------------------------------------------------------------------------------------------
        Bail out in the event of Wipeout failed to set valid clipping points, as a case in TR 313521 due to likely too tight tolerance.
        -----------------------------------------------------------------------------------------------------------------------------*/
        const AcGePoint2dArray  savedPoints = acWipeout->clipBoundary ();
        if (savedPoints.isEmpty())
            return  CantCreateWipeout;
        }
    acWipeout->setOrientation (RealDwgUtil::GePoint3dFromDPoint3d (range.low), RealDwgUtil::GeVector3dFromDVec3d (u), RealDwgUtil::GeVector3dFromDVec3d (v));

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::AddWipeoutEntity (ElementHandleCR inElement)
    {
    DPoint3dArray   points;
    switch (inElement.GetElementType())
        {
        case ARC_ELM:
        case ELLIPSE_ELM:
            this->StrokeClosedArcToPoints (points, 100, inElement);
            break;
        case SHAPE_ELM:
        case CMPLX_SHAPE_ELM:
            {
            MSElementCP     elem = inElement.GetElementCP ();
            int             numPoints = LineStringUtil::GetCount (*elem);
            if (numPoints < 3)
                return  CantExtractPoints;
            
            points.resize (numPoints);
            LineStringUtil::Extract (&points.front(), NULL, *elem);
            break;
            }
        default:
            return  WrongMstnElementType;
        }

    AcDbWipeout*    newWipeout = new AcDbWipeout ();
    if (NULL == newWipeout)
        return  OutOfMemoryError;

    AcDbObjectId    objectId = this->AddEntityToCurrentBlock (newWipeout, 0);
    if (objectId.isNull())
        {
        delete newWipeout;
        return  CantCreateWipeout;
        }

    // transform DGN points to DWG
    this->GetTransformFromDGN().Multiply (points, points);

    RealDwgStatus   status = this->SetAcDbWipeoutFromPoints (newWipeout, &points.front(), points.size());

    if (RealDwgSuccess == status)
        {
        this->UpdateEntityPropertiesFromElement (newWipeout, inElement);

        PrioritySorter* prioritySorter = this->GetPrioritySorter ();
        if (NULL != prioritySorter)
            prioritySorter->AddElement (inElement, -1, this->ElementIdFromObjectId(objectId));

        newWipeout->close ();
        }
    else if (newWipeout->objectId().isValid())
        {
        newWipeout->erase ();
        }
    else
        {
        delete newWipeout;
        }

    return  status;    
    }

/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/tests/DwgFileOpen.h $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

struct DwgFileOpenParams
    {
    bool m_useMultiProcess;
    BeFileName m_outputDgn; //Set to nullptr or empty to not save to a dgn file
    DgnFileOpenMode m_openMode = DgnFileOpenMode::ReadWrite;
    };

struct DwgTestHost : DwgExampleHost
    {
    virtual void _FatalError (WCharCP format, ...) override;
    };

struct DwgGlobals
    {
    static BeFileName BaseDir ();
    };

struct DwgFileOpen
    {
private:
    DwgFileOpenParams m_params;
    static DwgTestHost* s_dwgExampleHost;

    void InitHosts ();

public:
    DwgFileOpen (DwgFileOpenParams const& params);

    DgnFilePtr OpenDwgFile (BeFileNameCR dwgFileName);
    };

struct DwgFileOpenParameterized : ::testing::TestWithParam<BeXmlNodeP>
    {
private:
    bool m_notifyFailure;
    static BeFileName m_reportFolder;
    BeFileName m_xmlReportFile;
    BeXmlDomPtr m_xmlDiffs;

    BeFileName ReportFolder ();

public:
    BeFileName FullFileNameFromXml (BeXmlNodeP xmlNode);
    BeFileName DgnFileToSaveSingleProcess (BeXmlNodeP xmlNode);
    BeFileName DgnFileToSaveMultiProcess (BeXmlNodeP xmlNode);
    void Compare (DgnFilePtr dgnSingle, DgnFilePtr dgnMulti, DgnECFileDiff& diffs);
    void ListenToFailure (BeFileNameCR dwgFileName, IECDiff& diffs);
    };

struct XmlParamsSingleton
    {
private:
    XmlParamsSingleton () = default;

public:
    static bvector<BeXmlNodeP> const& Params ();
    };

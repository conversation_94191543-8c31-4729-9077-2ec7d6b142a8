/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgToDgnContext.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

/*---------------------------------------------------------------------------------------
Workaround the lack of access to RealDWG's Dimension object for DXF group codes 140 and 141.
---------------------------------------------------------------------------------------*/
struct MissingDimensionFields
    {

    AcDbObject*                   m_pAcDbObject;    //parent object
    double                        m_dLowerScaleFactor;
    double                        m_dUpperScaleFactor;
    /*AcString*                   m_strScaleName;*/ //for debug only

    MissingDimensionFields(AcDbObject* pObject)
        {
        m_pAcDbObject = pObject;
        m_dLowerScaleFactor = 1.0;
        m_dUpperScaleFactor = 1.0;
        //m_strScaleName = (AcString *)"1:1";
        }
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::RemapDwgForegroundColorIndex255 (bool whiteBackground)
    {
    /*-----------------------------------------------------------------------------------
    The same color index 255 serves for two different colors: when used as an entity
    color, it is the foreground color that varies with background color, i.e. behaving
    the same as color 7.  When it is used as a wipeout filled color, it is the background
    color that always matches the background.

    Since we have chosen 255 as the background color, we have to workaround the 255 as
    the foreground color by re-defining a new index in a special color book.  One issue
    with this workaround is that the new color loses the capability as foreground color
    that can vary with background color.  Perhaps in the future we shall make our color
    system to be able to separate the meaning of color 255 for element vs for fill, just
    the way ACAD does.  Before that happens, we have to live with this workaround.
    -----------------------------------------------------------------------------------*/
    RgbColorDef     remapped;
    remapped.red = remapped.green = remapped.blue = whiteBackground ? DWG_COLOR_ForegroundRemapped1 : DWG_COLOR_ForegroundRemapped2;

    // allow user to define their own remapped color for 255:
    WString     cfgVar;
    if ( (SUCCESS == ConfigurationManager::GetVariable (cfgVar, L"MS_DWG_FOREGROUNDCOLOR255RGB")) && !cfgVar.empty() )
        {
        int         red, green, blue;
        if (swscanf (cfgVar.c_str(), L"%d,%d,%d", &red, &green, &blue) >= 3)
            {
            remapped.red = (byte)red;
            remapped.green = (byte)green;
            remapped.blue = (byte)blue;
            }
        }

    m_remappedForegroundColorIndex = DWG_COLOR_Foreground255;

    IntColorDef     intColorDef;
    intColorDef.m_int = 0;
    intColorDef.m_rgb = remapped;

    // Get or create our special foreground color index
    UInt32          elementColor = DgnColorMap::CreateElementColor (intColorDef, this->GetSpecialDwgColorBookName(), this->GetDwgForegroundColor255Name(), *this->GetFile());

    if (INVALID_COLOR != elementColor)
        m_remappedForegroundColorIndex |= (ColorUtil::GetExtendedIndexFromRawColor(elementColor) << 8);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
static double   GetFirstAnnotationScaleFromObject (AcDbObjectCP acObject, AcDbObjectContextInterface* contextInterface, AcDbDatabase* database)
    {
    double                      scaleFound = 1.0;
    AcDbObjectContextManager*   contextManager = database->objectContextManager ();

    if (NULL != contextManager)
        {
        AcDbObjectContextCollection* const  contextCollection = contextManager->contextCollection (ACDB_ANNOTATIONSCALES_COLLECTION);

        if (NULL != contextCollection)
            {
            AcDbObjectContextCollectionIterator*    pIter = contextCollection->newIterator ();

            // find the first scale supported by the object
            for (pIter->start(); !pIter->done(); pIter->next())
                {
                AcDbObjectContext*  objectContext = NULL;

                if (Acad::eOk == pIter->getContext(objectContext))
                    {
                    AcDbAnnotationScale*    annoScale = (AcDbAnnotationScale*)objectContext;
                    if (NULL != annoScale && contextInterface->hasContext(const_cast<AcDbObjectP>(acObject), *annoScale) && Acad::eOk == annoScale->getScale(scaleFound))
                        break;

                    delete objectContext;
                    }
                }
            }
        }

    return  scaleFound;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Vinit.Mukund        11/20
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ReadDimensionFieldsCallback
(
    AcDb::DxfCode               dxfGroupCode,
    const void*                 dataIn,
    void*                       dataOut
)
    {
    MissingDimensionFields*    missingData = (MissingDimensionFields*) dataOut;

    double  scaleFound = 1.0;
    if (AcDb::kDxfHardPointerId == dxfGroupCode)
        {
        AcDbObjectId    objectIdIn = AcDbObjectId((AcDbStub*) dataIn);
        if (objectIdIn.isValid())
            {
            //for debug only
            //const ACHAR* pName = objectIdIn.objectClass()->name();

            AcDbObjectPointer<AcDbObject> acObjectPointer(objectIdIn, AcDb::kForRead);

            if (Acad::eOk == acObjectPointer.openStatus())
                {
                //NEEDSWORK_VINIT - 1.No API which give the AcDbScale object. 2. Using AcDbObject Extract method , fetching the first scale object from the list
                // Won't work - AcDbScale *pScale = AcDbScale::cast(missingData.m_pAcDbObject);
                if (0 == ::_wcsicmp(objectIdIn.objectClass()->name(), L"AcDbScale"))
                    missingData->m_pAcDbObject = AcDbObject::cast(acObjectPointer); //AcDbScale
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Vinit.Mukund        11/20
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ReadAnnotationScaleFieldsCallback
(
    AcDb::DxfCode               dxfGroupCode,
    const void*                 dataIn,
    void*                       dataOut
)
    {

    //scanning the AcDbScale object

    MissingDimensionFields*    missingData = (MissingDimensionFields*) dataOut;

    if (AcDb::kDxfXReal == dxfGroupCode)
        {
        double* pLowScale = (double*) dataIn;
        missingData->m_dLowerScaleFactor = (*pLowScale);

        }
    else if (AcDb::kDxfViewBrightness == dxfGroupCode)
        {
        double* pUpScale = (double*) dataIn;
        missingData->m_dUpperScaleFactor = (*pUpScale);
        }
    /*for debug only
    else if (0x012c == dxfGroupCode)
        {
        missingData->m_strScaleName = (AcString*) dataIn;
        } */
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Vinit.Mukund      10/2020
+---------------+---------------+---------------+---------------+---------------+------*/
static double   GetFirstAnnotationScaleFromObject(AcDbObjectCP acObject)
    {

    AcDbDatabase*   dwgDatabase = acObject->database();
    double  scaleFound = 1.0;
       
    AcDbObjectId    extensionDictionaryId = acObject->extensionDictionary();

    if (!extensionDictionaryId.isNull())
        {
        AcDbObject*  pObject;
        AcDbDictionaryPointer pExtensionDictionary(extensionDictionaryId, AcDb::kForRead);

        if (Acad::eOk == pExtensionDictionary.openStatus() &&
            Acad::eOk == pExtensionDictionary->getAt(L"AcDbContextDataManager", pObject, AcDb::kForRead))
            {

            AcDbDictionary  *pContextDataMgrDictionary = AcDbDictionary::cast(pObject);

            if (NULL != pContextDataMgrDictionary &&
                Acad::eOk == pContextDataMgrDictionary->getAt(L"ACDB_ANNOTATIONSCALES", pObject, AcDb::kForRead))
                {
                AcDbDictionary  *pAnnotationScaleDictionary = AcDbDictionary::cast(pObject);

                if (NULL == pAnnotationScaleDictionary)
                    return  scaleFound;

                for (AcDbDictionaryIterator* pIterator = pAnnotationScaleDictionary->newIterator(); !pIterator->done(); pIterator->next())
                    {

                    //fetching only first Annotation Scale from the object scale list.

                    AcDbObjectId    objectID = pIterator->objectId();

                    AcDbObjectPointer<AcDbObject> acObjectPointer(objectID, AcDb::kForRead);
                    if (Acad::eOk == acObjectPointer.openStatus())
                        {
                        
                        //for debug only.
                        //const ACHAR* pName = objectID.objectClass()->name();

                        MissingDimensionFields     missingData(nullptr);
                        ExtractionFiler         filer(ReadDimensionFieldsCallback, dwgDatabase, &missingData);
                        filer.ExtractFrom(acObjectPointer);


                        /*Limitation: ObjectARX don't support or provide any direct API to access AcDbScale object
                        so can't type cast to AcDbAnnotationScale.
                        Wont work - AcDbAnnotationScale *pScale = AcDbAnnotationScale::cast(missingData.m_pAcDbObject);*/
                        if (nullptr != missingData.m_pAcDbObject)
                            {
                            ExtractionFiler         extracFiler(ReadAnnotationScaleFieldsCallback, dwgDatabase, &missingData);
                            if (Acad::eOk == extracFiler.ExtractFrom(missingData.m_pAcDbObject) 
                                && missingData.m_dUpperScaleFactor > 0)
                                {
                                scaleFound = missingData.m_dLowerScaleFactor / missingData.m_dUpperScaleFactor;
                                }
                            }
                        break;
                        }
                    }
                }
            }
        }
    return  scaleFound;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Vinit.Mukund   12/20
+---------------+---------------+---------------+---------------+---------------+------*/
static bool ValidateAnnotationScale1Vs1_for_XrefObj(AcDbObjectCP acObject)
    {
    bvector <AcDbAnnotationScale *> AnnotationScaleList;

    AcDbObjectContextManager*   contextManager = acObject->database()->objectContextManager();
    AcDbObjectContextInterface* contextInterface = AcDbObjectContextInterface::cast(acObject->queryX(AcDbObjectContextInterface::desc()));

    if ((nullptr == contextManager) && (nullptr == contextInterface))
        return false;

    AcDbObjectContextCollection* const  contextCollection = contextManager->contextCollection(ACDB_ANNOTATIONSCALES_COLLECTION);

    if (nullptr == contextCollection)
        return false;

    AcDbObjectContextCollectionIterator*    pIter = contextCollection->newIterator();

    for (pIter->start(); !pIter->done(); pIter->next())
        {
        AcDbObjectContext*  objectContext = nullptr;

        if (Acad::eOk == pIter->getContext(objectContext))
            {
            AcDbAnnotationScale*    annoScale = (AcDbAnnotationScale*) objectContext;
            if (nullptr != annoScale && contextInterface->hasContext(const_cast<AcDbObjectP>(acObject), *annoScale))
                {
                AnnotationScaleList.push_back(annoScale);
                if (AnnotationScaleList.size() == 2)
                    break;
                }
            }
        else
            return false;
        }

    double outScale = 1.0;
    if (AnnotationScaleList.size() == 1 && Acad::eOk == AnnotationScaleList[0]->getScale(outScale))
        {
        if (fabs(fabs(1.0) - fabs(outScale)) < TOLERANCE_ScaleRatio)
            return true;
        }

    return false;
    }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertToDgnContext::GetDisplayedAnnotationScale (double& outScale, AcDbObjectCP acObject, bool isGetAnnoScaleFromXrefObj)
    {
    /*-----------------------------------------------------------------------------------
    This method gets an annotation scale following the ACAD rule:
        1) An annotative object supports CANNOSCALE: the effective scale is CANNOSCALE.
        2) An annotative object does not support CANNOSCALE:
            a) ANNOALLVISIBLE=1, the effective scale is the 1st scale in the supported scale list.
            b) ANNOALLVISIBLE=0, the object is not displayed.
        3) A non-annotative object always displays scale 1:1.
        4) If we are in an xRef, we have reset CANNOSCALE from master file earlier on, and:
            a) m_pModelIndexItem has been updated with the master file's CANNONSCALE
            b) An object that supports the effective CANNOSCALE behaves the same as is in master file
            c) An object that does not support the effective CANNOSCALE: turn off annotation scale.
            d) ACAD compounds xRef scale with annotation scale: a user/unit scaled attachment will have UseAnnotationScale turned off!
    -----------------------------------------------------------------------------------*/
    outScale = 1.0;

    // currently only support modelspace
    AcDbDatabase*   dwg = acObject->database ();
    if (NULL == dwg || acObject->ownerId() != acdbSymUtil()->blockModelSpaceId(dwg))
        return  false;

    AcDbAnnotativeObjectPE*         annotationPE = AcDbAnnotativeObjectPE::cast (acObject->queryX(AcDbAnnotativeObjectPE::desc()));
    AcDbObjectContextInterface*     contextInterface = AcDbObjectContextInterface::cast (acObject->queryX(AcDbObjectContextInterface::desc()));

    if (NULL != annotationPE && NULL != contextInterface && annotationPE->annotative(const_cast<AcDbObjectP>(acObject)))
        {
        
        bool bObjIsFromXref = false;
        AcDbDatabaseP   masterDwg = nullptr;
        if (DwgPlatformHost::Instance()._GetMasterDwgFile(masterDwg) && dwg != masterDwg)
            bObjIsFromXref = true;

        //Validation 1: Object from Xref and has only one scale i.e 1:1
        //Handling one special case of dwf where annotation scale of the object from xref , which has only one entry in the annotation object list. i.e 1:1
        if (bObjIsFromXref && ValidateAnnotationScale1Vs1_for_XrefObj(acObject))
              return false;

        if (bObjIsFromXref && ConfigurationManager::IsVariableDefinedAndTrue(L"_USTN_DWGSCALE_SKIPANNOTATIVEOBJECT"))
            return false;

        //Validation 2: If object has CANNOSCALE
        // if we are in an xRef, the effective CANNOSCALE has been saved in m_pModelIndexItem.
        AcDbAnnotationScale const*  cannoscale = m_pModelIndexItem->GetAnnotationScale ();

        // if the object supports CANNOSCALE, display it at the scale
        if (NULL != cannoscale && contextInterface->hasContext(acObject, *cannoscale))
            {
            if (Acad::eOk == cannoscale->getScale(outScale) && outScale > TOLERANCE_ZeroScale)
                outScale = 1.0 / outScale;
            else
                outScale = m_modelspaceAnnotationScale;

            return  true;
            }

        /*Validation 3 is not needed DgnAttachment address the it in ViewContext::IsDWGModelSpaceAttachment()*/
        //Validation 3: If object is from Xref
        // this object does not support CANNOSCALE - disable annotation scale if we are in an xRef - TFS 330531:
        
            //if (bObjIsFromXref)
            //    return  false;
    
        // if all scales are allowed, display it at 1 or at the first scale in the list if 1:1 does not exist:
        if (dwg->annoAllVisible() || annotationPE->forceAnnoAllVisible(const_cast<AcDbObjectP>(acObject)))
            {
            AcDbAnnotationScale const*  annoScale1v1 = this->GetAnnotationScale1vs1 ();
            if (NULL != annoScale1v1 && contextInterface->hasContext(acObject, *annoScale1v1))
                return  true;

            /*Replaced with new function, old function only support the first annoation scale where as new function is returning based on annoation object list which stored in object.
            old- GetFirstAnnotationScaleFromObject (AcDbObjectCP acObject, AcDbObjectContextInterface* contextInterface, AcDbDatabase* database)*
            Limitation- ACAD don't have API which directly return the annotation scale.*/
           
            outScale = GetFirstAnnotationScaleFromObject(acObject);

            if (outScale > TOLERANCE_ZeroScale)
                outScale = 1.0 / outScale;
            else
                outScale = 1.0;

            return  true;
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
static double   GetCANNOSCALE (AcDbDatabase* database)
    {
    AcDbAnnotationScale*    cannoscale = database->cannoscale ();
    double                  scale = 1.0;
    if (NULL != cannoscale && Acad::eOk == cannoscale->getScale(scale) && scale > TOLERANCE_ZeroScale)
        scale = 1.0 / scale;

    if (NULL != cannoscale)
        delete cannoscale;

    return  scale;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
ConvertToDgnContext::ConvertToDgnContext
(
DgnModelP                   seedModel,
FileHolder*                 pFileHolder,
RealDwgModelIndexItem*      pModelIndexItem,
AcDbProgressMeterP          progressMeter,
IDwgConversionSettings&     settings
) : ConvertContext (settings)
    {
    m_nextReferenceFileNumber       = 1;
    m_progressMeter                 = progressMeter;
    m_createUnnamedGroups           = -1;
    m_displayWipeoutFrames          = -1;
    m_inSharedCellCreation          = false;
    m_nextGraphicGroup              = 0;
    m_lightSourceFound              = false;
    m_defaultDisplayStyleIndex      = -1;
    m_orphanTagsetBlockList         = NULL;
    m_plotSettingsDictionaryId      = AcDbObjectId::kNull;
    m_noSaveToDgnCache              = false;
    m_customObjectViewDirection     = AcGeVector3d(1.0, 1.0, 1.0);
    m_customObjectCameraUp          = AcGeVector3d::kYAxis;
    m_customObjectVisualStyleId     = AcDbObjectId::kNull;
    m_customObjectIsolines          = 4;
    m_unloadedRasterImageDefs       = AcDbObjectIdArray(0, 8);
    m_invisibleBlocks               = AcDbObjectIdArray(0, 8);
    m_hiddenLayers                  = AcDbObjectIdArray (0, 8);
    m_upSeqOptionValue              = GetUpdateSequenceConfigOptions();

    auto inputDwg = pFileHolder->GetDatabase ();

    DgnFileP        file                = seedModel->GetDgnFileP();
    UInt64          nextDgnId           = file->GetHighestID () + 1;
    UInt64          nextDatabaseId      = RealDwgUtil::CastDBHandle (inputDwg->handseed());

    m_fileTime                      = 0.0;
    const ACHAR* fileName;
    FILETIME     lastModified;
    if ( (Acad::eOk == inputDwg->getFilename (fileName)) && (BSISUCCESS == RealDwgUtil::GetFileTimeStamps (NULL, NULL, &lastModified, fileName)) )
        m_fileTime = osTime_cvtNTFileTimeToMillis ((((Int64) lastModified.dwHighDateTime) << 32) + lastModified.dwLowDateTime);

    /*-------------------------------------------------------------------------------------------------------------------------
    Make sure that any elements that are created without specifying an element ID are created with an ElementID higher than any 
    we will encounter in the DWG file.  We also want to reserve some additional ID's as RealDWG sometimes can increase handseed
    after file loading.  When this happens it usually only increase by a few ID's.  In rare events of larger handseed increases
    we will remap elements in ShiftOverlappedElementIds at the post process.  By reserving ID's here we can improve file open 
    performance for most ID shifting cases.
    -------------------------------------------------------------------------------------------------------------------------*/
    m_highestIdOffset = 1000;
    WString cfgvar;
    if ( (SUCCESS == ConfigurationManager::GetVariable (cfgvar, L"MS_DWG_HIGHIESTID_OFFSET")) && !cfgvar.empty() )
        swscanf (cfgvar.c_str(), L"%d", &m_highestIdOffset);

    nextDatabaseId += m_highestIdOffset;

    if (nextDatabaseId > nextDgnId)
        {
        file->SetHighestID (nextDatabaseId);
        m_nextAvailableId   = nextDatabaseId;
        }
    else
        {
        m_nextAvailableId   = nextDgnId;
        }

    m_handseedBeforeCacheLoad = nextDatabaseId;
    m_exportActiveSettings = file->InReopenAfterSave();

    // initialize the context using the already-set storage units.
    this->Initialize (seedModel, pFileHolder, pModelIndexItem, DWGOpenUnitMode_StorageUnits);

    // if we are in an xref, override annotation scale from the master file:
    AcDbDatabase*   masterDwg = nullptr;
    if (!DwgPlatformHost::Instance()._GetMasterDwgFile(masterDwg) || nullptr == masterDwg)
        masterDwg = inputDwg;
    m_modelspaceAnnotationScale = GetCANNOSCALE (masterDwg);

    int             red = 0, green = 0, blue = 0;
    this->GetSettings().GetDesignBackgroundColor (red, green, blue);

    // use adjusted color table if modelspace background is NOT the default background color(black):
    bool            useAdjustedColorTable = !ISBLACKRGB (red, green, blue);
    // white background color changes color index 255 to {179, 179, 179}
    bool            isWhiteBackgroundColor = ISWHITERGB (red, green, blue);

    // save either the default or the adjusted ACAD color table to the DgnFile
    byte const*     effectiveColors = DgnColorMap::GetRawDwgColorsP (useAdjustedColorTable);
    DgnColorMapPtr  colorTable = DgnColorMap::CreateFromRgbColors ((RgbColorDef const*)effectiveColors);

    file->SetColorMap (colorTable.get());
    
    RemapDwgForegroundColorIndex255 (isWhiteBackgroundColor);

    m_acadTransparencyDisplay = DwgPlatformHost::Instance()._GetTransparencyDisplay ();

    // use ACIS or Parasolid kernel
    m_useAcisOutForBrep = true;
    if (BSISUCCESS == ConfigurationManager::GetVariable(cfgvar, L"MS_DWG_BREP_KERNEL"))
        {
        if (::_wcsicmp(cfgvar.c_str(), L"Parasolid") == 0)
            m_useAcisOutForBrep = false;
        else if (::_wcsicmp(cfgvar.c_str(), L"ACIS") == 0)
            m_useAcisOutForBrep = true;
        }

    m_keepBrepDerivitiveAsBrep = ConfigurationManager::IsVariableDefinedAndTrue (L"MS_DWG_OEBREP_AS_BREP");
    m_dropBrepAsProxy = ConfigurationManager::IsVariableDefinedAndTrue (L"MS_DWG_BREP_AS_PROXY");
    m_missingBlockAttrdefList.clear ();
    m_itemtypeHostsForPostProcess.clear ();

    m_ignoreEmptyHyperlinks = ConfigurationManager::IsVariableDefinedAndTrue (L"MS_DWG_IGNORE_EMPTYHYPERLINK");
    m_convertPlinegenAsCurves = ConfigurationManager::IsVariableDefinedAndTrue (L"MS_DWG_PLINEGEN_ASCURVES");
    m_skipSharedCellRangeValidation = ConfigurationManager::IsVariableDefinedAndTrue (L"MS_DWG_SHAREDCELL_SKIP_RANGEVALIDATION");
    m_skipViewportDrawnCellCreation = ConfigurationManager::IsVariableDefinedAndTrue (L"MS_DWG_SKIP_VIEWPORTDRAWNCELLS");

    // default the LOD of point clouds to 3:
    m_pointCloudsLevelOfDetails = 3;
    if (BSISUCCESS == ConfigurationManager::GetVariable(cfgvar, L"MS_DWG_LEVELOFDETAILS") && !cfgvar.empty())
        {
        swscanf (cfgvar.c_str(), L"%d", &m_pointCloudsLevelOfDetails);

        // LOD must be between 1-100, but we allow LOD=0 to disable processing point clouds:
        if (m_pointCloudsLevelOfDetails < 0)
            m_pointCloudsLevelOfDetails = 0;
        else if (m_pointCloudsLevelOfDetails > 100)
            m_pointCloudsLevelOfDetails = 100;
        }

    /*-----------------------------------------------------------------------------------
    Elements created from annotative objects that have option "match orientation to layout" 
    turned on may be set to independent view, to make them display correctly in sheet models.

    Set MS_DWG_MATCHORIENTATION_TOLAYOUT to below values to control the conversion:
        0 = Never set the View Independent flag (default)
        1 = Always set the View Independent flag for an annotative object that has the option
            of "match orientation to layout" turned on.
        2 = Set the View Independent flag only if the active model is a layout in the master file,
            and active view in the modelspace is not rotated.
    -----------------------------------------------------------------------------------*/
    int cfgvarVal = 0;
    if (BSISUCCESS == ConfigurationManager::GetVariable(cfgvar, L"MS_DWG_MATCHORIENTATION_TOLAYOUT") && !cfgvar.empty())
        swscanf (cfgvar.c_str(), L"%d", &cfgvarVal);

    if (1 == cfgvarVal)
        {
        // Always set View Independent flag
        m_matchObjectOrientationToLayout = true;
        }
    else if (2 == cfgvarVal && masterDwg->tilemode() == 0)
        {
        // Option 2, and a layout is active
        m_matchObjectOrientationToLayout = true;

        // disable the option if the active modelspace view is rotated:
        AcDbViewportTableRecord*    activeVport = nullptr;
        if (RealDwgSuccess == RealDwgUtil::OpenActiveViewportTableRecord(activeVport, masterDwg))
            {
            auto zAxis = activeVport->viewDirection().normal ();
            auto twist = activeVport->viewTwist ();
            if (fabs(zAxis.z - 1) > 0.01 || fabs(twist) > 0.01)
                m_matchObjectOrientationToLayout = false;
            activeVport->close ();
            }
        }
    else
        {
        // Option 0(default), or modelspace is active
        m_matchObjectOrientationToLayout = false;
        }
    /*-----------------------------------------------------------------------------------
    ACAD does not display LineType on a 3D polyline. It confuses user. Certain users expect LineTypes to be preserved.
    This Configuration Variable is introduced to resolve the conflicting requirements from different users.
    Set MS_DWG_PRESERVE_LINETYPE_ON_3DPOLYLINE to:
    true  = To Preserve the LineType on 3D Polylines
    false = To force LineType to 0 on 3D Polylines
    If the variable MS_DWG_PRESERVE_LINETYPE_ON_3DPOLYLINE isn't defined, LineType will be forced to 0.
    -----------------------------------------------------------------------------------*/
    m_preserveLinetypeOf3dPolyline = ConfigurationManager::IsVariableDefinedAndTrue(L"MS_DWG_PRESERVE_LINETYPE_ON_3DPOLYLINE");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/19
+---------------+---------------+---------------+---------------+---------------+------*/
bool    ConvertToDgnContext::IsOrientationMatchedToLayout (AcDbObjectP acEntity) const
    {
    if (m_matchObjectOrientationToLayout)
        return  RealDwgUtil::IsOrientationMatchedToLayout (acEntity);

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/00
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::ValidatePoints (DPoint3dP pPoint, size_t nPoints)
    {
    if (!this->ArePointsValid(pPoint, nPoints))
        {
        WChar    errorMessage[1024];
        swprintf (errorMessage, L"Point Off Design Plane : (%g,%g,%g)", pPoint->x, pPoint->y, pPoint->z);
        throw DiscardInvalidEntityException (errorMessage);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertToDgnContext::ArePointsValid (DPoint3dP pPoint, size_t nPoints)
    {
    for (const DPoint3d* pEnd = pPoint + nPoints; pPoint < pEnd; pPoint++)
        {
        bool    is3D = true;
        if ((pPoint->z < RMINDESIGNRANGE || pPoint->z > RMAXDESIGNRANGE) && this->GetSettings().OpenModelSpaceAs2d())
            {
            pPoint->z = 0.0;
            is3D = false;
            }

        // Sanity check - TR# 129088
        if (RealDwgUtil::IsPointOffDesignPlane(pPoint, is3D))
            {
            return  false;
            }
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/00
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SaveXRefBlockDisplayOverridesToDgn (ConvertToDgnContext& dgnContext)
    {
    AcDbBlockTablePointer           pBlocks  (dgnContext.GetDatabase()->blockTableId(), AcDb::kForRead);
    if (Acad::eOk != pBlocks.openStatus())
        return;

    AcDbBlockTableIterator*         pBlkIter;
    if (Acad::eOk != pBlocks->newIterator (pBlkIter))
        return;

    for (pBlkIter->start(); ! pBlkIter->done(); pBlkIter->step())
        {
        AcDbObjectId    blockId;
        if (Acad::eOk != pBlkIter->getRecordId (blockId))
            continue;

        AcDbBlockTableRecordPointer pBlock (blockId, AcDb::kForRead);

        if (Acad::eOk == pBlock.openStatus() && !pBlock->isLayout() && pBlock->isFromExternalReference())
            {
            ACHAR const *name;
            pBlock->getName (name);
            mdlRefFile_setDwgBlockDisplayOverride (dgnContext.GetFile(), name, !pBlock->isUnloaded());
            }
        }
    delete pBlkIter;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt ConvertToDgnContext::SaveDictionaryObjectsToDgn ()
    {
    AcDbDatabase*                       database = m_pFileHolder->GetDatabase();
    DgnModelP                           currentModel = this->GetModel ();
    DimStyleDgnCacheLoaderCollection    dimStyleCollection;

    this->SetModel (&this->GetFile()->GetDictionaryModel());

    this->SaveHeaderElementToDgn ();
    this->SaveDictionaryApplicationElementsToDgn ();
    this->SaveDwgTextStylesToDgn ();    // Must precede fonts.
    this->SaveDwgLinetypesToDgn ();     // Must precede layers.
    this->SaveColorBookTableToDgn ();
    this->SaveDwgLayersToDgn ();
    this->SaveLayerFiltersToDgn();
    // Moved UCS table to model filling process as we want to save ACS elements into desired models.
    this->SaveViewGroupsToDgn ();
    this->SaveDwgMultilineStylesToDgn ();
    this->SaveViewTableToDgn ();
    this->SaveDwgDimensionStylesToDgn (dimStyleCollection);
    this->SaveDwgMultileaderStylesToDgn (dimStyleCollection);

    dimStyleCollection.AddToFile (*GetFile());

    // won't save but will collect some data from DGN underlay definitions
    this->CollectDgnUnderlayDefinitionInfo ();

    // Don't need to go any further if we just want to repopulate FileHolder's index tables:
    if (m_noSaveToDgnCache)
        {
        this->SetModel (currentModel);
        return  SUCCESS;
        }

    this->SaveRegisteredApplicationsToDgn ();
    this->SaveColorTableToDgn ();

    if (this->GetSettings().CreateDGNMaterials())
        this->SaveMaterialsToDgn ();
    if (this->GetSettings().CreateDGNLights())
        this->SaveGlobalLightsFromActiveVportToDgn ();

    this->LoadDictionary (database->namedObjectsDictionaryId());
    this->UpdateHeaderElement ();

    SaveXRefBlockDisplayOverridesToDgn (*this);

    this->SetModel (currentModel);

    //If there is a GCS, load it into currentModel
    this->SaveGeoDataToDgn();

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SetModelInfoFromViewParameters
(
ModelInfoP                  modelInfo,
double                      gridDistanceX,
double                      gridDistanceY,
double                      snapDistanceX,
double                      snapDistanceY,
const AcGePoint2d&          viewSnapBase,
double                      snapRotation,
bool                        unitLock,
bool                        isoGrid,
int                         isoPair,
int                         gridMajor,
AcGePoint3d&                ucsOrigin,
AcGeVector3d&               ucsXDirection,
AcGeVector3d&               ucsYDirection,
double                      scaleToDGN
)
    {
    // AutoCAD seems to allow 0 GRIDUNIT to imply SNAPUNIT
    if (0.0 == gridDistanceX)
        gridDistanceX = snapDistanceX;

    if (0.0 == gridDistanceY)
        gridDistanceY = snapDistanceY;

    double      uorPerGrid          = gridDistanceX * scaleToDGN;
    double      gridRatio           = (0.0 == gridDistanceX) ? 1.0 : gridDistanceY / gridDistanceX;
    double      roundoffUnit        = snapDistanceX * scaleToDGN;
    double      roundoffRatio       = (0.0 == snapDistanceX) ? 1.0 : snapDistanceY / snapDistanceX;
    UInt32      refGrid             = gridMajor;

    DPoint2d    gridBase            = { viewSnapBase.x * scaleToDGN, viewSnapBase.y * scaleToDGN };

    IsoPlaneValues  isoPlaneRemap[3] = {IsoPlaneLeft, IsoPlaneTop, IsoPlaneRight};

    modelInfo->SetGridParams (&uorPerGrid, &refGrid, &gridRatio, &gridBase, &snapRotation);
    modelInfo->SetRoundoffUnit (&roundoffUnit, &roundoffRatio);
    modelInfo->SetIsUnitLocked (unitLock);
    modelInfo->SetIsoGrid (isoGrid);
    modelInfo->SetIsoPlane (isoPlaneRemap[isoPair]);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SetModelUnitsFromDwg (ModelInfoP modelInfo, AcDbDatabaseP dwg)
    {
    if (nullptr == modelInfo || nullptr == dwg)
        {
        BeAssert (false && L"Must pass in ModelInfo and AcDbDatabase!");
        return;
        }

    DwgLinearUnits  linearUnits = static_cast <DwgLinearUnits> (dwg->lunits());

    if (DWGLinearUnit_Engineering == linearUnits || DWGLinearUnit_Architectural == linearUnits)
        modelInfo->SetLinearUnitMode (DgnUnitFormat::MUSU);
    else
        modelInfo->SetLinearUnitMode (DgnUnitFormat::MU);

    // Set the linear precision
    int         linearPrecision = dwg->luprec();
    if (linearPrecision < 0)
        linearPrecision = 0;
    else if (linearPrecision > 8)
        linearPrecision = 8;

    byte            precisionByte = 4;
    PrecisionType   precisionType = PrecisionType::Decimal;

    switch (linearUnits)
        {
        case DWGLinearUnit_Scientific:
            precisionType = PrecisionType::Scientific;
            precisionByte = (byte) linearPrecision;
            break;

        case DWGLinearUnit_Decimal:
        case DWGLinearUnit_Engineering:
            precisionType = PrecisionType::Decimal;
            precisionByte = (byte) linearPrecision;
            break;

        case DWGLinearUnit_Fractional:
        case DWGLinearUnit_Architectural:
            precisionType = PrecisionType::Fractional;
            precisionByte = (byte) ((1 << linearPrecision) - 1);
            break;
        }

    modelInfo->SetLinearPrecision (DoubleFormatter::ToPrecisionEnum (precisionType, precisionByte));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SetModelInfoForModel (ModelInfoP modelInfo, bool isModelspace, AcDbDatabase* database)
    {
    if (isModelspace)
        {
        // modelspace annotation scale is currently supported
        modelInfo->SetIsUseAnnotationScaleOn (true);
        // MSLTSCALE specifies whether or not modelspace is applied by an annotation scale
        modelInfo->SetLineStyleScaleMode (database->msltscale() ? ModelInfo::LSSCALEMODE_CompoundScale : ModelInfo::LSSCALEMODE_LineStyleScale);
        // ANNOTATIVEDWG specifies whether or not the DWG file will behave as an annotative block when inserted into another file
        modelInfo->SetIsAnnotationCell (database->annotativeDwg());
        modelInfo->SetIsInCellList (true);

        AcDbAnnotationScale*    cannoscale = database->cannoscale ();
        if (NULL != cannoscale)
            {
            double              annoScale = 1.0;
            if (Acad::eOk == cannoscale->getScale(annoScale) && annoScale > TOLERANCE_AnnotationScale)
                modelInfo->SetAnnotationScaleFactor (1.0 / annoScale);

            delete cannoscale;
            }

        SetModelUnitsFromDwg (modelInfo, database);
        }
    else
        {
        // paperspace annotation scale is currently not supported
        modelInfo->SetIsUseAnnotationScaleOn (false);
        // PSLTSCALE is not specific to annotation scale, so always only use global linestyle scale
        modelInfo->SetLineStyleScaleMode (ModelInfo::LSSCALEMODE_LineStyleScale);
        modelInfo->SetIsAnnotationCell (false);
        // don't show layouts in cell list
        modelInfo->SetIsInCellList (false);
        }

    // ltscale is a global linestyle scale across all models
    modelInfo->SetLineStyleScale (database->ltscale());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ConvertToDgnContext::SetSheetInfoFromLayout (ModelInfoP modelInfo, AcDbLayout* pLayout)
    {
#if defined (REALDWG_FILER_DEBUG)
    RecordingFiler      filer (100);
    filer.RecordData (AcDbPlotSettings::cast(pLayout));
    filer.DumpList ("AcDbPlotSettings from DWGOUT filing:");
#endif

    DPoint2d            sheetOrigin, sheetSize, minMargin, maxMargin;
    double              rotation, scale;
    UnitDefinition      units;
    RealDwgStatus       status;

    if (RealDwgSuccess == (status = this->CalculateSheetPropertiesFromLayout(sheetOrigin, sheetSize, scale, rotation, units, minMargin, maxMargin, pLayout)))
        {
        SheetDefP       sheetDef = modelInfo->GetSheetDefP ();
        if (NULL == sheetDef)
            return  BadModel;

        // ACAD always sets annotation scale of 1:1 in the overall viewport.
        modelInfo->SetAnnotationScaleFactor (1.0);

        // Set sheet parameters
        sheetDef->SetOrigin (sheetOrigin);
        sheetDef->SetSize (sheetSize.x, sheetSize.y);
        sheetDef->SetUnits (units);
        sheetDef->SetRotation (0.0);

        // set plot scale and rotation for printer and round trip purposes:
        sheetDef->SetPlotScaleFactor (scale);
        sheetDef->SetDWGPaperOrientation (rotation);

        // set paper margins
        sheetDef->SetMargins (minMargin.y, minMargin.x, maxMargin.y, maxMargin.x);

        const ACHAR*    canonicalMediaName = nullptr;
        WString         formName;
        if (Acad::eOk == pLayout->getCanonicalMediaName(canonicalMediaName) && nullptr != canonicalMediaName)
            {
            formName = WString (canonicalMediaName);
            formName.ReplaceAll (L"_", L" ");
            }
        sheetDef->SetFormName (formName.GetWCharCP());

        if (pLayout->plotPlotStyles ())
            {
            const ACHAR* currentStyleSheet;
            pLayout->getCurrentStyleSheet (currentStyleSheet);
            sheetDef->SetPlotStyleTableFileName (currentStyleSheet);
            }
        else
            {
            sheetDef->SetPlotStyleTableFileName (0);
            }

        AcDbPlotSettings*   plotSettings = AcDbPlotSettings::cast (pLayout);
        if (NULL != plotSettings)
            {
            const ACHAR*    activePageSetupName = NULL;

            if (Acad::eOk == plotSettings->getPlotSettingsName(activePageSetupName) && NULL != activePageSetupName && activePageSetupName[0])
                sheetDef->SetSheetName (activePageSetupName);
            }

        sheetDef->Enable (true);

        // save sheet def to modelInfo
        modelInfo->SetSheetDef (*sheetDef);
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/2003
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::SaveModelInfoToDgn ()
    {
    ModelInfoPtr    modelInfo = m_model->GetModelInfo().MakeCopy ();
    if (!modelInfo.IsValid())
        return  OutOfMemoryError;

    AcGePoint3d     ucsOrigin;
    AcGeVector3d    ucsXAxis, ucsYAxis;
    MSElement       element;
    Transform       transform = this->GetTransformFromDGN();

    // Set the DGN transform on the model element - this is used when transforming xData.
    modelInfo->GetLinkageHolderElement (element);
    linkage_appendDoubleArrayLinkage (&element, DOUBLEARRAY_LINKAGE_KEY_DwgTransform, 12, &transform.form3d[0][0]);
    modelInfo->SetLinkageHolderElement (element);

    switch (m_pModelIndexItem->GetRealDwgModelType())
        {
        case RDWGMODEL_TYPE_DefaultModel:
            {
            AcDbDatabase*               database = this->GetFileHolder().GetDatabase ();
            AcDbViewportTableRecord*    pViewport = NULL;
            if (RealDwgSuccess == RealDwgUtil::OpenActiveViewportTableRecord(pViewport, database))
                {
                if (pViewport->isUcsSavedWithViewport())
                    {
                    pViewport->getUcs (ucsOrigin, ucsXAxis, ucsYAxis);
                    }
                else
                    {
                    ucsXAxis  = database->ucsxdir();
                    ucsYAxis  = database->ucsydir();
                    ucsOrigin = database->ucsorg();
                    }

                SetModelInfoFromViewParameters (modelInfo.get(),
                                                pViewport->gridIncrements().x, pViewport->gridIncrements().y,
                                                pViewport->snapIncrements().y, pViewport->snapIncrements().y,
                                                pViewport->snapBase(),
                                                pViewport->snapAngle(),
                                                pViewport->snapEnabled(),                               // Snap mode => unit lock.
                                                pViewport->isometricSnapEnabled(),
                                                pViewport->snapPair(),
                                                pViewport->gridMajor(),
                                                ucsOrigin, ucsXAxis, ucsYAxis,
                                                this->GetScaleToDGN());
                pViewport->close ();
                }

            SetModelInfoForModel (modelInfo.get(), true, database);

            break;
            }

        case RDWGMODEL_TYPE_Sheet:
            {
            AcDbDatabase*               database = this->GetFileHolder().GetDatabase ();
            AcDbBlockTableRecordPointer pBlock (m_pModelIndexItem->GetBlockTableRecordId(), AcDb::kForRead);
            AcDbObjectId                layoutId;
            if (Acad::eOk == pBlock.openStatus() && !(layoutId = pBlock->getLayoutId()).isNull())
                {
                AcDbLayoutPointer   pLayout (layoutId, AcDb::kForRead);
                if (Acad::eOk != pLayout.openStatus())
                    break;

                this->SetSheetInfoFromLayout (modelInfo.get(), pLayout);
                AcDbObjectIdArray   viewports = pLayout->getViewportArray();

                // apply the view params from the overall viewport(always the 1st viewport in the list) for sheet model
                AcDbObjectId        viewportId;
                if ( (viewports.length() > 0) && !(viewportId = viewports[0]).isNull())
                    {
                    AcDbViewportPointer pViewport (viewportId, AcDb::kForRead);
                    if (Acad::eOk != pViewport.openStatus())
                        break;

                    pViewport->getUcs (ucsOrigin, ucsXAxis, ucsYAxis);
                    AcGeVector2d    gridIncrement = pViewport->gridIncrement();
                    AcGeVector2d    snapIncrement = pViewport->snapIncrement();

                    SetModelInfoFromViewParameters (modelInfo.get(),
                                                    gridIncrement.x, gridIncrement.y,
                                                    snapIncrement.x, snapIncrement.y,
                                                    pViewport->snapBasePoint(),
                                                    pViewport->snapAngle(),
                                                    pViewport->isSnapOn(),                          // Snap mode => unit lock.
                                                    pViewport->isSnapIsometric(),
                                                    pViewport->snapIsoPair(),
                                                    pViewport->gridMajor(),
                                                    ucsOrigin, ucsXAxis, ucsYAxis,
                                                    this->GetScaleToDGN());
                    }
                }

            SetModelInfoForModel (modelInfo.get(), false, database);

            // copy units from default model info
            RealDwgModelIndexItem*  defaultModelIndexItem = this->GetFileHolder().GetDefaultModelItem ();
            if (NULL != defaultModelIndexItem)
                {
                ModelInfoCR         defaultModelInfo = defaultModelIndexItem->GetModelInfo ();
                modelInfo->SetLinearUnitMode (defaultModelInfo.GetLinearUnitMode());
                modelInfo->SetLinearPrecision (defaultModelInfo.GetLinearPrecision());
                modelInfo->SetDirectionMode (defaultModelInfo.GetDirectionMode());
                modelInfo->SetDirectionClockwise (defaultModelInfo.GetDirectionClockwise());
                modelInfo->SetDirectionBaseDir (defaultModelInfo.GetDirectionBaseDir());
                modelInfo->SetAngularMode (defaultModelInfo.GetAngularMode());
                modelInfo->SetAngularPrecision (defaultModelInfo.GetAngularPrecision());
                }
            else
                {
                DIAGNOSTIC_PRINTF ("Error finding the default model!\n");
                }

            break;
            }
        }

    AcDbObjectId    blockId;
    if (! (blockId = m_pModelIndexItem->GetBlockTableRecordId()).isNull())
        {
        modelInfo->GetLinkageHolderElement(element);

        AcDbBlockTableRecordPointer pBlock(blockId, AcDb::kForRead);
        if (Acad::eOk == pBlock.openStatus())
            {
            EditElementHandle   eeh(&element, m_model);
            if (SUCCESS == this->ExtractLinkagesAndGraphicGroupFromEntityXData(eeh, pBlock))
                modelInfo->SetLinkageHolderElement(*eeh.GetElementP());
            }
        }

    // update model & model index item with the above converted model info
    m_model->SetModelInfo (*modelInfo);
    m_model->SaveModelInfo (true);
    m_pModelIndexItem->SetModelInfo (modelInfo.get());

    return SUCCESS;
    }

void                        ConvertToDgnContext::OnPreLoadObjectIntoCache (const AcDbObjectId& entityId)
    {
    _OnPreLoadObjectIntoCache (entityId);
    }

void                        ConvertToDgnContext::_OnPreLoadObjectIntoCache (const AcDbObjectId&)
    {
    //empty on purpose
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                                   Sandip Ichake    12/2020
//---------------------------------------------------------------------------------------
int    ConvertToDgnContext::GetUpdateSequenceConfigOptions()
    {
    //Config variable to decide draw order of master file
    //0 or not set = default
    //1 = master file is the first
    //2 = master file is the last

    WString value;
    if (ConfigurationManager::IsVariableDefined(L"MS_DWG_UPDATESEQUENCEOPTION"))
        if (SUCCESS == ConfigurationManager::GetVariable(value, L"MS_DWG_UPDATESEQUENCEOPTION"))
            return BeStringUtilities::Wtoi(value.c_str());

    return 0;
    }

//---------------------------------------------------------------------------------------
// @bsimethod                                                   Sandip Ichake    12/2020
//---------------------------------------------------------------------------------------
int ConvertToDgnContext::GetMasterFilePos(UpdateSequenceElm* pUpdateSequenceElm)
    {
    UInt32 masterFilePos;
    for (masterFilePos = 0; masterFilePos < pUpdateSequenceElm->numFiles; masterFilePos++)
        {
        if (0 == pUpdateSequenceElm->attachmentId[masterFilePos])
            break;
        }
    return masterFilePos;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::SaveEntityToDgn
(
UpdateSequenceElm*          pUpdateSequenceElm,
AcDbObjectIdArray&          imageSequence,
bool&                       isFirstNonXRef,
bool&                       isFirstNonImage,
AcDbObjectId&               entityId
)
    {
    AcDbEntity*         pEntity = NULL;
    Acad::ErrorStatus   es = acdbOpenObject (pEntity, entityId, AcDb::kForRead);
    if (Acad::eOk != es)
        {
#ifdef REALDWG_DIAGNOSTICS
        printf ("Failed opening entity ID=%I64d. [%ls]\n", entityId.handle(), acadErrorStatusText(es));
#endif
        return  BSIERROR;
        }

    // don't save entities on hidden layers for now (dims for constraints for exmaple):
    int             foundAt = -1;
    if (m_hiddenLayers.find(pEntity->layerId(), foundAt) && foundAt >= 0)
        {
        pEntity->close ();
        return  BSISUCCESS;
        }

    bool            isXRef = 0 != this->ReferenceAttachmentIdFromEntity (pEntity);

    if (pEntity->isKindOf (AcDbRasterImage::desc()))
        {
        imageSequence.append (entityId);
        }
    else
        {
        if (isFirstNonImage)
            {
            isFirstNonImage = false;
            AcDbObjectId    nullId;
            imageSequence.append (nullId);
            }
        }

    if (pUpdateSequenceElm->numFiles < MAX_UPDATE_SEQUENCE_COUNT)
        {
        if (isXRef)
            {
            pUpdateSequenceElm->attachmentId[pUpdateSequenceElm->numFiles++] = this->ElementIdFromObjectId (entityId);
            }
        else if (isFirstNonXRef && !pEntity->isKindOf (AcDbViewport::desc()))
            {
            isFirstNonXRef = false;
            pUpdateSequenceElm->attachmentId[pUpdateSequenceElm->numFiles++] = 0;
            }
        }

    RealDwgStatus   status = this->LoadObjectIntoCache (pEntity, entityId);

    pEntity->close ();

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   06/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                 ConvertToDgnContext::SaveUpdateSequenceToDgnIfNeeded (UpdateSequenceElm* pUpdateSequenceElm)
    {
    UInt32  numEntries      = pUpdateSequenceElm->numFiles;
    bool    isSheetModel    = RDWGMODEL_TYPE_Sheet == m_pModelIndexItem->GetRealDwgModelType();

    if ( (numEntries > 1) || pUpdateSequenceElm->rasterRefsLast || isSheetModel)
        {
        pUpdateSequenceElm->ehdr.type      = MICROSTATION_ELM;
        pUpdateSequenceElm->ehdr.level     = MSUPDATESEQUENCE_LEVEL;

        // In a sheet model, we need to make sure that the "master file" is last. (Even if there's a SortEnts table, the Viewport isn't in the right place in it.)
        if (isSheetModel && (numEntries > 1))
            {
            UInt32 masterFilePos;
            for (masterFilePos = 0; masterFilePos < numEntries; masterFilePos++)
                {
                if (0 == pUpdateSequenceElm->attachmentId[masterFilePos])
                    break;
                }

            if (masterFilePos >= numEntries)
                {
                // master file not found. Insert it.
                pUpdateSequenceElm->attachmentId[numEntries] = 0;
                numEntries++;
                }

            if (masterFilePos < (numEntries - 1))
                {
                memmove (&pUpdateSequenceElm->attachmentId[masterFilePos], &pUpdateSequenceElm->attachmentId[masterFilePos+1], (numEntries - masterFilePos - 1) * sizeof (ElementId));
                pUpdateSequenceElm->attachmentId[numEntries-1] = 0;
                }
            }

        if (!isSheetModel)
            {
            switch (m_upSeqOptionValue)
                {
                    case 1: // move master file to first in update sequence
                    {
                    int masterFilePos = GetMasterFilePos(pUpdateSequenceElm);
                    if (0 == masterFilePos)
                        break;
                    UInt32 numEnts = pUpdateSequenceElm->numFiles;
                    if ((numEnts > 1))
                        {
                        memmove(&pUpdateSequenceElm->attachmentId[1],
                                &pUpdateSequenceElm->attachmentId[0],
                                (masterFilePos) * sizeof(ElementId));
                        pUpdateSequenceElm->attachmentId[0] = 0;
                        }
                    break;
                    }
                    case 2: // move master file to last in update sequence
                    {
                    int masterFilePos = GetMasterFilePos(pUpdateSequenceElm);
                    int numEnts = pUpdateSequenceElm->numFiles;
                    if ((numEnts > 1) && masterFilePos < (numEnts - 1))
                        {
                        memmove(&pUpdateSequenceElm->attachmentId[masterFilePos],
                                &pUpdateSequenceElm->attachmentId[masterFilePos + 1],
                                (numEnts - masterFilePos - 1) * sizeof(ElementId));
                        pUpdateSequenceElm->attachmentId[numEntries - 1] = 0;
                        }
                    break;
                    }
                    default:
                        break;
                }
            }

        pUpdateSequenceElm->ehdr.attrOffset = pUpdateSequenceElm->ehdr.elementSize = (offsetof (UpdateSequenceElm, attachmentId) + numEntries * sizeof (ElementId)) / 2;
        this->LoadElementIntoCache ((MSElementCP) pUpdateSequenceElm);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ResetCannoscaleFromLayout (AcDbDatabase* pDwg, AcDbAnnotationScale* cannoscale, AcDbAnnotationScale* layoutAnnoScale)
    {
    /*--------------------------------------------------------------------------------------
    Apparently ACAD and RealDWG works differently when CANNOSCALE != the annotation scale of
    the first viewport in a layout: ACAD uses annotation scale from the first viewport of the
    layout whereas RealDWG uses CANNOSCALE of the modelspace.  When these two scales are not
    the same, RealDWG produces incorrect display than ACAD does!  This comes out as a surpise
    because we'd expect ACAD to be based on RealDWG and thus behaves the same.  Not sure what
    has caused this different behavior.  Whatever it is, we are left out with few choices but
    to mimic what ACAD does in order to keep display fidelity: comparing these two scales and
    force CANNOSCALE to be the same as the one used by the first viewport in layout.  After
    viewportDraw is done, we will then reset CANNOSCALE back to original scale.

    Of course, this code shall no long be needed after we have implemented ACAD's annotation
    scale technology for MicroStation.
    --------------------------------------------------------------------------------------*/
    if (NULL != cannoscale && NULL != layoutAnnoScale)
        {
        double  modelScale = 1.0, layoutScale = 1.0;
        if (Acad::eOk == cannoscale->getScale(modelScale) && Acad::eOk == layoutAnnoScale->getScale(layoutScale) && 
            fabs(modelScale - layoutScale) > TOLERANCE_ZeroScale)
            {
            // temporarily reset CANNOSCALE to be the same as layout scale:
            return  Acad::eOk == pDwg->setCannoscale (layoutAnnoScale);
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/19
+---------------+---------------+---------------+---------------+---------------+------*/
static bool IsXrefAttachedInLayoutsOnly (AcDbDatabase* xrefDwg, AcDbDatabase* masterDwg)
    {
    if (masterDwg == xrefDwg || xrefDwg == nullptr)
        return  false;

    // find and check if xrefDwg is attached only to a paperspace in the master file
    const ACHAR* path = nullptr;
    if (Acad::eOk != xrefDwg->getFilename(path))
        return  false;

    auto xrefDwgName = BeFileName::GetFileNameAndExtension (path);
    auto modelspaceId = acdbSymUtil()->blockModelSpaceId (masterDwg);

    AcDbBlockTableIterator* iter = nullptr;
    AcDbBlockTablePointer   blocks(masterDwg->blockTableId(), AcDb::kForRead);
    if (blocks.openStatus() != Acad::eOk || Acad::eOk != blocks->newIterator(iter))
        return  false;

    bool inPaperspaceOnly = false;

    for (iter->start(); !iter->done(); iter->step())
        {
        AcDbObjectId    blockId;
        if (Acad::eOk != iter->getRecordId(blockId))
            continue;

        AcDbBlockTableRecordPointer block(blockId, AcDb::kForRead);
        if (Acad::eOk == block.openStatus() && block->isFromExternalReference())
            {
            if (Acad::eOk != block->pathName(path))
                continue;

            auto xRefName = BeFileName::GetFileNameAndExtension (path);
            if (!xRefName.EqualsI(xrefDwgName))
                continue;

            bool    foundInModelspace = false;
            AcDbObjectIdArray   ids;
            if (Acad::eOk == block->getBlockReferenceIds(ids))
                {
                for (int i = 0; i < ids.length(); i++)
                    {
                    AcDbBlockReferencePointer xInsert(ids[i], AcDb::kForRead);
                    if (xInsert.openStatus() == Acad::eOk && xInsert->ownerId() == modelspaceId)
                        {
                        foundInModelspace = true;
                        break;
                        }
                    }
                }
            inPaperspaceOnly = !foundInModelspace;
            break;
            }
        }

    delete iter;

    return  inPaperspaceOnly;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/15
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbAnnotationScale*     GetEffectiveCannoscaleInXref (AcDbDatabaseP masterDwg, AcDbDatabaseP xrefDwg)
    {
    static ACHAR*   s_xrefSuffix = L"_XREF";
    static int      s_suffixLength = static_cast <int> (wcslen(s_xrefSuffix));

    // this method attemps to remap CANNOSCALE from master file to xref file by name.
    if (nullptr == masterDwg || nullptr == xrefDwg)
        return  nullptr;

    // get the annotation scale name of master file's CANNOSACLE
    AcString                cannoscaleName;
    AcDbAnnotationScale*    masterCannoscale = masterDwg->cannoscale ();
    if (nullptr != masterCannoscale && Acad::eOk == masterCannoscale->getName(cannoscaleName))
        {
        AcDbObjectContextManager*   contextManager = xrefDwg->objectContextManager ();

        // get the annotation scale collection from the xref file:
        if (nullptr != contextManager)
            {
            AcDbObjectContextCollection* const  contextCollection = contextManager->contextCollection (ACDB_ANNOTATIONSCALES_COLLECTION);

            if (nullptr != contextCollection)
                {
                // if the scale name ends with _XREF, remove the xRef suffix from it:
                int         nChars = cannoscaleName.length ();
                if (nChars > s_suffixLength)
                    {
                    AcString    endsWith = cannoscaleName.substr (nChars - s_suffixLength, s_suffixLength);
                    if (0 == endsWith.compare(s_xrefSuffix))
                        cannoscaleName = cannoscaleName.substr (nChars - endsWith.length());
                    }
                
                // find the annotation scale from the xRef's scale collection by matching the CANNOSCALE name of the master file:
                AcDbObjectContext*  xrefCannoscale = contextCollection->getContext (cannoscaleName);

                // if the xref is only attached in layouts, like TFS1050779, apply scale 1:1
                if (xrefCannoscale != nullptr && cannoscaleName.compare(L"1:1") != 0)
                    {
                    if (IsXrefAttachedInLayoutsOnly(xrefDwg, masterDwg))
                        {
                        delete xrefCannoscale;
                        xrefCannoscale = contextCollection->getContext (L"1:1");
                        }
                    }

                if (masterCannoscale != nullptr)
                    delete masterCannoscale;

                return  AcDbAnnotationScale::cast(xrefCannoscale);
                }
            }
        }

    if (masterCannoscale != nullptr)
        delete masterCannoscale;

    return  nullptr;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/15
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     ResetCannoscaleFromMasterFile (AcDbDatabaseP currDwg, RealDwgModelIndexItem* modelIndexItem)
    {
    /*------------------------------------------------------------------------------------------------------
    ACAD always applies master file's active annotation scale to xRef's.  There is no option like what we have
    in MicroStation.  All objects support master file's CANNOSCALE are scaled by compounding CANNOSCALE with
    xRef scale.  To display annotative objects through a DgnAttachment, we do the following while creating an
    xRef file:
        1) Override CANNOSCALE and ALLANNOVISIBLE of current DWG file from the master DWG file
        2) Update RealDwgModelIndexItem of the modelspace with the overrides

    Just updating RealDwgModelIndexItem is not sufficient - we really have to change CANNOSCALE because we need
    the model size of an annotative object.  This allows the object to be displayed correct through a DgnAttachment
    whose UseAnnotationScale is either toggled on or off.
    -------------------------------------------------------------------------------------------------------*/
    AcDbDatabaseP       masterDwg = nullptr;
    if (DwgPlatformHost::Instance()._GetMasterDwgFile(masterDwg) && masterDwg != currDwg)
        {
        modelIndexItem->SetAnnoAllVisible (masterDwg->annoAllVisible());

        AcDbAnnotationScale*    effectiveCannoscale = GetEffectiveCannoscaleInXref (masterDwg, currDwg);
        if (nullptr != effectiveCannoscale)
            {
            // temporarily override current xRef file's CANNOSCALE from the master file:
            if (Acad::eOk == currDwg->setCannoscale(effectiveCannoscale))
                {
                // update the model index with the new scale:
                modelIndexItem->SetAnnotationScale (effectiveCannoscale);
                return  true;
                }
            else
                {
#ifdef DEBUG
                const ACHAR*    filename = nullptr;
                if (Acad::eOk != currDwg->getFilename(filename) || nullptr == filename)
                    filename = L"???";
                printf ("Failed reseting CANNOSCALE for xRef %ls\n", filename);
#endif
                delete effectiveCannoscale;
                }
            }
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    JP.Wenger       04/2019
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::_OnPostSaveAcisEntitiesToDgn (UpdateSequenceElm* pUpdateSequenceElm, AcDbObjectIdArray& imageSequence, bool isFirstNonXRef, bool isFirstNonImage)
    {
    return SUCCESS;
    }

void                        ConvertToDgnContext::NotifyConversionFailure (MSElementDescrP cellHeader)
    {
    _NotifyConversionFailure (cellHeader);
    }

void                        ConvertToDgnContext::_NotifyConversionFailure (MSElementDescrP)
    {
    //empty on purpose
    }

void                        ConvertToDgnMultiProcessingContext::_NotifyConversionFailure (MSElementDescrP cellHeader)
    {
    m_psElemsInSharedCell.push_back ({ cellHeader, m_curPSFileName });
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::SaveModelEntitiesToDgn (AcDbBlockTableRecord* pBlock)
    {
    bool                        isFirstNonXRef = true, isFirstNonImage = true;
    AcDbObjectIdArray           imageSequence;

    UpdateSequenceElm           *pUpdateSequenceElm = (UpdateSequenceElm*) _alloca (sizeof (UpdateSequenceElm) + MAX_UPDATE_SEQUENCE_COUNT * sizeof(ElementId));
    memset (pUpdateSequenceElm, 0, sizeof(*pUpdateSequenceElm));

    this->SetCurrentBlockId (pBlock->objectId());

    AcDbAnnotationScale*        cannoscale = NULL;
    AcDbDatabaseP               database = this->GetDatabase ();
    if (RDWGMODEL_TYPE_Sheet == m_pModelIndexItem->GetRealDwgModelType())
        {
        // reset CANNOSCALE for paperspace models:
        cannoscale = database->cannoscale ();
        if (!ResetCannoscaleFromLayout(database, cannoscale, m_pModelIndexItem->GetAnnotationScale()))
            {
            delete cannoscale;
            cannoscale = NULL;
            }
        }
    else if (RDWGMODEL_TYPE_DefaultModel == m_pModelIndexItem->GetRealDwgModelType())
        {
        // reset CANNOSCALE for modelspace model if we are in an Xref:
        cannoscale = database->cannoscale ();
        if (!ResetCannoscaleFromMasterFile(database, m_pModelIndexItem))
            {
            delete cannoscale;
            cannoscale = nullptr;
            }
        }

    /*------------------------------------------------------------------------------------------------------
    We choose to process UCS table here in model filling for these reasons:
        1) ACS elements must be saved to models
        2) Since Vancouver, we aroundtrip sheet ACS's back to sheet models
        3) Saving & reopening an active sheet model makes the new DgnFile to not have the Default model until
           the model is explicitly reopened, hence ACS's cannot be saved while processing dictionary section.
    -------------------------------------------------------------------------------------------------------*/
    this->SaveUcsTableToDgn ();

    AcDbSortentsTable*          pSortentsTable = NULL;
    Acad::ErrorStatus   sortEntsStatus = pBlock->getSortentsTable(pSortentsTable, AcDb::kForRead);
    if (Acad::eOk == sortEntsStatus)
        {
        // load elements to cache in sorted order.
        AcDbObjectIdArray       entIdArray;

        if (Acad::eOk == pSortentsTable->getFullDrawOrder(entIdArray))
            {
            for (int iEntity = 0, numEntities = entIdArray.length(); iEntity < numEntities; iEntity++)
                this->SaveEntityToDgn (pUpdateSequenceElm, imageSequence, isFirstNonXRef, isFirstNonImage, entIdArray.at(iEntity));
            }

        pSortentsTable->close ();
        }
    else
        {
        // load elements to cache by file position.
        AcDbBlockTableRecordIterator*     pEntIter;
        pBlock->newIterator(pEntIter, true, true);
        for (; !pEntIter->done(); pEntIter->step())
            {
            AcDbObjectId        entityId;
            if (Acad::eOk != pEntIter->getEntityId (entityId))
                continue;

            this->SaveEntityToDgn (pUpdateSequenceElm, imageSequence, isFirstNonXRef, isFirstNonImage, entityId);
            }

        delete pEntIter;
        }

    StatusInt result = _OnPostSaveAcisEntitiesToDgn (pUpdateSequenceElm, imageSequence, isFirstNonXRef, isFirstNonImage);

    if (NULL != cannoscale)
        {
        database->setCannoscale (cannoscale);
        delete cannoscale;
        }

    // TR 127064
    WString buffer;
    if ( (SUCCESS == ConfigurationManager::GetVariable (buffer, L"MS_RASTER_DWG_UPDATESEQUENCE_MODE")) && !buffer.empty() && (BeStringUtilities::Wtoi (buffer.c_str()) != 0) )
        {
        // 0 Use default
        // 1 Rasters should always be displayed under vectors
        // 2 Rasters should always be displayed over vectors
        pUpdateSequenceElm->rasterRefsLast = (BeStringUtilities::Wtoi (buffer.c_str()) == 2) ? true : false;
        }
    else
        {
        // Raster should be last if the more than one entry is found in the sequence and the first entry is not a raster
         pUpdateSequenceElm->rasterRefsLast = (imageSequence.length() > 1 && imageSequence[0].isNull());
        }

    this->SaveUpdateSequenceToDgnIfNeeded (pUpdateSequenceElm);

    m_pModelIndexItem->SetImageSequence (imageSequence);
    if (RDWGMODEL_TYPE_DefaultModel == m_pModelIndexItem->GetRealDwgModelType() && !PutActiveSettingsInDgn())
        this->SynchFlashlight();

    return result;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AppendPointDisplayElement
(
EditElementHandleR      outElement,
EditElementHandleR      childElement,
ConvertToDgnContext&    dgnContext
)
    {
    MSElementP          elemP = childElement.GetElementP ();

    elemP->ehdr.uniqueId = dgnContext.GetAndIncrementNextId();
    elemP->ehdr.level = LEVEL_BYCELL;
    elemP->hdr.dhdr.symb.color = COLOR_BYCELL;
    elemP->hdr.dhdr.symb.style = STYLE_BYCELL;
    elemP->hdr.dhdr.symb.weight = WEIGHT_BYCELL;

    SharedCellDefHandler::AddChildElement (outElement, childElement);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AppendPointDisplayLine
(
EditElementHandleR      outElement,
DPoint3dR               start,
DPoint3dR               end,
ConvertToDgnContext&    dgnContext
)
    {
    DSegment3d      line;

    line.point[0] = start;
    line.point[1] = end;

    EditElementHandle   eeh;
    if (BSISUCCESS == LineHandler::CreateLineElement (eeh, NULL, line, dgnContext.GetThreeD(), *dgnContext.GetModel()))
        AppendPointDisplayElement (outElement, eeh, dgnContext);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::SavePointDisplayDefinitionToDgn ()
    {
    /*-------------------------------------------------------------------------------------------------
    If the host tracks master vs reference file opennings and wants to override reference file variables,
    get the overrides from hostApp; otherwise use the system variables from current file.
    -------------------------------------------------------------------------------------------------*/
    AcDbDatabase*           database = NULL;
    if (!DwgPlatformHost::Instance()._GetMasterDwgFile(database) || NULL == database)
        database = m_pFileHolder->GetDatabase ();
    if (NULL == database)
        return  BSIERROR;
    
    int                     pdMode = database->pdmode();
    EditElementHandle       scdefEeh;

    SharedCellDefHandler::CreateSharedCellDefElement (scdefEeh, StringConstants::PointBlockName, this->GetThreeD(), *this->GetModel());
    SharedCellDefHandler::SetAnonymous (scdefEeh, true);
    SharedCellDefHandler::SetPointCell (scdefEeh, false);

    DPoint3d        zeroPoint;
    zeroPoint.Zero ();
    if (0 == pdMode || 1 == pdMode)
        {
        AppendPointDisplayLine (scdefEeh, zeroPoint, zeroPoint, *this);
        }
    else
        {
        double      displaySize;
        double      pdSize          = database->pdsize();
        double      drawingHeight   = database->extmax().y - database->extmin().y;
        DPoint3d    ll, ul, lr, ur;

        if (0.0 == pdSize)
            displaySize = .05 * drawingHeight;
        else if (pdSize < 0)
            displaySize = 1.0;
        else
            displaySize = pdSize;

        displaySize *= this->GetScaleToDGN();

        double      halfSize = displaySize / 2.0;
        ll.Init (-halfSize, -halfSize, 0.0);
        lr.Init ( halfSize, -halfSize, 0.0);
        ul.Init (-halfSize,  halfSize, 0.0);
        ur.Init ( halfSize,  halfSize, 0.0);

        switch (pdMode & 0x0007)
            {
            case 2:         // orthogonal cross
                {
                DPoint3d        origin, end;

                origin.Init (0.0, -displaySize, 0.0);
                end.Init (0.0, displaySize, 0.0);
                AppendPointDisplayLine (scdefEeh, origin, end, *this);

                origin.Init (-displaySize, 0.0, 0.0);
                end.Init (displaySize, 0.0, 0.0);
                AppendPointDisplayLine (scdefEeh, origin, end, *this);
                break;
                }

            case 3:         // Diagonal cross
                {
                DPoint3d        crossLL, crossLR, crossUR, crossUL;
                double          crossSize = .70 * displaySize;

                crossLL.Init (-crossSize, -crossSize, 0.0);
                crossLR.Init ( crossSize, -crossSize, 0.0);
                crossUL.Init (-crossSize,  crossSize, 0.0);
                crossUR.Init ( crossSize,  crossSize, 0.0);

                AppendPointDisplayLine (scdefEeh, crossLL, crossUR, *this);
                AppendPointDisplayLine (scdefEeh, crossUL, crossLR, *this);
                break;
                }

            case 4:         // Vertical hash
                {
                DPoint3d        topCenter = {0.0, halfSize, 0.0};
                AppendPointDisplayLine (scdefEeh, zeroPoint, topCenter, *this);
                break;
                }
            }

        if (0 != (pdMode & 32))         // Circle
            {
            RotMatrix       idMatrix;

            idMatrix.InitIdentity ();
            EditElementHandle   eeh;
            if (BSISUCCESS == EllipseHandler::CreateEllipseElement (eeh, NULL, zeroPoint, halfSize, halfSize, idMatrix, this->GetThreeD(), *this->GetModel()))
                AppendPointDisplayElement (scdefEeh, eeh, *this);
            }

        if (0 != (pdMode & 64))         // Rectangle
            {
            DPoint3d        points[5];

            points[0] = ll;
            points[1] = lr;
            points[2] = ur;
            points[3] = ul;
            points[4] = points[0];

            EditElementHandle   eeh;
            LineStringHandler::CreateLineStringElement (eeh, NULL, points, 5, this->GetThreeD(), *this->GetModel());
            AppendPointDisplayElement (scdefEeh, eeh, *this);
            }
        }

    SharedCellDefHandler::AddChildComplete (scdefEeh);
    //probably no longer need mdlSharedCell_setRangeDiag?

    MSElementP  elem = scdefEeh.GetElementP ();
    DRange3d    rangeVec;
    DataConvert::ScanRangeToDRange3d (rangeVec, elem->sharedCellDef.dhdr.range);
    rangeVec.high.subtract (&rangeVec.low);

    elem->sharedCellDef.origin.x = rangeVec.low.x + (rangeVec.high.x / 2);
    elem->sharedCellDef.origin.y = rangeVec.low.y + (rangeVec.high.y / 2);
    elem->sharedCellDef.origin.z = rangeVec.low.z + (rangeVec.high.z / 2);

    StatusInt   status = this->LoadElementIntoCache (scdefEeh);

    if (BSISUCCESS == status)
        {
        this->GetFileHolder().SetPointDisplayBlockId (scdefEeh.GetElementId());
        // LoadElementIntoCache does not load anonymous blocks into DgnFile whose keys are block names, so load this special shared cell def here:
        this->GetFile()->AddSharedCellDefinition ((PersistentElementRefP)scdefEeh.GetElementRef());
        }

    return status;
    }

void                        ConvertToDgnContext::_SaveBlockIterAsSharedCells (AcDbBlockTableIterator* pBlkIter)
    {
    for (pBlkIter->start (); !pBlkIter->done (); pBlkIter->step ())
        {
        AcDbObjectId    entityId;
        if (Acad::eOk != pBlkIter->getRecordId (entityId))
            continue;
        this->SaveSharedCellDefinitionToDgn (entityId);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::SaveSharedCellDefinitionsToDgn ()
    {
    this->SavePointDisplayDefinitionToDgn ();

    AcDbBlockTablePointer       pBlocks (m_pFileHolder->GetDatabase()->blockTableId(), AcDb::kForRead);
    if (Acad::eOk != pBlocks.openStatus())
        {
        BeAssert (false && L"Failed opening block table for read!");
        return BSIERROR;
        }

    AcDbBlockTableIterator*     pBlkIter;

    if (Acad::eOk != pBlocks->newIterator (pBlkIter))
        return SUCCESS;

    _SaveBlockIterAsSharedCells (pBlkIter);

    delete pBlkIter;

    if (!this->GetSettings().AttributesAsTags() && m_attrdefItemtypeLibrary.IsValid() && m_attrdefItemtypeLibrary->GetItemTypeCount() > 0 && m_attrdefItemtypeLibrary->IsModified())
        m_attrdefItemtypeLibrary->Update (false);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2004
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 BlockExportedAsNormalCell (AcDbBlockTableRecord* pBlock)
    {
    // If we're going to save as a regular cell (to maintain application data) don't export
    // definition as well or the IDs will overlap.
    AcDbObjectIdArray       refIds;

    pBlock->getBlockReferenceIds(refIds);

    if (1 == refIds.length())
        {
        AcDbBlockReferencePointer pBlockRef (refIds[0], AcDb::kForRead);
        if (Acad::eOk != pBlockRef.openStatus())
            {
            DIAGNOSTIC_PRINTF ("Unable to open block reference ID=%I64d. %ls\n", RealDwgUtil::CastDBHandle(refIds[0].handle()), acadErrorStatusText(pBlockRef.openStatus()));
            return false;
            }

        AcString                cellName;
        return SUCCESS == RealDwgXDataUtil::GetMicroStationXDataStringByKey (cellName, pBlockRef.object(), StringConstants::XDataKey_CellNameLinkage);
        }

    return false;
    }

void                        ConvertToDgnContext::_DiagnosticPrint (WStringCR message)
    {
    //empty on purpose
    }

void                        ConvertToDgnMultiProcessingContext::_DiagnosticPrint (WStringCR message)
    {
    DwgPlatformHost::Instance ()._FatalError (message.c_str ());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SaveSharedCellDefinitionToDgn (AcDbObjectId blockId)
    {
    /*-------------------------------------------------------------------------------------------------------
    This method saves a block definition to shared cell definition.  Do not attempt to do this if:
        1) It has already been saved earlier as a result of saving a child of another block (ElementIdInUse).
        2) We are saving the same block - a bad recursive block definition seen in TFS# 91011.
    -------------------------------------------------------------------------------------------------------*/
    ElementId   id = this->ElementIdFromObjectId (blockId);
    if (blockId == this->GetCurrentBlockId())
        {
        if (blockId == acdbSymUtil()->blockModelSpaceId(blockId.database()) || blockId == acdbSymUtil()->blockPaperSpaceId(blockId.database()))
            return RealDwgSuccess;

        WString message;
        WString::Sprintf (message, L"Skipping a bad recursive block definition ID=%I64d\n", id);
        _DiagnosticPrint (message);

        DIAGNOSTIC_PRINTF ("Skipping a bad recursive block definition ID=%I64d\n", id);
        return  DefectiveBlockDefinition;
        }

    if (!blockId.isNull() && !this->ElementIdInUse (id))
        {
        AcDbBlockTableRecordPointer     pBlock (blockId, AcDb::kForRead);
        if (Acad::eOk != pBlock.openStatus())
            {
            BeAssert (false && L"Cannot open block table record!");
            return  CantOpenObject;
            }

        if (!RealDwgUtil::IsModelOrPaperSpace(pBlock) && !pBlock->isFromExternalReference() && !BlockExportedAsNormalCell(pBlock) && !RealDwgUtil::IsRenderSettingsBlock(pBlock))
            this->LoadObjectIntoCache (blockId);
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::UpdateHeaderElement ()
    {
    PersistentElementRefP   type9Elem = this->GetFile()->GetPersistentTcbElementRef ();
    EditElementHandle       type9Eeh (type9Elem, this->GetModel());
    if (type9Eeh.IsValid())
        {
        TcbP                pTcb = (Tcb *) ((Dgn_header *)type9Eeh.GetElementP())->tcbinfo;
        if (this->PutActiveSettingsInDgn())
            {
            // allow the host to preserve active settings on re-opening a DWG file:
            DwgPlatformHost::Instance()._GetPersistentTcbForSilentSave (pTcb);

            // TR# 168010 - If an extended index is stored in the active color, we can't use the "in-memory" version
            // as it would have been lost when the file was closed - revert to the database.
            if (0 != ColorUtil::GetExtendedIndexFromRawColor(pTcb->symbology.color))
                pTcb->symbology.color  = this->GetDgnColor (this->GetDatabase()->cecolor());

            // while most TCB variables can be preserved, angular format DDdMM'SS" needs to be updated for correct precision.  TR#312225
            if (ANGULAR_UNITS_DegreesMinutesSeconds == this->GetDatabase()->aunits())
                this->SetTCBAngularUnitsFromDatabaseVariables (pTcb, this->GetDatabase());
            // restore xattributes attached to active text style - TFS 81744
            DgnTextStylePtr activeTextStyle = DgnTextStyle::GetSettings (*m_dgnFile);
            if (activeTextStyle.IsValid())
                DgnTextStyle::ReplaceSettings (*activeTextStyle.get(), *m_dgnFile);
            }
        else
            {
            // not a file re-open, directly set TCB from DWG file header:
            this->CreateTCBFromDatabaseVariables (pTcb);
            if (this->GetSettings().GraphicGroupAttributes())
                pTcb->fbfdcn.gglk = true;
            }

        pTcb->fillColor          = pTcb->symbology.color;
        pTcb->activeFillColorRGB =  pTcb->activeColorRGB = 0;           // Force using the TCB values directly.
        type9Eeh.ReplaceInModel (type9Elem);

        // give the host a chance to sync its cached TCB from the updated file header in DgnFile:
        DwgPlatformHost::Instance()._SetPersistentTcbForSilentSave (this->GetFile()->GetPersistentTcb());
        }

    m_dgnFile->SaveExtendedColorMap ();        // Incremental updates of extended table disabled for speed - updated here once instead.

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::SaveColorBookTableToDgn ()
    {
    if (m_noSaveToDgnCache)
        return  SUCCESS;

    // Note: This method populates the extendedColorMap. in ConvertToDgnContext::PostProcess, we call m_dgnFile->SaveExtendedColorMap to write the colorMap to the DGN file.
    AcDbObjectId            colorDictionaryId;

    if ( (colorDictionaryId = m_pFileHolder->GetDatabase()->colorDictionaryId()).isNull() )
        return BSIERROR;

    AcDbDictionaryPointer   pDictionary (colorDictionaryId, AcDb::kForRead);
    if (Acad::eOk != pDictionary.openStatus())
        return BSIERROR;

    AcDbDictionaryIterator* pIterator = pDictionary->newIterator();
    for (; !pIterator->done(); pIterator->next())
        {
        try
            {
            WChar             colorBookName[2048];
            RealDwgUtil::AcStringToMSWChar (colorBookName, pIterator->name (), _countof (colorBookName));

            WCharP            pEntry;
            for (pEntry = colorBookName; '\0' != *pEntry && '$' != *pEntry; pEntry++)
                ;

            if ('\0' != pEntry)
                {
                AcDbColorPointer pColor (pIterator->objectId(), AcDb::kForRead);
                if (Acad::eOk != pColor.openStatus())
                    continue;

                AcCmColor        cmColor;
                pColor->getColor (cmColor);

                IntColorDef      colorDef (cmColor.red(), cmColor.green(), cmColor.blue());
                DgnColorMap::CreateElementColor (colorDef, colorBookName, pEntry, *m_model->GetDgnFileP());
                *pEntry++ = '\0';
                }
            }
        catch (...)
            {
            }
        }
    delete pIterator;

    return SUCCESS;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt    ConvertToDgnContext::UpdateFontTable ()
    {
    m_dgnFile->GetDgnFontMapP()->SaveFontTable(false);
    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt    ConvertToDgnContext::SaveColorTableToDgn ()
    {
    /*--------------------------------------------------------------------------------------------------
    The DGN color table might have already been created for instance as a result of creating our special
    dwg foreground color.  Even if a color table does not yet exist, the first call to DgnFile::GetColorMapP
    as we do below would cause it to create a default color table anyway.  So we do not need to explicitly
    create a color table element(type 5).  We just need to override existing color table with which we have
    obtained from dwgsettings because it may be a user defined color table.
    ---------------------------------------------------------------------------------------------------*/
    DgnSymbologyData*   dgnSymbologyData = m_pFileHolder->GetDgnSymbologyData();
    DgnColorMapP        colorMap = this->GetFile()->GetColorMapP ();
    if (NULL != colorMap)
        {
        // color table exists in file
        UInt32*         colorTable = colorMap->GetTbgrColorsP ();
        for (int iColor = 0; iColor < DgnColorMap::INDEX_ColorCount; iColor++)
            {
            RgbColorDefCP   colorRgb = dgnSymbologyData->GetColor (iColor);
            IntColorDef     intColor(colorRgb->red, colorRgb->green, colorRgb->blue);

            colorTable[iColor] = intColor.m_int;
            }
        return  BSISUCCESS;
        }
    
    return  BSIERROR;
    }

SharedCellsCreationCmd::SharedCellsCreationCmd (ConvertToDgnContext& context)
    :m_context (context)
    {}

SharedCellData::SharedCellData (AcDbBlockTableRecord* block)
    :m_block (block)
    {}

SharedCellData& SharedCellsCreationCmd::AddSharedCell (AcDbBlockTableRecord* block)
    {
    m_data.emplace_back (block);
    return m_data.back ();
    }

//Duplicate with LoadObjectIntoCache
void LoadEehIntoCache (EditElementHandleR objectEeh, AcDbObject* pObject, ConvertToDgnContext& context)
    {
    context.LoadElementIntoCache (objectEeh);

    AcDbDictionary* pDictionary;
    if (NULL != (pDictionary = AcDbDictionary::cast (pObject)))
        context.LoadDictionaryIntoCache (pDictionary);
    }

bool SharedCellsCreationCmd::AddToDgnFile ()
    {
    bool result = true;

    for (SharedCellData& data : m_data)
        {
        BeAssert (data.m_sharedCellEeh.IsValid ());
        if (!data.m_sharedCellEeh.IsValid ())
            {
            result = false;
            continue;
            }
        PreAddToDgnFile (data);
        LoadEehIntoCache (data.m_sharedCellEeh, data.m_block, m_context);
        }
    return result;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::LoadObjectIntoCache (const AcDbObjectId& id)
    {
    try
        {
        if (id.isNull())
            return NullObjectId;

        AcDbObjectPointer<AcDbObject>   pObject (id, AcDb::kForRead);
        if (Acad::eOk != pObject.openStatus())
            return CantOpenObject;

        NOISYDEBUG_PRINTF ("Loading Object ID: %I64d\n", id.handle());

        return this->LoadObjectIntoCache (pObject, id);
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception caught opening object from ID\n");
        }

    return CantCreateObject;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::LoadObjectIntoCache (AcDbObject* pObject, const AcDbObjectId& id)
    {
    if (NULL == pObject)
        return NullObject;

    _OnPreLoadObjectIntoCache (id);

    AcDbDictionary*     pDictionary;
    EditElementHandle   objectEeh;
    RealDwgStatus       status;
    if (RealDwgSuccess == (status = this->ElementFromObject (objectEeh, pObject)) && objectEeh.IsValid ())
        {
        this->LoadElementIntoCache (objectEeh);

        if (NULL != (pDictionary = AcDbDictionary::cast (pObject)))
            this->LoadDictionaryIntoCache (pDictionary);
        }

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::LoadDictionaryIntoCache (AcDbDictionary* pDictionary)
    {
    /*-----------------------------------------------------------------------------------
    This method processes the input dictionary header and its immediate children.  It does
    not recurse into processing its grandchildren.  I don't see a need for recursion at
    this point, because we currently only support a couple of ACAD_ dictionaries which are
    all flat anyway.  If we will need to convert a particular dictionary with grandchildren
    we may want to handle the recursion in its DGN extension code.
    -----------------------------------------------------------------------------------*/
    if (pDictionary->objectId() != this->GetMicroStationDictionaryId())
        {
        // try converting dictionary header with its children, if this header has not yet been converted:
        DgnModelR   dictionaryModel = this->GetFile()->GetDictionaryModel ();

        if (!dictionaryModel.FindByElementId(this->ElementIdFromObjectId(pDictionary->objectId())))
            {
            EditElementHandle   dictionaryElm;
            if (RealDwgSuccess == this->ElementFromObject(dictionaryElm, pDictionary) && dictionaryElm.IsValid())
                {
                this->LoadElementIntoCache (dictionaryElm);
                return;
                }
            }

        UInt32  entryLimit = this->GetSettings().GetMaxDictionaryItems ();
        if (pDictionary->numEntries() > this->GetSettings().GetMaxDictionaryItems())
            {
            DIAGNOSTIC_PRINTF ("Excessive dictionary items %d (> %d) ignored.\n", pDictionary->numEntries(), entryLimit);
            return;
            }

        // try loading its children, if they have not yet been converted:
        AcDbDictionaryIterator* pIterator = pDictionary->newIterator();
        for (; !pIterator->done(); pIterator->next())
            {
            // apply dictionary wildcard filters if user has supplied them to us:
            if (ULONG_MAX == entryLimit && !RealDwgUtil::MatchStringFilters(WString(pIterator->name()), GetSettings().GetDictionaryWildcardFilters()))
                continue;

            // Export object if it has not already been done.
            AcDbObjectId            objectId = pIterator->objectId();
            if (!dictionaryModel.FindByElementId(this->ElementIdFromObjectId(objectId)))
                this->LoadObjectIntoCache (objectId);
            }
        delete pIterator;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::LoadDictionary (AcDbObjectId dictionaryId)
    {
    // open parent dictionary:
    AcDbDictionaryPointer   parentDictionary(dictionaryId, AcDb::kForRead);
    if (Acad::eOk != parentDictionary.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Failed opening dictionary ID=%I64d.\n", this->ElementIdFromObjectId(dictionaryId));
        return;
        }

    // save main dictionary to DGN cache - required if we want to save any child dictionary table:
    EditElementHandle       dictionaryElm;
    if (RealDwgSuccess == this->ElementFromObject(dictionaryElm, parentDictionary))
        this->LoadElementIntoCache (dictionaryElm);

    // find and save ACDB_PLOTSTYLENAME_DICTIONARY dictionary id from main dictionary:
    AcDbObjectId    objectId;
    if (Acad::eOk == parentDictionary->getAt(ACDB_PLOTSTYLENAME_DICTIONARY, objectId))
        this->SetPlotStyleNameDictionaryId (objectId);

    AcDbDictionaryIterator* pIterator = parentDictionary->newIterator();

    // loop through dictionary children:
    for (; !pIterator->done(); pIterator->next())
        {
        AcDbObject*         object = NULL;
        if (Acad::eOk == pIterator->getObject(object, AcDb::kForRead))
            {
            AcDbDictionary* childDictionary = AcDbDictionary::cast (object);

            if (NULL != childDictionary)
                this->LoadDictionaryIntoCache (childDictionary);
            else if (object->isAProxy())
                this->AddMissingObjectEnabler (object, false);

            object->close ();
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Failed opening child dictionary ID=%I64d.\n", this->ElementIdFromObjectId(pIterator->objectId()));
            }
        }
    delete pIterator;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
static  bool                TurnOffAnnotative (AcDbObject* object, AcDbAnnotativeObjectPE* annotationPE)
    {
    /*-----------------------------------------------------------------------------------
    We can turn off annotation scale on all annotative objects to get their native forms
    with correct sizes, except for dimension and leader.  The text size of a dimension and 
    the hookline size of a leader is controlled by the text height of their text styles.  
    If the text style has a non-zero text height, i.e. it overrides text size in the
    dimension style, turning off annotation scale on the dimension will result in a 
    text size change for the dimension or hookline size change for the leader.  Only if 
    the text style does not override the text size in the dimension style text size, we can 
    turn off its annotation scale and still retain correct text/hookline size.
    -----------------------------------------------------------------------------------*/
    AcDbDimension*  dimension = AcDbDimension::cast(object);
    AcDbLeader*     leader = AcDbLeader::cast(object);
    AcDbMLeader*    mleader = AcDbMLeader::cast(object);
    if (NULL == dimension && NULL == leader && NULL == mleader)
        return  false;

    // if the textstyle overrides dimension/leader text height, we have to world draw the dimension/leader.
    AcDbTextStyleTableRecordPointer textStyle;
    if (NULL != dimension)
        textStyle.open (dimension->dimtxsty(), AcDb::kForRead);
    else if (NULL != leader)
        textStyle.open (leader->dimtxsty(), AcDb::kForRead);
    else if (NULL != mleader)
        textStyle.open (mleader->textStyleId(), AcDb::kForRead);
    else
        return  false;

    if (Acad::eOk != textStyle.openStatus() || (!RealDwgUtil::IsObjectAnnotative(textStyle) && fabs(textStyle->textSize()) > TOLERANCE_ZeroSize))
        return  false;

    // the text style has a zero text height, we can turn off annotation scale from the dimension/leader.
    return Acad::eOk == object->upgradeOpen() && Acad::eOk == annotationPE->setAnnotative(object, false);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsParentBlockAnnotative (AcDbObjectP acObject)
    {
    AcDbObjectId    blockId = acObject->ownerId ();
    if (RealDwgUtil::GetModelSpaceId(acObject->database()) != blockId)
        {
        AcDbBlockTableRecordPointer     block(blockId, AcDb::kForRead);
        if (Acad::eOk == block.openStatus())
            return  RealDwgUtil::IsObjectAnnotative(block);
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertToDgnContext::NeedWorldDraw
(
AcDbObjectId&               viewportId,
bool&                       isDrawn,
bool&                       annotativeChanged,
AcDbObject*                 pObject
)
    {
    /*-----------------------------------------------------------------------------------
    Before we fully support annotative objects, we have no choice but drop them to world
    drawn cells under below conditions:

    1) In modelspace, we keep annotation scale.  We set model annotation scale from DWG's
       CANNOSCALE and turn on model annotation scale flag.  In annotative object's ToDgn code
       we will also set the element annotation flag based on an annotative object.
    2) In Paperspace, we either turn off annotation scale or viewport draw the annotative
       object.  This is because a) we have used annotation scale for DWG's plot scale, and
       b) a layout's annotation scale seems always 1:1(or at least ACAD does not have a
       way to create a different scale).  We turn off annotation scale for dimension objects
       so we can keep them as dimension types.  We world draw non-dimension objects.
    3) It appears that, currently the only Autodesk's annotative objects whose native
       forms do not respond to active annotation scale, or at least RealDWG does not give
       us the object's parameters applied by active annotation scale, are dimension and
       leader.  Comparing to all other objects, RealDWG gives us their parameters(sizes,
       coordinates, etc) that match active annotation scale.  Therefore we can simply
       draw these objects in their native forms before annotation scales are applied.
       A likely reason why RealDWG fails to give us dimension/leader parameters applied
       by active annotation scale may be that a dimension gets its parameters from dimstyle,
       whereas other objects have their parameters saved as part of the object.  For example,
       text height is a text property which can differ from the text style.
       As a result, we only need to world draw dimensions and leaders.  Furthermore, even for
       dimensions & leaders, if their text style does not override dimension style's text
       height, i.e. the height of the text style is 0, we can set the annotative status off
       such that they will be created as native dimensions & leaders.
    4) However, annotation scale visibility via CANNOSCALE and/or ANNOALLVISIBLE still needs
       to be applied to all annotative objects.  We do not world draw an annotative object
       which has become invisible as a result of annotation scale visibility control.
       In this case we set isDrawn=false, and return false to the caller to normal draw
       the element, and to set the element's invisible bit.
    -----------------------------------------------------------------------------------*/
    isDrawn = true;
    if (pObject->isKindOf(AcDbEntity::desc()))
        {
        AcDbAnnotativeObjectPE*     annotationPE = ACRX_PE_PTR (pObject, AcDbAnnotativeObjectPE);

        // check if the object is anntative:
        if (NULL != annotationPE && annotationPE->annotative(pObject))
            {
            // active anno scale is CANNOSCALE in modelspace and the anno scale of the 1st viewport in a paperspace
            AcDbAnnotationScale*    activeAnnoScale = m_pModelIndexItem->GetAnnotationScale ();
            if (NULL == activeAnnoScale)
                return  false;

            AcDbObjectContextInterface* objectContextInterface = ACRX_PE_PTR (pObject, AcDbObjectContextInterface);
            if (NULL == objectContextInterface)
                return  false;

            // does this entity supports CANNOSCALE in modelspace or layout scale?
            bool    hasActiveScale = objectContextInterface->hasContext (pObject, *activeAnnoScale);

            // an annotative object is visible only if the object supports CANNOSCALE, or ANNOALLVISIBLE is turned on.
            isDrawn = m_pModelIndexItem->GetAnnoAllVisible() || annotationPE->forceAnnoAllVisible(pObject) || hasActiveScale;

            if (RDWGMODEL_TYPE_DefaultModel == m_pModelIndexItem->GetRealDwgModelType())
                {
                // in modelspace or block section
                /*--------------------------------------------------------------------------------
                Turn off annotation scale if the parent block is non-annotative.
                --------------------------------------------------------------------------------*/
                if (this->IsInSharedCellCreation() && !IsParentBlockAnnotative(pObject))
                    annotativeChanged = TurnOffAnnotative (pObject, annotationPE);

                return  false;
                }
            else
                {
                // in paperspace
                /*--------------------------------------------------------------------------------------
                To apply rule #3 above, we would only consider dimensions and leaders for viewport draw.
                But Tolerance and MLeader are not currently supported, so they will have to go through
                world/viewport draw anyway.  These are paperspace objects that require a viewport ID for
                viewport draw.  The default viewport draw method does not have a viewport ID, so it will
                not draw these objects.  We want these objects to go through the viewport draw method with
                a correct viewport ID.
                --------------------------------------------------------------------------------------*/
                bool    isDimLeader = pObject->isKindOf(AcDbDimension::desc()) || pObject->isKindOf(AcDbLeader::desc()) || pObject->isKindOf(AcDbMLeader::desc());
                bool    isFcf = pObject->isKindOf(AcDbFcf::desc());
                bool    needViewportDraw = isDimLeader || isFcf;

                if (!needViewportDraw)
                    return  false;

                if (!isFcf)
                    {
                    /*--------------------------------------------------------------------------------------------------
                    We do not need to force viewport draw for supported objects if the have the same scale as the layout's.
                    Keep their native forms if the scales match.  Usually they are 1:1(I have yet to see a different scale).
                    ---------------------------------------------------------------------------------------------------*/
                    if (objectContextInterface->hasContext(pObject, *activeAnnoScale))
                        return  false;
                    }

                // turn off annotative for dim/leader types
                if (isDimLeader && (annotativeChanged = TurnOffAnnotative(pObject, annotationPE)))
                    return  false;

                viewportId = m_pModelIndexItem->GetOverallViewportId ();
                return  true;
                }
            }
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::ReportProgress ()
    {
    if (nullptr != m_progressMeter)
        m_progressMeter->meterProgress();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                     RayBentley     11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ConvertToDgnContext::ElementFromObject (EditElementHandleR eeh, AcDbObject* pObject)
    {
#if defined (REALDWG_NOISY_DEBUG)
    DIAGNOSTIC_PRINTF ("Exporting Object: %ls, ID: %I64d\n", pObject->isA()->name(), this->ElementIdFromObject (pObject));
#else if defined (REALDWG_DIAGNOSTICS)
    UInt64      id = this->ElementIdFromObject (pObject);
#endif

    if (NULL == pObject)
        return NullObject;

    this->ReportProgress ();

    try
        {
        AcDbObjectId        viewportId = AcDbObjectId::kNull;
        bool                isDrawn = true;
        bool                isChanged = false;

        if (this->NeedWorldDraw(viewportId, isDrawn, isChanged, pObject) && SUCCESS == this->WorldDrawToElements(AcDbEntity::cast(pObject), eeh, viewportId))
            return  RealDwgSuccess;

        RealDwgStatus status = ACRX_X_CALL (pObject, ToDgnExtension)->ToElement (pObject, eeh, *this);

        /*-------------------------------------------------------------------------------
        When an annotative object is not displayed per its visibility control, we choose
        to make the element invisible, instead of skipping it from ToElement method for
        the reason of maintaining possible element dependency(xdata, leader, etc).
        Again, this is not a perfect solution but rather a workaround before we fully
        support annotative objects in the future.
        -------------------------------------------------------------------------------*/
        if (SUCCESS == status && !isDrawn)
            RealDwgUtil::SetElementInvisible (eeh, true);

        // if we have turned off annotative, now turn it back on for round trip.
        if (isChanged)
            RealDwgUtil::SetObjectAnnotative (pObject, true);

        return status;
        }

    catch (DiscardInvalidEntityException& invalidEntityError)
        {
        WCharCP   objectName = pObject->isA()->name ();

        if (this->GetSettings().DiscardInvalidEntities())
            {
            RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_EntitiesOffDesignPlaneDiscarded, false, objectName, objectName);
            DIAGNOSTIC_PRINTF ("Discarding Invalid Entity: %ls, AcDbHandle: %I64d, %ls\n", objectName, RealDwgUtil::CastDBHandle (pObject->objectId().handle()), invalidEntityError.ErrorMessage());

            Acad::ErrorStatus   es;
            while (Acad::eHadMultipleReaders == (es = pObject->upgradeOpen()))
                pObject->close ();
            if (Acad::eOk == es)
                es = pObject->erase ();

            if (Acad::eOk != es)
                DIAGNOSTIC_PRINTF ("...failed to discard invalid entity! [%ls]\n", acadErrorStatusText(es));
            }
        else
            {
            RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_EntitiesOffDesignPlaneIgnored, false, objectName, objectName);
            DIAGNOSTIC_PRINTF ("Invalid Entity: %ls, AcDhHandle: %I64d, %ls\n", objectName, RealDwgUtil::CastDBHandle (pObject->objectId().handle()), invalidEntityError.ErrorMessage());
            }
        }

    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception Thrown Extracting Element From Object: %ls, AcDbHandle: %I64d\n", pObject->isA()->name(), RealDwgUtil::CastDBHandle (pObject->objectId().handle()));
        }

    return ExceptionCaught;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     AddLinestyleLinkageFromParams (EditElementHandleR eeh, LineStyleParamsCP styleParams, bool is3D)
    {
    int                 nLinkageBytes;
    StyleLink           linkage;
    if (0 != (nLinkageBytes = LineStyleLinkageUtil::CreateRawLinkage(&linkage, styleParams, is3D)))
        {
        bool            replaced = false;
        for (ElementLinkageIterator linkageIter = eeh.BeginElementLinkages(); linkageIter != eeh.EndElementLinkages(); linkageIter.ToNext())
            {
            if (STYLELINK_ID == linkageIter.GetLinkage()->primaryID)
                {
                eeh.ReplaceElementLinkage (linkageIter, linkage.linkHeader, &linkage.modifiers);
                replaced = true;
                break;
                }
            linkageIter.ToNext ();
            }

        if (!replaced)
            eeh.AppendElementLinkage (NULL, linkage.linkHeader, &linkage.modifiers);

        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Peter.Yu                        11/2019
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::SetElementSymbologyDirect
(
EditElementHandleR          eeh,
UInt32                      level,
UInt32*                     pColor,
UInt32*                     pWeight,
bool*                       pInvisible
)
    {
    MSElementP  element = eeh.GetElementP();

    if (NULL == element)
        return;
    
    element->ehdr.level = level;

    if (element->ehdr.isGraphics)
        {
        if (NULL != pColor)
            element->hdr.dhdr.symb.color = *pColor;

        if (NULL != pWeight)
            element->hdr.dhdr.symb.weight = *pWeight;

        // unfortunately, invisible can't be set with ElementPropertiesSetter.
        if (NULL != pInvisible)
            element->hdr.dhdr.props.b.invisible = *pInvisible;
        }

    // Handler does not expose children for any reason other than counting, hence ExposeChildrenReason::Count
    for (ChildElemIter child(eeh, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
        {
        EditElementHandle   childEeh(child, false);
        SetElementSymbologyDirect(childEeh, level, pColor, pWeight, pInvisible);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::SetElementSymbology
(
EditElementHandleR          eeh,
UInt32                      level,
UInt32*                     pColor,
Int32*                      style,
UInt32*                     pWeight,
UInt32*                     pFillColor,
bool*                       pInvisible,
double                      lineStyleScale,
double                      transparency,
DgnElementClass             elementClass
)
    {
    MSElementP  element = eeh.GetElementP();

    if (NULL == element)
        return;

    ElementPropertiesSetter propSetter;

    if (element->ehdr.type == TEXT_ELM || element->ehdr.type == TEXT_NODE_ELM)
        SetElementSymbologyDirect (eeh, level, pColor, pWeight, pInvisible);
    else
        {
        propSetter.SetLevel (level);
        if (element->ehdr.isGraphics)
            {
            if (NULL != pColor)
                propSetter.SetColor (*pColor);
            if (NULL != pWeight)
                propSetter.SetWeight (*pWeight);
            if (NULL != pInvisible)
                element->hdr.dhdr.props.b.invisible = *pInvisible;
            }
        }

    if (element->ehdr.isGraphics)
        {
        if (nullptr != style)
            {
            /*------------------------------------------------------------------------------------------------------
            A performance workaround: a custom linestyle on an element causes ElementPropertiesSetter to validate 
            element range by drawing the element. Elements like texts do not need custom linestyle.  Remove the line
            style from these elements to prevent them from being unnecessarily drawn.
            ------------------------------------------------------------------------------------------------------*/
            if (!eeh.GetHandler().IsSupportedOperation(&eeh, SupportOperation::LineStyle))
                {
                *style = 0;
                lineStyleScale = 1.0;
                }

            if (1.0 != lineStyleScale)
                {
                LineStyleParams lsParams;
                lsParams.Init();
                lsParams.modifiers |= STYLEMOD_SCALE;
                lsParams.scale = lineStyleScale;
                propSetter.SetLinestyle (*style, &lsParams);
                //This flag allows to preserve / append linestyle parameters in a call to _EachLineStyleCallback()
                propSetter.SetAppendLsParams(true);
                element->hdr.dhdr.symb.style = *style;

                for(ChildEditElemIter childElm(eeh, ExposeChildrenReason::Count); childElm.IsValid(); childElm = childElm.ToNext())
                    {
                    MSElementP  childElement = childElm.GetElementP();

                    if(IS_LINECODE(childElement->hdr.dhdr.symb.style) && !IS_LINECODE(*style))
                    childElement->hdr.dhdr.symb.style = *style;
                    }
                }
            else
                {
                propSetter.SetLinestyle (*style, NULL);
                }
            }

        propSetter.SetElementClass (elementClass);
        propSetter.SetTransparency (transparency);

        if (NULL != pFillColor && mdlElement_displayAttributePresent (eeh.GetElementCP(), FILL_ATTRIBUTE, NULL))
            propSetter.SetFillColor (*pFillColor);
        }

    propSetter.Apply (eeh);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SetGraphicGroup (MSElementDescrP pDescr, UInt32 graphicGroup)
    {
    if (NULL == pDescr)
        return;

    pDescr->el.hdr.dhdr.grphgrp = graphicGroup;

    for (MSElementDescrP pChild = pDescr->h.firstElem; NULL != pChild; pChild = pChild->h.next)
        SetGraphicGroup (pChild, graphicGroup);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           08/01
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertToDgnContext::HasHyperlinkXData
(
AcDbEntityCP                pEntity
)
    {
    RealDwgResBuf*          pXData = static_cast <RealDwgResBuf*> (pEntity->xData (StringConstants::RegAppName_PeUrl));

    if (NULL == pXData)
        return SUCCESS;

    AcString                title1, title2, url;
    bool                    isHyperlinked = SUCCESS == RealDwgXDataUtil::ExtractHyperlinkXData (title1, title2, url, pXData);

    RealDwgResBuf::Free (pXData);

    // too many DgnLinks appear drag down view update performance
    if (isHyperlinked && m_ignoreEmptyHyperlinks)
        isHyperlinked = title1.isEmpty() && title2.isEmpty() && url.isEmpty();

    return  isHyperlinked;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        AddPlotStyle (EditElementHandleR outElement, ElementId styleId)
    {
    Display_attribute               displayAttribute;
    Display_attribute_plotStyle     plotStyle;

    plotStyle.styleId = styleId;

    StatusInt   status = mdlElement_displayAttributeCreate (&displayAttribute, PLOTSTYLE_ATTRIBUTE, sizeof plotStyle, (UShort *)&plotStyle);

    if (SUCCESS == status)
        {
        MSElementP      elem = (MSElementP) malloc (outElement.GetElementCP()->Size() + 200);
        outElement.GetElementCP()->CopyTo (*elem);

        status = mdlElement_displayAttributeAdd (elem, &displayAttribute);
        if (SUCCESS == status)
            outElement.ReplaceElement (elem);

        free (elem);
        }

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::ExtractPlotStyleFromEntity (EditElementHandleR outElement, AcDbEntityCP inEntity)
    {
    AcDbObjectId        plotStyleId;
    if (AcDb::kPlotStyleNameById == inEntity->getPlotStyleNameId(plotStyleId) && !plotStyleId.isNull())
        return AddPlotStyle (outElement, this->ElementIdFromObjectId(plotStyleId));

    return  NotApplicable;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::ExtractMaterialFromEntity (EditElementHandleR outElement, AcDbEntityCP inEntity)
    {
    StatusInt   status = BSIERROR;
    ACHAR*      materialName = inEntity->material ();

    if (0 != materialName && 0 != _wcsicmp(materialName, L"BYLAYER") && 0 != _wcsicmp(materialName, L"BYBLOCK") && inEntity->materialId().isValid())
        status = MaterialManager::GetManagerR().SetMaterialAttachmentId (outElement, this->ElementIdFromObjectId (inEntity->materialId()));

    if (NULL != materialName)
        acutDelString (materialName);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }

/*---------------------------------------------------------------------------------**//**
* Restores the header and linkage information for an element descriptor
*
* @param        pEl             Modify this element so its header is restored.
* @param        elementContext  Element Context
* @bsimethod                                                     RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::ElementHeaderFromEntity
(
EditElementHandleR          eeh,
AcDbEntityCP                pEntity,
int                         mask
)
    {
    if (NULL == pEntity)
        return BSIERROR;

    UInt32              dgnColorIndex   = this->GetDgnColor (pEntity->entityColor());
    UInt32              dgnWeightIndex  = this->GetDgnWeight (pEntity->lineWeight());
    UInt32              levelId         = this->GetDgnLevel (pEntity->layerId());
    Int32               dgnStyleIndex   = this->GetDgnStyle (pEntity->linetypeId());
    UInt32              graphicGroup    = 0;           // May be reset from xData.
    bool                invisible       = (AcDb::kInvisible == pEntity->visibility());

    DgnElementClass    elementClass    = DgnElementClass::Primary;
    if (0 != (mask & HEADERRESTOREMASK_CONSTRUCTIONCLASS))
        elementClass = DgnElementClass::Construction;
    else if (0 != (mask & HEADERRESTOREMASK_DIMENSIONCLASS))
        elementClass = DgnElementClass::Dimension;

    if (HEADERRESTOREMASK_None != mask)
        {
        this->SetElementSymbology (eeh, 
                        levelId, 
                        (mask & HEADERRESTOREMASK_Color)   != 0 ? &dgnColorIndex : NULL,
                        (mask & HEADERRESTOREMASK_Style)   != 0 ? &dgnStyleIndex : NULL,
                        (mask & HEADERRESTOREMASK_Weight)  != 0 ? &dgnWeightIndex : NULL,
                        (mask & HEADERRESTOREMASK_Color)   != 0 ? &dgnColorIndex : NULL,
                        (mask & HEADERRESTOREMASK_Visible) != 0 ? &invisible : NULL,
                        pEntity->linetypeScale(),
                        this->GetDgnTransparency(pEntity->transparency(), pEntity),
                        elementClass);
        }

    // set uniqueId
    MSElementP          element = eeh.GetElementP ();
    if (NULL == element)
        return  BSIERROR;

    element->ehdr.uniqueId = this->ElementIdFromObject (pEntity);

    this->ExtractLinkagesAndGraphicGroupFromEntityXData (eeh, pEntity);
    if (0 != (mask & HEADERRESTOREMASK_XAttributes))
        this->ExtractXAttributesFromExtensionDictionary (eeh, pEntity);
    this->ExtractMaterialFromEntity (eeh, pEntity);
    this->ExtractPlotStyleFromEntity (eeh, pEntity);

    // post process hyperlinks
    if (this->HasHyperlinkXData(pEntity))
        this->AddPostProcessObject (pEntity);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::ExtractXAttributesFromExtensionDictionary (EditElementHandleR outElement, AcDbObjectCP acObject)
    {
    // open extension dictionary
    AcDbDictionaryPointer   acDictionary (acObject->extensionDictionary(), AcDb::kForRead);
    if (Acad::eOk != acDictionary.openStatus())
        return  CantOpenObject;

    // find our xrecord
    AcDbObjectId            objectId;
    if (Acad::eOk != acDictionary->getAt(StringConstants::XDataKey_XAttribute, objectId))
        return  RealDwgSuccess;

    // open the xrecord
    AcDbXrecordPointer      acXrecord (objectId, AcDb::kForRead);
    if (Acad::eOk != acXrecord.openStatus())
        return  CantOpenObject;

    // extract resbuf from the xrecord, and add the xattributes to element
    RealDwgResBuf           *resbufAll = NULL, *resbufXAttr = NULL;
    if (Acad::eOk == acXrecord->rbChain((struct resbuf**)&resbufAll, acObject->database()) &&
        NULL != (resbufXAttr = RealDwgXDataUtil::FindXDataByKey(resbufAll, AcDb::kDxfXdInteger32, StringConstants::XDataKey_XAttribute, false)))
        RealDwgXDataUtil::AddXAttributes (outElement, resbufXAttr);

    RealDwgResBuf::Free (resbufAll);
    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt        AppendBinaryXDataLinkage (EditElementHandleR outElement)
    {
    // build the xdata linkage header & data:
    size_t              xdataSize = RealDwgXDataUtil::XferBinaryData.size ();
    size_t              linkageSize = xdataSize + sizeof(LinkageHeader) + sizeof(UInt32);
    if (linkageSize >= MAX_ELEMENT_SIZE)
        return  XDataError;

    LinkageHeader       linkageHeader;
    ElementLinkageUtil::InitLinkageHeader (linkageHeader, LINKAGEID_XData, linkageSize);

    // get back the actual linkage size used on the element
    linkageSize = 2 * LinkageUtil::GetWords (&linkageHeader);

    byte*               xdataLinkage = (byte*) calloc (1, linkageSize);
    // set xdata size
    memcpy (xdataLinkage, &xdataSize, sizeof(UInt32));
    // set xdata buffer
    memcpy (xdataLinkage + sizeof(UInt32), &RealDwgXDataUtil::XferBinaryData.front(), xdataSize);

    // append xdata linkage to the element
    StatusInt   status = outElement.AppendElementLinkage (NULL, linkageHeader, xdataLinkage);

    free (xdataLinkage);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::ExtractLinkagesAndGraphicGroupFromEntityXData (EditElementHandleR outElement, AcDbObjectCP pObject)
    {
    // Get all XData chain from ARX object..
    RealDwgResBuf*  pAllXData;
    if (NULL == (pAllXData = static_cast<RealDwgResBuf*> (pObject->xData ())) )
        return RealDwgSuccess;

    // separate the XData chain into MicroStation XData, all the rest remains as nonMicroStation XData.
    RealDwgResBuf*  pNonMicroStationXData;
    RealDwgResBuf*  pMicroStationXData = RealDwgXDataUtil::SeparateXData (pNonMicroStationXData, pAllXData, StringConstants::RegAppName_MicroStation);

    // We save the non-MicroStation XData on the element as an XData Linkage.
    if (NULL != pNonMicroStationXData)
        {
        if (!GetSettings().IgnoreXData())
            {
            AcDbDatabase*       database = pObject->database ();
            if (NULL == database)
                database = this->GetDatabase ();
            // extract xdata from the object
            RealDwgXDataUtil::BinaryDataFromXData (RealDwgXDataUtil::XferBinaryData, pNonMicroStationXData, database);
            // append xdata linkage to the element
            if (SUCCESS != AppendBinaryXDataLinkage(outElement))
                DIAGNOSTIC_PRINTF ("Failed appending XDATA linkage to element ID=%I64d\n", this->ElementIdFromObject(pObject));
            }
        RealDwgResBuf::Free (pNonMicroStationXData);
        }

    // We process the MicroStation XData into application data that we need, storing it in the places that MicroStation can find and use it.

    // Find the XData that corresponds to MicroStation Application Linkages.
    RealDwgResBuf*  pBinaryChunk;
    if (NULL != (pBinaryChunk = RealDwgXDataUtil::FindXDataByKey (pMicroStationXData, AcDb::kDxfXdBinaryChunk, StringConstants::XDataKey_ApplicationLinkage, false)))
        {
        MSElementP              element = (MSElementP)malloc (MAX_ELEMENT_SIZE);
        RealDwgBinaryData       binaryData;

        pBinaryChunk->GetBinaryChunk (binaryData, false);

        for (RealDwgResBuf* pNext = pBinaryChunk->GetNext(); (NULL != pNext) && (AcDb::kDxfXdBinaryChunk == pNext->GetResType()); pNext = pNext->GetNext())
            pNext->GetBinaryChunk (binaryData, true);

        size_t                  dataSize = binaryData.size() / 2;
        if (dataSize > 0 && (element->ehdr.elementSize + dataSize) < MAX_ELEMENT_WORDS)
            {
            outElement.GetElementCP()->CopyTo (*element);

            Int16               *pEnd= element->buf + element->hdr.ehdr.elementSize;
            memcpy (pEnd, (UShort *) &binaryData.front(), 2 * dataSize);
            element->ehdr.elementSize += (UInt32)dataSize;

            outElement.ReplaceElement (element);

            DwgConvertEvents::GetInstance().FireAfterElementFromObject(outElement, *this);
            }

        free (element);
        }

    // Get graphic group data.
    RealDwgResBuf*              pGraphicGroupData;
    if (NULL != (pGraphicGroupData =  RealDwgXDataUtil::FindXDataByKey (pMicroStationXData, AcDb::kDxfXdInteger32, StringConstants::XDataKey_GraphicGroup, false)))
        {
        UInt32                  graphicGroupNumber = (UInt32) pGraphicGroupData->GetInt32();
        if (0 != graphicGroupNumber)
            SetGraphicGroup (outElement.GetElementDescrP(), graphicGroupNumber);
        }

    // Get transparency - honor legacy transparency workaround, but no longer create it for R2010 and above DWG's.
    RealDwgResBuf*              pTransparencyData;
    if (NULL != (pTransparencyData =  RealDwgXDataUtil::FindXDataByKey (pMicroStationXData, AcDb::kDxfXdReal, StringConstants::XDataKey_Transparency, false)))
        {
        ElementPropertiesSetter propSetter;
        propSetter.SetTransparency (pTransparencyData->GetDouble());
        propSetter.Apply (outElement);
        }

    // Get dependency linkages.
    /*-----------------------------------------------------------------------------------
    We are now stuck with legacy dependency name "Dep    ".  AcDbDictionary::setAt fails
    with a key containing spaces like this one, so we have to rename our depedency linkage
    key in order to save dependency linkages to XRecords using RealDWG.  But DWG files we
    have previsouly saved using OpenDWG contain the keys with spaces in them.  At least
    for a long while we have no choice but check for both keys.
    -----------------------------------------------------------------------------------*/
    RealDwgResBuf*              pAppDependencyData;
    if (NULL != (pAppDependencyData = RealDwgXDataUtil::FindXDataByKey (pMicroStationXData, AcDb::kDxfXdInteger16, StringConstants::XDataKey_ApplicationDependency, false)) ||
        NULL != (pAppDependencyData = RealDwgXDataUtil::FindXDataByKey (pMicroStationXData, AcDb::kDxfXdInteger16, StringConstants::XDataKey_ApplicationDependencyOld, false)))
        {
        DependencyLinkage       *pDependencyLinkage = NULL;
        while (SUCCESS == RealDwgXDataUtil::DependencyLinkageFromXData (&pDependencyLinkage, &pAppDependencyData, *this))
            {
            DependencyManagerLinkage::AppendLinkage (outElement, *pDependencyLinkage, 0);
            free (pDependencyLinkage);
            }
        }

    // Get legacy XAttributes.  Still have to extract them from xdata, althought since Vancouver XA is only saved as xrecord on extended dictionary.
    RealDwgResBuf*              pXAttributeData;
    if (NULL != (pXAttributeData = RealDwgXDataUtil::FindXDataByKey (pMicroStationXData, AcDb::kDxfXdInteger32, StringConstants::XDataKey_XAttribute, false)))
        RealDwgXDataUtil::AddXAttributes (outElement, pXAttributeData);

    RealDwgResBuf::Free (pMicroStationXData);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/02
+---------------+---------------+---------------+---------------+---------------+------*/
DEllipse3d      ConvertToDgnContext::TransformDEllipse (DEllipse3dCR inEllipse, TransformCR transform)
    {
    RotMatrix       rotMatrix;
    double          r0 = 0.0, r1 = 0.0, theta0 = 0.0, sweep = 0.0;
    DPoint3d        center3d;
    DEllipse3d      dEllipse;

    transform.Multiply (dEllipse, inEllipse);
    dEllipse.GetScaledRotMatrix (center3d, rotMatrix, r0, r1, theta0, sweep);

    DVec3d  column2;
    rotMatrix.getColumn (&column2, 2);
    if (!m_threeD && column2.z < 0.0)
        {
        DVec3d  column1;
        rotMatrix.getColumn (&column1, 1);
        column1.negate();
        column2.negate();
        rotMatrix.setColumn (&column1, 1);
        rotMatrix.setColumn (&column2, 2);
        theta0 = -theta0;
        sweep = -sweep;
        }

    this->ValidatePoints (&center3d, 1);

    dEllipse.FromScaledRotMatrix (center3d, rotMatrix, r0, r1, theta0, sweep);

    return  dEllipse;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus       ConvertToDgnContext::CreateElementFromDEllipse (EditElementHandleR eeh, DEllipse3dCR inEllipse, TransformCR transform)
    {
    DEllipse3d      ellipse = this->TransformDEllipse (inEllipse, transform);

    if (BSISUCCESS != ArcHandler::CreateArcElement(eeh, NULL, ellipse, this->GetThreeD(), *this->GetModel()))
        return EntityError;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromGeNurbCurve2d (EditElementHandleR eeh, const AcGeNurbCurve2d* pNurbCurve, TransformCR transform)
    {
    int                     degree      = pNurbCurve->degree();
    int                     nKnots      = pNurbCurve->numKnots();
    int                     nPoles      = pNurbCurve->numControlPoints();
    bool                    rational    = Adesk::kTrue == pNurbCurve->isRational();

    DPoint3dArray           poles;
    DoubleArray             knots;
    DoubleArray             weights;

    if (0 != nKnots)
        {
        for (int iKnot = 0; iKnot < nKnots; iKnot++)
            knots.push_back (pNurbCurve->knotAt (iKnot));
        }

    if (rational)
        {
        for (int iWeight = 0; iWeight < nPoles; iWeight++)
            weights.push_back (pNurbCurve->weightAt (iWeight));
        }

    DPoint3d    tmpPoint;
    for (int iPole = 0; iPole < nPoles; iPole++)
        poles.push_back (RealDwgUtil::DPoint3dFromGePoint2d (tmpPoint, pNurbCurve->controlPointAt (iPole)));


    return this->CreateElementFromDwgSplineParamsAndTransform (eeh, degree, nPoles, nKnots, false, rational, &poles.front(),
                                (0 == nKnots) ? NULL : &knots.front(), (!rational) ? NULL : &weights.front(), transform);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromGeNurbCurve3d (EditElementHandleR eeh, const AcGeNurbCurve3d* pNurbCurve, TransformCR transform)
    {
    int                     degree      = pNurbCurve->degree();
    int                     nKnots      = pNurbCurve->numKnots();
    int                     nPoles      = pNurbCurve->numControlPoints();
    bool                    rational    = Adesk::kTrue == pNurbCurve->isRational();

    DPoint3dArray           poles;
    DoubleArray             knots;
    DoubleArray             weights;

    if (0 != nKnots)
        {
        for (int iKnot = 0; iKnot < nKnots; iKnot++)
            knots.push_back (pNurbCurve->knotAt (iKnot));
        }

    if (rational)
        {
        for (int iWeight = 0; iWeight < nPoles; iWeight++)
            weights.push_back (pNurbCurve->weightAt (iWeight));
        }

    DPoint3d    tmpPoint;
    for (int iPole = 0; iPole < nPoles; iPole++)
        poles.push_back (RealDwgUtil::DPoint3dFromGePoint3d (tmpPoint, pNurbCurve->controlPointAt (iPole)));


    return this->CreateElementFromDwgSplineParamsAndTransform (eeh, degree, nPoles, nKnots, false, rational, &poles.front(),
                                (0 == nKnots) ? NULL : &knots.front(), (!rational) ? NULL : &weights.front(), transform);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::CreateBsplineCurveFromDwgSplineParams
(
MSBsplineCurve&             curve,
int                         degree,
size_t                      nPoles,
size_t                      nKnots,
bool                        closed,
bool                        rational,
bool                        showFrame,
DPoint3dP                   pPoles,
const double*               pKnots,
const double*               pWeights,
TransformCR                 transform
)
    {
    int                     status;
    int                     numPoles  = (int) nPoles;
    bool                    bValidNumber = nKnots == (closed ? nKnots == nPoles + 2*(degree + 1) - 1 : nPoles + degree + 1);
    bool                    bUseKnots = bValidNumber && (nKnots > 0 && NULL != pKnots);
    
    //In some curves, the numbers of Knots and Poles are not compatible, this assertion will detect this case.  TR#302294.
    if (nKnots != 0 && !bValidNumber)
        DIAGNOSTIC_PRINTF ("Unexpected number of BSpline curve knots: %d, poles: %d, degrees: %d\n", nKnots, numPoles, degree);

    memset (&curve, 0, sizeof(curve));

    curve.params.order            = degree + 1;
    curve.params.numPoles         = numPoles;
    curve.params.numKnots         = bUseKnots ? (closed ? numPoles - 1 : numPoles - (degree + 1)) : 0;
    curve.params.closed           = closed ? 1 : 0;
    curve.display.curveDisplay    = 1;
    curve.display.polygonDisplay  = showFrame ? 1 : 0;
    curve.rational                = rational ? 1 : 0;

    if (SUCCESS != (status = bspcurv_allocateCurve (&curve)))
        return status;

    if (bUseKnots)
        {
        memcpy (curve.knots, pKnots, nKnots * sizeof (double));
        status = bspknot_normalizeKnotVector (curve.knots, curve.params.numPoles, curve.params.order, curve.params.closed);
        }

    memcpy  (curve.poles, pPoles, numPoles * sizeof (DPoint3d));
    transform.Multiply (curve.poles, numPoles);

    this->ValidatePoints (curve.poles, numPoles);

    if (rational && NULL != pWeights)
        {
        memcpy (curve.weights, pWeights, numPoles * sizeof (double));
        bsputil_weightPoles (curve.poles, curve.poles, curve.weights, numPoles);
        }

    return status;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromDwgSplineParamsAndTransform
(
EditElementHandleR          eeh,
int                         degree,
size_t                      nPoles,
size_t                      nKnots,
bool                        closed,
bool                        rational,
DPoint3dP                   pPoles,
double*                     pKnots,
double*                     pWeights,
TransformCR                 transform
)
    {
    StatusInt               status;
    MSBsplineCurve          curve;
    if (BSISUCCESS != (status = this->CreateBsplineCurveFromDwgSplineParams (curve, degree, nPoles, nKnots, closed, rational, m_pFileHolder->GetDatabase()->splframe(),
                                                                             pPoles, pKnots, pWeights, transform)))
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    status = BSplineCurveHandler::CreateBSplineCurveElement (eeh, NULL, curve, this->GetThreeD(), *this->GetModel());
    bspcurv_freeCurve (&curve);

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromDwgSplineParams
(
EditElementHandleR          eeh,
int                         degree,
int                         nPoles,
int                         nKnots,
bool                        closed,
bool                        rational,
DPoint3dP                   pPoles,
double*                     pKnots,
double*                     pWeights
)
    {
    return this->CreateElementFromDwgSplineParamsAndTransform (eeh, degree, nPoles, nKnots, closed, rational, pPoles, pKnots, pWeights, this->GetTransformToDGN());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateElementFromArcParams
(
EditElementHandleR          eeh,
AcGeVector3d const*         pGeNormal,
AcGePoint3d const&          geCenter,
RotMatrixCP                 pRotMatrix,
double                      xRadius,
double                      yRadius,
double                      startAngle,
double                      sweepAngle,
double                      thickness,
bool                        closed,
AcDbEntity*                 pEntity
)
    {
    MSElementDescrP         pDescr = NULL;
    RotMatrix               rotMatrix;
    DPoint3d                center, zeroPoint;
    DEllipse3d              dEllipse;
    Transform               extrusionTransform, compositeTransform;

    if (NULL == pRotMatrix)
        rotMatrix.InitIdentity ();
    else
        rotMatrix = *pRotMatrix;

    if (NULL == pGeNormal)
        compositeTransform = this->GetTransformToDGN();
    else
        compositeTransform.productOf (&this->GetTransformToDGN(), RealDwgUtil::GetExtrusionTransform (extrusionTransform, *pGeNormal, 0.0));

    zeroPoint.Zero ();
    DVec3d  column0;
    rotMatrix.GetColumn (column0, 0);
    DVec3d  column1;
    rotMatrix.GetColumn (column1, 1);

    DPoint3d    outCenter;
    dEllipse.InitFromScaledVectors (zeroPoint, column0, column1, xRadius, yRadius, startAngle, sweepAngle);
    compositeTransform.Multiply (dEllipse);
    dEllipse.GetScaledRotMatrix (outCenter, rotMatrix, xRadius, yRadius, startAngle, sweepAngle);

    RealDwgUtil::DPoint3dFromGePoint3d (center, geCenter);
    this->GetTransformToDGN().Multiply (center);
    this->ValidatePoints (&center, 1);

    DVec3d  column2;
    rotMatrix.GetColumn (column2, 2);
    if (!this->GetThreeD() && column2.z < 0.0)
        {
        rotMatrix.GetColumn (column1, 1);
        column1.Negate();
        column2.Negate();
        rotMatrix.SetColumn (column1, 1);
        rotMatrix.SetColumn (column2, 2);
        startAngle = -startAngle;
        sweepAngle = -sweepAngle;
        }

    StatusInt   status;
    if (closed)
        status = EllipseHandler::CreateEllipseElement (eeh, NULL, center, xRadius, yRadius, rotMatrix, m_threeD, *m_model);
    else
        status = ArcHandler::CreateArcElement (eeh, NULL, center, xRadius, yRadius, rotMatrix, startAngle, sweepAngle, m_threeD, *m_model);

    if (BSISUCCESS != status)
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    this->ElementHeaderFromEntity (eeh, pEntity);
    if (NULL != pGeNormal && 0.0 != thickness)
        this->ApplyThickness (eeh, thickness, *pGeNormal, false);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AddLineWidthLinkage
(
EditElementHandleR          eeh,
double                      startWidth,
double                      endWidth,
bool                        threeD,
DgnModelP                   modelRef,
int                         continuousStyleId,
AcGeVector3d const*         normal
)
    {
    LineStyleParams         styleParams;
    double                  arcRadius   = 0.0;

    // NOTE: There should only be one line style linkage per element (except multilines)
    // By extracting the existing params, the modifiers are unified.
    if (BSISUCCESS != LineStyleUtil::GetParamsFromElement(&styleParams, eeh))
        return;

    styleParams.modifiers  |= (STYLEMOD_SWIDTH | STYLEMOD_TRUE_WIDTH);
    if (startWidth != endWidth)
        styleParams.modifiers |= STYLEMOD_EWIDTH;

    styleParams.startWidth = startWidth;
    styleParams.endWidth   = endWidth;

    styleParams.rMatrix = RotMatrix::FromIdentity ();

    if (SUCCESS == ArcHandler::Extract(NULL, NULL, NULL, NULL, &arcRadius, NULL, &styleParams.rMatrix, NULL, NULL, eeh))
        {
        double      arcDiameter = arcRadius * 2.0;

        // AutoCAD just truncates the width if it is greater than the radius.
        if (styleParams.startWidth > arcDiameter)
            styleParams.startWidth = arcDiameter;

        if (styleParams.endWidth > arcDiameter)
            styleParams.endWidth = arcDiameter;

        if (threeD && !styleParams.rMatrix.IsIdentity())
            styleParams.modifiers |= STYLEMOD_RMATRIX;
        }
    else if (threeD && nullptr != normal)
        {
        DPoint3d    zAxis;
        RealDwgUtil::RotMatrixFromArbitraryAxis (styleParams.rMatrix, RealDwgUtil::DPoint3dFromGeVector3d(zAxis, *normal));
        if (!styleParams.rMatrix.isIdentity())
            styleParams.modifiers |= STYLEMOD_RMATRIX;
        }

    if (AddLinestyleLinkageFromParams(eeh, &styleParams, threeD))
        {
        // not sure if below logic is still needed for Vancouver?
        MSElementP  element = eeh.GetElementP ();
        Int32       saveStyle =  element->hdr.dhdr.symb.style;

        element->hdr.dhdr.symb.style = continuousStyleId;                   // If by-level style, style not found as level cache not initialized (161550).
        DisplayHandler::ValidateElementRange (element, modelRef, false);    // Width will change range.
        element->hdr.dhdr.symb.style = saveStyle;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::SetLineWidth
(
EditElementHandleR          eeh,
double                      startWidth,
double                      endWidth,
AcGeVector3d const*         normal
)
    {
    if ( (0.0 == startWidth && 0.0 == endWidth) )
        return;

    AddLineWidthLinkage (eeh, startWidth * this->GetScaleToDGN(), endWidth * this->GetScaleToDGN(), m_threeD, m_model, GetDgnStyle (m_pFileHolder->GetDatabase()->continuousLinetype()), normal);

    for (ChildEditElemIter child(eeh, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
        AddLineWidthLinkage (child, startWidth * this->GetScaleToDGN(), endWidth * this->GetScaleToDGN(), m_threeD, m_model, GetDgnStyle (m_pFileHolder->GetDatabase()->continuousLinetype()), nullptr);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley  07/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::LoadElementIntoCache (EditElementHandleR eeh)
    {
    if (m_noSaveToDgnCache)
        return  SUCCESS;

    MSElementDescrP     elmdscr = eeh.GetElementDescrP ();
    StatusInt           status = LoadElementIntoCache (elmdscr);

    // update the persistent elementref in the output element handle:
    if (BSISUCCESS == status)
        eeh.SetElementRef (elmdscr->h.elementRef, m_model);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::LoadElementIntoCache (MSElementDescrP pDescr)
    {
    for (MSElementDescrP pCurrElmdscr = pDescr; pCurrElmdscr != NULL; pCurrElmdscr = pCurrElmdscr->h.next)
        {
        // compute element range for all elements, except for shared cells whose range should have been done by now:
        if (SHAREDCELL_DEF_ELM != pCurrElmdscr->el.ehdr.type)
            pDescr->Validate (m_model);

        StatusInt       status;
        if (SUCCESS != (status = dgnModel_loadDscrFromFile (m_model, pCurrElmdscr, m_fileTime)))
            return status;

        // Add the new named type 34 to shared cell cache as some element handlers may need it later.
        // This step used to be done in NonModelElementRefList::AddElemDscr prior to Vancouver, but it now goes into SharedCellDefHandler.
        if (SHAREDCELL_DEF_ELM == pCurrElmdscr->el.ehdr.type && !pCurrElmdscr->el.sharedCellDef.anonymous && NULL != pCurrElmdscr->h.elementRef)
            m_dgnFile->AddSharedCellDefinition((PersistentElementRefP)pCurrElmdscr->h.elementRef);

        // Make sure that we maintain the highestID in case some were assigned by loading the element.
        ElementId       highestCurrentId;
        if ((highestCurrentId = m_dgnFile->GetHighestID ()) >= m_nextAvailableId)
            m_nextAvailableId = highestCurrentId + 1;
        }
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley  07/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::LoadElementIntoCache (MSElementCP pElement)
    {
    EditElementHandle eeh (pElement, m_model);
    return LoadElementIntoCache (eeh);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley     11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::ValidateElement (MSElementDescrP pDescr)
    {
    if (NULL != pDescr)
        pDescr->Validate (m_model);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::ApplyThickness
(
EditElementHandleR          eeh,
double                      thickness,
const AcGeVector3d&         normal,
bool                        capIfClosed,
bool                        alwaysUseDirection
)
    {
    if (0.0 == thickness || !eeh.IsValid())
        return RealDwgSuccess;

    DVec3d                  extrusionDirection;
    RealDwgUtil::DVec3dFromGeVector3d (extrusionDirection, normal);

    ElementPropertiesSetter propSetter;
    propSetter.SetThickness (thickness * this->GetScaleToDGN(), &extrusionDirection, capIfClosed, alwaysUseDirection);
    propSetter.Apply (eeh);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
bool       ConvertToDgnContext::ElementIdInUse (ElementId elementId, bool dictionaryModel)
    {
    return _ElementIdInUse (elementId, dictionaryModel);
    }

bool       ConvertToDgnContext::_ElementIdInUse (ElementId elementId, bool dictionaryModel)
    {
    return (NULL != m_dgnFile->FindByElementId (elementId, dictionaryModel));
    }

void       ConvertToDgnContext::_AddToElementIdsInUse (ElementId id)
    {
    //Empty on purpose
    }

void       ConvertToDgnContext::AddToElementIdsInUse (ElementId id)
    {
    _AddToElementIdsInUse (id);
    }

void       ConvertToDgnMultiProcessingContext::_AddToElementIdsInUse (ElementId id)
    {
    m_elementIdsInUse.insert (id);
    }

bool       ConvertToDgnMultiProcessingContext::_ElementIdInUse (ElementId elementId, bool dictionaryModel)
    {
    return ConvertToDgnContext::_ElementIdInUse (elementId, dictionaryModel) || (m_elementIdsInUse.find (elementId) != m_elementIdsInUse.end ());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                     RayBentley     07/05
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32          ConvertToDgnContext::GetDgnColorIndexFromRGB
(
byte                        red,
byte                        green,
byte                        blue,
WCharCP                     pBook,
WCharCP                     pName
)
    {
    RgbColorDef     rgbColor;

    rgbColor.red   = red;
    rgbColor.green = green;
    rgbColor.blue  = blue;

    UInt32      compositeColor = this->GetFileHolder().GetDgnSymbologyData()->GetColorIndex (&rgbColor, *this);

    // below call tries to find the extended color index, creates a new one if not found.
    IntColorDef colorDef (rgbColor);
    UInt32      elementColor = DgnColorMap::CreateElementColor (colorDef, pBook, pName, *this->GetFile());

    if (INVALID_COLOR != elementColor)
        compositeColor = elementColor;
        
    return compositeColor;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                     RayBentley     11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      ConvertToDgnContext::GetDgnColor (AcCmEntityColor const& color)
    {
    switch (color.colorMethod())
        {
        case AcCmEntityColor::kByBlock:
            return COLOR_BYCELL;

        case AcCmEntityColor::kByLayer:
            return COLOR_BYLEVEL;

        case AcCmEntityColor::kByColor:
            return this->GetDgnColorIndexFromRGB (color.red(), color.green(), color.blue(), nullptr, nullptr);

        default:
            return this->GetDgnColor (color.colorIndex());

        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                     RayBentley     11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      ConvertToDgnContext::GetDgnColor (AcCmColor const& color)
    {
    switch (color.colorMethod())
        {
        case AcCmEntityColor::kByBlock:
            return COLOR_BYCELL;

        case AcCmEntityColor::kByLayer:
            return COLOR_BYLEVEL;

        case AcCmEntityColor::kByColor:
            {
            ACHAR const*    bookName = color.bookName ();
            ACHAR const*    colorName  = color.colorName ();

            if ( (NULL != colorName) && (0 != colorName[0]) && (NULL != bookName) && (0 != bookName[0]) )
                {
                return this->GetDgnColorIndexFromRGB (color.red(), color.green(), color.blue(), bookName, colorName);
                }
            else
                {
                return this->GetDgnColorIndexFromRGB (color.red(), color.green(), color.blue(), nullptr, nullptr);
                }
            }

        default:
            return this->GetDgnColor (color.colorIndex());

        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                     RayBentley     11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      ConvertToDgnContext::GetDgnColor (UInt16 colorIndex)
    {
    switch (colorIndex)
        {
        case DWG_COLOR_ByBlock:
            return COLOR_BYCELL;

        case DWG_COLOR_ByLayer:
            return COLOR_BYLEVEL;

        case DWG_COLOR_Foreground255:
            return  this->GetRemappedForegroundColorIndex();

        default:
            DwgSymbologyData*               dwgSymbologyData;
            DgnSymbologyData*               dgnSymbologyData;

            if ( (NULL != (dgnSymbologyData = m_pFileHolder->GetDgnSymbologyData ())) && (NULL != (dwgSymbologyData = m_pFileHolder->GetDwgSymbologyData ())) )
                return dgnSymbologyData->RemapColorIndex ((UInt32) colorIndex, dwgSymbologyData, *this);
            else
                return 0;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                     RayBentley     11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
Int32           ConvertToDgnContext::GetDgnStyle (AcDbObjectId lineStyleId)
    {
    return  m_pFileHolder->GetLinetypeIndex()->GetDgnId (lineStyleId.handle());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                     RayBentley     11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32          ConvertToDgnContext::GetDgnLevel
(
AcDbObjectId       layerId
)
    {
    return  m_pFileHolder->GetLayerIndex ()->GetDgnId (layerId.handle());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/10
+---------------+---------------+---------------+---------------+---------------+------*/
double          ConvertToDgnContext::GetDgnTransparency
(
const AcCmTransparency& dwgTransparency,
AcDbEntityCP            entity
)
    {
    if (NULL == entity)
        return this->GetDgnTransparency (dwgTransparency);

    AcDbObjectId        layerId = entity->layerId ();
    AcDbObjectId        ownerId = entity->ownerId ();

    return  this->GetDgnTransparency (dwgTransparency, &layerId, &ownerId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/10
+---------------+---------------+---------------+---------------+---------------+------*/
double          ConvertToDgnContext::GetDgnTransparency
(
const AcCmTransparency& dwgTransparency,
const AcDbObjectId*     layerId,
const AcDbObjectId*     blockId
)
    {
    double      dgnTransparency = 0.0;

    if (dwgTransparency.isByAlpha())
        {
        // DWG has reversed meaning by transparecy value: 0=completely transparent; 1=completely opaque.
#if RealDwgVersion >= 2011
        dgnTransparency = 1.0 - dwgTransparency.alphaPercent();
#else
        dgnTransparency = 1.0 - (double)dwgTransparency.alpha() / 255.0;
#endif
        // round down percent value:
        dgnTransparency = floor(100 * dgnTransparency) / 100.0;
        /*-------------------------------------------------------------------------------
        Apply effective transparency to element.
        deriving from the fomula:
        NetTransparency = 1 - (1 - elememntTransparency) * (1 - levelTransparency) * (1 - modelTransparency)
        to equation:
        elementTransparency = (netTransparency - levelTransparency) / (1 - levelTransparency)

        However, this only works for levelTransparency <= netTransparency.  When layer transparency is greater
        than entity transparency, make entity transparency "ByLayer".
        -------------------------------------------------------------------------------*/
        if (NULL != layerId && layerId->isValid())
            {
            AcDbLayerTableRecordPointer layer(*layerId, AcDb::kForRead);
            if (Acad::eOk == layer.openStatus())
                {
                AcCmTransparency    layerTransparency = layer->transparency ();
                double              levelTransparency = this->GetDgnTransparency (layerTransparency);

                if (TRANSPARENCY_IsSame(1.0, levelTransparency))
                    dgnTransparency = 0.0;
                else
                    dgnTransparency = (dgnTransparency - levelTransparency) / (1.0 - levelTransparency);

                if (dgnTransparency < 0.0)
                    dgnTransparency = TRANSPARENCY_ByLayer;
                }
            }
        }
    else if (dwgTransparency.isByLayer())
        {
        /*-------------------------------------------------------------------------------
        FUTUREWORK_TRANSPARENCY: MicroStation needs to support BYLAYER transparency!

        Temporary workaround: set BYLAYER transparency to 0%, which gets round-tripped back
        to BYLAYER.  Level transparency always affects element, as shown in above formula.
        This can work because a DWG entity will only have either BYLAYER/BYBLOCK or an 
        effective value.
        -------------------------------------------------------------------------------*/
        dgnTransparency = TRANSPARENCY_ByLayer;
        }
    else if (dwgTransparency.isByBlock() && NULL != blockId)
        {
        /*-------------------------------------------------------------------------------
        FUTUREWORK_TRANSPARENCY: MicroStation needs to support BYCELL transparency!

        Temporary workaround:
        1) Set effective value on element based on block's first instance's transparency.
        2) Set BYBLOCK transparency to 1% for round-trip purpose, if the block has no
            instance.
        This is rasther a kludge workaround, but it's better than losing BYBLOCK on a DWG 
        save or not displaying transparency at all on DWG open.
        -------------------------------------------------------------------------------*/
        dgnTransparency = TRANSPARENCY_ByBlock;

        AcDbDatabase*   dwg = this->GetDatabase ();
        AcDbObjectId    ownerId = *blockId;
        if (!ownerId.isValid() && ownerId != acdbSymUtil()->blockModelSpaceId(dwg) && ownerId != acdbSymUtil()->blockPaperSpaceId(dwg))
            {
            AcDbBlockTableRecordPointer block(ownerId, AcDb::kForRead);
            if (Acad::eOk == block.openStatus() && !RealDwgUtil::IsModelOrPaperSpace(block.object()))
                {
                AcDbObjectIdArray       insertIds;
                if (Acad::eOk == block->getBlockReferenceIds(insertIds) && insertIds.length() > 0)
                    {
                    AcDbBlockReferencePointer insert(insertIds[0], AcDb::kForRead);
                    if (Acad::eOk == insert.openStatus())
                        return  this->GetDgnTransparency (insert->transparency(), insert);
                    }
                }
            }
        }

    return  fabs(dgnTransparency);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::GetActiveSymbology (DgnPlatform::Symbology& symbology)
    {
    AcDbDatabase*   pDatabase = m_pFileHolder->GetDatabase();

    if (this->PutActiveSettingsInDgn())
        {
        // try to active symbology from the host - default to seed's TCB symbology
        TcbP        hostTcb = new Tcb ();
        if (DwgPlatformHost::Instance()._GetPersistentTcbForSilentSave(hostTcb))
            symbology = hostTcb->symbology;
        else
            symbology = this->GetFile()->GetPersistentTcb()->symbology;

        // TR# 168010 - If an extended index is stored in the active color, we can't use the "in-memory" version
        // as it would have been lost when the file was closed - revert to the database.
        if (0 != ColorUtil::GetExtendedIndexFromRawColor(symbology.color))
            symbology.color  = this->GetDgnColor (pDatabase->cecolor());

        delete hostTcb;
        }
    else
        {
        symbology.color  = this->GetDgnColor  (pDatabase->cecolor());
        symbology.weight = this->GetDgnWeight (pDatabase->celweight());
        symbology.style  = this->GetDgnStyle  (pDatabase->celtype());
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void stripSpaceFromCellName (Tcb* pTcb, Tcb::ActiveCellType ct)
    {
    WString cn = pTcb->GetActiveCellName (ct);
    cn.Trim ();
    
    pTcb->SetActiveCellName (ct, cn.c_str ());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/15
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId    GetValidActiveLayer (AcDbDatabase* dwg)
    {
    // MicroStation always turns the active level on - TFS 7725.
    AcDbObjectId                activeLayerId = dwg->clayer ();
    AcDbLayerTableRecordPointer acLayer (activeLayerId, AcDb::kForRead);

    if (Acad::eOk == acLayer.openStatus() && (acLayer->isOff() || acLayer->isFrozen()))
        {
        acLayer.close ();

        AcDbLayerTableIterator* iter = nullptr;
        AcDbLayerTablePointer   layerTable (dwg->layerTableId(), AcDb::kForRead);

        if (Acad::eOk == layerTable.openStatus() && Acad::eOk == layerTable->newIterator(iter))
            {
            for (iter->start(); !iter->done(); iter->step())
                {
                AcDbObjectId    layerId;
                if (Acad::eOk != iter->getRecordId(layerId) || activeLayerId == layerId)
                    continue;

                if (Acad::eOk == acLayer.open(layerId, AcDb::kForRead) && !acLayer->isOff() && !acLayer->isFrozen())
                    {
                    // a visible layer is found - reset active layer:
                    activeLayerId = layerId;
                    break;
                    }
                }
            }
        }

    return  activeLayerId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::CreateTCBFromDatabaseVariables (Tcb* pTcb)
    {
    // this method sets file header only - it does not synch host's TCB cache
    RealDwgModelIndexItem*      pActiveModelIndexItem;
    AcDbDatabase*               pDatabase = m_pFileHolder->GetDatabase();

    if (NULL != (pActiveModelIndexItem = m_pFileHolder->GetActiveModelIndexItem()))
        {
        pTcb->activeModel     = pActiveModelIndexItem->GetId();

        AcDbObjectId    blockTableRecordId = pActiveModelIndexItem->GetBlockTableRecordId();
        if (!blockTableRecordId.isNull())
            {
            AcDbBlockTableRecordPointer blockTableRecord (blockTableRecordId, AcDb::kForRead);
            if (Acad::eOk == blockTableRecord.openStatus())
                {
                pTcb->activeViewGroup = this->ElementIdFromObjectId (blockTableRecord->getLayoutId());
                this->ValidateActiveViewgroupId (pTcb->activeViewGroup);
                }
            }
        }

    pTcb->activeLevel = this->GetDgnLevel (GetValidActiveLayer(pDatabase));
    pTcb->symbology.color  = this->GetDgnColor  (pDatabase->cecolor());
    pTcb->symbology.weight = this->GetDgnWeight (pDatabase->celweight());
    pTcb->symbology.style  = this->GetDgnStyle  (pDatabase->celtype());

    AcDbTextStyleTableRecordPointer pTextStyle (pDatabase->textstyle(), AcDb::kForRead);
    if (Acad::eOk != pTextStyle.openStatus())
        {
        // Some DWG files may not have an active textstyle (TR 276096 by Andreas Zieritz) - default to style STANDARD:
        pTextStyle.open (acdbSymUtil()->textStyleStandardId(pDatabase), AcDb::kForRead);
        }

    DgnTextStylePtr     activeTextStyle = DgnTextStyle::GetSettings (*m_dgnFile);
    if (Acad::eOk == pTextStyle.openStatus() && activeTextStyle.IsValid())
        {
        // populate active style, which is a file settings xattr on table element, not a table entry element.
        this->SetDgnTextStyleFromAcDbTextStyle (activeTextStyle, pTextStyle);

        SignedTableIndex*   textstyleIndex = m_pFileHolder->GetTextStyleIndex ();
        if (nullptr != textstyleIndex)
            activeTextStyle->SetID (textstyleIndex->GetDgnId(this->ElementIdFromObjectId(pDatabase->textstyle())));

        AcString        name;
        if (Acad::eOk == pTextStyle->getName(name))
            activeTextStyle->SetName (name.kwszPtr());
        }

    double  textSize = this->GetScaleToDGN() * pDatabase->textsize();
    double  activeTextHeight = 0.0, activeTextWidth = 0.0, activeWidthFactor = 0.0;

    if (activeTextStyle.IsValid())
        {
        activeTextStyle->GetProperty(TextStyle_Height, activeTextHeight);
        activeTextStyle->GetProperty(TextStyle_Width, activeTextWidth);
        activeTextStyle->GetProperty(TextStyle_WidthFactor, activeWidthFactor);
        }

    // fix for TR 126429 Autocad has an active text style and and active text height. The active text style should be
    //   used to set up the data in the tcb. However if the active text height is different from that in the style
    //   then the active height needs to be set to this and the active text width should be calculated based on the active width factor
    if ((0.0 != textSize) && (textSize != activeTextHeight))
        {
        double widthFactor = (0.0 == activeWidthFactor) ? 1.0 : activeWidthFactor;
        activeTextHeight   = textSize;
        activeTextWidth    = textSize * widthFactor;

        if (activeTextStyle.IsValid())
            {
            activeTextStyle->SetProperty(TextStyle_Height, activeTextHeight);
            activeTextStyle->SetProperty(TextStyle_Width, activeTextWidth);
            }
        }

    // save the active style back to file:
    if (activeTextStyle.IsValid())
        DgnTextStyle::ReplaceSettings (*activeTextStyle.get(), *m_dgnFile);

    pTcb->mainDictionaryId          = this->ElementIdFromObjectId (pDatabase->namedObjectsDictionaryId());
    pTcb->textScaleLock             = (activeTextHeight == activeTextWidth);
    pTcb->lineStyleScale_deprecated = pDatabase->ltscale();
    pTcb->designCenterUnits         = pDatabase->insunits();

    // If the design Center units are in synch then it will be acceptable to synch them upon save - designate this by adding userData to file object.
    if (RealDwgUtil::AcDbUnitsValueFromDgnUnits (m_standardTargetUnits) == pTcb->designCenterUnits)
        m_dgnFile->SetDesignCenterUnitsOk (true);

    pTcb->plotAutoInvertColor = 7;
    if (this->GetSettings().SetAxisLockFromOrthoMode())
        pTcb->ext_locks.axis_lock = pDatabase->orthomode();

    pTcb->smartGeomSettings.nIsoparametrics  = pDatabase->isolines();
    pTcb->invisGeomDisplayMode = pDatabase->splframe() ? INVISGEOM_ALWAYS : INVISGEOM_NEVER;

    // TR124469. - if saving settings, dont force association lock on.
    pTcb->ext_locks.association = (2 == pDatabase->dimAssoc()) && RealDwgUtil::MSVersionFromAcVersion(m_pFileHolder->GetDatabase()->originalFileVersion()) >= DwgFileVersion_2000;
    pTcb->ext_locks.ignoreReferenceLevelOverrides = ! pDatabase->visretain();
    pTcb->ext_locks.scaleViewportLineStyles = pDatabase->psltscale();

    // reference clip flag is now a view attribute, but we keep the full value of xclipFrame for plotting and roundtrip: 0=no display, 1=display & plot, 2=display but no plot
    pTcb->ext_locks2.displayReferenceClipBoundaries = pDatabase->xclipFrame();

    if (1.0 == pTcb->lineStyle.scale || pTcb->lineStyle.scale <= 0.0)
        pTcb->lineStyle.modifiers &= ~STYLEMOD_SCALE;
    else
        pTcb->lineStyle.modifiers |=STYLEMOD_SCALE;

    pTcb->ext_locks2.continousLineStringStyles = pDatabase->plinegen();

    pTcb->lineStyle.modifiers |= STYLEMOD_TRUE_WIDTH;
    double  lineWidth = pDatabase->plinewid() * this->GetScaleToDGN();

    if (0.0 == lineWidth)
        pTcb->lineStyle.modifiers &= (STYLEMOD_SWIDTH | STYLEMOD_EWIDTH);
    else
        pTcb->lineStyle.modifiers |= (STYLEMOD_SWIDTH | STYLEMOD_EWIDTH);

    pTcb->lineStyle.startWidth = pTcb->lineStyle.endWidth = lineWidth;

    // NOTE: Set legacy boresite lock in case file is opened in earlier versions of V8/XM...
    pTcb->cntrl1.boresite_deprecated = true;    // AutoCAD had no equivalent to boresite lock.

    pTcb->ext_locks.sharedCells = true;         // AutoCAD supports only shared cells.
    pTcb->gridOrientation = 1;                  // AutoCAD grids are always oriented to world X-Y.
    pTcb->msToolSettings.areaPattern.associativeBoundary = true;
    pTcb->msToolSettings.renderView.createInternalMaterials = true;

    // Bug in early V7 TCB conversion left active cell names with 6 spaces
    stripSpaceFromCellName (pTcb, Tcb::ACTIVE_CELL_TYPE_Normal);
    stripSpaceFromCellName (pTcb, Tcb::ACTIVE_CELL_TYPE_Terminator);
    stripSpaceFromCellName (pTcb, Tcb::ACTIVE_CELL_TYPE_Pattern);
    stripSpaceFromCellName (pTcb, Tcb::ACTIVE_CELL_TYPE_Point);

    // set default model specific properties
    DgnModelP       defaultModel = m_dgnFile->FindLoadedModelById (m_dgnFile->GetDefaultModelId());
    if (NULL == defaultModel)
        return;
    ModelInfoPtr    modelInfo = defaultModel->GetModelInfo().MakeCopy ();
    if (!modelInfo.IsValid())
        return;

    // Set the linear units.
    SetModelUnitsFromDwg (modelInfo.get(), pDatabase);

    modelInfo->SetLineStyleScale (pTcb->lineStyleScale_deprecated);

    defaultModel->SetModelInfo (*modelInfo.get());

    this->SetTCBAngularUnitsFromDatabaseVariables (pTcb, pDatabase);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/11
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::SetTCBAngularUnitsFromDatabaseVariables (Tcb* pTcb, AcDbDatabase* pDatabase)
    {
    int                 anglePrec = pDatabase->auprec();
    DirectionMode       dirMode = DirectionMode::Azimuth;
    AngleMode           angleUnit = AngleMode::Degrees;

    switch (pDatabase->aunits())
        {
        default:
        case ANGULAR_UNITS_DecimalDegrees:
            angleUnit = AngleMode::Degrees;
            break;

        case ANGULAR_UNITS_DegreesMinutesSeconds:
            anglePrec = RealDwgUtil::AccuracyFromDmsAngularPrecision (&angleUnit, anglePrec);
            break;

        case ANGULAR_UNITS_Gradians:
            angleUnit = AngleMode::Centesimal;
            break;

        case ANGULAR_UNITS_Radians:
            angleUnit = AngleMode::Radians;
            break;

        case ANGULAR_UNITS_Bearing:
            dirMode   = DirectionMode::Bearing;
            anglePrec = RealDwgUtil::AccuracyFromDmsAngularPrecision (&angleUnit, anglePrec);
            break;
        }

    // set default model specific properties
    DgnModelP       defaultModel = m_dgnFile->FindLoadedModelById (m_dgnFile->GetDefaultModelId());
    if (NULL == defaultModel)
        return;

    ModelInfoPtr    modelInfo = defaultModel->GetModelInfo().MakeCopy ();
    if (!modelInfo.IsValid())
        return;

    modelInfo->SetDirectionMode (dirMode);
    modelInfo->SetDirectionClockwise (pDatabase->angdir());
    modelInfo->SetDirectionBaseDir (pDatabase->angbase() * msGeomConst_degreesPerRadian);
    modelInfo->SetAngularMode (angleUnit);
    modelInfo->SetAngularPrecision ((AnglePrecision) anglePrec);

    defaultModel->SetModelInfo (*modelInfo.get());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Venkat.Kalyan                   08/2006
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertToDgnContext::GetBigFontCodePage (LangCodePage& codePage, UInt16 bigFontNum)
    {
    if (!bigFontNum)
        return false;

    DgnFontNumMapP fontMap = m_dgnFile->GetDgnFontMapP();
    DgnFontCP      bigFont = fontMap->GetFontP (bigFontNum);

    if (NULL == bigFont)
        return false;

    bigFont = bigFont->GetRenderFontCP();
    if (!bigFont->IsValid() || !bigFont->IsShxBigFont())
        return false;

    codePage = bigFont->GetCodePage();
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::GetFontIdsFromTextStyle
(
int*                        pFontId,
int*                        pBigFontId,
AcDbTextStyleTableRecord*   pTextStyle
)
    {
    Adesk::Boolean  bold        = false;
    Adesk::Boolean  italic      = false;
    Charset         charset     = Charset::kUndefinedCharset;
    FontPitch       pitch       = FontPitch::kDefault;
    FontFamily      family      = FontFamily::kDoNotCare;

    *pFontId = *pBigFontId = 0;

    ACHAR*      typeface = NULL;
    pTextStyle->font (typeface, bold, italic, charset, pitch, family);

    if ( (NULL == typeface) || (0 == typeface[0]) ) // If no typeface then must be shx.
        {
        ACHAR const*    fileName;
        pTextStyle->fileName (fileName);
        *pFontId     = m_pFileHolder->GetShapeFontId (fileName, GetModel ());
        /*------------------------------------------------------------------------------
        * HACK: In some cases, text styles in DWG/DXF files do not have a small font
        * associated with them. In such cases, we were using a value of 0 for the small
        * font id.  THis causes problems because 0 is a RSC font and hence the RSC font
        * is used.  I have made the change to use the ACAD default font in case a
        * text style does not have a small font assciated with it.
        *-------------------------------------------------------------------------------*/
        if (0 == *pFontId)
            *pFontId = -1;

        ACHAR const*    bigFontFileName;
        if (Acad::eOk == pTextStyle->bigFontFileName (bigFontFileName))
            *pBigFontId = m_pFileHolder->GetShapeFontId (bigFontFileName, GetModel ());
        }
    else
        {
        *pFontId = m_pFileHolder->GetTrueTypeFontId (typeface, GetModel ());
        }

    if (NULL != typeface)
        acutDelString (typeface);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Venkat.Kalyan                   04/2006
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::AdjustForBackwardsUpsideDownText
(
TextSizeParam   *pTextSize,
RotMatrix       *pRMatrix,
bool            mirrorInX,
bool            mirrorInY
)
    {
    if (!this->GetThreeD())
        {
        pTextSize->size.width *= (mirrorInX ? - 1.0 : 1.0);
        pTextSize->size.height *= (mirrorInY ? - 1.0 : 1.0);
        }
    else
        {
        bool    changed = false;
        DVec3d  xCol, yCol, zCol;
        if (mirrorInX)
            {
            pRMatrix->GetColumn (xCol, 0);
            xCol.Scale (-1.0);
            if (!mirrorInY)
                pRMatrix->GetColumn (yCol, 1);
            changed = true;
            }

        if (mirrorInY)
            {
            pRMatrix->GetColumn (yCol, 1);
            yCol.Scale (-1.0);
            if (!mirrorInX)
                pRMatrix->GetColumn (xCol, 0);
            changed = true;
            }

        if (changed)
            {
            pRMatrix->GetColumn (zCol, 2);
            pRMatrix->InitFromColumnVectors (xCol, yCol, zCol);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::GetDgnTextTransformFromDwg
(
TextSizeParamP          pTextSize,
DPoint3dP               pOrigin,
RotMatrixP              pRMatrix,
AcGeVector3d const&     normal,
AcGePoint3d const&      position,
double                  rotation,
double                  height,
double                  widthFactor
)
    {
    RotMatrix               fontToLocalMatrix;

    fontToLocalMatrix.initFromAxisAndRotationAngle (2, rotation);
    fontToLocalMatrix.scaleColumns (&fontToLocalMatrix, height * widthFactor, height, 1.0);

    Transform               fontToLocalTransform;
    fontToLocalTransform.InitIdentity();
    fontToLocalTransform.setMatrix (&fontToLocalMatrix);

    Transform               extrusionTransform;
    if (NULL != RealDwgUtil::GetExtrusionTransform (extrusionTransform, normal, 0.0))
        fontToLocalTransform.productOf (&extrusionTransform, &fontToLocalTransform);

    DPoint3d    translation;
    RealDwgUtil::DPoint3dFromGePoint3d (translation, position);
    if (RealDwgUtil::CoerceInvalidElevation (translation.z))
        DIAGNOSTIC_PRINTF ("Ignoring invalid text elevation: %g\n", translation.z);
    fontToLocalTransform.setTranslation (&translation);

    Transform    compositeTransform;
    compositeTransform.InitProduct (this->GetTransformToDGN(), fontToLocalTransform);
    compositeTransform.GetTranslation (*pOrigin);

    DVec3d          scale;
    RotMatrix       rotMatrix;
    compositeTransform.GetMatrix (rotMatrix);
    rotMatrix.NormalizeColumnsOf (rotMatrix, scale);

    this->ValidatePoints (pOrigin, 1);

    if (!this->GetThreeD())
        {
        DVec3d column2;
        rotMatrix.GetColumn (column2, 2);
        if (column2.z < 0.0)
            {
            DIAGNOSTIC_PRINTF ("Flipping negative z-plane to possitive for text!\n");
            DVec3d  column1;
            rotMatrix.GetColumn (column1, 1);
            column1.Negate();
            column2.Negate();
            rotMatrix.SetColumn (column1, 1);
            rotMatrix.SetColumn (column2, 2);
            scale.y = -scale.y;
            }
        if (rotMatrix.determinant() < 0.0)
            {
            DIAGNOSTIC_PRINTF ("Flipping negative text matrix!");
            column2.Negate();
            rotMatrix.SetColumn (column2, 2);
            scale.y = -scale.y;
            }
        }

    pTextSize->mode = TXT_BY_TILE_SIZE;
    pTextSize->size.width = scale.x;
    pTextSize->size.height = scale.y;

    *pRMatrix = rotMatrix;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                    ConvertToDgnContext::GetDgnTextSizeAndTransform
(
TextSizeParam*              pTextSize,
DPoint3dP                   pOrigin,
RotMatrixP                  pRMatrix,
const AcGePoint3d&          position,
const AcGeVector3d&         direction,
const AcGeVector3d&         normal,
const double                width,
const double                height,
TransformCP                 transform2Dgn
)
    {
    DVec3d          scale;
    AcGeTol         acTol;
    acTol.setEqualVector (TOLERANCE_VectorEqual);

    DVec3d  translation, column0, column1, column2;
    RealDwgUtil::DPoint3dFromGePoint3d (translation, position);
    RealDwgUtil::DPoint3dFromGeVector3d (column0, direction.isZeroLength(acTol) ? AcGeVector3d::kXAxis : direction);
    RealDwgUtil::DPoint3dFromGeVector3d (column2, normal.isZeroLength(acTol) ? AcGeVector3d::kZAxis : normal);

    // Surprisingly, these aren't always normalized.
    column0.normalize (&column0);
    column2.normalize (&column2);
    column1.crossProduct (&column2, &column0);

    RotMatrix       rotMatrix;
    rotMatrix.initFromColumnVectors (&column0, &column1, &column2);
    rotMatrix.scaleColumns (&rotMatrix, width, height, 1.0);

    Transform       transform;
    transform.initIdentity ();
    transform.setMatrix (&rotMatrix);
    transform.setTranslation (&translation);

    Transform       compositeTransform;
    compositeTransform.productOf (nullptr == transform2Dgn ? &this->GetTransformToDGN() : transform2Dgn, &transform);

    compositeTransform.getMatrix (&rotMatrix);
    rotMatrix.normalizeColumnsOf (&rotMatrix, &scale);

    rotMatrix.getColumn (&column2, 2);
    if (!this->GetThreeD() && column2.z < 0.0)
        {
        column2.negate();
        rotMatrix.setColumn (&column2, 2);

        rotMatrix.getColumn (&column1, 1);
        column1.negate();
        rotMatrix.setColumn (&column1, 1);

        scale.y = -scale.y;
        }

    *pRMatrix = rotMatrix;

    memset (pTextSize, 0, sizeof(*pTextSize));
    pTextSize->mode = TXT_BY_TILE_SIZE;
    pTextSize->size.width  = scale.x;
    pTextSize->size.height = scale.y;
    compositeTransform.getTranslation (pOrigin);
    }


static byte  s_justTable[4][6] = 
    {
    {(byte)TextElementJustification::LeftBaseline,  (byte)TextElementJustification::CenterBaseline,  (byte)TextElementJustification::RightBaseline,  (byte)TextElementJustification::LeftBaseline,  (byte)TextElementJustification::CenterMiddle,    (byte)TextElementJustification::LeftBaseline},
    {(byte)TextElementJustification::LeftDescender, (byte)TextElementJustification::CenterDescender, (byte)TextElementJustification::RightDescender, (byte)TextElementJustification::LeftDescender, (byte)TextElementJustification::CenterDescender, (byte)TextElementJustification::LeftDescender},
    {(byte)TextElementJustification::LeftMiddle,    (byte)TextElementJustification::CenterMiddle,    (byte)TextElementJustification::RightMiddle,    (byte)TextElementJustification::LeftMiddle,    (byte)TextElementJustification::CenterMiddle,    (byte)TextElementJustification::LeftMiddle},
    {(byte)TextElementJustification::LeftTop,       (byte)TextElementJustification::CenterTop,       (byte)TextElementJustification::RightTop,       (byte)TextElementJustification::LeftTop,       (byte)TextElementJustification::CenterTop,       (byte)TextElementJustification::LeftTop}
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::GetDgnTextParamsFromDwg
(
TextParamWide*              pParams,
AcDb::TextHorzMode          horizontalMode,
AcDb::TextVertMode          verticalMode,
double                      obliqueAngle,
bool                        isVertical,
AcDbObjectId                textStyleId
)
    {
    memset (pParams, 0, sizeof(*pParams));

    // get justification
    if (verticalMode < AcDb::kTextBase || horizontalMode < AcDb::kTextLeft || verticalMode > AcDb::kTextTop || horizontalMode > AcDb::kTextFit)
        {
        pParams->just = TextElementJustification::LeftBaseline;    // out of bounds error
        }
    else if (horizontalMode == AcDb::kTextFit)
        {
        /*-------------------------------------------------------------------------------
        According to DXF document, group code 72 is 5 for fit between points IF 73 is 0.
        However, ACAD seems always ignores 73 if 72 is 5.  Such example is seen in DWG
        produced by Polaris as in TR 222668.
        -------------------------------------------------------------------------------*/
        // set fitted bit on text param wide
        pParams->exFlags.acadFittedText = true;
        pParams->just = TextElementJustification::RightBaseline; // preserve secondary text loc
        }
    else
        {
        pParams->just = static_cast<TextElementJustification>(s_justTable[verticalMode][horizontalMode]);
        }

    /* justification is always overridden as dwg styles have none */
    pParams->overridesFromStyle.just  = true;
    pParams->overridesFromStyle.nodeJust = true;
    pParams->exFlags.styleOverrides = true;
    // get oblique
    // TR 74414 - text always has a local oblique angle.  Use it to start with and if it is not 0, that means Microstation uses italics ON.
    // This was determined by placing examples, and looking at output DXF files.  However, if the angle is 0 do not turn on italics.
    if (0.0 != obliqueAngle)
        pParams->flags.slant = true;

    pParams->slant = obliqueAngle;

    // get font numbers
    if (textStyleId.isValid())
        {
        Adesk::Boolean      bold = false, italic = false;
        Charset             charset = Charset::kUndefinedCharset;
        FontPitch           pitch = FontPitch::kDefault;
        FontFamily          family = FontFamily::kDoNotCare;

        AcDbTextStyleTableRecordPointer pTextStyle (textStyleId, AcDb::kForRead);
        if (Acad::eOk != pTextStyle.openStatus())
            return;

        this->GetFontIdsFromTextStyle ((int *) &pParams->font, (int *) &pParams->shxBigFont, pTextStyle);

        ACHAR*      typeface = NULL;
        pTextStyle->font (typeface, bold, italic, charset, pitch, family);
        acutDelString (typeface);

        if (0 != pParams->shxBigFont)
            pParams->flags.shxBigFont = true;

        LangCodePage         codePage = LangCodePage::Unicode;
        if (!this->GetBigFontCodePage (codePage, pParams->shxBigFont))
            codePage = GetAnsiCodePage ();

        if (NULL != pParams)
            pParams->SetCodePage (codePage);

        if (pTextStyle->isVertical ())
            pParams->flags.vertical = true;

        if (0.0 == pTextStyle->textSize ())
            {
            pParams->overridesFromStyle.height = true;
            pParams->overridesFromStyle.width = true;
            pParams->exFlags.styleOverrides = true;
            }
        /* TR 92744 even though the italic override on this entity is set to 0 if the font is ttf
           and italic then we should set the italic override for the text. So the italic ttf font is used*/
        if (0.0 == pParams->slant && italic)
            {
            pParams->flags.slant = true;
            pParams->overridesFromStyle.italics = true;
            pParams->overridesFromStyle.slant = true;
            pParams->exFlags.styleOverrides = true;
            }
        else if (pParams->slant != pTextStyle->obliquingAngle ())
            {
            // TR 74414 - We demark an override when the values differ.  However, this concept of override is not equivalent across Microstation
            // and ACAD.  For example: if you change a text style in ACAD 2002 to alter the oblique angle, Text elements do not change BUT
            // ML Text elements will... even ones who have oblique values different from the style.
            pParams->overridesFromStyle.italics = true;
            pParams->overridesFromStyle.slant = true;
            pParams->exFlags.styleOverrides = true;
            }
        else if (italic && 0 == pTextStyle->obliquingAngle ())
            {
            /* if the xdata code says the shapefile is italic then make microstation use the italic ttf font
               by switching on italics and leaving the slant angle at 0 */
            pParams->slant = 0.0;
            pParams->flags.slant = true;
            pParams->overridesFromStyle.italics = true;
            pParams->overridesFromStyle.slant = true;
            pParams->exFlags.styleOverrides = true;
            }

        pParams->exFlags.bold = bold;
        pParams->textStyleId = m_pFileHolder->GetTextStyleIndex()->GetDgnId (textStyleId.handle());
        pParams->flags.textStyle = true;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/2003
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId                   ConvertToDgnContext::GetPointDisplayBlockId ()
    {
    ElementId   blockId;
    if (0 == (blockId = m_pFileHolder->GetPointDisplayBlockId()))
        m_pFileHolder->SetPointDisplayBlockId (blockId = this->GetAndIncrementNextId());

    return blockId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/2003
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId                   ConvertToDgnContext::GetAndIncrementNextId ()
    {
    // When exporting, make sure that the next ID used is always higher than the current ID.
    ElementId           currentHighestId = dgnFileObj_getHighestElementID (m_dgnFile);

    if (m_nextAvailableId <= currentHighestId)
        {
        return m_nextAvailableId = dgnFileObj_useNextElementID (m_dgnFile);
        }
    else
        {
        ElementId           id;
        dgnFileObj_setHighestElementID (m_dgnFile, id = m_nextAvailableId);

        m_nextAvailableId++;

        return id;
        }
    }

/*---------------------------------------------------------------------------------**//**
* Makes the line style continuous over the polyline
* @bsimethod                                                    ChuckKirschman      02/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::AddLineStyleContinuousLinkage (EditElementHandleR eeh)
    {
    MSElementP  element = eeh.GetElementP ();
    if (NULL == element)
        return;

    /*-----------------------------------------------------------------------------------
    mdlLineStyle_extractParams rejects the extraction of linestyle params when lstyle
    is a line code.  One such a case is seen with elements coming from a proxy entity.
    Set it to -1 to bypass this for now.  The caller is expected to update lstyle later.
    -----------------------------------------------------------------------------------*/
    if (IS_LINECODE(element->hdr.dhdr.symb.style))
        element->hdr.dhdr.symb.style = -1;

    // NOTE: There should only be one line style linkage per element (except multilines)
    // By extracting the existing params, the modifiers are unified.
    LineStyleParams          styleParams;
    LineStyleUtil::GetParamsFromElement (&styleParams, eeh);
    styleParams.modifiers  |= STYLEMOD_NOSEGMODE;

    if (AddLinestyleLinkageFromParams(eeh, &styleParams, m_threeD))
        element = eeh.GetElementP ();

    if (CMPLX_SHAPE_ELM == element->ehdr.type || CMPLX_STRING_ELM == element->ehdr.type)
        {
        for (ChildEditElemIter child(eeh, ExposeChildrenReason::Count); child.IsValid(); child=child.ToNext())
            this->AddLineStyleContinuousLinkage (child);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static TextElementJustification DgnJustificationFromMLTextAttachment (AcDbMText::AttachmentPoint attachment)
    {
    TextElementJustification    dgnJustification = TextElementJustification::LeftBaseline;

    switch  (attachment)
        {
        case AcDbMText::kTopLeft:
           dgnJustification = TextElementJustification::LeftTop;
           break;

        case AcDbMText::kTopCenter:
            dgnJustification = TextElementJustification::CenterTop;
            break;

        case AcDbMText::kTopRight:
            dgnJustification = TextElementJustification::RightTop;
            break;

        case AcDbMText::kMiddleLeft:
            dgnJustification = TextElementJustification::LeftMiddle;
            break;

        case AcDbMText::kMiddleCenter:
            dgnJustification = TextElementJustification::CenterMiddle;
            break;

        case AcDbMText::kMiddleRight:
            dgnJustification = TextElementJustification::RightMiddle;
            break;

        case AcDbMText::kBottomLeft:
            dgnJustification = TextElementJustification::LeftDescender;
            break;

        case AcDbMText::kBottomCenter:
            dgnJustification = TextElementJustification::CenterDescender;
            break;

        case AcDbMText::kBottomRight:
            dgnJustification = TextElementJustification::RightDescender;
            break;
        }
    return dgnJustification;
    }

/*---------------------------------------------------------------------------------**//**
* Fix up bad data for fitted Text
*
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 fixupBadDataForFittedText
(
AcGePoint3d&                userOrigin,
AcGePoint3d&                originalOrigin,
DPoint2d&                   scale,
AcString&                   textString,
TextBlockPropertiesCR       tbProps,
ParagraphPropertiesCR       paraProps,
RunPropertiesR              runProps,
double                      originalWidthFactor,
ConvertToDgnContext&        dgnExportContext
)
    {
    if (0.0 == scale.y)
        return;

    ParagraphPropertiesPtr  testParaProps   = paraProps.Clone ();
    RunPropertiesPtr        testRunProps    = runProps.Clone ();
    
    testParaProps->SetJustification (TextElementJustification::LeftBaseline);
    testRunProps->SetFontSize (DPoint2d::From (1000.0, 1000.0));

    TextBlock textBlock (tbProps, *testParaProps, *testRunProps, tbProps.GetDgnModelR ());
    textBlock.FromDText (textString.kwszPtr ());
    
    double      distBetweenOrigins  = originalOrigin.distanceTo (userOrigin);
    DRange3d    nominalRange        = textBlock.GetNominalRange ();
    DPoint3d    textDiagonal;       textDiagonal.DifferenceOf (nominalRange.high, nominalRange.low);
    
    if (textDiagonal.x <= TOLERANCE_ZeroSize)
        return;

    double widthFactor = (distBetweenOrigins / (textDiagonal.x / testRunProps->GetFontSize ().x));
    widthFactor /= scale.y;

    // This is a hack to fix files in which the width factor is wrong.  Not sure how such files are created but in the case of fitted text coming from ACAD some times the width factor does not match the real width factor.  So, I am adding this code where we calculate a new width factor.  If the new width factor is more than 5% of the incoming width factor, I am using the new width factor.  the original fix by Paul did this only for large (>50) values of the widht factor.  This for TR 129224.
    if ((fabs (widthFactor - originalWidthFactor) / originalWidthFactor) > ACAD_WIDTH_FACTOR_TOLERANCE)
        {
        DPoint2d fontSize = runProps.GetFontSize ();
        fontSize.x *= widthFactor;
        
        runProps.SetFontSize (fontSize);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Venkat.Kalyan                   10/2005
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 getMicroStationJustificationFromAutoCADVerticalAlignmentMode
(
TextElementJustification&   pJustification,
AcDb::TextHorzMode          horizontalMode
)
    {
    switch (horizontalMode)
        {
        case AcDb::kTextLeft:
            pJustification = TextElementJustification::LeftTop;
            return;

        case AcDb::kTextCenter:
        case AcDb::kTextMid:
            pJustification = TextElementJustification::LeftMiddle;
            return;

        case AcDb::kTextRight:
        case AcDb::kTextFit:
        case AcDb::kTextAlign:
            pJustification = TextElementJustification::LeftBaseline;
            return;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           01/03
+---------------+---------------+---------------+---------------+---------------+------*/
double                      ConvertToDgnContext::GetLeaderBoxRange
(
DRange3d&                   range,
double                      dimgap,
AcDbLeader*                 pLeader
)
    {
    double  width  = pLeader->annoWidth ();
    double  height = pLeader->annoHeight ();

    if (width > 0.0 && height > 0.0)
        {
        // leader contains valid annotation size, should use them
        width  *= this->GetScaleToDGN ();
        height *= this->GetScaleToDGN ();

        // build the range in the same order as text range does:
        range.low.x  = 0.0;
        range.low.y  = -height;

        range.high.x = width;
        range.high.y = 0.0;

        return  width;
        }

    return  width;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/02
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        ConvertToDgnContext::CreateUnnamedGroups
(
)
    {
    if (m_createUnnamedGroups < 0)
        m_createUnnamedGroups = ConfigurationManager::IsVariableDefined (L"MS_DWG_UNNAMEDGROUPS") ? 1 : 0;

    return 0 != m_createUnnamedGroups;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/05
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsInternetProtocol (WStringR outUrl)
    {
    if (!outUrl.empty())
        {
        if (outUrl.StartsWithI(L"HTTP://") || outUrl.StartsWithI(L"FILE://"))
            return  true;

        if (outUrl.StartsWithI(L"HTTPS://"))
            return  true;

        if (outUrl.StartsWithI(L"MAILTO:"))
            return  true;

        if (outUrl.StartsWithI(L"FTP://"))
            return  true;

        // round tripping special case: ustnkeyin
        if (outUrl.StartsWithI(L"ustnkeyin:"))
            return  true;

        // sniff string...
        if (outUrl.StartsWithI(L"WWW.") || outUrl.StartsWithI(L"FTP."))
            {
            // prepend protocol type
            if (outUrl.StartsWithI(L"FTP."))
                outUrl = WString(L"ftp://") + outUrl;
            else
                outUrl = WString(L"http://") + outUrl;

            return  true;
            }
       }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/05
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::AddDesignLink
(
EditElementHandleR          outElement,
AcString const&             name,
AcString const&             target,
AcString const&             path
)
    {
    if (!outElement.IsValid())
        return BSIERROR;

    // create tree specs from the element
    DgnLinkTreeSpecPtr      treeSpec = DgnLinkManager::CreateTreeSpec (outElement);
    if (!treeSpec.IsValid())
        return  BSIERROR;

    // get link tree
    DgnLinkTreePtr          linkTree = DgnLinkManager::ReadLinkTree (*treeSpec.get(), true);
    if (!linkTree.IsValid())
        return  BSIERROR;

    // get node name
    WString     nodeName;
    if (!name.isEmpty())
        nodeName.assign (name.kwszPtr());

    // get file path
    WString     filePath;
    if (!path.isEmpty())
        filePath.assign (path.kwszPtr());

    // parse view and model from target: viewName,modelName
    WString     modelName, viewName;
    if (!target.isEmpty())
        {
        // the targets view & model are seperated by a comma in the target string:
        int     commaFound = target.find (L',');

        if (commaFound >= 0)
            {
            // view & model targets are explicitly specified in the string:
            modelName.assign (target.substr(commaFound + 1, target.length() - commaFound).kwszPtr());
            viewName.assign (target.substr(0, commaFound).kwszPtr());
            }
        else
            {
            // target is a view in active model (no comma).
            modelName.clear ();
            viewName.assign (target.kwszPtr());
            }

        // provide the default model name for a link that targets at modelspace
        AcDbBlockTableRecordPointer     currentBlock (this->GetCurrentBlockId(), AcDb::kForRead);
        if (modelName.empty() && filePath.empty() && Acad::eOk == currentBlock.openStatus() && RealDwgUtil::IsModelOrPaperSpace(currentBlock))
            modelName.assign (this->GetFileHolder().GetDefaultModelItem()->GetModelName());
        }

    // get the link tree root and create a type specific leaf
    DgnLinkTreeBranchR          treeRoot = linkTree->GetRootR ();
    TempDgnLinkTreeLeafOwner    treeLeaf;
    StatusInt                   status = BSIERROR;
    bool                        isUrlLink = false;

    if (!viewName.empty())
        treeLeaf = DgnLinkManager::CreateLink (status, DGNLINK_TYPEKEY_Region);
    else if (!modelName.empty())
        treeLeaf = DgnLinkManager::CreateLink (status, DGNLINK_TYPEKEY_Model);
    else if (isUrlLink = IsInternetProtocol(filePath))
        treeLeaf = DgnLinkManager::CreateLink (status, DGNLINK_TYPEKEY_URLLink);
    else
        treeLeaf = DgnLinkManager::CreateLink (status, DGNLINK_TYPEKEY_File);

    if (BSISUCCESS != status || treeLeaf.IsNull())
        return  BSIERROR;

    // DGN link treats a \ as a folder separator in node name, so change them to /
    RealDwgUtil::ReplaceBackSlashes (nodeName, true);

    // workaround the null name for follow link code which depends on a name:
    if (nodeName.empty())
        treeLeaf->SetName (NAME_UnknownHyperlink);
    else
        treeLeaf->SetName (nodeName.GetWCharCP());

    if (DgnLinkAddChildStatus::Success != treeRoot.AddChild(*treeLeaf, -1))
        DIAGNOSTIC_PRINTF ("Error adding design link to element %I64d\n", outElement.GetElementId());

    DgnLinkP            dgnLink = treeLeaf->GetLinkP ();
    if (NULL == dgnLink)
        return  BSIERROR;

    if (!viewName.empty())
        {
        DgnRegionLinkP  viewLink = dynamic_cast <DgnRegionLinkP> (dgnLink);
        if (NULL == viewLink)
            return  BSIERROR;

        viewLink->SetTarget (DGNLINK_REGIONTYPE_View, viewName.GetWCharCP());

        DgnModelLinkP   modelLink = viewLink->GetModelLinkP ();
        if (NULL != modelLink)
            modelLink->SetModelName (modelName.GetWCharCP());

        DgnFileLinkP    fileLink = viewLink->GetFileLinkP ();
        if (!filePath.empty() && nullptr != fileLink)
            fileLink->SetMoniker (DgnDocumentMoniker::CreateFromFileName(filePath.GetWCharCP()).get());
        }
    else if (!modelName.empty())
        {
        // create a model link
        DgnModelLinkP   modelLink = dynamic_cast <DgnModelLinkP> (dgnLink);
        if (NULL == modelLink)
            return  BSIERROR;

        modelLink->SetModelName (modelName.GetWCharCP());

        DgnFileLinkP    fileLink = modelLink->GetFileLinkP ();
        if (!filePath.empty() && nullptr != fileLink)
            fileLink->SetMoniker (DgnDocumentMoniker::CreateFromFileName(filePath.GetWCharCP()).get());
        }
    else if (isUrlLink)
        {
        DgnURLLinkP     urlLink = dynamic_cast <DgnURLLinkP> (dgnLink);
        if (NULL == urlLink)
            return  BSIERROR;

        urlLink->SetAddress (filePath.GetWCharCP());
        }
    else
        {
        // create a file link
        DgnFileLinkP    fileLink = dynamic_cast <DgnFileLinkP> (dgnLink);
        if (NULL == fileLink)
            return  BSIERROR;

        if (!filePath.empty())
            fileLink->SetMoniker (DgnDocumentMoniker::CreateFromFileName(filePath.GetWCharCP()).get());
        }

    status = DgnLinkManager::WriteLinkTree (*linkTree.get(), outElement);

    if (BSISUCCESS == status)
        status = outElement.ReplaceInModel (outElement.GetElementRef());

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::AddPostProcessObject
(
AcDbObjectCP                pObject
)
    {
    ElementId       id = this->ElementIdFromObject (pObject);

    mdlAvlTree_insertNode (m_pPostProcessIdTree, (void *) &id, sizeof(id));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/03
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ToDgnPostProcessIdFreeFunction (void* nodeToFreeP, void* optArgP)
    {
    ElementId*              pID     = static_cast<ElementId*> (nodeToFreeP);
    ConvertToDgnContext*    context = static_cast<ConvertToDgnContext*> (optArgP);

    AcDbObjectId        objectId;

    if (Acad::eOk == context->GetDatabase()->getAcDbObjectId (objectId, false, context->DBHandleFromElementId (*pID), 0))
        {
        AcDbObjectPointer<AcDbObject>   pObject (objectId, AcDb::kForRead);
        if (Acad::eOk != pObject.openStatus())
            return;

        try
            {
            RealDwgResBuf*  xData;
            if (NULL != (xData = static_cast <RealDwgResBuf*> (pObject->xData(StringConstants::RegAppName_PeUrl))) )
                {
                AcString    title1, title2, url;

                // add design link or engineering link
                if (SUCCESS == RealDwgXDataUtil::ExtractHyperlinkXData (title1, title2, url, xData))
                    {
                    EditElementHandle  elem(context->ElementIdFromObject(pObject), context->GetModel());

                    if (context->GetSettings().HyperlinkAsEngineeringLink() && !url.isEmpty())
                        context->AddEngineeringLink (elem, title1, url);
                    else if (!title1.isEmpty() || !title2.isEmpty() || !url.isEmpty())
                        context->AddDesignLink (elem, title1, title2, url);
                    }
                RealDwgResBuf::Free (xData);
                }

            ACRX_X_CALL (pObject, ToDgnExtension)->ToElementPostProcess (pObject.object(), *context);
            }

        catch (...)
            {
            DIAGNOSTIC_PRINTF ("Exception Thrown During Export PostProcessing For Object: %ls, AcDhHandle: %I64d\n", pObject->isA()->name(), RealDwgUtil::CastDBHandle (pObject->objectId().handle()));
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::PostProcess ()
    {
    // This must be done after each each export to keep the font table in synch with the font manager font number assigments.
    this->UpdateFontTable ();
    m_dgnFile->SaveExtendedColorMap ();

    // create item types for block references that have missing attrdefs:
    this->AddItemTypesForMissingAttrdefs ();

    // now post process each & every element we have saved from the normal process:
    mdlAvlTree_free (&m_pPostProcessIdTree, ToDgnPostProcessIdFreeFunction, this);
    m_pPostProcessIdTree = mdlAvlTree_init (AVLKEY_ELEMENTID);

    // merge orphan tagset definitions - original logic from fixing TF# 107893.
    if (NULL != m_orphanTagsetBlockList)
        {
        DgnModelR       dictionaryModel = m_dgnFile->GetDictionaryModel ();

        for each (OrphanTagdefBlock* currTagsetBlock in *m_orphanTagsetBlockList)
            {
            EditElementHandle   tagsetElement;
            if (0 != currTagsetBlock->m_tagDefElementId &&
                SUCCESS == TagSetHandler::Create(tagsetElement, &currTagsetBlock->m_tagDefs.front(), (int)currTagsetBlock->m_tagDefs.size(), 
                                                 currTagsetBlock->m_tagSetName, NULL, false, *m_dgnFile, 0))
                {
                MSElementDescrP     tagsetElmdscr = tagsetElement.ExtractElementDescr ();
                tagsetElmdscr->el.ehdr.uniqueId = currTagsetBlock->m_tagDefElementId;

                // replace existing tagset element or add it as a new one
                PersistentElementRefP   tagsetRef = dictionaryModel.FindByElementId (currTagsetBlock->m_tagDefElementId);
                if (NULL == tagsetRef || BSISUCCESS != dictionaryModel.ReplaceElementDescr(tagsetElmdscr, tagsetRef, true, false))
                    this->LoadElementIntoCache (tagsetElmdscr);

                tagsetElmdscr->Release ();
                }
            delete currTagsetBlock;
            }

        delete m_orphanTagsetBlockList;
        m_orphanTagsetBlockList = NULL;
        }

#ifdef ADD_LXOSETUP_ELEMENTS
    // Add the required untitled setup for Luxology as otherwise it will add after cache fill hence will result in unsaved cache upon file opened.
    LxoSetupManager::GetManagerR().InitForFile (*m_dgnFile);
    LxoEnvironmentManager::GetManagerR().InitForFile (*m_dgnFile);
#endif

    // save off DWG Attribute Definition Itemtype Library so we can compare it against editted library when saving back to DWG:
    if (!this->GetSettings().AttributesAsTags() && m_attrdefItemtypeLibrary.IsValid() && m_attrdefItemtypeLibrary->GetItemTypeCount() > 0)
        m_pFileHolder->SetDwgOriginatedItemtypeLibrary (m_attrdefItemtypeLibrary.get());

    this->ShiftOverlappedElementIds ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/11
+---------------+---------------+---------------+---------------+---------------+------*/
static void     ChangeAllCacheElementIds (MSElementDescrP elmdscrIn)
    {
    for (MSElementDescrP elmdscr = elmdscrIn; NULL != elmdscr; elmdscr = elmdscr->h.next)
        {
        PersistentElementRefP    cacheElem = (PersistentElementRefP)elmdscr->h.elementRef;
        if (NULL != cacheElem)
            {
            if (elmdscr->el.ehdr.uniqueId != cacheElem->GetElementId())
                DwgConversionDataAccessor::ChangeElementId (cacheElem, elmdscr->el.ehdr.uniqueId);

            for (MSElementDescrP child = elmdscr->h.firstElem; NULL != child; child = child->h.next)
                ChangeAllCacheElementIds (child);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/11
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt    ReplaceAllCacheElements (MSElementDescrP elmdscrIn)
    {
    StatusInt               status = BSIERROR;
    PersistentElementRefP   cacheElem = (PersistentElementRefP)elmdscrIn->h.elementRef;
    if (NULL != cacheElem)
        {
        DgnModelP           dgnCache = cacheElem->GetDgnModelP ();
        if (NULL != dgnCache)
            {
            status = dgnCache->ReplaceElementDescr (elmdscrIn, cacheElem, true, true);
            if (SUCCESS != status)
                DIAGNOSTIC_PRINTF ("Error replacing linestyle table element %I64d. STATUS=%d\n", elmdscrIn->el.ehdr.uniqueId, status);
            }
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/11
+---------------+---------------+---------------+---------------+---------------+------*/
static void     UpdateLinestyleTables (ElementId handseedAfterCacheLoad, ConvertToDgnContextR context)
    {
    /*-----------------------------------------------------------------------------------
    Linestyle def & name tables have their own data dependencies on element ID's which 
    need to be resolved with a linestyle expert API.  The re-written cache elements shall
    not be found in cache any longer as their ID's will have been changed here.
    -----------------------------------------------------------------------------------*/
    DgnModelP           modelRef = context.GetModel ();
    MSElementDescrP     lsDefTable = NULL, lsNameTable = NULL;

    if (SUCCESS == DgnTableUtilities::GetUniqueTable (&lsDefTable, NULL, modelRef, MS_LSTYLE_DEF_LEVEL) &&
        SUCCESS == DgnTableUtilities::GetUniqueTable (&lsNameTable, NULL, modelRef, MS_LSTYLE_NAME_LEVEL))
        {
        DgnFileP        dgnFile = context.GetFile ();
        ElementId       currHighestId = context.GetNextAvailableId ();

        // synch cache highest element ID
        if (currHighestId > dgnFileObj_getHighestElementID(dgnFile))
            dgnFileObj_setHighestElementID (dgnFile, currHighestId);

        bool            changed = false;
        lineStyle_changeAllElementIdsForDwg (&changed, lsDefTable, lsNameTable, context.GetHandseedBeforeCacheLoad() - 1, handseedAfterCacheLoad, dgnFile);

        if (changed)
            {
            // make two runs to rewrite the linestyle tables: first, change element ID's in cache
            ChangeAllCacheElementIds (lsDefTable);
            ChangeAllCacheElementIds (lsNameTable);
            // then replace the cache elements
            ReplaceAllCacheElements (lsDefTable);
            ReplaceAllCacheElements (lsNameTable);
            }

        // re-synch from cache highest ID
        currHighestId = dgnFileObj_getHighestElementID (dgnFile);
        context.SetNextAvailableId (currHighestId);
        }
    else
        {
        lsDefTable->Release ();
        lsNameTable->Release ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::ShiftOverlappedElementIds ()
    {
    /*-----------------------------------------------------------------------------------
    We do not add new objects into database during file opening or cache loading.  
    However, RealDWG may do that.  For example, MLEADER worldDraw may add in database an
    AcDbFontTableRecord which is not even a published object type, likely a result from
    a deep cloning of the object.  The change of handseed during cache load invalidates
    our assumption that the handseed is the highest object ID and all newly saved elements
    can use incremental ID's greater than handseed.  Now these new DGN elements already
    saved in cache actually have the same unique ID's as those new DWG objects added by
    RealDWG.  This causes a problem for roundstripping these ID overlapped elements back
    to DWG.  For example, during DWG save, we'd find a matching object ID from a supposedly
    new element that should have no match in DWG.  We'd then attempt to open the wrong
    object and do through a wrong update process.  Worse yet, as a case seen in TR 326450,
    attempting to manipulate AcDbFontTableRecord actually causes RealDWG to crash at the
    object close call!

    This is what Autodesk has told us why HANDSEED value increases after file loading:
    "There are a number of things that can happen to a dwg during open that will use up handles.
    Unfortunately, this depends on the dwg version of the drawing, what versions of RealDWG/Acad 
    have opened and saved the drawing before, what build of RealDWG/Acad is doing the opening 
    (a Service Pack can change it), and even what the current view in the drawing is, so 
    it's not practical to know when it will happen and how many handles will be used.  For 
    example, if the drawing does not have the newer materials data that was added several 
    years ago, then when the drawing is opened, any materials that are referenced by entities 
    in the current view will have the new materials data added and stored in XRecords in 
    the extension dictionaries of the corresponding AcDbMaterials.  Another case is if the 
    drawing does not contain the defaults of certain new object types.  In that case we will 
    create those default objects and add them during dwg open.  Another case is certain 
    entity types that are always saved in one form and changed to another when the drawing 
    is opened. This is done so that they are always compatible with an earlier version of 
    RealDWG/Acad that used the same dwg format.  MLeaders are like that."

    To get around this problem, we find all those overlapped elements and shift their ID's
    to the new highest DGN ID's.  Searching element by ID may be an expensive process but
    only very rare cases can reach here because we have reserved some ID's for the highest
    ID which should cover majority cases, if not all, in which RealDWG will ever increase 
    handseeds after loading a DWG file.    
    -----------------------------------------------------------------------------------*/
    if (m_handseedBeforeCacheLoad > 0)
        {
        DgnFileP    dgnFile = this->GetFile ();
        ElementId   handseedAfterCacheLoad = RealDwgUtil::CastDBHandle (this->GetDatabase()->handseed());

        // first check & update linestyle table entries
        if (m_handseedBeforeCacheLoad < handseedAfterCacheLoad)
            UpdateLinestyleTables (handseedAfterCacheLoad, *this);

        // then find all other elements and shift up their id's
        for (ElementId id = m_handseedBeforeCacheLoad; id < handseedAfterCacheLoad; id++)
            {
            PersistentElementRefP   cacheElem = dgnFile->FindByElementId (id, false);
            if (NULL != cacheElem)
                {
                /*-----------------------------------------------------------------------
                We do not have a good way to update tags for changed tagset ID's.  Before
                we can find a way around that, we have to keep the original ID values we
                have created and do not shift them, as shifted ID's cause tags to fail
                due to missing tagset ID's (TR 340341).
                This exception (i.e. not shifting ID's for tagset elements) works for 
                tagsets because when dirty cache gets saved to DWG later, tagsets are 
                processed via tags such that they do not go through SaveElementToDatabase
                which is where DWG ID's are compared with DGN ID's.
                -----------------------------------------------------------------------*/
                if (DgnStoreHdrHandler::IsDgnStoreElement(ElementHandle(cacheElem), TAGSET_ID, TAGID_BSI))
                    continue;

                DwgConversionDataAccessor::ChangeElementId (cacheElem, this->GetAndIncrementNextId());
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ReadWipeoutVariableCallback
(
AcDb::DxfCode               dxfGroupCode,
const void*                 dataIn,
void*                       dataOut
)
    {
    WipeoutVariable*        wipeoutVariable = (WipeoutVariable*)dataOut;

    if (70 == dxfGroupCode)
        wipeoutVariable->m_showFrame = *((Int16*)dataIn);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
static Int16                GetShowFrameFromWipeoutVariableDictionary
(
const AcDbObject*           wipeoutVarObj
)
    {
    WipeoutVariable         wipeoutVariable(0);
    ExtractionFiler         filer (ReadWipeoutVariableCallback, wipeoutVarObj->database(), &wipeoutVariable);

    wipeoutVarObj->dxfOut(&filer, Adesk::kTrue, NULL);

    return  wipeoutVariable.m_showFrame;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
bool    ConvertToDgnContext::DisplayWipeoutFrames
(
)
    {
    if (m_displayWipeoutFrames >= 0)
        return (1 == m_displayWipeoutFrames);

    m_displayWipeoutFrames = 1;

    AcDbObjectId            mainDictionaryId;
    if ( (mainDictionaryId = m_pFileHolder->GetDatabase()->namedObjectsDictionaryId()).isNull() )
        return true;

    AcDbDictionaryPointer   pMainDictionary (mainDictionaryId, AcDb::kForRead);
    if (Acad::eOk != pMainDictionary.openStatus())
        return true;

    AcDbObjectId            wipeoutVarId;
    if (Acad::eOk != pMainDictionary->getAt (L"ACAD_WIPEOUT_VARS", wipeoutVarId))
        return true;

    AcDbObjectPointer<AcDbObject>   pEntry (wipeoutVarId, AcDb::kForRead);
    if (Acad::eOk != pEntry.openStatus())
        return true;

    m_displayWipeoutFrames = GetShowFrameFromWipeoutVariableDictionary (pEntry);

    return (1 == m_displayWipeoutFrames);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      05/04
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::CreateProxyCell
(
EditElementHandleR          eeh,
ElementAgendaR              elemList,
AcDbEntity*                 pEntity
)
    {
    DPoint3d        origin;
    RotMatrix       rotMatrix;
    origin.Zero();
    rotMatrix.InitIdentity();

    NormalCellHeaderHandler::CreateCellElement (eeh, (WCharP) pEntity->isA()->name(), origin, rotMatrix, m_threeD, *m_model);
    if (!eeh.IsValid())
        return  CantCreateCell;

    // use MSElementDescr::AppendChild for performance reason
    MSElementDescrP cellHeader = eeh.GetElementDescrP ();
    MSElementDescrP lastChild = NULL;
    FOR_EACH (EditElementHandleR child, elemList)
        cellHeader->AppendChild (&lastChild, child.ExtractElementDescr());

    if (SUCCESS != NormalCellHeaderHandler::AddChildComplete(eeh))
        return  CantCreateCell;

    this->ElementHeaderFromEntity (eeh, pEntity);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/09
+---------------+---------------+---------------+---------------+---------------+------*/
Int32                       ConvertToDgnContext::GetDgnTextStyle (AcDbObjectId dwgTextStyle)
    {
    return this->GetFileHolder().GetTextStyleIndex()->GetDgnId (dwgTextStyle.handle());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   06/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt               ConvertToDgnContext::ParseApplicationDescription
(
WCharP&               appName,
WCharP&               vendorInfo,
WCharCP               inDescription
)
    {
    if ( (NULL == inDescription) || (0 == *inDescription) )
        return BSIERROR;

    WChar localDescription[4096];
    wcscpy (localDescription, inDescription);

    WCharP    appDescription = localDescription;

    // if starts with quote, skip past it.
    if ('\"' == appDescription[0])
        appDescription++;

    // if ends with quote, get rid of it.
    size_t length = wcslen (appDescription);
    if ('\"' == appDescription[length-1])
        appDescription[--length] = 0;

    // find first separator, which separates the appName from the vendor info.
    WCharP   separator;
    if (NULL != (separator = wcschr (appDescription, '|')))
        *separator = 0;
    else
        separator = &appDescription[length-1];

    appName     = appDescription;
    vendorInfo  = separator+1;
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   06/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                    ConvertToDgnContext::AddMissingObjectEnabler (AcDbObject* pObject, bool proxyOk)
    {
    // puts a missing object enabler into the cache (which holds a map of missing object enablers).
    WCharP            appName;
    WCharP            vendorInfo;

    AcDbProxyObject*    pProxyObj;
    if (NULL != (pProxyObj = AcDbProxyObject::cast (pObject)))
        {
        if (0 != (AcDbProxyEntity::kDisableProxyWarning & pProxyObj->proxyFlags()))
            return;

        if (BSISUCCESS == this->ParseApplicationDescription (appName, vendorInfo, pProxyObj->applicationDescription()))
            m_model->AddMissingEnabler (appName, vendorInfo, false, false);

        return;
        }

    AcDbProxyEntity*        pProxyEnt;
    if (NULL == (pProxyEnt = AcDbProxyEntity::cast (pObject)))
        return;

    if (0 != (AcDbProxyEntity::kDisableProxyWarning & pProxyEnt->proxyFlags()))
        return;

    if (BSISUCCESS == this->ParseApplicationDescription (appName, vendorInfo, pProxyEnt->applicationDescription()))
        m_model->AddMissingEnabler (appName, vendorInfo, true, proxyOk);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/09
+---------------+---------------+---------------+---------------+---------------+------*/
RgbFactor               ConvertToDgnContext::GetRGBFactor (const AcCmEntityColor& dwgColor, double scale)
    {
    RgbFactor   rgbFactor;

    if (dwgColor.isByColor())
        {
        scale /= 255.0;
        rgbFactor.red   = scale * dwgColor.red();
        rgbFactor.green = scale * dwgColor.green();
        rgbFactor.blue  = scale * dwgColor.blue();
        }
    else if (dwgColor.isByACI())
        {
        scale /= 255.0;
        const RgbColorDef*  rgbDef = this->GetFileHolder().GetDwgSymbologyData()->GetColor (dwgColor.colorIndex());
        rgbFactor.red   = scale * rgbDef->red;
        rgbFactor.green = scale * rgbDef->green;
        rgbFactor.blue  = scale * rgbDef->blue;
        }
    else
        {
        rgbFactor.red = rgbFactor.green = rgbFactor.blue = 0.0;
        }

    return  rgbFactor;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    JP.Wenger       04/2019
+---------------+---------------+---------------+---------------+---------------+------*/
ConvertToDgnMultiProcessingContext::ConvertToDgnMultiProcessingContext
(
DgnModelP modelRef,
FileHolder* pFileHolder,
RealDwgModelIndexItem* pModelIndexItem,
AcDbProgressMeterP progressMeter,
IDwgConversionSettings& settings
)
    :
    ConvertToDgnContext (modelRef, pFileHolder, pModelIndexItem, progressMeter, settings),
    m_satFileProcessingMode (SatFileProcessingMode::WriteToFile),
    m_sharedCellsCreation (*this),
    m_satToPsChoice (&m_satToPs)
    {
    ReplaceBlockExt ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    JP.Wenger       04/2019
+---------------+---------------+---------------+---------------+---------------+------*/
void ConvertToDgnMultiProcessingContext::_OnPreLoadObjectIntoCache (const AcDbObjectId& entityId)
    {
    m_curEntityId = entityId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    JP.Wenger       04/2019
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt ConvertToDgnMultiProcessingContext::_OnPostSaveAcisEntitiesToDgn
(
UpdateSequenceElm* pUpdateSequenceElm,
AcDbObjectIdArray& imageSequence,
bool isFirstNonXRef,
bool isFirstNonImage
)
    {
    StatusInt result = SUCCESS;
    if (CurrentSatToPs ().WaitForFinish ())
        {
        m_satFileProcessingMode = SatFileProcessingMode::ReadFromFile;

        for (BeFileName const& psfileName : CurrentSatToPs ().PsFileNames ())
            {
            auto found = m_satEntityMapping.find (BeFileName (BeFileName::GetFileNameWithoutExtension (psfileName).c_str ()));
            if (found == m_satEntityMapping.end ())
                {
#ifdef REALDWG_DIAGNOSTICS
                ::wprintf (L"Failed to find the ACIS entity corresponding to file %ls]\n", psfileName.c_str ());
#endif
                result = ERROR;
                continue;
                }
            auto entityId = found->second;
            m_curPSFileName = psfileName;
            this->SaveEntityToDgn (pUpdateSequenceElm, imageSequence, isFirstNonXRef, isFirstNonImage, entityId);
            }
        }

    return result;
    }

SatFileConversionMP& ConvertToDgnMultiProcessingContext::CurrentSatToPs ()
    {
    return *m_satToPsChoice;
    }

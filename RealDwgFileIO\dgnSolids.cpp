/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnSolids.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtSurfaceOrSolid : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleR elemHandle, AcDbObjectP existingObject, ConvertFromDgnContextR context) const
    {
    // get mapped type
    bool        isSolid = NULL != (dynamic_cast <SolidHandler*> (&elemHandle.GetHandler()));
    bool        isFlat = context.IsFlatSurfaceOrSolid (elemHandle);

    return  ToDwgExtCone::GetSolidOrSurfaceAcRxClass (isSolid, isFlat, elemHandle, context.GetSettings());
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    AcRxClass*          typeNeeded = GetTypeNeeded (elemHandle, existingObject, context);

    // set drop options in case we need to drop soild/surface
    DropGeometry        dropSolids (DropGeometry::OPTION_Solids);
    dropSolids.SetSolidsOptions (DropGeometry::SOLID_Wireframe);

    // drop to wireframe
    if (NULL == typeNeeded)
        return  context.DropElementToDwg (acObject, existingObject, elemHandle, dropSolids);

    // do not create a new solid here - a new solid will be created by AcDbBody::acisIn, but try preserve existing solid type:
    if (RealDwgUtil::IsObjectAcisType(existingObject) || !RealDwgUtil::IsClassAcisType(typeNeeded))
        acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);
    else
        acObject = nullptr;
    
    // solids, regions, bodies are all ACIS entities
    if (nullptr == acObject || RealDwgUtil::IsObjectAcisType(acObject))
        {
        AcDbObjectP     newObject = NULL;
        RealDwgStatus   status = context.ConvertSolidElementToAcis (newObject, acObject, elemHandle);
        if (RealDwgSuccess == status)
            {
            if (nullptr == acObject)
                acObject = newObject;
            return  status;
            }

        // ACIS type could have been replaced with a different type - a valid status
        if (ReplacedObjectType == status && NULL != newObject)
            {
            if (acObject->objectId().isValid())
                {
                if (Acad::eOk != acObject->handOverTo(newObject))
                    acObject->erase ();
                }
            else if (acObject->isNewObject())
                {
                delete acObject;
                }
            acObject = newObject;
            return  RealDwgSuccess;
            }

        // failed converting to ACIS - drop to wireframe. Since acObject was instanticated from existingObject, do not erase existingObject:
        return  context.DropElementToDwg (acObject, NULL, elemHandle, dropSolids);
        }

    // facet solid to mesh
    AcDbPolyFaceMesh*   acPolyface = AcDbPolyFaceMesh::cast (acObject);
    if (NULL != acPolyface)
        {
        if (RealDwgSuccess == context.SetAcDbPolyfaceMeshFromElement(acPolyface, elemHandle))
            {
            if (acPolyface != acObject)
                {
                // a new polyface has replaced existing object - delete the old object
                if (acObject->objectId().isValid())
                    {
                    DIAGNOSTIC_PRINTF ("Error: input polyface mesh ID=%I64d unexpectedly added in database!\n", elemHandle.GetElementId());
                    acObject->erase ();
                    }
                else
                    {
                    delete acObject;
                    }
                acObject = acPolyface;
                }
            return  RealDwgSuccess;
            }
        else
            {
            // failed creating polyface mesh - drop to wireframe.  Since acObject was instantiated from existingObject, do not erase existingObject:
            return  context.DropElementToDwg (acObject, NULL, elemHandle, dropSolids);
            }
        }

    assert (false && L"Missing implementation in solid(19)/surface(18) element's ToDwgExtension!");
    return NoConversionMethod;
    }

};  // ToDwgExtSolidOrSurface


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtSurface : public ToDwgExtSurfaceOrSolid
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    DPoint3d            extrusionDirection;
    double              extrusionDistance = 0.0;
    EditElementHandle   extrusionElement;

    // first try to convert an extruded surface to a 2D entity with thickness:
    if (RealDwgSuccess == context.ExtractThicknessFromElement(extrusionElement, extrusionDirection, extrusionDistance, elemHandle) &&
        !context.IsNonPlanarPolyline(extrusionElement, false))
        {
        SaveElementPurpose  oldPurpose = context.GetSaveElementPurpose ();
        context.SetSaveElementPurpose (SAVEELEMENTPURPOSE_ForExtrusion);

        RealDwgStatus   status = context.CreateObjectFromElement (acObject, extrusionElement);

        context.SetSaveElementPurpose (oldPurpose);

        if (RealDwgSuccess == status)
            {
            if (NULL == acObject)
                return  ExtrusionFailure;

            // The spline and ellipse are of a 3D entity type, which cannot be extruded
            if (acObject->isKindOf(AcDbSpline::desc()) || acObject->isKindOf(AcDbEllipse::desc()))
                {
                // clean up the object and fall through to the default method
                if (acObject->objectId().isValid())
                    acObject->erase ();
                else
                    delete acObject;

                acObject = NULL;
                }
            else
                {
                DVec3d      defaultNormal = DVec3d::From (0.0, 0.0, 1.0);
                AcDbEntityP acEntity = AcDbEntity::cast (existingObject);
                if ((NULL != acEntity || NULL != (acEntity = AcDbEntity::cast(acObject))) && acEntity->isPlanar())
                    {
                    AcGePlane           plane;
                    AcDb::Planarity     planeFlag;
                    if (Acad::eOk == acEntity->getPlane(plane, planeFlag) && AcDb::kPlanar == planeFlag)
                        RealDwgUtil::DVec3dFromGeVector3d (defaultNormal, plane.normal());

                    AcDbLine *acLine = NULL;
                    if(NULL != (acLine = AcDbLine::cast(acObject)))
                        acLine->setNormal(RealDwgUtil::GeVector3dFromDPoint3d (extrusionDirection));
                    }

                if (defaultNormal.DotProduct(extrusionDirection) < 0.0)
                    extrusionDistance = -extrusionDistance;

                // Add thickness to the profile entity
                return this->AddThicknessToEntity (AcDbEntity::cast(acObject), extrusionDistance);
                }
            }
        }
    
    return __super::ToObject (elemHandle, acObject, existingObject, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   AddThicknessToEntity (AcDbEntityP acEntity, double thickness) const
    {
    if (fabs(thickness) <= TOLERANCE_ExtrusionRound)
        return  RealDwgSuccess;

    if (NULL == acEntity)
        return  ExtrusionFailure;

    if (acEntity->isNewObject() && !acEntity->isErased())
        return  this->SetThickness (acEntity, thickness);

    AcDbObjectId    objectId = acEntity->objectId ();
    if (!objectId.isValid())
        return  ExtrusionFailure;

    AcDbObjectPointer<AcDbPolyline>     pline(objectId, AcDb::kForWrite);
    if (Acad::eOk == pline.openStatus())
        return  this->SetThickness (pline, thickness);

    AcDbObjectPointer<AcDb2dPolyline>   pline2d(objectId, AcDb::kForWrite);
    if (Acad::eOk == pline.openStatus())
        return  this->SetThickness (pline2d, thickness);

    AcDbObjectPointer<AcDbCircle>       circle(objectId, AcDb::kForWrite);
    if (Acad::eOk == circle.openStatus())
        return  this->SetThickness (circle, thickness);

    AcDbObjectPointer<AcDbArc>          arc(objectId, AcDb::kForWrite);
    if (Acad::eOk == arc.openStatus())
        return  this->SetThickness (arc, thickness);

    AcDbObjectPointer<AcDbLine>         line(objectId, AcDb::kForWrite);
    if (Acad::eOk == line.openStatus())
        return  this->SetThickness (line, thickness);

    AcDbObjectPointer<AcDbSolid>        solid(objectId, AcDb::kForWrite);
    if (Acad::eOk == solid.openStatus())
        return  this->SetThickness (solid, thickness);

    AcDbObjectPointer<AcDbTrace>        trace(objectId, AcDb::kForWrite);
    if (Acad::eOk == trace.openStatus())
        return  this->SetThickness (trace, thickness);

    assert (false && L"Thickness is not supported on this object type!!");

    return  ExtrusionFailure;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetThickness (AcDbEntityP acObject, double thickness) const
    {
    AcDbPolyline*   pline = AcDbPolyline::cast (acObject);
    if (NULL != pline)
        return Acad::eOk == pline->setThickness(thickness) ? RealDwgSuccess : ExtrusionFailure;

    AcDb2dPolyline* pline2d = AcDb2dPolyline::cast (acObject);
    if (NULL != pline2d)
        return Acad::eOk == pline2d->setThickness(thickness) ? RealDwgSuccess : ExtrusionFailure;

    AcDbCircle*     circle = AcDbCircle::cast (acObject);
    if (NULL != circle)
        return Acad::eOk == circle->setThickness(thickness) ? RealDwgSuccess : ExtrusionFailure;

    AcDbArc*        arc = AcDbArc::cast (acObject);
    if (NULL != arc)
        return Acad::eOk == arc->setThickness(thickness) ? RealDwgSuccess : ExtrusionFailure;

    AcDbLine*       line = AcDbLine::cast (acObject);
    if (NULL != line)
        return Acad::eOk == line->setThickness(thickness) ? RealDwgSuccess : ExtrusionFailure;

    AcDbSolid*      solid = AcDbSolid::cast (acObject);
    if (NULL != solid)
        return Acad::eOk == solid->setThickness(thickness) ? RealDwgSuccess : ExtrusionFailure;

    AcDbTrace*      trace = AcDbTrace::cast (acObject);
    if (NULL != trace)
        return Acad::eOk == trace->setThickness(thickness) ? RealDwgSuccess : ExtrusionFailure;

    assert (false && L"Thickness is not supported on this object type!!");

    return  ExtrusionFailure;
    }


};  // ToDwgExtSurface


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtSolid : public ToDwgExtSurfaceOrSolid
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/10
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    if (context.GetSettings().CreateExtrusionsFromProjectedSolids())
        {
        ToDwgExtSurface surfaceToDwg;
        return surfaceToDwg.ToObject (elemHandle, acObject, existingObject, context);
        }

    return __super::ToObject (elemHandle, acObject, existingObject, context);
    }

};  // ToDwgExtSolid


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          02/12
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtBrepElement : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleR elemHandle, AcDbObjectP existingObject, ConvertFromDgnContextR context) const
    {
    // try retaining original ACIS type
    if (RealDwgUtil::IsObjectAcisType(existingObject))
        return  existingObject->isA ();

    // get mapped type
    bool        isFlat = context.IsFlatSurfaceOrSolid (elemHandle);
    return  ToDwgExtCone::GetSolidOrSurfaceAcRxClass (true, isFlat, elemHandle, context.GetSettings());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    BrepCellHeaderHandler*  handler = dynamic_cast <BrepCellHeaderHandler*> (&elemHandle.GetHandler());
    if (NULL == handler)
        return  BadElementHandler;

    AcRxClass*              typeNeeded = GetTypeNeeded (elemHandle, existingObject, context);

    DropGeometry            dropSolids (DropGeometry::OPTION_Solids);
    dropSolids.SetSolidsOptions (DropGeometry::SOLID_Wireframe);

    // drop to wireframe
    if (NULL == typeNeeded)
        return  context.DropElementToDwg (acObject, existingObject, elemHandle, dropSolids);

    // do not create a new solid - it shall be created by AcDbBody::acisIn, but try preserve existing solid type:
    if (RealDwgUtil::IsObjectAcisType(existingObject) || !RealDwgUtil::IsClassAcisType(typeNeeded))
        acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);
    else
        acObject = nullptr;

    if (RealDwgUtil::IsClassAcisType(typeNeeded))
        {
        AcDbObjectP     newObject = NULL;
        RealDwgStatus   status = this->SetAcisEntityFromBRep (newObject, acObject, elemHandle, handler, context);
        if (RealDwgSuccess == status && nullptr == acObject)
            {
            acObject = newObject;
            }
        else if (ReplacedObjectType == status && nullptr != newObject && nullptr != acObject)
            {
            if (acObject->objectId().isValid())
                acObject->handOverTo (newObject);
            else if (acObject->isNewObject())
                delete acObject;
            acObject = newObject;
            status = RealDwgSuccess;
            }
        return  status;
        }

    AcDbPolyFaceMesh*       acPolyface = AcDbPolyFaceMesh::cast (acObject);
    if (NULL != acPolyface)
        {
        RealDwgStatus   status = context.SetAcDbPolyfaceMeshFromElement (acPolyface, elemHandle);
        if (RealDwgSuccess == status && acPolyface != acObject)
            {
            // a new polyface has replaced existing one, delete the old obejct
            delete acObject;
            acObject = acPolyface;
            }
        return  status;
        }

    assert (false && L"Missing implementation in Brep element's ToDwgExtension!");
    return NoConversionMethod;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcisEntityFromBRep (AcDbObjectP& newObject, AcDbObjectP& desiredObject, ElementHandleCR inElement, BrepCellHeaderHandler* handler, ConvertFromDgnContextR context) const
    {
    ISolidKernelEntityPtr   kernelEnt;
    if (SUCCESS != handler->GetBRepDataEntity(inElement, kernelEnt))
        return  CantCreateAcisElement;

    // below conversion can replace an ACIS type with another ACIS type or even a different entity type - a valid status
    RealDwgStatus   status = context.SetAcisEntityFromKernelEntity (newObject, desiredObject, inElement, *kernelEnt.get());

    if (RealDwgSuccess != status && ReplacedObjectType != status && ConversionInChildrenProc != status)
        {
        DIAGNOSTIC_PRINTF ("Error converting Brep element (%I64d) to ACIS, dropping to wireframe\n",inElement.GetElementId());
        DropGeometry    dropSolids (DropGeometry::OPTION_Solids);
        dropSolids.SetSolidsOptions (DropGeometry::SOLID_Wireframe);

        // drop to wireframe.  Since desiredObject might be instantiated from existingObject, erase it after drop:
        status = context.DropElementToDwg (newObject, desiredObject, inElement, dropSolids);
        }

    return  status;
    }

};  // ToDwgExtBrepElement


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          06/12
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtBSplineSurface : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    AcRxClass*          typeNeeded = GetTypeNeeded (elemHandle, existingObject, context);
    if (NULL != typeNeeded)
        {
        // do not create a new solid - it shall be created by AcDbBody::acisIn, but try preserve existing solid type:
        if (RealDwgUtil::IsObjectAcisType(existingObject) || !RealDwgUtil::IsClassAcisType(typeNeeded))
            acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);
        else
            acObject = nullptr;
 
        AcDbPolygonMesh*    acPolygon = AcDbPolygonMesh::cast (acObject);
        if (NULL != acPolygon)
            return  context.SetAcDbPolygonMeshFromBSplineSurface (acPolygon, elemHandle);

        AcDbPolyFaceMesh*   acPolyface = AcDbPolyFaceMesh::cast (acObject);
        if (NULL != acPolyface)
            {
            RealDwgStatus   status = context.SetAcDbPolyfaceMeshFromElement (acPolyface, elemHandle);
            if (RealDwgSuccess == status && acPolyface != acObject)
                {
                // a new polyface has replaced existing object - delete the old object
                if (acObject->objectId().isValid())
                    {
                    DIAGNOSTIC_PRINTF ("Error: input polyface mesh ID=%I64d unexpectedly added in database!\n", elemHandle.GetElementId());
                    acObject->erase ();
                    }
                else
                    {
                    delete acObject;
                    }
                acObject = acPolyface;
                }
            return  status;
            }

        if (RealDwgUtil::IsClassAcisType(typeNeeded))
            {
            AcDbObjectP     newObject = NULL;
            RealDwgStatus   status = context.ConvertSolidElementToAcis (newObject, acObject, elemHandle);
            if (RealDwgSuccess == status && nullptr == acObject)
                {
                acObject = newObject;
                }
            else if (ReplacedObjectType == status && nullptr != newObject && nullptr != acObject)
                {
                if (acObject->objectId().isValid())
                    {
                    if (Acad::eOk != acObject->handOverTo(newObject))
                        acObject->erase ();
                    }
                else if (acObject->isNewObject())
                    {
                    delete acObject;
                    }
                acObject = newObject;
                status = RealDwgSuccess;
                }
            if (RealDwgSuccess == status || ReplacedObjectType == status)
                return  status;
            // failed ACIS conversion - drop the bspline surface to wireframe
            }
        }

    // drop BSpline surface to wireframe
    DropGeometry        dropSurface (DropGeometry::OPTION_Solids);
    dropSurface.SetSolidsOptions (DropGeometry::SOLID_Wireframe);

    return context.DropElementToDwg (acObject, NULL, elemHandle, dropSurface);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleR inElement, AcDbObjectP existingObject, ConvertFromDgnContextR context) const
    {
    if (SolidSurface_Wireframe == context.GetSettings().GetSolidSurfaceMapping(false))
        return  NULL;

    MSBsplineSurface    surface;
    if (BSISUCCESS != BSplineSurfaceHandler::SurfaceFromElement(surface, inElement))
        return  NULL;

    // use unsmoothed 3D grid polyline when possible b/c it's cheaper than ACIS
    if ((surface.uParams.order == surface.vParams.order) && (surface.uParams.order == 2) &&
         (0 == surface.uParams.numKnots) && (0 == surface.vParams.numKnots) &&
         (0 == surface.rational) && (0 == surface.numBounds) &&
         (0x7fff >= surface.uParams.numPoles) &&    // ACAD limit
         (0x7fff >= surface.vParams.numPoles))      // ACAD limit
        {
        bspsurf_freeSurface (&surface);
        return  AcDbPolygonMesh::desc ();
        }
    else if (SolidSurface_Acis == context.GetSettings().GetSolidSurfaceMapping (false))
        {
        // other B-spline surfaces that could be exported as 3D grid
        // polylines don't look smooth in ACAD, so we have to use the
        // heavyweight ACIS body.
        bspsurf_freeSurface (&surface);
        return  AcDbBody::desc ();
        }

    bspsurf_freeSurface (&surface);
    return  AcDbPolyFaceMesh::desc ();
    }

};  // ToDwgExtBSplineSurface



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::DropElementToDwg (AcDbObjectP& acObject, AcDbObjectP existingObj, ElementHandleCR inElement, DropGeometry& geometryOptions)
    {
    DisplayHandlerP displayHandler = inElement.GetDisplayHandler ();
    if (NULL == displayHandler)
        return  BadElementHandler;

    ElementAgenda   droppedAgenda;
    if (SUCCESS != displayHandler->Drop(inElement, droppedAgenda, geometryOptions) || droppedAgenda.size() == 0)
        return  DropFailed;

    bool            replaceExistingObject = false;

    // we will add all dropped elements to current block so we need to clear out the output object:
    if (NULL == existingObj)
        {
        // we must have created the new object via AcRxClass::create(), delete it now:
        if (NULL != acObject && acObject->isNewObject() && !acObject->isErased())
            delete acObject;
        }
    else if (NULL != acObject && acObject->objectId().isValid())
        {
        // we have not only created the new object but also handed the existing object ID over to the new object, switch it back and delete the new object now:
        if (NULL != existingObj && !existingObj->objectId().isValid())
            {
            acObject->handOverTo (existingObj);
            delete acObject;
            }
        else
            {
            acObject->erase ();
            }
        }
    else if (NULL != existingObj && (NULL == acObject || !acObject->objectId().isValid()))
        {
        // we have not created the new object yet, and we can try to preserve the existing object ID
        replaceExistingObject = true;
        }
    acObject = NULL;

    // maintain the same base priority as the input source element's, and add a second priority for each dropped element:
    ElementRefP     inElemRef = inElement.GetElementRef ();
    PrioritySorter* prioritySorter = this->GetPrioritySorter ();
    if (NULL != prioritySorter && NULL != inElemRef && !inElemRef->IsComplexComponent())
        prioritySorter->SetBaseElementPosition (inElemRef->GetFilePos());

    UInt32          count = 0;
    FOR_EACH (EditElementHandleR primitive, droppedAgenda)
        {
        ToDwgExtension* toDwg = ToDwgExtension::Cast (primitive.GetHandler());
        if (NULL == toDwg)
            {
            assert (false && L"Missing ToDwgExtension for the dropped element handler!");
            continue;
            }

        // save the rest primitives to database
        AcDbObjectP     newObject = NULL;
        RealDwgStatus   status = toDwg->ToObject (primitive, newObject, NULL, *this);
        if (RealDwgSuccess != status)
            {
            DIAGNOSTIC_PRINTF ("Error on dropped element type %d from source element type %d\n", primitive.GetElementType(), inElement.GetElementType());
            continue;
            }

        AcDbEntityP     newEntity = AcDbEntity::cast (newObject);
        if (NULL == newEntity)
            {
            DIAGNOSTIC_PRINTF ("Error on dropped element type %d from source element type %d\n", primitive.GetElementType(), inElement.GetElementType());
            if (NULL != newObject)
                delete newObject;
            continue;
            }

        // set new entity's properties
        if (!newEntity->isKindOf(AcDbHatch::desc()))
            this->UpdateEntityPropertiesFromElement (newEntity, primitive);
        // add new entity to database, if not already added
        AcDbObjectId    newId = newEntity->objectId ();
        if (!newId.isValid())
            {
            // this is our chance to replace existing object with the first dropped object
            if (0 == count && replaceExistingObject)
                existingObj->handOverTo (newEntity);
            else
                newId = this->AddEntityToCurrentBlock (newEntity, 0);
            }

        newEntity->close ();

        // set new entity sort order, which is relative to the parent input element:
        if (NULL != prioritySorter && newId.isValid())
            prioritySorter->AddElement (inElement, count, this->ElementIdFromObjectId(newId));
        count++;
        }
    
    if (NULL != prioritySorter)
        prioritySorter->SetBaseElementPosition (0);

    return  count > 0 ? RealDwgSuccess : DropFailed;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus    ConvertFromDgnContext::DropElementToBlock (AcDbObjectId& blockId, ElementHandleCR inElement, DropGeometry& dropOptions)
    {
    bool        isNewBlock = false;
    if (!blockId.isValid())
        {
        // block does not exist, create a new one:
        AcDbBlockTablePointer       blockTable (this->GetDatabase()->blockTableId(), AcDb::kForWrite);
        Acad::ErrorStatus           acStatus = blockTable.openStatus ();
        if (Acad::eOk == acStatus)
            {
            AcDbBlockTableRecord*   newBlock = new AcDbBlockTableRecord ();
            acStatus = blockTable->add (newBlock);
            if (Acad::eOk == acStatus)
                {
                blockId = newBlock->objectId ();
                // make it an anonymous block
                acStatus = newBlock->setName (DIMENSION_ELM == inElement.GetElementType() ? L"*D" : L"*U");
                if (Acad::eOk != acStatus)
                    DIAGNOSTIC_PRINTF ("Error setting anonymous block name, ID=%I64d. [%ls]\n", inElement.GetElementId(), acadErrorStatusText(acStatus));

                if (this->GetSettings().SaveBlockUnitsFromFileUnits())
                    newBlock->setBlockInsertUnits (RealDwgUtil::AcDbUnitsValueFromDgnUnits(this->GetStandardTargetUnits()));

                // default origin to the "transformation origin"
                DisplayHandlerP     displayHandler = inElement.GetDisplayHandler ();
                if (NULL != displayHandler)
                    {
                    DPoint3d        origin = DPoint3d::From (0, 0, 0);
                    displayHandler->GetTransformOrigin (inElement, origin);
                    this->GetTransformFromDGN().Multiply (origin);

                    newBlock->setOrigin (RealDwgUtil::GePoint3dFromDPoint3d(origin));
                    }
                
                newBlock->close ();
                isNewBlock = true;
                }
            else
                {
                delete newBlock;
                }
            }
        if (Acad::eOk != acStatus)
            return  CantAddNewBlock;
        }

    AcDbObjectId    currentBlockId = this->GetCurrentBlockId ();

    // make it the current block in out context:
    this->SetCurrentBlockId (blockId);

    AcDbObjectP     nullObj = NULL;
    RealDwgStatus   status = this->DropElementToDwg (nullObj, NULL, inElement, dropOptions);

    this->SetCurrentBlockId (currentBlockId);

    // delete the new block we have created on drop failure:
    if (RealDwgSuccess != status && isNewBlock)
        {
#if defined (REALDWG_DIAGNOSTICS)
        WString     typeName;
        inElement.GetHandler().GetTypeName (typeName, 100);
        DIAGNOSTIC_PRINTF ("Failed dropping element to block! [ID=%I64d, type=%ls]\n", inElement.GetElementId(), typeName.c_str());
#endif
        AcDbBlockTableRecordPointer     block (blockId, AcDb::kForWrite);
        if (Acad::eOk == block.openStatus() && Acad::eOk == block->erase())
            block.close ();
        }
                                                
    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/16
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::DropGraphicsToBlock (AcDbObjectP oldObject, ElementHandleCR inElement, DropGraphics::Options options, WCharCP blockName)
    {
    ElementAgenda   droppedPrimitives;
    DropGeometry    dropGeom ((DropGeometry::Options)(DropGeometry::OPTION_Complex | DropGeometry::OPTION_AppData));
    DropGraphics    dropOpts (options);

    if (DropGraphics::OPTION_Patterns | options)
        dropOpts.SetPatternBoundary (DropGraphics::BOUNDARY_Include);

    if (BSISUCCESS != DropToElementDrawGeom::DoDrop(inElement, droppedPrimitives, dropGeom, dropOpts) || droppedPrimitives.IsEmpty())
        return  DropFailed;

    AcDbSmartBlockTableRecordPointer    acBlock;

    /*-----------------------------------------------------------------------------------
    We will create a new block to wrap the dropped primitives if:
        1) the parent (i.e. current block in this context) is modelspace or a paperspace, or
        2) current block is a user block (as opposed to an anonymouse block)
    If current block is an anonymouse block, we simply drop the primitives in that block.
    -----------------------------------------------------------------------------------*/
    AcDbObjectId    currentBlockId = this->GetCurrentBlockId ();
    bool            isNewBlock = currentBlockId == acdbSymUtil()->blockModelSpaceId(this->GetDatabase());
    if (!isNewBlock && Acad::eOk == acBlock.open(currentBlockId, AcDb::kForRead))
        {
        isNewBlock = !acBlock->isLayout() && !acBlock->isAnonymous();
        acBlock.close ();
        }

    if (isNewBlock)
        {
        isNewBlock = false;
        AcDbBlockTablePointer   blockTable (this->GetDatabase()->blockTableId(), AcDb::kForWrite);
        if (Acad::eOk == blockTable.openStatus() && Acad::eOk == acBlock.create() && Acad::eOk == blockTable->add(acBlock))
            {
            acBlock->setName ((nullptr == blockName || 0 == *blockName) ? L"*X" : blockName);
            this->SetCurrentBlockId (acBlock->objectId());
            acBlock.close ();
            isNewBlock = true;
            }
        }

    size_t  count = 0;
    for(auto& primitive : droppedPrimitives)
        {
        AcDbObjectP     newObject = nullptr;
        RealDwgStatus   status = RealDwgSuccess;
        ToDwgExtension* toDwg = ToDwgExtension::Cast (primitive.GetHandler());

        if (nullptr != toDwg)
            status = toDwg->ToObject (primitive, newObject, nullptr, *this);

        if (RealDwgSuccess != status)
            {
            DIAGNOSTIC_PRINTF ("Error creating dropped primitive type %d!\n", primitive.GetElementType());
            continue;
            }

        AcDbEntityP     newEntity = AcDbEntity::cast (newObject);
        if (nullptr == newEntity)
            {
            DIAGNOSTIC_PRINTF ("Error on dropped element type %d\n", primitive.GetElementType());
            if (NULL != newObject)
                delete newObject;
            continue;
            }

        this->UpdateEntityPropertiesFromElement (newEntity, primitive);

        if (!newEntity->objectId().isValid())
            this->AddEntityToCurrentBlock (newEntity, 0);

        newEntity->close ();
        count++;
        }

    // create an insert entity for the new anonymous block
    if (isNewBlock && count > 0)
        {
        AcDbBlockReference* acInsert = new AcDbBlockReference ();
        if (nullptr == acInsert)
            return  OutOfMemoryError;

        if (Acad::eOk != acInsert->setBlockTableRecord(this->GetCurrentBlockId()))
            return  BlockTableRecordMissing;

        // if the input entity has been added to db, hand its id over to the new insert entitity; otherwise add it to current block:
        if (nullptr == oldObject || !oldObject->objectId().isValid() || Acad::eOk != oldObject->handOverTo(acInsert, true, true))
            this->AddEntityToBlockId (currentBlockId, acInsert, inElement.GetElementId());

        this->UpdateEntityPropertiesFromElement (acInsert, inElement);

        if (acInsert->objectId().isValid())
            acInsert->close ();
        else
            delete acInsert;
        }
    else if (nullptr != oldObject && oldObject->objectId().isValid())
        {
        oldObject->erase ();
        }

    // restore current block
    this->SetCurrentBlockId (currentBlockId);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::BlockFromApplicationData
(
AcDbObjectId&               blockId,
ElementHandleR              inElement
)
    {
    IDwgConversionSettings& settings = this->GetSettings ();
    bool                    isFlat = this->IsFlatSurfaceOrSolid (inElement);
    AcRxClass*              typeNeeded = ToDwgExtCone::GetSolidOrSurfaceAcRxClass (true, isFlat, inElement, settings);
    if (NULL == typeNeeded)
        return  NotApplicable;

    // The application must implement IBRepQuery to create BRep data
    IBRepQuery*             brepHandler = dynamic_cast <IBRepQuery*> (&inElement.GetHandler());
    if (NULL == brepHandler)
        return  BadElementHandler;

    ISolidKernelEntityPtr   kernelEnt;
    if (NULL == brepHandler || SUCCESS != brepHandler->GetBRepDataEntity(inElement, kernelEnt))
        return  CantCreateAcisElement;

    AcDbEntityP             newEntity = AcDbEntity::cast (typeNeeded->create());
    AcDbPolyFaceMesh*       acPolyface = AcDbPolyFaceMesh::cast (newEntity);
    RealDwgStatus           status = CantCreateMesh;
    
    if (NULL != acPolyface)
        {
        IFacetOptionsPtr    facetOptions = IFacetOptions::Create ();
        facetOptions->SetChordTolerance (0.0);

        IFacetTopologyTablePtr  faceter;
        if (SUCCESS != T_HOST.GetSolidsKernelAdmin ()._FacetBody(faceter, *kernelEnt, *facetOptions.get()))
            return  CantCreateMesh;

        PolyfaceHeaderPtr       dgnPolyface = PolyfaceHeader::CreateVariableSizeIndexed ();
        if (!faceter->ConvertToPolyface(*dgnPolyface.get(), *faceter.get(), *facetOptions.get()))
            return  CantCreateMesh;

        ToDwgExtMeshHeader      meshToDwg;
        status = meshToDwg.SetAcDbPolyfaceMeshFromMeshElement (acPolyface, dgnPolyface, inElement, *this);
        if (RealDwgSuccess != status)
            return  status;
        }
    else    // 3dsolid/body/region
        {
        AcDbObjectP             newObject = newEntity;
        AcDbObjectP             replacingObject = NULL;
        status = this->SetAcisEntityFromKernelEntity (replacingObject, newObject, inElement, *kernelEnt.get());

        // AcDbBody::acisIn() could have replaced an ACIS type with another ACIS type or even a different entity type - a valid status
        if (ReplacedObjectType == status && NULL != replacingObject)
            {
            newEntity = AcDbEntity::cast (newObject);
            if (NULL == newEntity)
                {
                if (newObject->objectId().isValid())
                    newObject->erase ();
                else
                    delete newObject;
                return  CantCreateAcisElement;
                }
            }
        else if (RealDwgSuccess != status)
            return  status;
        }

    return this->AddEntityToBlockId (blockId, newEntity, 0).isValid() ? RealDwgSuccess : CantCreateCell;
    }

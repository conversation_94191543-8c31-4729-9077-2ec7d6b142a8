/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdBlock.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtBlock : public ToDgnExtension
{
protected:

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbBlockTableRecord*   pBlock = AcDbBlockTableRecord::cast (acObject);
    bool                    hasVisibleChild = false;
    bool                    wasInSharedCellCreation = context.IsInSharedCellCreation ();

    // set the shared cell def creation mark if not already set(i.e. not creating a nested shared cell def):
    if (!wasInSharedCellCreation)
        context.SetInSharedCellCreation (true);

    // First go through and export any block definitions for inserts within this block.
    AcDbBlockTableRecordIterator*     pEntIter;
    pBlock->newIterator (pEntIter, true, true);
    for (; !pEntIter->done(); pEntIter->step())
        {
        AcDbEntity*         pChild;
        if (Acad::eOk != pEntIter->getEntity (pChild, AcDb::kForRead))
            continue;

        if (pChild->isKindOf (AcDbBlockReference::desc()))
            {
            AcDbObjectId    childBlockId = ((AcDbBlockReference*) pChild)->blockTableRecord();

            if (childBlockId != pBlock->objectId())             // avoid infinite recursion.
                context.SaveSharedCellDefinitionToDgn (childBlockId);
            }

        if (!hasVisibleChild && IsEntityVisible(pChild))
            hasVisibleChild = true;

        pChild->close();
        }
    delete pEntIter;

    CreateSharedCellDefinition (outElement, pBlock, context);

    Transform     currentTo, currentFrom, currentLocal, untranslate;
    untranslate.InitIdentity ();
    DVec3d  translation;
    context.GetTransformFromDGN().GetTranslation (translation);
    translation.Negate();
    untranslate.SetTranslation (translation);

    context.PushTransform (&currentTo, &currentFrom, &currentLocal, &untranslate);
    context.SetCurrentBlockId (pBlock->objectId());

    /*---------------------------------------------------------------------------------------------------------
    We opt to use MSElementDescrP over ElementHandle for performance reason: directly appending a child element
    to the end of the element chain is a lot faster than walking through the entire chain just to get to the end 
    of it, as it currently does with SharedCellDefHandler::AddChildElement which calls MSElemenrDescr::AppendDescr 
    instead of AppendChild.
    ---------------------------------------------------------------------------------------------------------*/
    MSElementDescrP         cellHeader = outElement.GetElementDescrP ();
    MSElementDescrP         lastChild = NULL;

    AcDbSortentsTable*      pSortentsTable = NULL;
    if (Acad::eOk == pBlock->getSortentsTable(pSortentsTable, AcDb::kForRead))
        {
        // create elements from entities in sorted order:
        AcDbObjectIdArray   entIdArray;

        if (Acad::eOk == pSortentsTable->getFullDrawOrder(entIdArray))
            {
            for (int i = 0; i < entIdArray.length(); i++)
                ElementFromEntity (cellHeader, &lastChild, entIdArray.at(i), pBlock, context);
            }

        pSortentsTable->close ();
        }
    else
        {
        // create elements from entities by file position:
        pBlock->newIterator (pEntIter, true, true);
        for (; !pEntIter->done(); pEntIter->step() )
            {
            AcDbObjectId    entityId;
            if (Acad::eOk == pEntIter->getEntityId(entityId))
                ElementFromEntity (cellHeader, &lastChild, entityId, pBlock, context);
            }
        delete pEntIter;
        }

    if (pBlock->origin().x != 0.0 || pBlock->origin().y != 0.0 || pBlock->origin().z != 0.0)
        {
        DPoint3d            basePoint;
        Transform           baseTransform;

        RealDwgUtil::DPoint3dFromGePoint3d (basePoint, pBlock->origin());

        basePoint.Scale (-context.GetScaleToDGN());
        baseTransform.InitFrom (basePoint);

        TransformInfo       transInfo (baseTransform);
        for (ChildEditElemIter child(outElement); child.IsValid(); child = child.ToNext())
            child.GetHandler (MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (child, transInfo);
        }

    // process tags on block header: load a type 39 into cache, get a list of type 37's, append 37's as children:
    MSElementDescrP         tagsInSharedcellDef = NULL;
    bool                    tag1Invisible = false;
    if (context.GetSettings().AttributesAsTags() && RealDwgSuccess == context.SaveBlockAttrdefsToDgn(&tagsInSharedcellDef, pBlock) && NULL != tagsInSharedcellDef)
        {
        /*------------------------------------------------------------------------------------------------------
        An empty shared cell def with only invisible tags will produce invalid range which results in undesirable
        behavior.  Temporarily set the 1st tag visible to get a range, then reset it back.  We do not need to set 
        them all on because we do not care about a correct range.  We only need a range that won't fail otherwise 
        a valid operation such as a case in TFS 24431.
        ------------------------------------------------------------------------------------------------------*/
        tag1Invisible = NULL == lastChild && tagsInSharedcellDef->el.hdr.dhdr.props.b.invisible;
        if (tag1Invisible)
            tagsInSharedcellDef->el.hdr.dhdr.props.b.invisible = false;

        outElement.GetElementDescrP()->AppendChild (&lastChild, tagsInSharedcellDef);
        }

    context.PopTransform (&currentTo, &currentFrom, &currentLocal);
    context.SetCurrentBlockId (AcDbObjectId::kNull);

    // clear the shared cell creation mark or let the parent do it if we are in a nested block - TFS 139496.
    if (!wasInSharedCellCreation)
        context.SetInSharedCellCreation (false);

    if (BSISUCCESS != SharedCellDefHandler::AddChildComplete(outElement))
        return  MstnElementUnacceptable;

    // reset the 1st tag's visibility
    if (tag1Invisible)
        tagsInSharedcellDef->el.hdr.dhdr.props.b.invisible = true;

    // set isCellAnnotation
    this->SetIsAnnotation (outElement, pBlock);
    // set rangDiagonal
    this->SetRangeDiagonal (outElement);

    // shared def with all invisible children may have invalid range, save it for shared cell instance process:
    if (!hasVisibleChild && !this->IsRangeValid(outElement))
        context.AddInvisibleBlock (pBlock->objectId());

    if (!context.GetSettings().AttributesAsTags())
        context.SaveBlockAttrdefsAsItemTypedefs (pBlock);

    return RealDwgSuccess;
    }
    
public:

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/17
+---------------+---------------+---------------+---------------+---------------+------*/
bool    IsEntityVisible (AcDbEntityP entity) const
    {
    if (nullptr != entity)
        {
        // a point entity is controlled by both entity visibility and the sysyem var PDMODE==1, TFS 706075.
        AcDbPoint*  point = AcDbPoint::cast (entity);
        if (nullptr != point)
            {
            AcDbDatabaseP   dwg = point->database ();
            if (nullptr != dwg && 1 == dwg->pdmode())
                return  false;
            }

        // an attrdef entity will not be a graphical element in the shared cell def, TFS 706075
        AcDbAttributeDefinition*    attrdef = AcDbAttributeDefinition::cast (entity);
        if (nullptr != attrdef)
            return  false;

        return AcDb::kVisible == entity->visibility();
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
static bool            IsRangeValid (ElementHandleCR inElement)
    {
    ScanRangeCP     range = inElement.GetIndexRange ();

    if ((range->xhighlim < range->xlowlim && range->yhighlim < range->ylowlim && range->zhighlim < range->zlowlim) ||
        (0 == range->xhighlim && 0 == range->yhighlim && 0 == range->zhighlim && 0 == range->xlowlim && 0 == range->ylowlim && 0 == range->zlowlim))
        return  false;

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ElementFromEntity
(
MSElementDescrP             cellHeader,
MSElementDescrH             lastChild,
const AcDbObjectId&         entityId,
const AcDbBlockTableRecord* pBlock,
ConvertToDgnContextR        context
) const
    {
    AcDbEntity*         pEntity = NULL;
    if (Acad::eOk != acdbOpenObject(pEntity, entityId, AcDb::kForRead))
        return  CantOpenObject;

    context.OnPreLoadObjectIntoCache (entityId);

    if (pEntity->isKindOf (AcDbBlockReference::desc()) && pBlock->objectId() == ((AcDbBlockReference*) pEntity)->blockTableRecord())
        {
        const ACHAR* blockName;
        pBlock->getName (blockName);
        DIAGNOSTIC_PRINTF ("Ignoring Self reference to block: %ls\n", blockName);
        pEntity->close ();
        return  CantOpenObject;
        }

    SaveAndAddEntityToCellDefinition (cellHeader, lastChild, pEntity, context);

    pEntity->close();

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void            CreateSharedCellDefinition
(
EditElementHandleR          outElement,
AcDbBlockTableRecord*       pBlock,
ConvertToDgnContextR        context
) const
    {
    AcString        blockName;
    pBlock->getName (blockName);

    WChar         wideName[MAX_CELLNAME_LENGTH];
    RealDwgUtil::AcStringToMSWChar (wideName, blockName, MAX_CELLNAME_LENGTH);

    SharedCellDefHandler::CreateSharedCellDefElement (outElement, wideName, context.GetThreeD(), *context.GetModel());
    SharedCellDefHandler::SetAnonymous (outElement, pBlock->isAnonymous());
    SharedCellDefHandler::SetDimScaleOption (outElement, true);
    SharedCellDefHandler::SetMlineScaleOption (outElement, true);
    SharedCellDefHandler::SetDimRotationOption (outElement, true);

    // set description
    const ACHAR*    comments = nullptr;
    if (Acad::eOk == pBlock->comments(comments) && nullptr != comments && 0 != comments[0])
        SharedCellDefHandler::SetDescription (outElement, comments);

    MSElementP      element = outElement.GetElementP ();
    element->ehdr.uniqueId = context.ElementIdFromObject (pBlock);

    // Don't leave these floating... (dgncompare displays spurious errors).
    element->ehdr.level = 0;
    element->hdr.dhdr.symb.color = 0;
    element->hdr.dhdr.symb.weight = 0;
    element->hdr.dhdr.symb.style = 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void            SetIsAnnotation (EditElementHandleR outElement, AcDbBlockTableRecord* block)
    {
    bool        isAnnotative = false;

    AcDbAnnotativeObjectPE*     annotationPE = ACRX_PE_PTR (block, AcDbAnnotativeObjectPE);
    if (NULL != annotationPE)
        isAnnotative = annotationPE->annotative (block);

    SharedCellDefHandler::SetAnnotation (outElement, isAnnotative);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus   SetRangeDiagonal (EditElementHandleR outElement)
    {
    MSElementP  element = outElement.GetElementP ();

    DRange3d    range;
    DataConvert::ScanRangeToDRange3d (range, element->sharedCellDef.dhdr.range);

    if (range.low.x <= range.high.x || range.low.y <= range.high.y || range.low.z <= range.high.z)
        {
        element->sharedCellDef.rangeDiag.org.DifferenceOf (range.low, element->sharedCellDef.origin);
        element->sharedCellDef.rangeDiag.end.DifferenceOf (range.high, element->sharedCellDef.origin);
        return  RealDwgSuccess;
        }

    element->sharedCellDef.rangeDiag.org = element->sharedCellDef.rangeDiag.end = element->sharedCellDef.origin;
    return  CantSetRange;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/12
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus    SaveAndAddEntityToCellDefinition (MSElementDescrP cellHeader, MSElementDescrH lastChild, AcDbEntityP pEntity, ConvertToDgnContextR context)
    {
    int     cellType = cellHeader->el.ehdr.type;
    if (SHAREDCELL_DEF_ELM != cellType && CELL_HEADER_ELM != cellType)
        return  WrongMstnElementType;

    StatusInt           status = BSISUCCESS;
    RealDwgStatus       rdStat = RealDwgSuccess;
    EditElementHandle   childElement;
    if (!pEntity->isKindOf (AcDbAttributeDefinition::desc ()) &&     // Attributes within block handled seperately after.
        !pEntity->isKindOf (AcDbBlockBegin::desc ()) &&
        !pEntity->isKindOf (AcDbBlockEnd::desc ()) &&
        RealDwgSuccess == (rdStat = context.ElementFromObject (childElement, pEntity)) &&
        childElement.IsValid ())
        {
        MSElementDescrP childElmdscr = NULL;
        if (NULL == childElement.GetElementDescrCP ()->h.next)
            childElmdscr = childElement.ExtractElementDescr ();
        else
            childElement.GetElementDescrCP ()->Duplicate (&childElmdscr);

        if (NULL != childElmdscr)
            cellHeader->AppendChild (lastChild, childElmdscr);
        else
            status = BSIERROR;
        }
    else if (ConversionInChildrenProc == rdStat)
        context.NotifyConversionFailure (cellHeader);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

};  // ToDgnExtBlock

SharedCellData& ConvertToDgnContext::SharedCellDataFromBlock (AcDbBlockTableRecord* pBlock)
    {
    return _SharedCellDataFromBlock (pBlock);
    }

SharedCellData& ConvertToDgnContext::_SharedCellDataFromBlock (AcDbBlockTableRecord* pBlock)
    {
    BeAssert (false);
    static SharedCellData data (nullptr);
    return data;
    }

SharedCellData& ConvertToDgnMultiProcessingContext::_SharedCellDataFromBlock (AcDbBlockTableRecord* pBlock)
    {
    return m_sharedCellsCreation.AddSharedCell (pBlock);
    }

//Duplicate with part of ToDgnExtBlock::ToElement
class           ToDgnExtBlockMultiProcessing : public ToDgnExtBlock
{
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement1,
ConvertToDgnContextR    context
) const override
    {
    AcDbBlockTableRecord*   pBlock = AcDbBlockTableRecord::cast (acObject);
    bool hasVisibleChild = false;
    bool wasInSharedCellCreation = context.IsInSharedCellCreation ();

    // set the shared cell def creation mark if not already set(i.e. not creating a nested shared cell def):
    if (!wasInSharedCellCreation)
        context.SetInSharedCellCreation (true);

    // First go through and export any block definitions for inserts within this block.
    AcDbBlockTableRecordIterator*     pEntIter;
    pBlock->newIterator (pEntIter, true, true);
    for (; !pEntIter->done(); pEntIter->step())
        {
        AcDbEntity*         pChild;
        if (Acad::eOk != pEntIter->getEntity (pChild, AcDb::kForRead))
            continue;

        if (pChild->isKindOf (AcDbBlockReference::desc()))
            {
            AcDbObjectId    childBlockId = ((AcDbBlockReference*) pChild)->blockTableRecord();

            if (childBlockId != pBlock->objectId())             // avoid infinite recursion.
                context.SaveSharedCellDefinitionToDgn (childBlockId);
            }

        if (!hasVisibleChild && IsEntityVisible(pChild))
            hasVisibleChild = true;

        pChild->close();
        }
    delete pEntIter;

    SharedCellData& sharedCellData = context.SharedCellDataFromBlock (pBlock);
    outElement1.Invalidate ();
    EditElementHandleR outElement = sharedCellData.m_sharedCellEeh;

    CreateSharedCellDefinition (outElement, pBlock, context);

    SharedCellCmdParams& params = sharedCellData.m_params;
    params.hasVisibleChild = hasVisibleChild;
    params.wasInSharedCellCreation = wasInSharedCellCreation;

    params.untranslate.InitIdentity ();
    DVec3d  translation;
    context.GetTransformFromDGN().GetTranslation (translation);
    translation.Negate();
    params.untranslate.SetTranslation (translation);

    context.PushTransform (&params.currentTo, &params.currentFrom, &params.currentLocal, &params.untranslate);
    context.SetCurrentBlockId (pBlock->objectId());

    Transform currentTo = params.currentTo;
    Transform currentFrom = params.currentFrom;
    Transform currentLocal = params.currentLocal;

    /*---------------------------------------------------------------------------------------------------------
    We opt to use MSElementDescrP over ElementHandle for performance reason: directly appending a child element
    to the end of the element chain is a lot faster than walking through the entire chain just to get to the end 
    of it, as it currently does with SharedCellDefHandler::AddChildElement which calls MSElemenrDescr::AppendDescr 
    instead of AppendChild.
    ---------------------------------------------------------------------------------------------------------*/
    MSElementDescrP         cellHeader = outElement.GetElementDescrP ();
    params.lastChild = NULL;

    AcDbSortentsTable*      pSortentsTable = NULL;
    if (Acad::eOk == pBlock->getSortentsTable(pSortentsTable, AcDb::kForRead))
        {
        // create elements from entities in sorted order:
        AcDbObjectIdArray   entIdArray;

        if (Acad::eOk == pSortentsTable->getFullDrawOrder(entIdArray))
            {
            for (int i = 0; i < entIdArray.length(); i++)
                ElementFromEntity (cellHeader, &params.lastChild, entIdArray.at(i), pBlock, context);
            }

        pSortentsTable->close ();
        }
    else
        {
        // create elements from entities by file position:
        pBlock->newIterator (pEntIter, true, true);
        for (; !pEntIter->done(); pEntIter->step() )
            {
            AcDbObjectId    entityId;
            if (Acad::eOk == pEntIter->getEntityId(entityId))
                ElementFromEntity (cellHeader, &params.lastChild, entityId, pBlock, context);
            }
        delete pEntIter;
        }

    context.PopTransform (&currentTo, &currentFrom, &currentLocal);
    context.SetCurrentBlockId (AcDbObjectId::kNull);

    // clear the shared cell creation mark or let the parent do it if we are in a nested block - TFS 139496.
    if (!wasInSharedCellCreation)
        context.SetInSharedCellCreation (false);

    //Makes the parameterized test DwgFileOpenP/DwgFileOpenParameterized.OpenAllXml/ corresponding to lufterplan_isolated2_compressed.dwg pass (and many others)
    context.AddToElementIdsInUse (context.ElementIdFromObject (pBlock));

    return RealDwgSuccess;
    }
};

//Duplicate with part of ToDgnExtBlock::ToElement
RealDwgStatus SharedCellsCreationCmd::PreAddToDgnFile (SharedCellData& data)
    {
    auto& outElement = data.m_sharedCellEeh;
    auto pBlock = data.m_block;
    auto& params = data.m_params;

    if (pBlock->origin().x != 0.0 || pBlock->origin().y != 0.0 || pBlock->origin().z != 0.0)
        {
        DPoint3d            basePoint;
        Transform           baseTransform;

        RealDwgUtil::DPoint3dFromGePoint3d (basePoint, pBlock->origin());

        basePoint.Scale (-m_context.GetScaleToDGN());
        baseTransform.InitFrom (basePoint);

        TransformInfo       transInfo (baseTransform);
        for (ChildEditElemIter child(outElement); child.IsValid(); child = child.ToNext())
            child.GetHandler (MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (child, transInfo);
        }

    // process tags on block header: load a type 39 into cache, get a list of type 37's, append 37's as children:
    MSElementDescrP         tagsInSharedcellDef = NULL;
    bool                    tag1Invisible = false;
    if (m_context.GetSettings().AttributesAsTags() && RealDwgSuccess == m_context.SaveBlockAttrdefsToDgn(&tagsInSharedcellDef, pBlock) && NULL != tagsInSharedcellDef)
        {
        /*------------------------------------------------------------------------------------------------------
        An empty shared cell def with only invisible tags will produce invalid range which results in undesirable
        behavior.  Temporarily set the 1st tag visible to get a range, then reset it back.  We do not need to set 
        them all on because we do not care about a correct range.  We only need a range that won't fail otherwise 
        a valid operation such as a case in TFS 24431.
        ------------------------------------------------------------------------------------------------------*/
        tag1Invisible = NULL == params.lastChild && tagsInSharedcellDef->el.hdr.dhdr.props.b.invisible;
        if (tag1Invisible)
            tagsInSharedcellDef->el.hdr.dhdr.props.b.invisible = false;

        outElement.GetElementDescrP()->AppendChild (&params.lastChild, tagsInSharedcellDef);
        }

    m_context.PopTransform (&params.currentTo, &params.currentFrom, &params.currentLocal);
    m_context.SetCurrentBlockId (AcDbObjectId::kNull);

    // clear the shared cell creation mark or let the parent do it if we are in a nested block - TFS 139496.
    if (!params.wasInSharedCellCreation)
        m_context.SetInSharedCellCreation (false);

    if (BSISUCCESS != SharedCellDefHandler::AddChildComplete(outElement))
        return  MstnElementUnacceptable;

    // reset the 1st tag's visibility
    if (tag1Invisible)
        tagsInSharedcellDef->el.hdr.dhdr.props.b.invisible = true;

    // set isCellAnnotation
    ToDgnExtBlock::SetIsAnnotation (outElement, pBlock);
    // set rangDiagonal
    ToDgnExtBlock::SetRangeDiagonal (outElement);

    // shared def with all invisible children may have invalid range, save it for shared cell instance process:
    if (!params.hasVisibleChild && !ToDgnExtBlock::IsRangeValid(outElement))
        m_context.AddInvisibleBlock (pBlock->objectId());

    if (!m_context.GetSettings().AttributesAsTags())
        m_context.SaveBlockAttrdefsAsItemTypedefs (pBlock);

    return RealDwgSuccess;
    }

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtSharedCellDefinition : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/12
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsLessThan (ElementHandleCR tag1, ElementHandleCR tag2)
    {
    ElementId   key1 = TagElementHandler::GetSetDefinitionID(tag1) + tag1.GetElementCP()->attrElm.attrDefID;
    ElementId   key2 = TagElementHandler::GetSetDefinitionID(tag2) + tag2.GetElementCP()->attrElm.attrDefID;
    return  key1 < key2;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    WChar               cellName[MAX_CELLNAME_LENGTH] = { 0 };

    if (SUCCESS != CellUtil::ExtractName(cellName, _countof(cellName), elemHandle))
        return  WrongMstnElementType;
    bool                isAnonymous = CellUtil::IsAnonymous (elemHandle);
    if (RealDwgUtil::IsPointCellName(cellName, !context.SavingChanges() || !isAnonymous))
        return  RealDwgSuccess;
    DPoint3d            origin;
    if (SUCCESS != CellUtil::ExtractOrigin(origin, elemHandle))
        return  WrongMstnElementType;
    RotMatrix           matrix;
    if (SUCCESS != CellUtil::ExtractRotation(matrix, elemHandle))
        return  WrongMstnElementType;

    // if this block has been saved to database by dim style, don't try it again.
    if ((NULL == existingObject || !existingObject->objectId().isValid()) &&
        context.ExistingObjectIdFromElementId(elemHandle.GetElementId()).isValid())
        return  RealDwgIgnoreEntity;

    // use existing or create a new block table record:
    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbBlockTableRecord::desc());
    AcDbBlockTableRecord*   pBlock = AcDbBlockTableRecord::cast (acObject);
    if (NULL == pBlock)
        return  NullObject;

    AcString            blockName;
    pBlock->getName (blockName);

    if (0 == cellName[0] || (isAnonymous && !RealDwgUtil::IsExtrudedPointCellName(cellName)))
        {
        if ( blockName.isEmpty() || ('*' != *(blockName.kwszPtr())) )
            pBlock->setName (AcString ("*U"));
        }
    else
        {
        // still need to validate name, as a case shown in TR 315815.
        context.ValidateName (cellName);
        AcString        newName = AcString(cellName);
        if (newName != blockName)
            {
            context.DeduplicateBlockName (newName);
            pBlock->setName (newName.kwszPtr());
            }
        }

    // set block comments
    MSElementCP     elem = elemHandle.GetElementCP ();
    WChar           comments[MAX_CELLDSCR_LENGTH] = { 0 };
    if (nullptr != elem && BSISUCCESS == CellUtil::GetCellDescription(comments, sizeof(comments) - 1, *elem))
        pBlock->setComments (comments);

    if (context.GetSettings().SaveBlockUnitsFromFileUnits())
        pBlock->setBlockInsertUnits (RealDwgUtil::AcDbUnitsValueFromDgnUnits(context.GetStandardTargetUnits()));

    Transform           currentTo, currentFrom, currentLocal;
    context.ClearTransformTranslation (&currentTo, &currentFrom, &currentLocal);

    // Extract attributes.
    bvector<ElementHandle>  tags;
    for (ChildElemIter child(elemHandle); child.IsValid(); child = child.ToNext())
        {
        if (ATTRIBUTE_ELM == child.GetElementType())
            {
            // Copy the attribute to seperate chain so we can sort them and check them in correct order. (TR# 124267).
            tags.push_back (child);
            }
        }

    AcDbObjectId    objectId = pBlock->objectId ();
    // must add the block table record in order to add its children to database
    if (objectId.isNull())
        {
        AcDbBlockTablePointer   blockTable (context.GetDatabase()->blockTableId(), AcDb::kForWrite);
        if (Acad::eOk == blockTable.openStatus())
            objectId = context.AddBlockToBlockTable (blockTable, pBlock, elemHandle.GetElementId());
        }

    // set annotative from shared cell definition
    RealDwgUtil::SetObjectAnnotative (pBlock, CellUtil::IsAnnotation(elemHandle));

    // close this blockdef so it can be opened again for adding children at next steps.
    pBlock->close ();

    context.SaveBlockChildrenToDatabase (objectId, NULL, elemHandle, true);       // Last arg true  - ignore Attributes - they are checked in below.

    // Now sort the attribute chain (by set index) and check in - If we dont do the sorting that attributes
    // attached to the block inserts will be in a different order and AutoCAD will misalign the prompts (TR# 124267).
    std::sort (tags.begin(), tags.end(), IsLessThan);
    MSElementDescrP     pAttributes = NULL, pThisAttribute = NULL, pLastAttribute = NULL;
    for each (ElementHandleCR tag in tags)
        {
        pThisAttribute = MSElementDescr::Allocate (*tag.GetElementCP(), context.GetModel());
        MSElementDescr::InitOrAddToChainWithTail (&pAttributes, &pLastAttribute, pThisAttribute);
        }

    // save attribute definitions
    if (NULL != pAttributes)
        {
        ElementHandle   eeh (pAttributes, false, true);
        context.SaveBlockChildrenToDatabase (objectId, NULL, eeh, false);
        pAttributes->Release ();
        }

    context.PopTransform (&currentTo, &currentFrom, &currentLocal);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Katherine Wu    04/2021
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbBlockTableRecord*   CloneBlock (AcDbBlockTableRecord* pTemplateBlock, ConvertFromDgnContextR  context)
    {
    AcDbBlockTable*                 pBlockTable;
    if (Acad::eOk != context.GetDatabase()->getSymbolTable (pBlockTable, AcDb::kForWrite))
        {
        DIAGNOSTIC_PRINTF ("Failed clone block due to a failure of opening block table!\n");
        return NULL;
        }

    AcDbBlockTableRecord*           pBlockClone = new AcDbBlockTableRecord();

    // set block name before adding it as a database resident
    AcString    blockName;
    pTemplateBlock->getName (blockName);
    context.DeduplicateTableName (pBlockTable, blockName);
    pBlockClone->setName (blockName.kwszPtr());
    pBlockClone->setOrigin (pTemplateBlock->origin());

    if (context.GetSettings().SaveBlockUnitsFromFileUnits())
        pBlockClone->setBlockInsertUnits (RealDwgUtil::AcDbUnitsValueFromDgnUnits (context.GetStandardTargetUnits()));

    pBlockTable->add (pBlockClone);
    pBlockTable->close();

    // append child entities to block after the new block has become a database resident
    AcDbObjectIdArray                   childIdArray;
    AcDbBlockTableRecordIterator*       pEntIter;
    pTemplateBlock->newIterator (pEntIter, true, true);
    for (; !pEntIter->done(); pEntIter->step())
        {
        AcDbObjectId    entityId;
        if (Acad::eOk != pEntIter->getEntityId (entityId))
            continue;
        childIdArray.append(entityId);
        }
    delete  pEntIter;

    AcDbDatabase*                 pDatabase = context.GetDatabase();
    AcDbIdMapping*                pMapping = new AcDbIdMapping();
    pMapping->setDestDb (pDatabase);
    AcDbObjectId                  blockId = pBlockClone->objectId();
    pBlockClone->close();

    Acad::ErrorStatus errorStatus = pDatabase->deepCloneObjects (childIdArray, blockId, *pMapping);
    if (Acad::eOk != errorStatus)
        {
        DIAGNOSTIC_PRINTF("Failed cloning new block %ls for shared cell overrides\n", blockName.kwszPtr());
        delete pBlockClone;
        delete pMapping;
        return NULL;
        }

    delete pMapping;
    return pBlockClone;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Katherine Wu    04/2021
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbBlockTableRecord*   FindCloneBlock (AcDbBlockTableRecord* pTemplateBlock, int overrideMask, ConvertFromDgnContextR  context, bool& isCreated)
    {
    if (NULL == pTemplateBlock)
        return NULL;

    isCreated = false;

    T_BlockCloneBlockMap& cloneBlockMap = context.GetOverrideCloneBlockMap();

    AcDbObjectId templateBlockId = pTemplateBlock->objectId();
    if (cloneBlockMap.end() != cloneBlockMap.find (templateBlockId))
        {
        T_MaskBlockIdMap& maskBlockMap = cloneBlockMap[templateBlockId];
        if (maskBlockMap.end() != maskBlockMap.find (overrideMask))
            {
            AcDbBlockTableRecordPointer foundBlock (cloneBlockMap[templateBlockId][overrideMask], AcDb::kForWrite);
            return foundBlock;
            }
        }

    isCreated = true;
    AcDbBlockTableRecord* pCloneBlock = CloneBlock (pTemplateBlock, context);
    if (NULL != pTemplateBlock && !pCloneBlock->objectId().isNull())
        {
        cloneBlockMap[templateBlockId][overrideMask] = pCloneBlock->objectId();
        return pCloneBlock;
        }

    return NULL;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2004
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ToObjectPostProcess
(
ElementHandleR          inElement,
AcDbObjectP             acObject,
ConvertFromDgnContextR  context
) const
    {
    /*------------------------------------------------------------------------------------
    AutoCAD has no support for shared cell overrides.  If overrides exist we must simulate
    them by setting the block component properties to "byBlock".  In some cases, if the
    overrides are not uniform amongst all instances then this will necessitate making
    additional copies of the block definitions with the appropriate IDs.
    ------------------------------------------------------------------------------------*/
    AcDbBlockTableRecord*   pBlock = AcDbBlockTableRecord::cast (acObject);
    AcDbObjectIdArray       referenceIds;
    pBlock->getBlockReferenceIds (referenceIds);

    DgnModelP               model = context.GetModel ();
    int                     overridePresentMask = 0;
    int                     nReferences = referenceIds.length();
    int*                    pOverrides = (int*) _alloca (nReferences * sizeof (int));

    memset (pOverrides, 0, nReferences * sizeof (int));
    for (int iReference=0; iReference < nReferences; iReference++)
        {
        EditElementHandle   elemHandle(context.ElementIdFromObjectId (referenceIds[iReference]), model);
        if (elemHandle.IsValid() && SHARED_CELL_ELM == elemHandle.GetElementType())
            {
            MSElementP      element = elemHandle.GetElementP ();
            pOverrides[iReference] = SymbologyOverrideIndex (&((SharedCell*)element)->m_override);
            overridePresentMask |= (0x0001 << pOverrides[iReference]);
            }
        }

    if (1 != overridePresentMask)
        {
        AcDbDatabase*           pDatabase = context.GetDatabase();
        int                     nUniqueOverrides = 0;

        for (int j=0; j <= SHAREDCELL_OVERRIDE_All; j++)
            if (0 != (overridePresentMask & (1 << j)))
                nUniqueOverrides++;

        if (1 == nUniqueOverrides)
            {
            context.SetCurrentBlockId (pBlock->objectId());
            for (int j=0; j <= SHAREDCELL_OVERRIDE_All; j++)
                if (0 != (overridePresentMask & (1 << j)))
                    OverrideBlockChildren (pBlock, context, j);
            }
        else
            {
            for (int j=1; j <= SHAREDCELL_OVERRIDE_All; j++)
                {
                if (0 != (overridePresentMask & (1 << j)))
                    {
                    bool isCreated = false;
                    AcDbBlockTableRecord*           pBlockClone = FindCloneBlock (pBlock, j, context, isCreated);
                    if (NULL == pBlockClone)
                        continue;                    

                    if (isCreated)
                        OverrideBlockChildren (pBlockClone, context, j);

                    for (int iReference=0; iReference < nReferences; iReference++)
                        {
                        if (pOverrides[iReference] == j)
                            {
                            AcDbObject*             pObject = NULL;
                            AcDbBlockReference*     pReference = NULL;
                            if (Acad::eOk == acdbOpenObject (pObject, referenceIds[iReference], AcDb::kForWrite, false))
                                pReference = AcDbBlockReference::cast (pObject);

                            if (NULL != pReference)
                                pReference->setBlockTableRecord (pBlockClone->objectId());

                            if (NULL != pObject)
                                pObject->close();
                            }
                        }
                    pBlockClone->close ();
                    }
                }
            }
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2004
+---------------+---------------+---------------+---------------+---------------+------*/
static int                 SymbologyOverrideIndex
(
SCOverride*                 pOverride
)
    {
    int             index = 0;

    if (pOverride->level)
        index |= SHAREDCELL_OVERRIDE_Level;

    if (pOverride->color)
        index |= SHAREDCELL_OVERRIDE_Color;

    if (pOverride->style)
        index |= SHAREDCELL_OVERRIDE_Style;

    if (pOverride->weight)
        index |= SHAREDCELL_OVERRIDE_Weight;

    return index;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2004
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 OverrideBlockChildren
(
AcDbBlockTableRecord*       pBlock,
ConvertFromDgnContextR      context,
int                         overrideMask
)
    {
    AcDbDatabase*           pDatabase = context.GetDatabase();
    AcDbObjectId            layer0Id = pDatabase->layerZero();
    AcDbObjectId            byBlockLineTypeId = pDatabase->byBlockLinetype();
    AcCmColor               byBlockColor;
    byBlockColor.setByBlock(); //setColorMethod- deprecated in realdwg2021

    AcDbBlockTableRecordIterator*     pEntIter;
    pBlock->newIterator (pEntIter, true, true);
    for ( ; !pEntIter->done(); pEntIter->step() )
        {
        AcDbEntity*     pEntity;
        if (Acad::eOk != pEntIter->getEntity (pEntity, AcDb::kForWrite))
            continue;

        if (0 != (overrideMask & SHAREDCELL_OVERRIDE_Level))
            pEntity->setLayer (layer0Id, true);

        if (0 != (overrideMask & SHAREDCELL_OVERRIDE_Color))
            pEntity->setColor (byBlockColor);

        if (0 != (overrideMask & SHAREDCELL_OVERRIDE_Style))
            pEntity->setLinetype (byBlockLineTypeId, true);

        if (0 != (overrideMask & SHAREDCELL_OVERRIDE_Weight))
            pEntity->setLineWeight (AcDb::kLnWtByBlock, true);

        AcDbBlockReference* blockReference = AcDbBlockReference::cast (pEntity);
        if (NULL != blockReference)
            {
            // If the ancestry shared cell has overrides, it should broadcast the overrides to its descendants. 
            // By do this, the ancestry cell can't override its descendants directly, it should always refer 
            // to the descendant clone. Otherwise, the descendants cell instances won't be consistent with its
            // definition both to the existing and new create ones. We find the clone block cached in the context
            // first, redirect the ancestry cell to it if found and create a new clone if it's not found.(TFS#1110555)
            AcDbObjectId            blockId;
            if (!(blockId = blockReference->blockTableRecord()).isNull())
                {
                AcDbBlockTableRecordPointer acBlock (blockId, AcDb::kForWrite);
                if (Acad::eOk == acBlock.openStatus())
                    {
                    bool isCreated = false;
                    AcDbBlockTableRecord * pBlockClone = FindCloneBlock (acBlock, overrideMask, context, isCreated);
                    if (NULL == pBlockClone)
                        continue;

                    if (isCreated)
                        OverrideBlockChildren (pBlockClone, context, overrideMask);

                    blockReference->setBlockTableRecord (pBlockClone->objectId());
                    }
                }
            }


        pEntity->recordGraphicsModified (false);
        pEntity->close();
        }
    }

};  // ToDwgExtSharedCellDefinition


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::AddInvisibleBlock (AcDbObjectId const& blockId)
    {
    return  m_invisibleBlocks.append(blockId) >= 0 ? RealDwgSuccess : OutOfMemoryError;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertToDgnContext::IsInvisibleBlock (AcDbObjectId const& blockId)
    {
    int         foundAt = -1;
    if (m_invisibleBlocks.find(blockId, foundAt) && foundAt >= 0)
        return  true;
    return  false;
    }

void ConvertToDgnMultiProcessingContext::_SaveBlockIterAsSharedCells (AcDbBlockTableIterator* blockIter)
    {
    ConvertToDgnContext::_SaveBlockIterAsSharedCells (blockIter);

    if (!CurrentSatToPs ().WaitForFinish ())
        return;

    m_satFileProcessingMode = SatFileProcessingMode::ReadFromFile;

    //complete the shared cells
    for (PsChildrenOfSharedCell& psElm : m_psElemsInSharedCell)
        {
        auto found = m_satEntityMapping.find (BeFileName (BeFileName::GetFileNameWithoutExtension (psElm.m_fileName).c_str ()));
        if (found == m_satEntityMapping.end ())
            {
#ifdef REALDWG_DIAGNOSTICS
            ::wprintf (L"Failed to find the ACIS entity corresponding to file %ls]\n", found->first.c_str ());
#endif
            continue;
            }

        auto entityId = found->second;
        //m_curPSFileName = psElm.m_fileName;
        
        m_curPSFileName =
            BeFileName (BeFileName::GetDirectoryName (psElm.m_fileName.c_str ()).c_str ())
            .AppendToPath (BeFileName::GetFileNameWithoutExtension (psElm.m_fileName.c_str ()).c_str ())
            .AppendExtension (L"x_b");

        BeAssert (BeFileName::DoesPathExist (m_curPSFileName.c_str ()));
        
        AcDbEntity*         pEntity = nullptr;
        Acad::ErrorStatus   es = acdbOpenObject (pEntity, entityId, AcDb::kForRead);
        if (Acad::eOk != es)
            {
#ifdef REALDWG_DIAGNOSTICS
            printf ("Failed opening entity ID=%I64d. [%ls]\n", entityId.handle (), acadErrorStatusText (es));
#endif
            continue;
            }

        MSElementDescrP     lastChild = nullptr;
        if (RealDwgSuccess != ToDgnExtBlock::SaveAndAddEntityToCellDefinition (psElm.m_sharedCellHeader, &lastChild, pEntity, *this))
            DIAGNOSTIC_PRINTF ("Failed adding child element consisting of a ParaSolid body!\n");

        pEntity->close ();
        }

    //Add to the dgn file
    m_sharedCellsCreation.AddToDgnFile ();

    //Set to the next multiprocess conversion session
    m_satToPsChoice = &m_satToPsModelEntities;
    m_satFileProcessingMode = SatFileProcessingMode::WriteToFile;
    }

void ReplaceBlockExt1 (ToDgnExtBlock* blockExt)
    {
    AcRxDictionary* classDictionary = acrxClassDictionary;
    if (nullptr == classDictionary)
        return;

    AcRxClass*      dgnExtClass = AcRxClass::cast (classDictionary->at (L"ToDgnExtension"));
    if (nullptr == dgnExtClass)
        return;

    AcRxClass*      blockRef = AcDbBlockTableRecord::desc ();
    BeAssert (blockRef != nullptr);
    if (nullptr == blockRef)
        return;
    
    AcRxObject*     currentToDgnExt = blockRef->queryX (dgnExtClass);
    if (nullptr != currentToDgnExt)
        {
        // The host has registered an ToDgnExtension on AcDbBlockTableRecord, remove it:
        currentToDgnExt = blockRef->delX (dgnExtClass);
        delete currentToDgnExt;
        }

    blockRef->addX (dgnExtClass, blockExt);
    }

void ConvertToDgnMultiProcessingContext::ReplaceBlockExt ()
    {
    ReplaceBlockExt1 (new ToDgnExtBlockMultiProcessing ());
    }

ConvertToDgnMultiProcessingContext::~ConvertToDgnMultiProcessingContext ()
    {
    ReplaceBlockExt1 (new ToDgnExtBlock ());
    }

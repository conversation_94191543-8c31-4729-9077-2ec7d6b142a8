/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSatFileConversionMP.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

#include <Mstn\RealDWG\rSatToPs.h>

#include <PSolid\parasolid_kernel.h>
#include <PSolidAcisInterop\PSolidAcisInterop.h>

#include <fstream>

USING_NAMESPACE_BENTLEY_SQLITE
USING_NAMESPACE_BENTLEY_DGNPLATFORM

BEGIN_BENTLEY_NAMESPACE
namespace RealDwg {
BeFileName RootDir ()
    {
    BeFileName fol (_wgetenv (L"AppData"));
    fol.AppendToPath (L"Bentley").AppendToPath (L"MicroStation").AppendToPath (L"AcisToPS");

    BeFileName::CreateNewDirectory (fol.c_str ());
    return fol;
    }

BeGuid NewCachedGuid::Id ()
    {
    if (!m_guid.IsValid ())
        {
        BeSQLiteLib::Initialize (RootDir ());
        m_guid.Create ();
        }
    return m_guid;
    }

WStringAsGuid::WStringAsGuid (WStringCR str)
    :m_str (str)
    {}
BeGuid WStringAsGuid::Id ()
    {
    BeGuid id;
    id.FromString (Utf8String (m_str).c_str ());
    return id;
    }

SatDirectory::SatDirectory (IGuidPtr guid)
    :m_guid (guid)
    {}

IGuidPtr SatDirectory::Guid ()
    {
    return m_guid;
    }

BeFileName SatDirectory::SessionDir ()
    {
    BeFileName fol = RootDir ().AppendToPath (m_guid->IdAsWString ().c_str ());
    BeFileName::CreateNewDirectory (fol.c_str ());
    return fol;
    }

bvector<BeFileName> SatDirectory::FilesInSessionDir (std::function<bool (BeFileNameCR)> predicate)
    {
    BeFileListIterator beIt (SessionDir () + L"\\*", false);
    bvector<BeFileName> fileNames;
    BeFileName fileName;
    while (beIt.GetNextFileName (fileName) == SUCCESS)
        {
        if (predicate (fileName))
            fileNames.push_back (fileName);
        }

    return fileNames;
    }

bool SatDirectory::AnySatFile (BeFileNameR satFile)
    {
    auto files = FilesInSessionDir ([] (BeFileNameCR fileName)
        {
        return BeFileName::GetExtension (fileName.c_str ()).EqualsI (L"sat");
        });
    if (!files.empty ())
        {
        satFile = files[0];
        return true;
        }
    return false;
    }

bool SatDirectory::HasSatFiles ()
    {
    return 
        !FilesInSessionDir (
            [] (BeFileNameCR fileName)
            {
            return BeFileName::GetExtension (fileName.c_str ()).EqualsI (L"sat");
            })
        .empty ();
    }

bool SatDirectory::IsEndState ()
    {
    return
        !FilesInSessionDir (
            [] (BeFileNameCR fileName)
            {
            return BeFileName::GetFileNameAndExtension (fileName.c_str ()).EqualsI (L"EndFile.txt");
            })
        .empty ();
    }

HANDLE SatMutexChild::Open (WStringCR mutexName)
    {
    return OpenMutexW (
        MUTEX_ALL_ACCESS,            // request full access
        FALSE,                       // handle not inheritable
        mutexName.c_str ()
    );
    }
bool SatMutexChild::IsValid (HANDLE mutexH)
    {
    return mutexH != nullptr;
    }
DWORD SatMutexChild::WaitToAcquire (HANDLE mutexH)
    {
    return WaitForSingleObject (
        mutexH,    // handle to mutex
        INFINITE);   // no time-out interval
    }
bool SatMutexChild::ReleaseSatMutex (HANDLE mutexH)
    {
    return ReleaseMutex (mutexH);
    }
void SatMutexChild::Close(HANDLE mutexH)
    {
    CloseHandle (mutexH);
    }

BeFileName SatFileProcessing::ProcessingDir ()
        {
        BeFileName dir = m_satDir.SessionDir ().AppendToPath (L"Processing");
        BeFileName::CreateNewDirectory (dir.c_str ());
        return dir;
        }
BeFileName SatFileProcessing::MovedToProcessingDir (BeFileNameCR satFile)
    {
    BeFileName newFile = ProcessingDir ().AppendToPath (BeFileName::GetFileNameAndExtension (satFile.c_str ()).c_str ());
    if (BeFileNameStatus::Success != BeFileName::BeMoveFile (satFile, newFile))
        {
        m_log->Log (L"Error: failed to move sat file ", satFile.c_str (), L" to ", newFile.c_str ());
        return BeFileName (L"");
        }
    return newFile;
    }
void SatFileProcessing::FindFileToProcess ()
    {
    m_fileToProcess = BeFileName (L"");

    BeFileName satFile;
    if (!m_satDir.AnySatFile (satFile))
        return;

    m_fileToProcess = MovedToProcessingDir (satFile);

    if (BeFileName::DoesPathExist (m_fileToProcess.c_str ()))
        m_notifyToProcess = true;
    }
SatFileProcessing::SatFileProcessing (ILogPtr log, WStringCR guid, SatMutexChildPtr mutex)
    :m_appends (log), m_log (log), m_satDir (new WStringAsGuid (guid)), m_mutexName (guid), m_mutex (mutex)
    {}
bool SatFileProcessing::ProcessSatFile ()
    {
    BeFileName psFileName =
        m_satDir
        .SessionDir ()
        .AppendToPath (BeFileName::GetFileNameWithoutExtension (m_fileToProcess.c_str ()).c_str ())
        .AppendExtension (L"x_b");

    double convertScale = 1'000.0;

    PSolidAcisInterop::SATFileToXMTFile (m_fileToProcess.c_str (), psFileName.c_str (), &convertScale, nullptr);

    //file containing the convert scaling info
    BeFileName scaleFile (psFileName.c_str ());
    scaleFile.AppendExtension (L"txt");
    std::wofstream out;
    out.open (scaleFile.c_str ());
    out << convertScale << std::endl;
    out.close ();

    return true;
    }
bool SatFileProcessing::AppendToPathEnv ()
    {
    BeFileName acisDir = m_appends.CurrentDir ();
    acisDir.PopDir ();
    acisDir.AppendToPath (L"Acis");

    if (!BeFileName::DoesPathExist (acisDir.c_str ()))
        return true; //test environment

    return m_appends.AddToPath (acisDir);
    }
bool SatFileProcessing::Start ()
    {
    if (!AppendToPathEnv ())
        return false;

    PSolidAcisInterop::StartAcisSession ();
    return true;
    }
bool SatFileProcessing::Process ()
    {
    HANDLE satMutex = m_mutex->Open (m_mutexName);

    if (!m_mutex->IsValid (satMutex))
        {
        m_log->Log (L"Error: failed to open mutex");
        return false;
        }

    if (!Start ())
        return false;

    bool endState = false;

    do
        {
        DWORD dwWaitResult = m_mutex->WaitToAcquire (satMutex);

        switch (dwWaitResult)
            {
            // The thread got ownership of the mutex
            case WAIT_OBJECT_0:
                {
                FindFileToProcess ();

                // Release ownership of the mutex object
                if (!m_mutex->ReleaseSatMutex (satMutex))
                    {
                    m_log->Log (L"Error: failed to release mutex");
                    }

                if (m_notifyToProcess)
                    {
                    //MessageBoxW (nullptr, m_fileToProcess.c_str (), L"SAT file to process", MB_OK);
                    if (!ProcessSatFile ())
                        m_log->Log (L"Error: failed to process sat file ", m_fileToProcess.c_str ());
                    m_notifyToProcess = false;
                    }
                break;
                }

            // The thread got ownership of an abandoned mutex
            // The database is in an indeterminate state
            case WAIT_ABANDONED:
                {
                m_log->Log (L"Error: the thread got ownership of an abandoned mutex");
                return false;
                }
            }
        endState = m_satDir.IsEndState () && !m_satDir.HasSatFiles ();
        }
    while (!endState);

    CloseHandle (satMutex);

    return true;
    }

bool SatFileConversionMP::CreateUniqueMutex()
    {
    if (m_satMutex != nullptr)
        return true;

    if (nullptr == (m_satMutex = CreateMutexW (
        nullptr,
        FALSE,
        m_satDir->Guid ()->IdAsWString ().c_str ()
    )))
        return false;
    return true;
    }
SatFileConversionMP::SatFileConversionMP (int maxProcesses, SatDirectoryPtr satDir)
    :m_maxChildrenProcesses (maxProcesses), m_satDir (satDir)
    {
    BeAssert (m_maxChildrenProcesses >= 1);
    }
SatFileConversionMP::~SatFileConversionMP ()
    {
    //SignalEnd ();
    BeFileName::EmptyAndRemoveDirectory (m_satDir->SessionDir ().GetWCharCP());
    if (m_satMutex != nullptr)
        CloseHandle (m_satMutex);
    }

BeFileName SatFileConversionMP::NewAcisFileName (WCharCP origin)
    {
    BeFileName fileName (BeFileName::GetDirectoryName (origin).c_str ());
    fileName
        .AppendToPath ((BeFileName::GetFileNameWithoutExtension (origin).c_str () + std::to_wstring (m_satId++)).c_str ())
        .AppendExtension (L"sat");
    return fileName;
    }

void SatFileConversionMP::SetSatOutputStrategy (std::function<bool (WCharCP)> const& outputToSat)
    {
    m_outputToSat = outputToSat;
    }

BeFileName SatFileConversionMP::OutputToSatAsynch ()
    {
    //create the sat file in a temp directory
    BeFileName tempDir (m_satDir->SessionDir ().AppendToPath (L"temp"));
    BeFileName::CreateNewDirectory (tempDir.c_str ());

    BeFileName baseName ((L"satFile" + std::to_wstring (m_satId++)).c_str ());
    baseName.AppendExtension (L"sat");

    BeFileName satFileName (tempDir);
    satFileName.AppendToPath (baseName);

    if (!m_outputToSat (satFileName.c_str ()))
        return BeFileName (L"");

    //move it asynchronously to the directory to be processed by children processes
    if (!CreateUniqueMutex ())
        return BeFileName (L"");

    HANDLE satMutex = OpenMutexW (
        MUTEX_ALL_ACCESS,            // request full access
        FALSE,                       // handle not inheritable
        m_satDir->Guid ()->IdAsWString ().c_str ()
    );

    if (satMutex == nullptr)
        return BeFileName (L"");

    if (WAIT_OBJECT_0 != WaitForSingleObject (
        satMutex,    // handle to mutex
        INFINITE     // no time-out interval
    ))
        return BeFileName (L"");

    BeFileName destFileName = m_satDir->SessionDir ().AppendToPath (baseName.c_str ());
    bool res = BeFileNameStatus::Success == BeFileName::BeMoveFile (satFileName.c_str (), destFileName.c_str ());

    return ReleaseMutex (satMutex) && res ? destFileName : BeFileName (L"");
    }

BeFileName SatFileConversionMP::CurrentDir () const
    {
    HMODULE hModule = GetModuleHandleW (NULL);
    WCHAR path[MAX_PATH];
    GetModuleFileNameW (hModule, path, MAX_PATH);
    return BeFileName (BeFileName::GetDirectoryName (path).c_str ());
    }

BeFileName SatFileConversionMP::ExeFileName () const
    {
    BeFileName exeFileName =
        CurrentDir ()
        .AppendToPath (L"MdlSys")
        .AppendToPath (L"AsNeeded")
        .AppendToPath (L"Smartsolid")
        .AppendToPath (L"SatToPSProcessing")
        .AppendExtension (L"exe");
    
    if (BeFileName::DoesPathExist (exeFileName.c_str ()))
        return exeFileName;

    //Used in test environment
    exeFileName =
        CurrentDir ()
        .AppendToPath (L"SatToPSProcessing")
        .AppendExtension (L"exe");

    if (BeFileName::DoesPathExist (exeFileName.c_str ()))
        return exeFileName;
    return BeFileName (L"");
    }

WString SatFileConversionMP::CmdLine ()
    {
    return m_satDir->Guid ()->IdAsWString ();
    }

bool SatFileConversionMP::SpawnSatToPsChildProcess ()
    {
    // additional information
    PROCESS_INFORMATION pi;
    STARTUPINFOW si;

    // set the size of the structures
    ZeroMemory (&si, sizeof (si));
    si.cb = sizeof (si);
    ZeroMemory (&pi, sizeof (pi));

    WString cmdLine (L" " + CmdLine ());
    auto cmd = const_cast<LPWSTR> (cmdLine.c_str ());

    BOOL success =
        CreateProcessW (
            ExeFileName ().c_str (),
            cmd,
            NULL,                                           // Process handle not inheritable
            NULL,                                           // Thread handle not inheritable
            FALSE,                                          // Set handle inheritance to FALSE
            CREATE_SUSPENDED | CREATE_NO_WINDOW,            // The primary thread of the new process is created in a suspended state, and does not run until the ResumeThread function is called.
            NULL,                                           // Use parent's environment block
            NULL,                                           // Use parent's starting directory 
            &si,                                            // Pointer to STARTUPINFO structure
            &pi                                             // Pointer to PROCESS_INFORMATION structure (removed extra parentheses)
        );

    if (success != TRUE)
        return false;

    //We want the children processes to stop if the parent process is killed
    //cf https://blogs.msdn.microsoft.com/oldnewthing/20131209-00/?p=2433
    HANDLE hJob = CreateJobObjectW (nullptr, nullptr);

    JOBOBJECT_EXTENDED_LIMIT_INFORMATION info = { };
    info.BasicLimitInformation.LimitFlags =
        JOB_OBJECT_LIMIT_KILL_ON_JOB_CLOSE;

    SetInformationJobObject (
        hJob,
        JobObjectExtendedLimitInformation,
        &info,
        sizeof (info)
    );

    success = AssignProcessToJobObject (hJob, pi.hProcess);
    if (success == TRUE)
        {
        success = ResumeThread (pi.hThread) != (DWORD) -1;
        }
    if (success != TRUE)
        {
        TerminateProcess (pi.hProcess, 0);
        CloseHandle (pi.hProcess);
        CloseHandle (pi.hThread);
        return false;
        }

    m_pis.push_back ({ pi, hJob });
    return true;
    }

bool SatFileConversionMP::SpawnSatToPsChildrenProcesses ()
    {
    bool res = true;
    if (m_maxChildrenProcesses <= 0)
        return res;

    SYSTEM_INFO sysinfo;
    GetSystemInfo (&sysinfo);
    m_maxChildrenProcesses = min ((int) sysinfo.dwNumberOfProcessors, m_maxChildrenProcesses);

    while (--m_maxChildrenProcesses >= 0)
        res = SpawnSatToPsChildProcess () && res;
    return res;
    }

BeFileName SatFileConversionMP::EndFile ()
    {
    BeFileName sessionDir = m_satDir->SessionDir ();
    return sessionDir.AppendToPath (L"EndFile").AppendExtension (L"txt");
    }

void SatFileConversionMP::SignalEnd ()
    {
    BeFile myFile;
    myFile.Create (EndFile ().c_str ());
    }

bool SatFileConversionMP::WaitForFinish ()
    {
    //SignalEnd ();

    bool success = true;
    for (SpawnedInfo& spanwedInfo : m_pis)
        {
        auto& pi = spanwedInfo.m_pi;

        // Wait for the process to finish
        DWORD result = WaitForSingleObject (pi.hProcess, INFINITE);

        // Get and verify the exit code
        DWORD exitCode = -1;
        success = (TRUE == GetExitCodeProcess (pi.hProcess, &exitCode)) && (0 == exitCode) && success;

        // Close process and thread handles. 
        CloseHandle (pi.hProcess);
        CloseHandle (pi.hThread);

        CloseHandle (spanwedInfo.m_hJob);

        success = (WAIT_OBJECT_0 == result) && success;
        }

    CloseHandle (m_satMutex);
    m_satMutex = nullptr;

    return success;
    }

bvector<BeFileName> SatFileConversionMP::PsFileNames ()
    {
    auto psfilesfilter = [] (BeFileNameCR fileName) -> bool
        {
        return BeFileName::GetExtension (fileName.c_str ()).EqualsI (L"x_b");
        };

    return m_satDir->FilesInSessionDir (psfilesfilter);
    }

double SatFileConversionMP::ConvertScaleFromPsFile (BeFileName psFileName)
    {
    psFileName.AppendExtension (L"txt");

    double convertScale = -1.0;
    std::wifstream in;
    in.open (psFileName.c_str ());
    if (in.good ())
        in >> convertScale;
    in.close ();
    return convertScale;
    }

PathEnvAppend::PathEnvAppend (ILogPtr log)
    :m_log (log)
    {}
BeFileName PathEnvAppend::CurrentDir ()
    {
    HMODULE hModule = GetModuleHandleW (NULL);
    WCHAR path[MAX_PATH];
    GetModuleFileNameW (hModule, path, MAX_PATH);
    return BeFileName (BeFileName::GetDirectoryName (path).c_str ());
    }
bool PathEnvAppend::AddToPath (BeFileNameCR fileName)
    {
    if (!BeFileName::DoesPathExist (fileName.c_str ()))
        {
        m_log->Log (L"Appending a filename to the PATH environment variable failure, ", fileName.c_str (), L" doesn't exist.");
        return false;
        }

    //cf https://msdn.microsoft.com/en-us/library/tb2sfw2z.aspx?f=255&MSPPError=-2147217396
    wchar_t* pathvar;
    size_t requiredSize;

    _wgetenv_s (&requiredSize, NULL, 0, L"PATH");
    if (requiredSize == 0)
        return false;

    pathvar = (wchar_t*) malloc (requiredSize * sizeof (wchar_t));
    if (!pathvar)
        return false;

    // Get the value of the PATH environment variable.  
    _wgetenv_s (&requiredSize, pathvar, requiredSize, L"PATH");

    WString newPath (pathvar);
    newPath += WString (L";") + fileName;

    // Attempt to change path.
    _wputenv_s (L"PATH", newPath.c_str ());

    // Test that the new path contains the input folder
    //BeAssert (WString (_wgetenv (L"PATH")).ContainsI (fileName));

    free (pathvar);

    return true;
    }

UniqueLogFileName::UniqueLogFileName (WStringCR strGuid)
    :UniqueLogFileName (new WStringAsGuid (strGuid))
    {}
UniqueLogFileName::UniqueLogFileName (IGuidPtr guid)
    :m_satDir (guid)
    {}
BeFileName UniqueLogFileName::FileName ()
    {
    return m_satDir.SessionDir ().AppendToPath (m_guid.IdAsWString ().c_str ()).AppendExtension (L"txt");
    }

} // Ends RealDWG namespace
END_BENTLEY_NAMESPACE

/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdMText.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

struct DropMTextContext
    {
    MSElementDescrP         m_cellHeader;
    MSElementDescrP         m_lastChild;
    ConvertToDgnContext*    m_toDgnContext;
    LevelId                 m_level;

    DropMTextContext (MSElementDescrP cellHeaderIn, LevelId level, ConvertToDgnContext* contextIn) 
        { 
        m_cellHeader = cellHeaderIn;
        m_toDgnContext = contextIn;
        m_lastChild = NULL;
        m_level = level;
        }
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void     OverrideTextStyleFromMText (DgnTextStyleR dgnTextstyle, AcDbMText const* mtext)
    {
    // In ACAD, the Upsidedown flag on text style has no impact on MTexts.  It only impacts on DTexts.
    dgnTextstyle.SetProperty (TextStyle_Upsidedown, false);

    // When TTF is used, ACAD does not support backwards and vertical from text style
    DgnFontCP   font = dgnTextstyle.GetFont ();
    if (NULL != font && DgnFontType::TrueType == font->GetType())
        {
        dgnTextstyle.SetProperty (TextStyle_Backwards, false);
        dgnTextstyle.SetProperty (TextStyle_Vertical, false);
        return;
        }

    // Apply mtext's flow direction to backwards and vertical flags:
    switch (mtext->flowDirection())
        {
        case AcDbMText::kLtoR:
            dgnTextstyle.SetProperty (TextStyle_Backwards, false);
            dgnTextstyle.SetProperty (TextStyle_Vertical, false);
            break;

        case AcDbMText::kRtoL:
            dgnTextstyle.SetProperty (TextStyle_Backwards, true);
            dgnTextstyle.SetProperty (TextStyle_Vertical, false);
            break;

        case AcDbMText::kTtoB:
        case AcDbMText::kBtoT:
            dgnTextstyle.SetProperty (TextStyle_Backwards, false);
            dgnTextstyle.SetProperty (TextStyle_Vertical, true);
            break;

        // no changes for bystyle
        case AcDbMText::kByStyle:
        default:
            break;
        }
    }
    
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::ProcessMText
(
TextBlockR                  textBlock,
RotMatrixP                  pRotMatrix,
AcDbMText const*            pMText,
UInt32                      color,
bool*                       pFieldsPresent,
AcDbObjectCP                parentObject
)
    {
    ACHAR*      rawStringContent    = pMText->contents ();
    AcString    stringContent;
    AcDbField*  pField              = NULL;
    
    if (pMText->hasFields() && (Acad::eOk == pMText->getField (L"TEXT", pField, AcDb::kForRead)))
        {
        if (NULL != pFieldsPresent)
            *pFieldsPresent = true;
        
        AcString    acString = rawStringContent;
        this->GetFieldsString (stringContent, pField, acString);
        
        pField->close ();
        pField = NULL;
        }
    else
        {
        if (NULL != pFieldsPresent)
            *pFieldsPresent = false;
        
        stringContent = rawStringContent;
        }
    
    if (NULL != rawStringContent)
        acutDelString (rawStringContent);
    
    if (stringContent.isEmpty ())
        return EmptyText;
    
    // TFS 753830 - replace fractions that cause problems with ShxFont (do this after fields extraction):
    if (this->GetFileHolder().IsFileReadOnlyForPrinting())
        RealDwgUtil::ReplaceFractionMarkups (stringContent, pMText->textStyle());

    // TFS 1038453 - pMText->rotation() returns 0, but pMText->direction() makes an effective rotation. The DXF specs says that the last filed entry wins!
    double rotation = pMText->rotation ();
    auto direction = pMText->direction ();
    if (fabs(rotation) < MIN_AngleRadians && fabs(direction.x - 1.0) > TOLERANCE_VectorEqual)
        {
        // work on the mtext's plane
        AcGeMatrix3d    ecs;
        pMText->getEcs (ecs);
        direction.transformBy (ecs.transpose());
        // calc the rotation angle from the x-axis of the ecs to the direction of the mtext
        DVec3d  textDir;
        RealDwgUtil::DVec3dFromGeVector3d (textDir, direction);
        rotation = DVec3d::UnitX().AngleToXY (textDir);
        }

    TextSizeParam   textSizeParam;
    DPoint3d        userOrigin;
    RotMatrix       orientation;
    
    this->GetDgnTextTransformFromDwg (&textSizeParam, &userOrigin, &orientation, pMText->normal (), pMText->location (), rotation, pMText->textHeight (), 1.0);
    
    double                  heightFromStyle = 0.0;
    DPoint2d                textSize;
    textSize.Init (textSizeParam.size.width, textSizeParam.size.height);

    // get annotation scale
    double      annoScale = 1.0;
    bool        isAnnotative = this->GetDisplayedAnnotationScale (annoScale, NULL == parentObject ? pMText : parentObject);

    if ((NULL != parentObject) && (NULL != AcDbMLeader::cast(parentObject)))
        {
        /*
        NEEDWORK_VINIT - MLeader turns to non-annotative when its annotion scale is different from modelspace annotation scale.
        This condition is addessed in rdMultiLeader. The text from MLeader should be handled also.
        ToDgnExtMLeader* pToDgnExtMLeader = dynamic_cast <ToDgnExtMLeader*> ACRX_X_CALL(AcDbMLeader::cast(parentObject), ToDgnExtension);
        isAnnotative = pToDgnExtMLeader->IsAnnotative();
        */
        if (isAnnotative && fabs(this->GetModelspaceAnnotationScale() - annoScale) > TOLERANCE_ZeroScale)
            isAnnotative = false;
        }

    if (!isAnnotative)
        {
        /*---------------------------------------------------------------------------------------------------
        When the model annotation scale is on, the input TextBlock created by model has annotation scale set.
        Remove annotation scale before we populate text data into it, as otherwise TextBlock::SetProperties
        will effectively invert scale the text block.
        ---------------------------------------------------------------------------------------------------*/
        TextBlockProperties     noScaleProps = textBlock.GetProperties ();
        if (noScaleProps.HasAnnotationScale())
            {
            noScaleProps.ClearAnnotationScale ();
            textBlock.SetProperties (noScaleProps);
            }
        }

    TextBlockPropertiesPtr  tbProps;
    ParagraphPropertiesPtr  paraProps;
    RunPropertiesPtr        runProps;
    
    AcDbTextStyleTableRecordPointer dwgTextStyle    (pMText->textStyle (), AcDb::kForRead);
    DgnTextStylePtr                 dgnTextStyle;
    
    if ((Acad::eOk == dwgTextStyle.openStatus ()) && (dgnTextStyle = DgnTextStyle::GetByID (m_pFileHolder->GetTextStyleIndex ()->GetDgnId (pMText->textStyle ().handle ()), *m_dgnFile)).IsValid ())
        {
        textSize.x *= dwgTextStyle->xScale ();

        OverrideTextStyleFromMText (*dgnTextStyle, pMText);
        
        tbProps     = TextBlockProperties::Create (*dgnTextStyle, *m_model);
        paraProps   = ParagraphProperties::Create (*dgnTextStyle, *m_model);
        runProps    = RunProperties::Create (*dgnTextStyle, *m_model);
        }
    else
        {
        tbProps     = TextBlockProperties::Create (*m_model);
        paraProps   = ParagraphProperties::Create (*m_model);
        runProps    = RunProperties::Create (DgnFontManager::GetDefaultTrueTypeFont (), textSize, *m_model);

        heightFromStyle = Acad::eOk == dwgTextStyle.openStatus() ? dwgTextStyle->textSize() : 0.0;
        }

    // Only mark as overridden if zero or different.
    if ((0.0 == heightFromStyle) || !textSize.IsEqual (runProps->GetFontSize ()))
        runProps->SetFontSize (textSize);
    
    // set or remove annotation scale
    if (isAnnotative)
        tbProps->SetAnnotationScale (annoScale);
    else
        tbProps->ClearAnnotationScale ();
    
    // Always claim justification is overridden.
    paraProps->SetJustification (DgnJustificationFromMLTextAttachment (pMText->attachment ()));
    
    switch (pMText->lineSpacingStyle ())
        {
        case AcDb::kExactly:
            {
            double lsValue = pMText->lineSpacingFactor () * pMText->textHeight () * this->GetScaleToDGN () * ACAD_LINESPACING_EXACTLY_FACTOR;
            
            if (paraProps->GetLineSpacingType () != DgnLineSpacingType::ExactFromLineTop)
                paraProps->SetLineSpacingType (DgnLineSpacingType::ExactFromLineTop);
            
            if (paraProps->GetLineSpacingValue () != lsValue)
                paraProps->SetLineSpacingValue (lsValue);
            
            break;
            }
        
        case AcDb::kAtLeast:
            {
            double lsValue = pMText->lineSpacingFactor ();
            
            if (paraProps->GetLineSpacingType () != DgnLineSpacingType::AtLeast)
                paraProps->SetLineSpacingType (DgnLineSpacingType::AtLeast);
            
            if (paraProps->GetLineSpacingValue () != lsValue)
                paraProps->SetLineSpacingValue (lsValue);
            
            break;
            }
        }
    
    if (!runProps->HasColor () || (runProps->GetColor () != color))
        runProps->SetColor (color);
    
    AcCmColor dwgFillColor;
    if (pMText->backgroundFillOn () && (Acad::eOk == pMText->getBackgroundFillColor (dwgFillColor)))
        {
        if (!runProps->ShouldUseBackground ())
            runProps->SetShouldUseBackground (true);
        
        UInt32 dgnFillColor             = (pMText->useBackgroundColorOn () ? DWG_COLOR_Background255 : this->GetDgnColor (dwgFillColor));
        UInt32 existingDgnFillColor;    runProps->GetBackgroundStyle (&existingDgnFillColor, NULL, NULL, NULL, NULL);
       
        if (dgnFillColor != existingDgnFillColor)
            {
            if (RealDwgUtil::IsACADTextBackgrdTextFrameBehavior () && pMText->showBorders ()) //NEEDS_WORK Ashraf
                {
                Int32 lineType = 0;
                UInt32 lineWeight = 0;
            
                lineType = GetDgnStyle (pMText->linetypeId ());
                lineWeight = GetDgnWeight (pMText->lineWeight ());
                runProps->SetBackgroundStyle (&dgnFillColor, &dgnFillColor, &lineType, &lineWeight, NULL);
                }
            else
                runProps->SetBackgroundStyle (&dgnFillColor, &dgnFillColor, NULL, NULL, NULL);
            }

        
        double scale = 1.0;
        if (Acad::eOk == pMText->getBackgroundScaleFactor (scale))
            {
            double      paddingValue        = (textSize.y * fabs (scale - 1.0));
            DPoint2d    padding;            padding.Init (paddingValue, paddingValue);
            DPoint2d    existingPadding;    runProps->GetBackgroundStyle (NULL, NULL, NULL, NULL, &existingPadding);
            
            if (!existingPadding.IsEqual (padding))
                runProps->SetBackgroundStyle (NULL, NULL, NULL, NULL, &padding);
            }
        }
    
    // this is a vertical node if the mtext runs top-down or bottom up, or it can also go by textstyle:
    bool isVertical = AcDbMText::kTtoB == pMText->flowDirection() || AcDbMText::kBtoT == pMText->flowDirection();
    if (AcDbMText::kByStyle == pMText->flowDirection() && Acad::eOk == dwgTextStyle.openStatus())
        isVertical = Adesk::kTrue == dwgTextStyle->isVertical();
    
    if (tbProps->IsVertical () != isVertical)
        tbProps->SetIsVertical (isVertical);
    
    // set word wrap width
    double      wordWrapWidth = pMText->width() * this->GetScaleToDGN();
    if (DgnFontType::TrueType == runProps->GetFont().GetType() && wordWrapWidth > 2.0)
        wordWrapWidth += TEXT_WORDWRAP_ADJUST;

    /*-------------------------------------------------------------------------------------------------------
    Somehow the word wrap width appears to react to model annoation scale - when it is the same as the text's
    annotation, it works fine.  When model scale differs to text's scale, the effetive width changes by the
    the difference between the two scales, as shown in TFS 960793.
    -------------------------------------------------------------------------------------------------------*/
    if (isAnnotative && this->GetCurrentBlockId() == acdbSymUtil()->blockModelSpaceId(this->GetDatabase()))
        {
        double modelScale = this->GetModelspaceAnnotationScale ();
        if (fabs(modelScale) > TOLERANCE_ZeroScale && fabs(modelScale - annoScale) > TOLERANCE_ScaleRatio)
            wordWrapWidth *= modelScale / annoScale;
        }

    tbProps->SetDocumentWidth (wordWrapWidth);
    
    // TR 104269 & 115497: if the word wrap distance is less than 10% of the width of a character, set the word wrap distance to 0.0 (no word wrapping).
    if (tbProps->GetDocumentWidth () <= (textSize.y * 0.1))
        tbProps->SetDocumentWidth (0.0);

    textBlock.SetType (TEXTBLOCK_TYPE_DwgMText);
    textBlock.SetNodeNumber (m_pFileHolder->GetAndIncrementNextAvailableTextNodeID ());
    textBlock.SetOrientation (orientation);
    textBlock.SetUserOrigin (userOrigin);

    textBlock.SetProperties (*tbProps);
    textBlock.SetParagraphPropertiesForAdd (*paraProps);
    textBlock.SetRunPropertiesForAdd (*runProps);
    textBlock.SetTextNodeProperties (runProps.get ());

    DwgContextForTextBlock     contextForTextBlock (this, NULL, pMText);
    textBlock.FromMText ((WCharCP)stringContent.kwszPtr (), contextForTextBlock, this->GetScaleToDGN ());

    if (NULL != pRotMatrix)
        *pRotMatrix = orientation;

    textBlock.PerformLayout ();
    
    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertToDgnContext::GetTextSize
(
DRange3dR                   textRange,
size_t&                     numLines,
const AcDbEntity*           pEntity
)
    {
    StatusInt               status = BSIERROR;
    TextBlock               textBlock (*this->GetModel()->GetDgnModelP());

    const AcDbMText*        pMText;
    const AcDbText*         pText;
    if (NULL != (pMText = AcDbMText::cast (pEntity)))
        status = this->ProcessMText (textBlock, NULL, pMText);
    else if (NULL != (pText = AcDbText::cast (pEntity)))
        status = this->ProcessText (textBlock, nullptr, nullptr, nullptr, nullptr, nullptr, true, nullptr, pText);

    if (SUCCESS == status)
        {
        textRange = textBlock.GetNominalRange ();
        if (bsiDRange3d_isNull (&textRange))
            return BSIERROR;

        textRange.low.scale (&textRange.low, this->GetScaleFromDGN());
        textRange.high.scale (&textRange.high, this->GetScaleFromDGN());

        numLines = textBlock.GetLineCount (textBlock.Begin (), textBlock.End ());
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/03
+---------------+---------------+---------------+---------------+---------------+------*/
BentleyStatus               ConvertToDgnContext::CreateShapeElementByRange
(
EditElementHandleR          shapeElem,
DRange3dCR                  range,
double                      dimgap,
ElementHandleCR             nodeElem,
RotMatrixCR                 nodeMatrix,
AcDbMText const*            pMText
)
    {
    DPoint3d    points[5], origin;

    // set rectangle shape points, on text plane
    points[0].x = points[3].x = range.low.x  + dimgap;
    points[0].y = points[1].y = range.low.y  + dimgap;
    points[1].x = points[2].x = range.high.x - dimgap;
    points[3].y = points[2].y = range.high.y - dimgap;
    points[0].z = points[1].z = points[2].z = points[3].z = 0.0;
    points[4] = points[0];

    // we are going to move the rectangle to text location, so get the origin first:
    RealDwgUtil::DPoint3dFromGePoint3d (origin, pMText->location());
    this->GetTransformToDGN().Multiply (origin);
    // transform the origin to text plane
    if (!nodeMatrix.IsIdentity())
        nodeMatrix.MultiplyTranspose (origin);

    /*-------------------------------------------------------------------
    then move the origin to top-left point based on which the range was
    calculated.  the fact that the origin of the range being at top-left
    make x components to subtract and y component to add the text location:
    -------------------------------------------------------------------*/
    double  halfWidth  = 0.5 * (range.high.x - range.low.x);
    double  halfHeight = 0.5 * (range.high.y - range.low.y);
    switch (pMText->attachment())
        {
        case AcDbMText::kTopRight:
            origin.x -= halfWidth;
        case AcDbMText::kTopCenter:
            origin.x -= halfWidth;
            break;

        case AcDbMText::kMiddleRight:
            origin.x -= halfWidth;
        case AcDbMText::kMiddleCenter:
            origin.x -= halfWidth;
        case AcDbMText::kMiddleLeft:
            origin.y += halfHeight;
            break;

        case AcDbMText::kBottomRight:
            origin.x -= halfWidth;
        case AcDbMText::kBottomCenter:
            origin.x -= halfWidth;
        case AcDbMText::kBottomLeft:
            origin.y += 2.0 * halfHeight;
            break;

        default: // top-left attachment has 0 translation as that is the origin of the range
            break;
        }

    // now move the rectangle to text location
    for (int iPoint = 0; iPoint < 5; iPoint++)
        points[iPoint].add (&origin);

    // transform the rectangle to the world
    if (!nodeMatrix.isIdentity())
        {
        for (int iPoint = 0; iPoint < 5; iPoint++)
            nodeMatrix.multiply (&points[iPoint]);
        }

    return ShapeHandler::CreateShapeElement (shapeElem, &nodeElem, points, 5, this->GetThreeD(), *this->GetModel());
    }


/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtMText : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/04
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbObjectId             GetReactingLeader (const AcDbMText* pMText)
    {
    const AcDbVoidPtrArray*     reactors = pMText->reactors();
    int                         numReactors;
    AcDbObjectId                nullId;

    if ( (NULL == reactors) || (0 == (numReactors = reactors->length())) )
        return  nullId;

    for (int iReactor=0; iReactor < numReactors; iReactor++)
        {
        void *pSomething = reactors->at (iReactor);
        if (acdbIsPersistentReactor (pSomething))
            {
            AcDbObjectId persistentReactorId = acdbPersistentReactorObjectId (pSomething);
            AcDbObjectPointer<AcDbObject>   pPersistentObject (persistentReactorId, AcDb::kForRead);
            if (Acad::eOk == pPersistentObject.openStatus() && pPersistentObject->isKindOf(AcDbLeader::desc()))
                {
                return persistentReactorId;
                }
            }
        }
    return nullId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           09/02
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 AppendElementToNoteCell
(
MSElementDescrP             pCellElmdscr,
MSElementDescrH             ppLastChild,
EditElementHandleR          element,
AcDbLeader*                 pLeader,
ConvertToDgnContext&        context
)
    {
    if (NULL == pCellElmdscr)
        return;

    MSElementDescrP     pNewElmdscr = element.ExtractElementDescr ();

    // hook line or text box symbology should go with the leader object:
    if (pLeader->dimclrd().isByBlock())
        pNewElmdscr->el.hdr.dhdr.symb.color  = context.GetDgnColor (pLeader->colorIndex());
    else
        pNewElmdscr->el.hdr.dhdr.symb.color  = context.GetDgnColor (pLeader->dimclrd().colorIndex());

    if (AcDb::kLnWtByBlock == pLeader->dimlwd())
        pNewElmdscr->el.hdr.dhdr.symb.weight = context.GetDgnWeight (pLeader->lineWeight());
    else
        pNewElmdscr->el.hdr.dhdr.symb.weight = context.GetDgnWeight (pLeader->dimlwd());

    pNewElmdscr->el.ehdr.level = context.GetDgnLevel (pLeader->layerId());

    // assign a new element id
    pNewElmdscr->el.ehdr.uniqueId = context.GetAndIncrementNextId ();

    pCellElmdscr->AppendChild (ppLastChild, pNewElmdscr);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/02
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 BuildNoteCell
(
EditElementHandleR          outElement,
const AcDbMText*            pMText,
RotMatrixCR                 nodeMatrix,
TextBlock&                  textBlock,
ConvertToDgnContext&        context
)
    {
    AcDbObjectId    leaderId;
    if ((NULL == outElement.PeekElementDescrCP()) || (leaderId = GetReactingLeader(pMText)).isNull())
        return false;

    AcDbLeaderPointer   pLeader (leaderId, AcDb::kForRead);
    if (Acad::eOk != pLeader.openStatus())
        return false;

    // don't make the mtext a note cell if the leader is world drawn.
    AcDbObjectId    viewportId;
    bool            isDrawn = true, isChanged = false;
    if (context.NeedWorldDraw(viewportId, isDrawn, isChanged, pLeader))
        return  false;

    DgnModelP   model = context.GetModel ();

    // this mtext is found to have a leader reactor, wrap the text node into a cell.
    EditElementHandle   cellEh;
    NormalCellHeaderHandler::CreateOrphanCellElement (cellEh, NULL, context.GetThreeD(), *model);

    // add note linkage to cell header
    if (BSISUCCESS != mdlNote_addNoteCellLinkage(cellEh))
        {
        if (isChanged)
            RealDwgUtil::SetObjectAnnotative (pLeader, true);
        return false;
        }

    // set cell origin
    MSElementDescrP     cellElmdscr = cellEh.ExtractElementDescr ();
    MSElementDescrP     textElmdscr = outElement.ExtractElementDescr ();
    DPoint3d            origin;
    context.GetNoteCellOrigin (origin, pLeader);
    if (context.GetThreeD())
        {
        cellElmdscr->el.cell_3d.origin = origin;
        }
    else
        {
        cellElmdscr->el.cell_2d.origin.x = origin.x;
        cellElmdscr->el.cell_2d.origin.y = origin.y;
        }

    /*--------------------------------------------------------------------
    To match the leader's linkage ID, as well as to ensure round trip back
    to DWG to end up with a unique mtext entity, set the mtext's ID
    on the note cell element and set a new element ID on the converted
    text element:
    ---------------------------------------------------------------------*/
    cellElmdscr->el.ehdr.uniqueId = context.ElementIdFromObject (pMText);
    textElmdscr->el.ehdr.uniqueId = context.GetAndIncrementNextId ();

    // add this mtext into the new cell
    MSElementDescrP     lastChild = NULL;
    cellElmdscr->AppendChild (&lastChild, textElmdscr);

    DRange3d    range;
    double      dimgap   = pLeader->dimgap() * context.GetScaleToDGN();
    double      dimscale = pLeader->dimscale ();
    if (dimscale > 0.0)
        dimgap *= dimscale;

    double      width = context.GetLeaderBoxRange (range, dimgap, pLeader);
    if (width <= 0.0)
        {
        // leader entity does not have annotation size set, get it directly from mtext:
        range = textBlock.GetNominalRange ();
        if (!range.isNull())
            width = range.high.x - range.low.x;
        }

    // Add a hook line into the cell
    DSegment3d  lineSeg;
    if (SUCCESS == context.GetLeaderHookLine(&lineSeg.point[0], width, pLeader))
        {
        EditElementHandle   lineElm;
        if (BSISUCCESS == LineHandler::CreateLineElement(lineElm, NULL, lineSeg, context.GetThreeD(), *model))
            AppendElementToNoteCell (cellElmdscr, &lastChild, lineElm, pLeader, context);
        }

    // Add a bounding box around the text node
    if (dimgap < 0.0 && width > 0.0)
        {
        EditElementHandle   shapeElm;
        ElementHandle       templateElm(textElmdscr, false, false);
        if (SUCCESS == context.CreateShapeElementByRange (shapeElm, range, dimgap, templateElm, nodeMatrix, pMText))
            AppendElementToNoteCell (cellElmdscr, &lastChild, shapeElm, pLeader, context);
        }

    // add dimension dependency to cell
    ElementId   dimId = context.ElementIdFromObject (pLeader);
    cellElmdscr->h.dgnModelRef = model;
    mdlLinkage_setElementIdsUsingDescr (&cellElmdscr, &dimId, 1, DEPENDENCYAPPID_Note, DEPENDENCYAPPVALUE_Dimension, DEPENDENCY_ON_COPY_DeepCopyRootsAcrossFiles);

    outElement.SetElementDescr (cellElmdscr, true, false, model);

    NormalCellHeaderHandler::SetCellRange (outElement);

    if (isChanged)
        RealDwgUtil::SetObjectAnnotative (pLeader, true);

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsFontNameValid (AcString& fontName, bool& isShx, const ACHAR* candidateName)
    {
    if (NULL != candidateName && 0 != candidateName[0])
        {
        isShx = false;
        fontName.assign (candidateName);
        if (fontName.length() > 4)
            {
            AcString    extension = fontName.substr (fontName.length()-4, 4);
            isShx = 0 == extension.compareNoCase(L".shx");
            }

        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 GetFontNameFromTextFragment (AcString& fontName, const ACHAR* candidate1, const ACHAR* candidate2)
    {
    /*-----------------------------------------------------------------------------------
    Make our best guess what font thie piece of text uses.  AcDbMTextFragment suggests
    that "font" is for SHX and "fontname" is for TTF, but that is not true - I see an SHX
    font name in both!
    -----------------------------------------------------------------------------------*/
    bool        isShx = false;
    if (IsFontNameValid(fontName, isShx, candidate1) || IsFontNameValid(fontName, isShx, candidate2))
        return  isShx;

    // handle bad data - use default TTF:
    WString     defaultFontName;
    DgnFontManager::GetDefaultTrueTypeFont().GetFontFileName (defaultFontName);
    fontName.assign (defaultFontName.c_str());

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/10
+---------------+---------------+---------------+---------------+---------------+------*/
static int                  CreateTextElementFromFragment (AcDbMTextFragment *fragment, void *userData)
    {
    if (NULL == fragment || NULL == userData || NULL == fragment->text || 0 == fragment->text[0])
        return  1;

    DropMTextContext*       dropContext = (DropMTextContext*)userData;
    DgnModelP               modelRef = dropContext->m_toDgnContext->GetModel ();

    LangCodePage            codePage = LangCodePage::Unicode;
    TextParamWide           textParams;
    memset (&textParams, 0, sizeof textParams);

    AcString                fontName;
    bool                    isShx = GetFontNameFromTextFragment(fontName, fragment->font, fragment->fontname);
    if (isShx)
        {
        textParams.font = dropContext->m_toDgnContext->GetFileHolder().GetShapeFontId (fontName.kwszPtr(), modelRef);
        if (NULL != fragment->bigfont && 0 != fragment->bigfont[0])
            {
            textParams.shxBigFont = dropContext->m_toDgnContext->GetFileHolder().GetShapeFontId (fragment->bigfont, modelRef);
            if (!dropContext->m_toDgnContext->GetBigFontCodePage(codePage, textParams.shxBigFont))
                codePage = dropContext->m_toDgnContext->GetAnsiCodePage ();
            }
        }
    else
        {
        textParams.font = dropContext->m_toDgnContext->GetFileHolder().GetTrueTypeFontId (AcString(fragment->fontname), modelRef);
        }
    
    textParams.SetCodePage (codePage);

    DgnFontP                font = NULL, bigfont = NULL;
    font = DgnFontManager::GetDgnFontMapP(modelRef)->GetFontP (textParams.font);
    if (textParams.flags.shxBigFont)
        bigfont = DgnFontManager::GetDgnFontMapP(modelRef)->GetFontP (textParams.shxBigFont);


    textParams.exFlags.bold = fragment->bold;

    if (fabs(fragment->obliqueAngle) > MIN_AngleRadians)
        {
        textParams.flags.slant = true;
        textParams.slant = fragment->obliqueAngle;
        }
    else
        {
        textParams.flags.slant = fragment->italic;
        }

    textParams.just = TextElementJustification::LeftBaseline;
    textParams.overridesFromStyle.just  = true;
    textParams.overridesFromStyle.nodeJust = true;
    textParams.exFlags.styleOverrides = true;

    double                  width = fragment->capsHeight * fragment->widthFactor;
    DPoint3d                origin;
    RotMatrix               matrix;
    TextSizeParam           textSize;
    dropContext->m_toDgnContext->GetDgnTextSizeAndTransform (&textSize, &origin, &matrix, fragment->location, fragment->direction, fragment->normal, width, fragment->capsHeight);

    if (fragment->underlined)
        {
        textParams.flags.underline = true;
        textParams.underlineSpacing = IS_TRUETYPE_FONTNUMBER(textParams.font) ? TRUETYPE_UNDERLINEOFFSET * fabs(textSize.size.height) : SHX_UNDERLINEOFFSET * fabs(textSize.size.height);
        textParams.overridesFromStyle.underline = true;
        textParams.overridesFromStyle.underlineOffset = true;
        }
    if (fragment->overlined)
        {
        textParams.exFlags.overline = true;
        textParams.overlineSpacing = IS_TRUETYPE_FONTNUMBER(textParams.font) ? TRUETYPE_OVERLINEOFFSET * fabs(textSize.size.height) : SHX_OVERLINEOFFSET * fabs(textSize.size.height);
        textParams.overridesFromStyle.overline = true;
        textParams.overridesFromStyle.overlineOffset = true;
        }
    if (fabs(fragment->trackingFactor - 1.0) > TOLERANCE_ZeroScale)
        {
        textParams.exFlags.acadInterCharSpacing = true;
        textParams.flags.interCharSpacing = true;
        textParams.characterSpacing = fragment->trackingFactor;
        textParams.overridesFromStyle.interCharSpacing = true;
        textParams.overridesFromStyle.acadInterCharSpacing = true;
        }

    // create text element using TextBlock:
    TextBlock               textBlock (*modelRef->GetDgnModelP());
    TextBlockPropertiesPtr  textBlockProperties = textBlock.GetProperties().Clone ();
    if (textBlockProperties.IsValid())
        {
        textBlockProperties->ClearAnnotationScale ();
        textBlock.SetProperties (*textBlockProperties.get());
        }

    textBlock.SetTextElementOrigin (origin);
    textBlock.SetOrientation (matrix);

    RunProperties           runProperties (textParams, (DPoint2dCR)textSize.size, *modelRef);
    textBlock.SetRunPropertiesForAdd (runProperties);
    textBlock.AppendText (fragment->text);

    EditElementHandle       elemHandle;
    if (TEXTBLOCK_TO_ELEMENT_RESULT_Success == TextHandlerBase::CreateElement(elemHandle, NULL, textBlock))
        {
        // copy properties from cell header representing original mtext entity(we don't need extra packages like xdata etc):
        MSElementDescrP     elmdscr = elemHandle.ExtractElementDescr ();
        elmdscr->el.hdr.dhdr.symb.color = dropContext->m_toDgnContext->GetDgnColor (fragment->color);
        elmdscr->el.hdr.dhdr.symb.weight = dropContext->m_cellHeader->el.hdr.dhdr.symb.weight;
        elmdscr->el.hdr.dhdr.symb.style = dropContext->m_cellHeader->el.hdr.dhdr.symb.style;
        elmdscr->el.hdr.dhdr.props.b.invisible = dropContext->m_cellHeader->el.hdr.dhdr.props.b.invisible;
        elmdscr->el.ehdr.level = dropContext->m_level;

        dropContext->m_cellHeader->AppendChild (&dropContext->m_lastChild, elmdscr);
        }

    return  1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/10
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        DropMTextToFragments (AcDbMText* mtext, EditElementHandleR outElement, ConvertToDgnContextR context)
    {
    NormalCellHeaderHandler::CreateOrphanCellElement (outElement, NAME_ColumnText, context.GetThreeD(), *context.GetModel());
    context.ElementHeaderFromEntity (outElement, mtext);

    // if an annotative object is in modelspace, will create an annotative cell
    double  annoScale = 1.0;
    bool    isAnnotative = context.GetDisplayedAnnotationScale (annoScale, mtext);
    if (isAnnotative)
        AnnotationCellHeaderHandler::CreateFromNormalCell (outElement);

    MSElementDescrP         elmdscr = outElement.ExtractElementDescr ();
    DropMTextContext        dropContext(elmdscr, context.GetDgnLevel(mtext->layerId()), &context);
    mtext->explodeFragments (AcDbMTextEnum(CreateTextElementFromFragment), (void *)&dropContext);

    // don't save an empty text cell.
    if (NULL == elmdscr->h.firstElem)
        {
        elmdscr->Release ();
        return  DropFailed;
        }

    if (CELL_HEADER_ELM == elmdscr->el.ehdr.type)
        {
        DPoint3d            origin;
        RealDwgUtil::DPoint3dFromGePoint3d (origin, mtext->location());
        context.GetTransformToDGN().Multiply (origin);

        if (elmdscr->el.hdr.dhdr.props.b.is3d)
            elmdscr->el.cell_3d.origin = origin;
        else
            elmdscr->el.cell_2d.origin.Init (origin);
        }
        
    outElement.SetElementDescr (elmdscr, true, false);

    // set annotation scale but do not transform the cell as the dropped texts get scaled sizes:
    if (isAnnotative)
        AnnotationScale::SetAsXAttribute (outElement, &annoScale);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 CanDropColumnText (AcDbMText* pMText)
    {
    bool                    canDrop = false;
    AcDbMText::ColumnType   columnType = AcDbMText::kNoColumns;
    if (Acad::eOk == pMText->getColumnType(columnType) && AcDbMText::kNoColumns != columnType)
        {
        ACHAR*              string = pMText->contents ();
        if (NULL != string && 0 != *string)
            {
            // don't drop if it contains fields
            AcDbObjectId    fieldId;
            if (pMText->hasFields() && Acad::eOk == pMText->getField(L"TEXT", fieldId) && fieldId.isValid())
                return  false;
            
            const ACHAR*    markup = string;

            // don't drop if there is neither \px nor \N markup:
            if (NULL == wcsstr(markup, L"\\px") && NULL == wcsstr(markup, L"\\N"))
                return  false;

            // don't drop if found stack fractions \S...# and \S.../ both of which contain a line segment that does not go through the drop code:
            canDrop = true;
            while (NULL != markup && NULL != (markup = wcsstr(markup, L"\\S")))
                {
                markup += 2;
                while (NULL != markup && 0 != *markup && L'\\' != *markup && L';' != *markup)
                    {
                    if (L'#' == *markup || L'/' == *markup)
                        {
                        canDrop = false;
                        break;
                        }
                    markup++;
                    }
                if (!canDrop)
                    break;
                }
            }
        free (string);
        }

    return  canDrop;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbMText*          acMText = AcDbMText::cast (acObject);
    ColumnTextDropMode  dropColumnTexts = context.GetSettings().GetColumnTextDropMode ();

    // we do not currently support column texts - drop it
    if (DropColumnText_SeparateTexts == dropColumnTexts || (DropColumnText_Auto == dropColumnTexts && CanDropColumnText(acMText)))
        return  DropMTextToFragments (acMText, outElement, context);

    return  this->ConvertMTextToTextnode (outElement, acMText, context);
    }


public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertMTextToTextnode (EditElementHandleR outElement, AcDbMText* acMText, ConvertToDgnContextR context) const
    {
    bool            fieldsPresent   = false;
    UInt32          color           = context.GetDgnColor (acMText->entityColor());
    double          textHeight      = acMText->textHeight ();
    DgnModelP       model           = context.GetModel ();

    TextBlock       textBlock (*model->GetDgnModelP());

    RotMatrix       rMatrix;
    RealDwgStatus   status = context.ProcessMText (textBlock, &rMatrix, acMText, color, &fieldsPresent);
    if (RealDwgSuccess != status)
        return status;

    textBlock.SetForceTextNodeFlag (true);

    if (BSISUCCESS != TextHandlerBase::CreateElement(outElement, NULL, textBlock))
        return  CantCreateText;

    if (RealDwgUtil::IsACADTextBackgrdTextFrameBehavior () && acMText->showBorders ())
        TextNodeHandler::SetTextFrameFlag (outElement);


    /* set everything on the elements except the colour */
    int             mask = HEADERRESTOREMASK_Default & ~HEADERRESTOREMASK_Color;
    context.ElementHeaderFromEntity (outElement, acMText, mask);

    // if the mtext orients to paperspace viewports, set node element view independent
    if (context.IsOrientationMatchedToLayout (acMText))
        StrokeAnnotationElm::SetCanOrientRefElemToMatchSheetLayout (outElement);

    // below method relies on the pDocument to be processed above
    BuildNoteCell (outElement, acMText, rMatrix, textBlock, context);

    if (fieldsPresent)
        {
        context.AddPostProcessObject (acMText);
#if defined (REALDWG_DIAGNOSTICS_VERBOSE)
        RealDwgUtil::DumpField ("Original Field", acMText);
#endif
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContextR context) const override
    {
    // Should only get here if the text contained fields
    DgnModelP           model = context.GetModel ();
    EditElementHandle   originalElement(context.ElementIdFromObject(acObject), model);

    if (originalElement.IsValid())
        {
        bool                isNote = false;
        EditElementHandle   textElement;

        // If it is a cell header element, find its child text
        if (CELL_HEADER_ELM == originalElement.GetElementType())
            {
            isNote = mdlNote_isNote (originalElement);

            for (ChildEditElemIter child(originalElement, ExposeChildrenReason::Count); child.IsValid(); child.ToNext())
                {
                textElement.Duplicate (child);
                break;
                }
            }

        if (!textElement.IsValid())
            textElement.Duplicate (originalElement);

        // Convert fields from dwg to dgn format
        if (!TextField::ConvertFieldsFromDWG(textElement))
            return  CantCreateFields;

        // rewrite the text element to cache - if it is in a cell, must replace the whole cell:
        StatusInt       status = BSISUCCESS;
        ElementRefP     replaceRef = textElement.GetElementRef ();
        ElementRefP     parentRef = nullptr == replaceRef ? nullptr : replaceRef->GetOutermostParent ();
        if (nullptr != parentRef && (SHAREDCELL_DEF_ELM == parentRef->GetElementType() || CELL_HEADER_ELM == parentRef->GetElementType()))
            {
            EditElementHandle   cellElem (parentRef);
            status = RealDwgUtil::ReplaceChildElementInCell (cellElem, textElement);

            status = cellElem.ReplaceInModel (parentRef);
            }
        else
            {
            status = textElement.ReplaceInModel (replaceRef);
            }
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
        }

    return RealDwgSuccess;
    }

};  // ToDgnExtMText

/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnTexts.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp


/*---------------------------------------------------------------------------------**//**
* @bsimet<PERSON>d                                                    <PERSON>    06/03
+---------------+---------------+---------------+---------------+---------------+------*/
static int      GetParentElementType (MSElementDescrCP pDescr)
    {
    return (pDescr->el.ehdr.complex && NULL != pDescr->h.myHeader) ? pDescr->h.myHeader->el.ehdr.type : -1;
    }

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtTextElement : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*              GetTypeNeeded (ElementHandleR elemHandle, TextBlockCR textBlock, ConvertFromDgnContextR context) const
    {
    MSElementDescrCP    elmdscr = elemHandle.GetElementDescrCP ();
    ElementRefP         elementRef = elemHandle.GetElementRef ();
    bool                canCreateDText = textBlock.IsDTextCompatible ();
    int                 parentType = GetParentElementType (elmdscr);

    if ((CELL_HEADER_ELM == parentType ||
        (TEXT_NODE_ELM == parentType && CELL_HEADER_ELM == GetParentElementType(elmdscr->h.myHeader))) &&
        context.IsEnterDataField(&elmdscr->el) && canCreateDText)
        {
        return  AcDbAttributeDefinition::desc();
        }
    else if (context.IsTextShapeEntity(textBlock))
        {
        return  AcDbShape::desc();
        }
    else if (!canCreateDText && NULL != elementRef)
        {
        ElementRefP     parentRef = elementRef->GetParentElementRef ();
        bool            forceDText = parentType == -1 && NULL != parentRef && TEXT_NODE_ELM == parentRef->GetElementType();

        /* TR #112622 We can get into a situation where we are loosing elements on dropping text nodes to dtext. If the text element in the node cannot
           create valid dtext then this piece of code will try and create mtext out of it which we aleady know we cannot create. so force
           dtext creation here. An example of this is the TextRendering linkage attached to a text element which removes the left side
           bearing from the text when rendering. This is correctly invalid according to the canCreateDText method, however it should not stop
           us creating DText if we have to drop the text node. */

        if (!forceDText)
            return  AcDbMText::desc();
        }
    return  AcDbText::desc();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/10
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    MSElementCP         elem = elemHandle.GetElementCP ();
    if (elem->hdr.dhdr.props.b.invisible && NULL != linkage_extractFromElement(NULL, elem, PATTERN_ID, NULL, NULLFUNC, NULL))
        return  RealDwgIgnoreElement;

    TextBlock           textBlock (elemHandle);
    AcRxClass*          typeNeeded = GetTypeNeeded (elemHandle, textBlock, context);

    acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);

    // ATTDEF must precede DTEXT to avoid incorrect type inheritance
    AcDbAttributeDefinition*    acAttdef = AcDbAttributeDefinition::cast (acObject);
    if (NULL != acAttdef)
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (context.AttributeDefinitionFromElement(acAttdef, elemHandle));

    AcDbText*           acText = AcDbText::cast (acObject);
    if (NULL != acText)
        return context.DTextFromTextBlock (acText, textBlock, elemHandle);

    AcDbMText*          acMText = AcDbMText::cast (acObject);
    if (NULL != acMText)
        return context.MTextFromTextBlock (acMText, textBlock, elemHandle);

    AcDbShape*          acShape = AcDbShape::cast (acObject);
    if (NULL != acShape)
        return context.SetAcDbShapeFromTextElement (acShape, elemHandle);

    assert (false && L"A text element type not handled in ToObject!");
    return  CantCreateText;
    }

};  // ToDwgExtTextElement


#define AUTOCAD_HEIGHTANDDESCENDERFACTOR    1.2
#define AUTOCAD_HEIGHTANDLINEGAPFACTOR      (5.0 / 3.0) // While documented as "1.66", I empirically found 5/3 to be better (which is ~1.66 to begin with).

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater  02/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 GetDwgLineSpacingFactorAndStyleFromDgn
(
AcDb::LineSpacingStyle*     pStyle,
double*                     pLineSpacingFactor,
double                      dgnLineSpacing,
double                      annotationScale,
DgnLineSpacingType          dgnLineSpacingStyle,
double                      dgnHeight,
double                      scaleFromDGN,
bool                        iVertical
)
    {
    dgnLineSpacing *= (DgnLineSpacingType::AtLeast != dgnLineSpacingStyle) ? annotationScale : 1.0;

    if (dgnLineSpacing != 0.0)
        {
        switch (dgnLineSpacingStyle)
            {
            case DgnLineSpacingType::Automatic:
            case DgnLineSpacingType::Exact:
                {
                *pStyle = AcDb::kExactly;

                if (iVertical != 0)
                    {
                    if (dgnLineSpacing <= dgnHeight)
                        *pLineSpacingFactor = (dgnHeight * AUTOCAD_HEIGHTANDDESCENDERFACTOR) / (dgnHeight * AUTOCAD_HEIGHTANDLINEGAPFACTOR);
                    else if (dgnHeight > TOLERANCE_TextHeight)
                        *pLineSpacingFactor = dgnLineSpacing / (dgnHeight * 0.5);
                    else
                        *pLineSpacingFactor = 1.0;
                    }
                else
                    {
                    if (dgnLineSpacing < dgnHeight)
                        {
                        double offset = (dgnHeight * AUTOCAD_HEIGHTANDDESCENDERFACTOR) / (dgnHeight * AUTOCAD_HEIGHTANDLINEGAPFACTOR);
                        /* TR 83891 check to see that lines of text dont bump into each other 1.2== height(=1) + linespacing */
                        if (offset * AUTOCAD_HEIGHTANDDESCENDERFACTOR > dgnHeight)
                            *pLineSpacingFactor = offset;
                        else if (dgnHeight > TOLERANCE_TextHeight)
                            *pLineSpacingFactor = (1.2 + ((dgnLineSpacing /dgnHeight)-1) * 0.6);
                        else
                            *pLineSpacingFactor = 1.0;

                        }
                    else
                        {
                        if (dgnHeight > TOLERANCE_TextHeight)
                            *pLineSpacingFactor = (1.2 + ((dgnLineSpacing /dgnHeight)-1) * 0.6);
                        else
                            *pLineSpacingFactor = 1.0;
                        }
                    }
                }
                break;
            case DgnLineSpacingType::AtLeast:  // at least
                *pStyle = AcDb::kAtLeast;
                *pLineSpacingFactor = dgnLineSpacing;
                break;
            case DgnLineSpacingType::ExactFromLineTop: // exact
                *pStyle = AcDb::kExactly;
                if (dgnHeight < TOLERANCE_TextHeight)
                    *pLineSpacingFactor = 1.0;
                else
                    *pLineSpacingFactor = dgnLineSpacing / (dgnHeight * AUTOCAD_HEIGHTANDLINEGAPFACTOR);
                break;
            }
        }
    else
        {
        if (dgnHeight > TOLERANCE_TextHeight)
            *pLineSpacingFactor = (dgnHeight * AUTOCAD_HEIGHTANDDESCENDERFACTOR) / (dgnHeight * AUTOCAD_HEIGHTANDLINEGAPFACTOR);
        else
            *pLineSpacingFactor = 1.0;
        *pStyle = AcDb::kExactly;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DonFu           10/02
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SetNoteAttachmentPoint
(
AcDbMText*                  pMText,
RotMatrixCR                 matrix,
DPoint3dCR                  hookPoint,
ElementHandleCR             dimElement,
TextBlockR                  textBlock,
ConvertFromDgnContext&      context
)
    {
    int attachment = pMText->attachment ();

    // if attachment point is already middle-left or middle-right, don't bother.
    if (!(attachment == AcDbMText::kTopLeft ||
          attachment == AcDbMText::kBottomLeft ||
          attachment == AcDbMText::kTopRight ||
          attachment == AcDbMText::kBottomRight))
        return;

    // only do this for DGN's First Line Justification mode:
    if (dimElement.IsValid() && DIMSTYLE_VALUE_MLNote_VerAttachment_TopLine != RealDwgUtil::GetVerAttachmentFromNote(dimElement))
        return;

    int newLineAt = 0;

    // We may come back to handle multiline texts in the future.
    // For now don't bother if there are more than one line in text body.
    // A refined check will be done in the line arranger but this simple
    // pre-check may save unnecessary expense processing line arranger:
    ACHAR*      string = pMText->contents ();
    AcString    oString (string);
    if (NULL != string)
        acutDelString (string);
    while (!oString.isEmpty() && (newLineAt = oString.find(L"\\P")) > 0)
        {
        // if a valid new line mark exists, do not change the attachment:
        if (oString.kwszPtr()[newLineAt-1] != '\\')
            return;

        oString = oString.substr (newLineAt+2, -1);
        }

    if (newLineAt <= 0)
        {
        // now carry out full evaluation of text line count:
        textBlock.PerformLayout ();

        // if text auto-wraps, don't do anything
        if (textBlock.GetLineCount (textBlock.Begin (), textBlock.End ()) > 1)
            return;

        // get unrotated text width
        DRange3d    docRange = textBlock.GetNominalRange ();
        double      boxWidth = (docRange.high.x - docRange.low.x) * context.GetScaleFromDGN ();

        // transform origin and proposed attachment point to text plane:
        DPoint3d    origin, attachPoint = hookPoint;
        RealDwgUtil::DPoint3dFromGePoint3d (origin, pMText->location());
        matrix.multiplyTranspose (&origin);
        matrix.multiplyTranspose (&attachPoint);

        // check attachment point on the same side with the hook point?
        if (origin.x - attachPoint.x >= 0.0)
            {
            pMText->setAttachment (AcDbMText::kMiddleLeft);
            // shift text origin to the left side
            if (attachment == AcDbMText::kTopRight || attachment == AcDbMText::kBottomRight)
                origin.x -= boxWidth;
            }
        else
            {
            pMText->setAttachment (AcDbMText::kMiddleRight);
            // shift text origin to the right side
            if (attachment == AcDbMText::kTopLeft || attachment == AcDbMText::kBottomLeft)
                origin.x += boxWidth;
            }

        // shift text origin up/down to the middle of text, which is same as hook point's y coordinate
        origin.y = attachPoint.y;

        // reset origin to the new attachment point
        matrix.multiply (&origin);
        // if user wants to remove z-coordinate in DWG file, do so now:
        if (context.GetSettings().IsZeroZCoordinateEnforced())
            origin.z = 0.0;
        pMText->setLocation (RealDwgUtil::GePoint3dFromDPoint3d(origin));

        // unset line width
        pMText->setWidth (0);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/06
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 HasLevelColorOverride
(
LevelId                     level,
ConvertFromDgnContext&      context
)
    {
    if (context.UseLevelSymbologyOverrides())
        {
        bool         colorOverride = false, styleOverride = false, weightOverride = false;

        context.GetFileHolder().GetLayerIndex()->GetSymbologyOverrides (&colorOverride, &styleOverride, &weightOverride, level);

        return  0 != colorOverride;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Jeff.Marker     08/08
+---------------+---------------+---------------+---------------+---------------+------*/
static double               ComputeEffectiveDWGLineBreakLength (TextBlockCR textBlock)
    {
    // If we actually have a line break length, use it.
    double textBlockWordLineBreakLength = textBlock.GetProperties ().GetDocumentWidth ();
    if (0.0 != textBlockWordLineBreakLength)
        return textBlockWordLineBreakLength;

    // AutoCAD ignores leading indents of MText with a 0.0 line break length.
    //  If we do have indents, then artificially specify a line break length to make it look equivalent.
    //  Also note that if the TextBlock type is already MText, it already looks appripropriate (i.e. ignores leading indents).
    if (!textBlock.ContainsLeadingIndents () || textBlock.IsMTextType ())
        return 0.0;

    DRange3d textBlockNominalRange = textBlock.GetNominalRange ();
    double  wrappingWidth = textBlockNominalRange.high.x - textBlockNominalRange.low.x;

    // twice the width for word wrapping is too big - TFS 636980, 1.2 is more reasonable:
    wrappingWidth *= 1.2;

    return wrappingWidth;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      04/01
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbMText::AttachmentPoint  DwgAttachmentPointFromDgn (TextElementJustification just)
    {
    switch (just)
        {
        case TextElementJustification::LeftTop:
        case TextElementJustification::LeftMarginTop:
        case TextElementJustification::LeftCap:
        case TextElementJustification::LeftMarginCap:
            return  AcDbMText::kTopLeft;
        case TextElementJustification::CenterTop:
        case TextElementJustification::CenterCap:
            return  AcDbMText::kTopCenter;
        case TextElementJustification::RightTop:
        case TextElementJustification::RightMarginTop:
        case TextElementJustification::RightCap:
        case TextElementJustification::RightMarginCap:
            return  AcDbMText::kTopRight;
        case TextElementJustification::LeftMiddle:
        case TextElementJustification::LeftMarginMiddle:
            return  AcDbMText::kMiddleLeft;
        case TextElementJustification::CenterMiddle:
            return  AcDbMText::kMiddleCenter;
        case TextElementJustification::RightMiddle:
        case TextElementJustification::RightMarginMiddle:
            return  AcDbMText::kMiddleRight;
        case TextElementJustification::LeftBaseline:
        case TextElementJustification::LeftMarginBaseline:
        case TextElementJustification::LeftDescender:
        case TextElementJustification::LeftMarginDescender:
            return  AcDbMText::kBottomLeft;
        case TextElementJustification::CenterBaseline:
        case TextElementJustification::CenterDescender:
            return  AcDbMText::kBottomCenter;
        case TextElementJustification::RightBaseline:
        case TextElementJustification::RightMarginBaseline:
        case TextElementJustification::RightDescender:
        case TextElementJustification::RightMarginDescender:
            return  AcDbMText::kBottomRight;
        }
    assert (true);  // should never come here
    return  AcDbMText::kTopLeft;
    }



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          12/10
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtTextNode : public ToDwgExtension
{
    mutable ElementRefP         m_targetElementRef;

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleCR inElement, AcDbObjectP existingObject, TextBlockCR textBlock, ConvertFromDgnContextR context) const
    {
    if (NULL != existingObject && existingObject->isKindOf(AcDbAttribute::desc()))
        return  AcDbAttribute::desc ();

    /*-----------------------------------------------------------------------------------
    Extract the assoc point we originally created on the textnode from a multiline attribute 
    and round trip it back as an attribute.  This textnode must have been created when the
    option "Attributes As Tags" was turned on.  Text nodes created for item types should
    be caught by IsItemTypeFieldOnCell and processed via the shared cell element.
    -----------------------------------------------------------------------------------*/
    AssocPoint  assocPoint;
    AssociativePoint::InitOrigin (assocPoint, NULL);

    if (BSISUCCESS == AssociativePoint::ExtractPoint(assocPoint, inElement, 0, 1) &&
        BSISUCCESS == AssociativePoint::GetRoot(&m_targetElementRef, NULL, NULL, NULL, assocPoint, inElement.GetModelRef(), 0) &&
        SHARED_CELL_ELM == m_targetElementRef->GetElementType())
        return  AcDbAttribute::desc ();

    return  AcDbMText::desc ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsItemTypeFieldOnCell (TextBlockCR textBlock, ConvertFromDgnContextR context) const
    {
    // only the whole string as a field
    TextFieldPtr    field = textBlock.GetField (textBlock.Begin());
    if (field.IsValid() && field->GetHandler().GetFieldType() == TextField::FieldType_Element)
        {
        // is target a cell or shared cell element?
        DgnElementECInstanceCP  elmInstance = nullptr;
        DgnECInstancePtr        ecInstance = field->FindTarget ();
        if (ecInstance.IsValid() && nullptr != (elmInstance = ecInstance->GetAsElementInstance()))
            {
            ElementHandleCR     eh = elmInstance->GetElementHandle ();
            MSElementTypes      targetType = (MSElementTypes)eh.GetElementType ();
            if (SHARED_CELL_ELM == targetType || CELL_HEADER_ELM == targetType)
                {
                // is the EC intance of DWG attribute item type schema?
                ItemTypeLibraryPtr  itemtypeLibrary = context.GetFileHolder().FindBySchemaNameWithMap ( ecInstance->GetClass().GetSchema().GetName().GetWCharCP(), *context.GetFile() );

                if (itemtypeLibrary.IsValid())
                    {
                    /*-----------------------------------------------------------------------------------
                    When saving changes, save target block reference ID so it will be processed together.
                    This is not necessary for saving DGN to DWG as all cells will be processed for sure.
                    -----------------------------------------------------------------------------------*/
                    if (context.SavingChanges())
                        {
                        context.SetItemTypeTarget (eh.GetElementId(), false);
                        m_targetElementRef = eh.GetElementRef ();
                        }
                    return  true;
                    }
                }
            }
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus       ToObject
(
ElementHandleR              elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&                acObject,           // The object created, if any.
AcDbObjectP                 existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR      context             // The context
) const override

    {
    m_targetElementRef = nullptr;

    TextBlock               textBlock(elemHandle);
    if (this->IsItemTypeFieldOnCell(textBlock, context))
        return DwgObjectChangesIgnored;

    AcRxClass*              typeNeeded = GetTypeNeeded (elemHandle, existingObject, textBlock, context);

    acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);
    if (NULL == typeNeeded)
        return DwgObjectChangesIgnored;

    // in most cases, we'd get an mtext
    AcDbMText*              acMText = AcDbMText::cast (acObject);
    if (NULL != acMText)
        return this->SetAcDbMTextFromTextnode (acMText, elemHandle, textBlock, true, context);

    // we have converted multiline attribute to textnode, now convert it back to multiline attribute
    AcDbAttribute*          acAttribute = AcDbAttribute::cast (acObject);
    if (NULL != acAttribute)
        return  this->SetAcDbAttributeFromTextnode (acObject, elemHandle, textBlock, context);

    assert (false);
    return NoConversionMethod;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbMTextFromTextnode (AcDbMText* acMText, ElementHandleCR inElement, TextBlockR textBlock, bool checkIn, ConvertFromDgnContextR context) const
    {
    if (textBlock.IsEmpty())
        return EmptyText;

    if (!textBlock.IsMTextCompatible(inElement.GetModelRef()))
        return IncompatibleText;

    RealDwgStatus   status = context.MTextFromTextBlock (acMText, textBlock, inElement, checkIn);

    // do not let DD destroy our leader data by re-evaluating leader during releasing the smart pointer
    acMText->recordGraphicsModified (false);

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbAttributeFromTextnode (AcDbObjectP& acObject, ElementHandleR inElement, TextBlockR textBlock, ConvertFromDgnContextR context) const
    {
    AcDbMText*      mtext = new AcDbMText ();
    RealDwgStatus   status = this->SetAcDbMTextFromTextnode (mtext, inElement, textBlock, false, context);

    if (RealDwgSuccess == status)
        {
        AcDbAttribute*      attribute = AcDbAttribute::cast (acObject);
        WString             attributeTag;
        if (BSISUCCESS != LinkageUtil::ExtractNamedStringLinkageByIndex(attributeTag, STRING_LINKAGE_KEY_Name, 0, inElement.GetElementCP()))
            {
            attributeTag.assign (attribute->tagConst());
            if (attributeTag.empty())
                attributeTag.assign (L"__");
            }

        Acad::ErrorStatus   errorStatus = attribute->setTag (attributeTag.c_str());
        
        if (textBlock.IsDTextCompatible())
            status = context.DTextFromTextBlock (attribute, textBlock, inElement);
        else
            status = ConvertItemTypeToAttribute::SetAcDbAttributeOrDropMText (attribute, mtext, context);

        if (ReplacedObjectType == status)
            {
            // failed converting textnode to attribute - drop to mtext
            acObject = mtext;
            return  status;
            }
        else if (RealDwgSuccess == status && !attribute->objectId().isValid())
            {
            AcDbObjectId                insertId;
            AcDbBlockReferencePointer   acInsert;
            if (nullptr == m_targetElementRef || !(insertId = context.ExistingObjectIdFromElementId(m_targetElementRef->GetElementId())).isValid() || Acad::eOk != acInsert.open(insertId, AcDb::kForWrite))
                {
                // target block reference not exists - drop to mtext
                acObject = mtext;
                return  ReplacedObjectType;
                }

            context.AddAttributeToBlockReference (acInsert, attribute, inElement.GetElementId());
            }
        }

    if (mtext->objectId().isValid())
        mtext->close ();
    else
        delete mtext;

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/03
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ToObjectPostProcess
(
ElementHandleR              elemHandle,
AcDbObjectP                 acObject,
ConvertFromDgnContextR      context
) const
    {
    if (TextField::ElementContainsFields(elemHandle))
        {
        TextBlock           textBlock (elemHandle);

        if (!textBlock.IsEmpty ())
            {
            AcDbMText*              pMText = AcDbMText::cast (acObject);
            Bentley::WString        markup;
            DwgContextForTextBlock  mapper (NULL, &context, AcDbEntity::cast (acObject));
            double                  scale = 1.0 / context.GetScaleFromDGN();
            bool                    convertEdf = context.GetSettings().ConvertEmptyEDFToSpace();

            textBlock.ConvertToMTextCompatibleTextBlock ();
            textBlock.ToMText (markup, elemHandle.GetModelRef(), &mapper, scale, true, convertEdf);

            AcString                acString(markup.c_str());
            context.ExtractFields (acObject, pMText->contents(), acString);

            if (!pMText->hasFields())
                {
                textBlock.ToMText (markup, elemHandle.GetModelRef(), &mapper, scale, false, convertEdf);
                pMText->setContents (markup.c_str());
                }
            }
        }

    return  RealDwgSuccess;
    }


};  // ToDwgExtTextNode



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          08/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtNoteCell : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    /*-----------------------------------------------------------------------------------
    The strategy to save a note element to DWG is as follows:

    1) A note that is properly attached to a dimension leader is always handled by the note
        handler, therefore this ToObject method is called.  Such a note consists of a note
        cell element and one or more leader dimensions, which are handled accordingly:
        a) The note cell has more than one dependent dimension roots: save it to multileader entity.
        b) There is only one dependent dimension root: save it to traditional mtext+leader entities.
    2) A widowed note cell not attached to a dimension is handled by type 2 cell handler.
        It will be saved by cell handler's ToObject method.
    3) A widowed leader dimension without a note cell attached is handled by dimension handler.
        It will be saved by dimension handler's ToObject method.

    Since we cover all scenarios properly as listed above, we no longer have to post processing
    mtext nor dimension.  In other words, if they are paired, they ought to be taken care
    of by this method.
    -----------------------------------------------------------------------------------*/
    NoteCellHeaderHandler*  noteHandler = dynamic_cast <NoteCellHeaderHandler*> (&elemHandle.GetHandler());
    if (NULL == noteHandler)
        return  BadElementHandler;

    // find all dimension dependents on the note cell
    ElementAgenda           dimensionArray;
    if (RealDwgUtil::GetLeaderDimensionsFromNote(&dimensionArray, NULL, elemHandle) < 1)
        return this->DropNoteCellToComponents (elemHandle, existingObject, context);

    // if there is only one dimension leader attached to the note, save it as mtext + single leader
    if (1 == dimensionArray.size() && (NULL == existingObject || !existingObject->isKindOf(AcDbMLeader::desc())))
        return  this->SaveNoteAsMTextAndLeader (acObject, existingObject, elemHandle, dimensionArray.front(), context);

    // we have all dimensions attached to the note, directly create a DWG multileader from them
    ConvertMultileaderFromDgnNote   mleaderCreator(elemHandle, context);
    return mleaderCreator.CreateMLeaderFromNote (acObject, existingObject, dimensionArray);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SaveNoteAsMTextAndLeader
(
AcDbObjectP&            acObject,
AcDbObjectP             existingObject,
ElementHandleCR         noteCell,
ElementHandleCR         dimElement,
ConvertFromDgnContextR  context
) const
    {
    // get note cell element and its handler
    NoteCellHeaderHandler*  noteHandler = dynamic_cast <NoteCellHeaderHandler*> (&noteCell.GetHandler());
    if (NULL == noteHandler)
        return  BadElementHandler;

    // get the text component
    TextBlockPtr    textBlock = noteHandler->GetTextPart (noteCell, ITextPartId());
    if (textBlock.IsNull())
        return  MstnElementUnacceptable;

    if (textBlock->IsEmpty())
        return EmptyText;

    if (!textBlock->IsMTextCompatible(noteCell.GetModelRef()))
        return  IncompatibleText;

    // create mtext
    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbMText::desc());
    AcDbMText*      acMText = AcDbMText::cast (acObject);
    if (NULL == acMText)
        return  CantCreateText;

    // try creating an mtext from the text block:
    RealDwgStatus   status = context.MTextFromTextBlock (acMText, *textBlock.get(), noteCell);
    if (RealDwgSuccess != status)
        return  status;

    // get the dimension component - if not found, keep the mtext just created
    if (!dimElement.IsValid())
        return  CantAccessMstnElement;

    DimensionElmCP  dim = (DimensionElmCP) dimElement.GetElementCP();
    if (NULL == dim || dim->nPoints < 1)
        return  MstnElementUnacceptable;

    ElementId       dimElemId = dim->ehdr.uniqueId;
    DPoint3d        hookPoint = dim->GetPoint(dim->nPoints - 1);
    context.GetTransformFromDGN().Multiply (hookPoint);

    /*-------------------------------------------------------------------------------
    If a text note element contains only one line of text, we can change the mtext
    attachment point to the middle left/right justification.  We can't do this with
    more than one lines of text though.
    -------------------------------------------------------------------------------*/
    SetNoteAttachmentPoint (acMText, textBlock->GetOrientation(), hookPoint, dimElement, *textBlock.get(), context);

    // We got here because the note has a valid dimension attached. Now save the mtext in preparation for leader creation.
    AcDbObjectId        mtextObjId = acMText->objectId ();
    if (!mtextObjId.isValid())
        mtextObjId = context.AddEntityToCurrentBlock (acMText, noteCell.GetElementCP()->ehdr.uniqueId);

    // close mtext object now to allow it to be opened again in leader creation code
    Acad::ErrorStatus   es = acMText->close ();

    // if there already exists a leader, open it now.
    AcDbObjectId        leaderObjId = context.ExistingObjectIdFromElementId (dimElemId);
    AcDbLeader*         existingLeader = NULL;
    if (!leaderObjId.isValid() || Acad::eOk != acdbOpenObject(existingLeader, leaderObjId, AcDb::kForWrite))
        existingLeader = NULL;

    // create or use existing leader object
    AcDbObjectP         object = context.InstantiateOrUseExistingObject (existingLeader, AcDbLeader::desc());
    AcDbLeader*         acLeader = AcDbLeader::cast (object);
    if (NULL == acLeader)
        return  CantCreateLeader;

    // if new, save it to database or else AcDbLeader::attachAnnotation will fail
    leaderObjId = acLeader->objectId ();
    if (!leaderObjId.isValid())
        context.AddEntityToCurrentBlock ((AcDbEntity*)acLeader, dimElemId);

    // convert leader dimension to DWG leader object
    ConvertLeaderFromDgnDimension   leaderFromDgn(dimElement, &noteCell, context);
    status = leaderFromDgn.SetLeaderFromNoteDimension (acLeader);

    // re-open mtext to add the leader reactor as well as for the caller to set entity header
    if (Acad::eOk != acdbOpenObject(acMText, mtextObjId, AcDb::kForWrite))
        status = CantOpenObject;

    leaderObjId = acLeader->objectId ();
    if (leaderObjId.isValid())
        {
        if (RealDwgSuccess == status)
            {
            // try to add leader object id as a reactor of the mtext
            acMText->addPersistentReactor (leaderObjId);
            acMText->recordGraphicsModified (false);
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Error creating leader entity: %d\n", dimElemId);
            }
        es = acLeader->close ();
        if (Acad::eOk != es)
            DIAGNOSTIC_PRINTF ("Error closing leader entity: %d, [%ls]\n", dimElemId, acadErrorStatusText(es));
        }

    return  status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   DropNoteCellToComponents (ElementHandleCR inElement, AcDbObjectP existingObject, ConvertFromDgnContextR context) const
    {
    AcDbObjectP     nullObject = NULL;
    DropGeometry    dropCell ((DropGeometry::Options) (DropGeometry::OPTION_SharedCells | DropGeometry::OPTION_Complex));
    dropCell.SetSharedCellOptions (DropGeometry::SHAREDCELL_Geometry);

    // DropGraphics drops a cell and ignores its invisible components - don't bother to delete them
    RealDwgStatus   status = context.DropElementToDwg (nullObject, existingObject, inElement, dropCell);
    if (RealDwgSuccess != status)
        {
        DIAGNOSTIC_PRINTF ("Failed dropping dangling note cell ID=%I64d\n", inElement.GetElementId());
        status = DropFailed;
        }

    return  status;
    }

};  // ToDwgExtNoteCell



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::MTextFromTextBlock (AcDbMText* pMText, TextBlockR textBlock, ElementHandleCR inElement, bool checkIn)
    {
    // preset entity header for mtext and allow later overrides as needed
    this->UpdateEntityPropertiesFromElement (pMText, inElement);
    
    // Must be computed before ConvertToMTextCompatibleTextBlock because it depends on knowing the type
    //  (e.g. DGN vs. MText) of the TextBlock when it started -- if MText, TextBlock already honors the
    //  fact that leading tabs are ignored; if DGN, we must work around this for better export.
    double effectiveDWGLineBreakLength = ComputeEffectiveDWGLineBreakLength (textBlock) * this->GetScaleFromDGN ();

    textBlock.ConvertToMTextCompatibleTextBlock ();

    if (textBlock.IsEmpty ())     // means the document is empty
        {
        // set a valid text height to avoid ACAD's audit error:
        pMText->setTextHeight (textBlock.GetNodeHeight () * this->GetScaleFromDGN());
        pMText->setWidth (1.0);
        pMText->recordGraphicsModified (false);
        return RealDwgSuccess;
        }
    MSElementDescrCP    elmdscr = inElement.GetElementDescrCP ();
    if (NULL == elmdscr)
        return  MstnElementUnacceptable;

    RunPropertiesCP     runProperties = textBlock.GetRunProperties (0);
    if (NULL == runProperties)
        return  MstnElementUnacceptable;

    ParagraphProperties paragraph1Props = textBlock.Begin().GetCurrentParagraphP()->GetProperties ();

    TextParamWide           textParams;
    TextBlockPropertiesCR   textProperties = textBlock.GetProperties ();
    double                  annoScale = textBlock.GetProperties ().GetAnnotationScale ();

    paragraph1Props.ToElementData (textParams, annoScale);
    runProperties->ToElementData (textParams, annoScale);

    // get text size from text block and set the height to mtext:
    // setTextHeight would fail on negative text height and would set it to active height instead:
    DPoint2d        scale;
    double          dAutoCADTextHeight = fabs (this->GetTextSizeFromTextBlock(&scale, textBlock));
    dAutoCADTextHeight *= this->GetScaleFromDGN ();

    // set annotation scale in modelspace for text and text node elements
    bool            annotative = false;
    if (textProperties.HasAnnotationScale() && this->CanSaveAnnotationScale())
        annotative = this->AddAnnotationScaleToObject (pMText, annoScale, checkIn ? inElement.GetElementId() : 0);
    else
        RealDwgUtil::SetObjectAnnotative (pMText, false);

    pMText->setTextHeight (dAutoCADTextHeight);

    DPoint3d                    userOrigin = textBlock.GetUserOrigin ();
    Bentley::WString            markup;
    DwgContextForTextBlock      mapper(NULL, this, pMText);

    textBlock.ToMText (markup, this->GetModel(), &mapper, 1/this->GetScaleFromDGN(), true, !this->GetSettings().ConvertEmptyEDFToSpace());

    AcString contentString   = AcString (markup.c_str());
    if (this->ExtractFieldContents(contentString) && checkIn)
        {
        if (!pMText->objectId().isValid())
            this->AddEntityToCurrentBlock (pMText, inElement.GetElementId());

        // make sure we save textnode element as ToDwgExtension depends on correct element type:
        ElementHandleCR         textnode = CELL_HEADER_ELM == inElement.GetElementType() ? ChildElemIter(inElement, ExposeChildrenReason::Count) : inElement;
        this->PostProcessingRequired (textnode, pMText->objectId());
        }

    if (0.0 == scale.y || 0.0 == scale.x)
        {
        pMText->recordGraphicsModified (false);
        return ScaleError;
        }

    pMText->setTextStyle (this->TextStyleFromDgnTextParams(textParams));

    /* add rsc to tree of fonts to export */
    for (size_t iStyle = 0, nStyles = textBlock.GetRunPropertiesCount (); iStyle < nStyles; iStyle++)
        m_pFileHolder->AddFontToInstallTree (&textBlock.GetRunProperties(iStyle)->GetFont());

    RotMatrix                   matrix;
    matrix.InitProduct (this->GetLocalTransform(), textBlock.GetOrientation());

    DVec3d                      rX, rY, rZ;
    matrix.getColumns (&rX, &rY, &rZ);
    rX.normalize ();
    rY.normalize();

    // extract the backwards component
    if (0 != textProperties.IsBackwards())
        rX.negate ();

    // extract the upsidedown component
    if (0 != textProperties.IsUpsideDown())
        rY.negate ();

    // Reset Z Axis to avoid non-rotation matrix
    matrix.initFrom2Vectors (&rX, &rY);

    DPoint3d                    extrusionDirection, extrusionOrigin;
    double                      extrusionAngle;
    this->GetTransformFromDGN().Multiply (userOrigin);
    RealDwgUtil::ExtractExtrusionAndOrigin (extrusionDirection, extrusionOrigin, extrusionAngle, matrix, userOrigin);

    // if user wants to remove z-coordinate in DWG file, do so now:
    if (this->GetSettings().IsZeroZCoordinateEnforced())
        userOrigin.z = 0.0;

    pMText->setLocation         (RealDwgUtil::GePoint3dFromDPoint3d (userOrigin));
    pMText->setDirection        (RealDwgUtil::GeVector3dFromDPoint3d (rX));
    pMText->setNormal           (RealDwgUtil::GeVector3dFromDPoint3d (extrusionDirection));
    pMText->setRotation         (extrusionAngle);
    pMText->setWidth            (effectiveDWGLineBreakLength);

    if (!HasLevelColorOverride (elmdscr->el.ehdr.level, *this))
        pMText->setColor        (this->GetColorFromDgn (runProperties->GetColor(), pMText->colorIndex()));

    TextElementJustification    iJustification = paragraph1Props.GetJustification ();
    pMText->setAttachment       (DwgAttachmentPointFromDgn (iJustification));

    if (textProperties.IsVertical())
        pMText->setFlowDirection (AcDbMText::kTtoB);
    else
        pMText->setFlowDirection (AcDbMText::kLtoR);

    DgnLineSpacingType          lineSpacingType = paragraph1Props.GetLineSpacingType ();
    AcDb::LineSpacingStyle      lineSpacingStyle;
    double                      lineSpacingFactor;
    GetDwgLineSpacingFactorAndStyleFromDgn (&lineSpacingStyle, &lineSpacingFactor, paragraph1Props.GetLineSpacingValue(), textBlock.GetProperties ().GetAnnotationScale(),
                                            lineSpacingType, scale.y, this->GetScaleFromDGN(), textProperties.IsVertical());

    pMText->setLineSpacingStyle (lineSpacingStyle);
    pMText->setLineSpacingFactor(lineSpacingFactor);

    // set mtext background mask:
    pMText->setBackgroundFill (true == textParams.flags.bgColor);
    if (true == textParams.flags.bgColor)
        {
        pMText->setUseBackgroundColor (DWG_COLOR_Background255 == textParams.backgroundFillColor);
        pMText->setBackgroundFillColor (this->GetColorFromDgn(textParams.backgroundFillColor, DWG_COLOR_Background255, this->GetModel()));

        double  backgroundMargin = textParams.backgroundBorder.y / scale.y;
        if (annoScale > TOLERANCE_ZeroScale)
            backgroundMargin /= annoScale;

        pMText->setBackgroundScaleFactor (1.0 + backgroundMargin);

        if (RealDwgUtil::IsACADTextBackgrdTextFrameBehavior () && (textParams.backgroundColor != textParams.backgroundFillColor))
            {
            pMText->setShowBorders (true);
            //pMText->setLineWeight (GetLineWeightFromDgn (textParams.backgroundWeight, pMText->lineWeight ()), false);
            //pMText->setLinetype (GetLineTypeFromDgn (textParams.backgroundStyle));          
            }
        }

    pMText->setContents (contentString.kwszPtr());

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/11
+---------------+---------------+---------------+---------------+---------------+------*/
double          ConvertFromDgnContext::GetTextSizeFromTextBlock (DPoint2dP size, TextBlockCR textBlock)
    {
    RunPropertiesCP     runProperties = textBlock.GetRunProperties (0);

    DPoint2d            scale = runProperties->GetFontSize ();

    if (0.0 != textBlock.GetNodeHeight ())
        scale.y = scale.x = textBlock.GetNodeHeight ();

    // TR 113096 Dont use node number height when saving microstation line spaced text to dwg. As it is not necessarily the same
    // as the height of the text it can cause autocad's line spacing to be calculated incorrectly
    ParagraphProperties paragraph1Props = textBlock.Begin().GetCurrentParagraphP()->GetProperties ();
    DgnLineSpacingType  lineSpacingType = paragraph1Props.GetLineSpacingType ();
    double              acadTextHeight = 0.0;

    if (lineSpacingType == DgnLineSpacingType::Automatic || lineSpacingType == DgnLineSpacingType::Exact || 0.0 == textBlock.GetNodeHeight())
        acadTextHeight = scale.y;
    else
        acadTextHeight = textBlock.GetNodeOrFirstRunHeight();

    if (NULL != size)
        *size = scale;

    return  acadTextHeight;
    }

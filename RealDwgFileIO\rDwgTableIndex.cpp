/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgTableIndex.cpp $
|
|  $Copyright: (c) 2016 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include    "rDwgInternal.h"
#include    <Mstn\RealDwg\rDwgUtil.h>

BEGIN_BENTLEY_NAMESPACE

namespace RealDwg {

/*=================================================================================**//**
*
* XXXTableIndex
*
*  AutoCAD Tables are based on the element IDs directly, whereas MicroStation uses
*   a seperate indexing based on 32 bit integer IDs.  This class provides a two way
*   mapping to and from the table IDs and the element Ids.
*
* @bsiclass                                                     RayBentley      11/2002
+===============+===============+===============+===============+===============+======*/
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetOriginatedInDwgIdsFromDescr
(
ElementIdArrayR             originatedInDwgList,
MSElementDescrCP            pDescr
)
    {
    originatedInDwgList.clear();
    if (NULL != pDescr)
        {
        for (MSElementDescrCP pEntryDescr = pDescr->h.firstElem; NULL != pEntryDescr; pEntryDescr = pEntryDescr->h.next)
            {
            if (DgnPlatform::TABLE_ENTRY_ELM == pEntryDescr->el.ehdr.type)
                originatedInDwgList.push_back (pEntryDescr->el.ehdr.uniqueId);
            }
        }
    }

struct DbHandleToDgnIdNode
    {
    UInt64              dbHandle;
    Int32               dgnId;
    };

struct DgnIdToDbHandleNode
    {
    Int32               dgnId;
    UInt64              dbHandle;
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
SignedTableIndex::SignedTableIndex
(
bool     negativeIdsOnly
)
    {
    m_handleToDgnIdTree             = mdlAvlTree_init (AVLKEY_UINT64);
    m_dgnIdToHandleTree             = mdlAvlTree_init (AVLKEY_LONG);
    m_negativeIdsOnly               = negativeIdsOnly;
    m_nextDgnId                     = negativeIdsOnly ? -1 : 1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
SignedTableIndex::~SignedTableIndex
 (
 )
    {
    mdlAvlTree_free (&m_handleToDgnIdTree, nullptr, NULL);
    mdlAvlTree_free (&m_dgnIdToHandleTree, nullptr, NULL);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbHandle                  SignedTableIndex::GetDBHandle (Int32 dgnId)
    {
    DgnIdToDbHandleNode     *pNode;

    if (NULL != (pNode = (DgnIdToDbHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
        return pNode->dbHandle;

    AcDbHandle  nullHandle;
    nullHandle.setNull();
    return nullHandle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                SignedTableIndex::GetObjectId
(
Int32                       dgnId,
AcDbDatabase*               pDatabase
)
    {
    AcDbHandle      dbHandle;
    if ( (dbHandle = GetDBHandle (dgnId)).isNull() )
        return AcDbObjectId::kNull;

    AcDbObjectId    objectId;
    if (Acad::eOk != pDatabase->getAcDbObjectId (objectId, false, dbHandle, 0))
        {
        objectId.setNull();
        return objectId;
        }

    return objectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
Int32                       SignedTableIndex::GetDgnId (AcDbHandle dbHandle)
    {
    DbHandleToDgnIdNode    *pNode;
    UInt64                  handleVal = RealDwgUtil::CastDBHandle(dbHandle);

    if (NULL != (pNode = (DbHandleToDgnIdNode *) mdlAvlTree_search (m_handleToDgnIdTree, &handleVal)))
        return pNode->dgnId;

    return 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
Int32                      SignedTableIndex::GetOrNextDgnId (AcDbHandle dbHandle)
    {
    Int32      dgnId;
    if (0 != (dgnId = this->GetDgnId (dbHandle)))
        return dgnId;

    Int32 returnVal = m_negativeIdsOnly ? m_nextDgnId-- : m_nextDgnId++;

    // add an entry to this table as well as the DWG originated list.
    AddEntry (returnVal, dbHandle, true);

    return returnVal;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   SignedTableIndex::AddEntry
(
Int32           dgnId,
AcDbHandle      dbHandle,
bool            originatedInDwg
)
    {
    DbHandleToDgnIdNode         dbHandleToDgnIdNode;
    DgnIdToDbHandleNode         dgnIdToDbHandleNode;

    dbHandleToDgnIdNode.dgnId    = dgnIdToDbHandleNode.dgnId = dgnId;
    dbHandleToDgnIdNode.dbHandle = dgnIdToDbHandleNode.dbHandle = RealDwgUtil::CastDBHandle(dbHandle);

    StatusInt               status;

    if (SUCCESS == (status = mdlAvlTree_insertNode (m_handleToDgnIdTree, &dbHandleToDgnIdNode, sizeof(dbHandleToDgnIdNode))))
        status = mdlAvlTree_insertNode (m_dgnIdToHandleTree, &dgnIdToDbHandleNode, sizeof (dgnIdToDbHandleNode));

    if (originatedInDwg)
        m_originatedInDwgList.push_back (dgnIdToDbHandleNode.dbHandle);

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   SignedTableIndex::RemoveEntry
(
AcDbHandle      dbHandle
)
    {
    DbHandleToDgnIdNode     *pDbHandleToDgnIdNode;
    DgnIdToDbHandleNode     *pDgnIdToDbHandleNode;
    UInt64                  handleVal = RealDwgUtil::CastDBHandle(dbHandle);

    if (NULL != (pDbHandleToDgnIdNode = (DbHandleToDgnIdNode *) mdlAvlTree_search (m_handleToDgnIdTree, &handleVal)) &&
        NULL != (pDgnIdToDbHandleNode = (DgnIdToDbHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &pDbHandleToDgnIdNode->dgnId)))
        {
        mdlAvlTree_deleteNode (m_handleToDgnIdTree, pDbHandleToDgnIdNode);
        mdlAvlTree_deleteNode (m_dgnIdToHandleTree, pDgnIdToDbHandleNode);
        return SUCCESS;
        }
    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
ElementIdArrayR             SignedTableIndex::GetOriginatedInDwgList ()
    {
    return m_originatedInDwgList;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SignedTableIndex::SetOriginatedInDwgIds (MSElementDescrCP desc)
    {
    // this saves the ElementIDs (which are == the AcDbHandles) of the DWG entities we started with.
    SetOriginatedInDwgIdsFromDescr (m_originatedInDwgList, desc);
    }


struct DgnIdToLayerHandleNode
    {
    UInt32              dgnId;
    UInt64              dbHandle;
    bool                colorOverride;
    bool                styleOverride;
    bool                weightOverride;
    AcDbObjectId        constructionClassChild;
    AcDbObjectId        patternClassChild;
    AcDbObjectId        linearPatternedClassChild;
    } ;

struct DbHandleToUDgnIdNode
    {
    UInt64              dbHandle;
    UInt32              dgnId;
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
LayerTableIndex::LayerTableIndex
(
)
    {
    m_handleToDgnIdTree             = mdlAvlTree_init (AVLKEY_UINT64);
    m_dgnIdToHandleTree             = mdlAvlTree_init (AVLKEY_ULONG);
    m_nextDgnId                     = 1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
LayerTableIndex::~LayerTableIndex
 (
 )
    {
    mdlAvlTree_free (&m_handleToDgnIdTree, nullptr, NULL);
    mdlAvlTree_free (&m_dgnIdToHandleTree, nullptr, NULL);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbHandle                  LayerTableIndex::GetDBHandle (UInt32 dgnId)
    {
    DgnIdToLayerHandleNode      *pNode;

    if (NULL != (pNode = (DgnIdToLayerHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
        return pNode->dbHandle;

    AcDbHandle  nullHandle;
    nullHandle.setNull();
    return nullHandle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                LayerTableIndex::GetObjectId
(
UInt32                      dgnId,
AcDbDatabase*               pDatabase
)
    {
    AcDbHandle      dbHandle;
    if ((dbHandle = GetDBHandle (dgnId)).isNull())
        return AcDbObjectId::kNull;

    AcDbObjectId    objectId;
    if (Acad::eOk != pDatabase->getAcDbObjectId (objectId, false, dbHandle, 0))
        {
        objectId.setNull();
        return objectId;
        }

    return objectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      LayerTableIndex::GetDgnId (AcDbHandle dbHandle)
    {
    DbHandleToUDgnIdNode   *pNode;
    UInt64                  handleVal = RealDwgUtil::CastDBHandle(dbHandle);

    if (NULL != (pNode = (DbHandleToUDgnIdNode *) mdlAvlTree_search (m_handleToDgnIdTree, &handleVal)))
        return pNode->dgnId;

    return 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      LayerTableIndex::GetOrNextDgnId (AcDbHandle dbHandle)
    {
    UInt32      dgnId;
    if (0 != (dgnId = this->GetDgnId (dbHandle)))
        return dgnId;

    UInt32 returnVal = m_nextDgnId++;

    // add an entry to this table as well as the DWG originated list.
    AddEntry (returnVal, dbHandle, true, false, false, false);

    // skip LEVEL_DEFAULT_LEVEL_ID.
    if (LEVEL_DEFAULT_LEVEL_ID == m_nextDgnId)
        m_nextDgnId++;

    return returnVal;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   LayerTableIndex::AddEntry
(
UInt32                      dgnId,
AcDbHandle                  dbHandle,
bool                        originatedInDwg,
bool                        colorOverride,
bool                        styleOverride,
bool                        weightOverride
)
    {
    DbHandleToUDgnIdNode        dbHandleToDgnIdNode;
    DgnIdToLayerHandleNode      dgnIdToLayerHandleNode;

    dbHandleToDgnIdNode.dgnId    = dgnIdToLayerHandleNode.dgnId = dgnId;
    dbHandleToDgnIdNode.dbHandle = dgnIdToLayerHandleNode.dbHandle = RealDwgUtil::CastDBHandle(dbHandle);

    dgnIdToLayerHandleNode.colorOverride = colorOverride;
    dgnIdToLayerHandleNode.styleOverride = styleOverride;
    dgnIdToLayerHandleNode.weightOverride = weightOverride;

    StatusInt               status;

    if (SUCCESS == (status = mdlAvlTree_insertNode (m_handleToDgnIdTree, &dbHandleToDgnIdNode, sizeof(dbHandleToDgnIdNode))))
        status = mdlAvlTree_insertNode (m_dgnIdToHandleTree, &dgnIdToLayerHandleNode, sizeof (dgnIdToLayerHandleNode));
    else
        {
        DgnIdToLayerHandleNode     *pNode;
        if (NULL != (pNode = (DgnIdToLayerHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
            {
            pNode->colorOverride = colorOverride;
            pNode->styleOverride = styleOverride;
            pNode->weightOverride = weightOverride;
            }
        }
    if (originatedInDwg)
        m_originatedInDwgList.push_back (dgnIdToLayerHandleNode.dbHandle);

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   LayerTableIndex::RemoveEntry
(
AcDbHandle      dbHandle
)
    {
    DbHandleToUDgnIdNode    *pDbHandleToDgnIdNode;
    DgnIdToLayerHandleNode  *pDgnIdToDbHandleNode;
    UInt64                  handleVal = RealDwgUtil::CastDBHandle(dbHandle);

    if (NULL != (pDbHandleToDgnIdNode = (DbHandleToUDgnIdNode *) mdlAvlTree_search (m_handleToDgnIdTree, &handleVal)) &&
        NULL != (pDgnIdToDbHandleNode = (DgnIdToLayerHandleNode *)  mdlAvlTree_search (m_dgnIdToHandleTree, &pDbHandleToDgnIdNode->dgnId)))
        {
        mdlAvlTree_deleteNode (m_handleToDgnIdTree, pDbHandleToDgnIdNode);
        mdlAvlTree_deleteNode (m_dgnIdToHandleTree, pDgnIdToDbHandleNode);
        return SUCCESS;
        }
    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   LayerTableIndex::GetSymbologyOverrides
(
bool*                       pColorOverride,
bool*                       pStyleOverride,
bool*                       pWeightOverride,
UInt32                      dgnId
)
    {
    DgnIdToLayerHandleNode     *pNode;

    if (NULL != (pNode = (DgnIdToLayerHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
        {
        if (NULL != pColorOverride)
            *pColorOverride  = pNode->colorOverride;
        if (NULL != pStyleOverride)
            *pStyleOverride  = pNode->styleOverride;
        if (NULL != pWeightOverride)
            *pWeightOverride = pNode->weightOverride;
        return SUCCESS;
        }
    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                LayerTableIndex::GetConstructionClassChild (UInt32 dgnId)
    {
    DgnIdToLayerHandleNode     *pNode;

    if (NULL != (pNode = (DgnIdToLayerHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
        return pNode->constructionClassChild;

    AcDbObjectId    nullId;
    return nullId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                LayerTableIndex::GetPatternClassChild (UInt32 dgnId)
    {
    DgnIdToLayerHandleNode     *pNode;

    if (NULL != (pNode = (DgnIdToLayerHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
        return pNode->patternClassChild;

    AcDbObjectId    nullId;
    return nullId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                LayerTableIndex::GetLinearPatternedClassChild (UInt32 dgnId)
    {
    DgnIdToLayerHandleNode     *pNode;

    if (NULL != (pNode = (DgnIdToLayerHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
        return pNode->linearPatternedClassChild;

    AcDbObjectId    nullId;
    return nullId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   LayerTableIndex::SetConstructionClassChild
(
UInt32                      dgnId,
AcDbObjectId                layerId
)
    {
    DgnIdToLayerHandleNode     *pNode;

    if (NULL != (pNode = (DgnIdToLayerHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
        pNode->constructionClassChild = layerId;

    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   LayerTableIndex::SetPatternClassChild
(
UInt32                      dgnId,
AcDbObjectId                layerId
)
    {
    DgnIdToLayerHandleNode     *pNode;

    if (NULL != (pNode = (DgnIdToLayerHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
        pNode->patternClassChild = layerId;

    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   LayerTableIndex::SetLinearPatternedClassChild
(
UInt32                      dgnId,
AcDbObjectId                layerId
)
    {
    DgnIdToLayerHandleNode     *pNode;

    if (NULL != (pNode = (DgnIdToLayerHandleNode *) mdlAvlTree_search (m_dgnIdToHandleTree, &dgnId)))
        pNode->linearPatternedClassChild = layerId;

    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
ElementIdArrayR             LayerTableIndex::GetOriginatedInDwgList ()
    {
    return m_originatedInDwgList;
    }

} // End RealDwg namespace

END_BENTLEY_NAMESPACE

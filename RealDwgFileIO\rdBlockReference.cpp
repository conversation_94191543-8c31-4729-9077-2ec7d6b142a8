/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdBlockReference.cpp $
|
|  $Copyright: (c) 2020 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
// This file is #included in rDwgDgnExtension.cpp


/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtBlockReference : public ToDgnExtension
{
public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbBlockReference* pBlockReference = AcDbBlockReference::cast (acObject);

    if (pBlockReference->blockTableRecord().isNull())
        {
        DIAGNOSTIC_PRINTF ("Insert: %I64d Ignored - no block table record\n", context.ElementIdFromObject (acObject));
        return BlockTableRecordMissing;
        }

    AcDbBlockTableRecordPointer     pBlockTR (pBlockReference->blockTableRecord(), AcDb::kForRead, false);
    if (Acad::eOk != pBlockTR.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Insert: %I64d Ignored - no block table record\n", context.ElementIdFromObject (acObject));
        return BlockTableRecordMissing;
        }

    if (IsXRefInBlock (pBlockReference, pBlockTR))
        {
        ACHAR           *pathName;
        pBlockTR->pathName (pathName);
        DIAGNOSTIC_PRINTF ("Ignoring XRef within Block - %ls\n", pathName);
        delete pathName;
        return XRefWithinBlock;
        }

    DPoint3d            origin;
    RealDwgUtil::DPoint3dFromGePoint3d (origin, pBlockReference->position());
    if (origin.maxAbs () > 1.0E50)
        {
        ElementId       id = context.ElementIdFromObject (acObject);
        DIAGNOSTIC_PRINTF ("Insert %I64d has invalid origin: ", id);
        if (context.GetSettings().DiscardInvalidEntities())
            {
            RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_EntitiesOffDesignPlaneDiscarded, false, acObject->isA()->name(), acObject->isA()->name());
            DIAGNOSTIC_PRINTF ("ignored per dwgsettings!\n");
            return OriginOutOfRange;
            }

        DIAGNOSTIC_PRINTF ("attempted fixing the origin!\n");

        // try fixing origin to get something in, rather than completely throwing it away - TFS 22986
        WString         idString;
        WString::Sprintf (idString, L"%I64d", id);
        RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_EntitiesOffDesignPlaneFixed, false, acObject->isA()->name(), idString.GetWCharCP());

        if (fabs(origin.x) >= RMAXDESIGNRANGE)
            origin.x = 0.0;
        if (fabs(origin.y) >= RMAXDESIGNRANGE)
            origin.y = 0.0;
        if (fabs(origin.z) >= RMAXDESIGNRANGE)
            origin.z = 0.0;
        }

    /*------------------------------------------------------------------------------------------------------
    We currently only support modelspace annotation scale.  If the annotation scale is not the same as model 
    annotation scale, we have to disable the annotation scale.
    ------------------------------------------------------------------------------------------------------*/
    DPoint3d            scale;
    double              annoScale = 1.0;
    bool                isAnnotative = this->GetEffectiveScale (scale, annoScale, pBlockReference, pBlockTR, context);

    // extract the clipping boundary
    bool                isXref = pBlockTR->isFromExternalReference ();
    DgnClipData         clipData;
    this->GetClipDataFromBlockReference (clipData, isXref, pBlockTR->origin(), pBlockReference, context);

    size_t              nClipPoints = clipData.m_pointArray.size ();
    Transform           transformToDGN = context.GetTransformToDGN();
    transformToDGN.Multiply (origin);

    DgnModelRefP        modelRef = context.GetModel ();
    const ACHAR*        blockName = NULL;
    pBlockTR->getName (blockName);

    const ACHAR*        comments = nullptr;
    if (Acad::eOk != pBlockTR->comments(comments) || nullptr == comments || 0 == comments[0])
        comments = nullptr;

    // build composite matrixes we will need for ref attachment or cell/shared cell header
    RotMatrix           matrix, scaleMatrix;
    scaleMatrix.InitFromScaleFactors (scale.x, scale.y, scale.z);
    matrix.InitFromAxisAndRotationAngle (2, pBlockReference->rotation());
    matrix.InitProduct (matrix, scaleMatrix);

    Transform           extrusionTransform;
    if (NULL != RealDwgUtil::GetExtrusionTransform (extrusionTransform, pBlockReference->normal(), 0.0))
        {
        RotMatrix       extrusionMatrix;
        extrusionTransform.GetMatrix (extrusionMatrix);
        matrix.InitProduct (extrusionMatrix, matrix);
        }

    StatusInt           status = SUCCESS;

    if (isXref)
        {
        DVec3d              column0;
        matrix.GetColumn (column0, 0);

        // Create a reference file attachment.
        ReferenceFileElm*   refData = (ReferenceFileElm*)malloc (MAX_ELEMENT_SIZE);
        if (NULL == refData)
            return  OutOfMemoryError;
        memset (refData, 0, sizeof(*refData));

        refData->ehdr.type                       = REFERENCE_ATTACH_ELM;
        refData->fb_opts.snap_lock               = refData->fb_opts.locate_lock = true;
        refData->fb_opts.attachmentFromDWG       = true;
        refData->fb_opts.levelControlsDisplay    = false;
        refData->ehdr.uniqueId                   = context.ElementIdFromObject (pBlockReference);
        refData->fileNumber                      = context.GetNextReferenceFileNumber();
        refData->masterOrigin                    = origin;
        refData->scale                           = column0.magnitude();
        refData->fd_opts.rotateClipping          = true;
        refData->fd_opts.lstyleScale             = true;
        refData->fd_opts.display                 = !pBlockTR->isUnloaded();
        refData->fd_opts.displayRasterRefs       = true;
        refData->ehdr.level                      = context.GetDgnLevel (pBlockReference->layerId());

        UInt32              clipLevel = refData->ehdr.level;
        DPoint2dP           pClipPoints = NULL;
        RotMatrixP          pClipRMatrix = NULL;
        if (nClipPoints > 0)
            {
            pClipRMatrix = &clipData.m_matrix;
            pClipPoints = &clipData.m_pointArray.front();

            RotMatrix       clipInverse;
            clipInverse.InverseOf (clipData.m_matrix);

            clipInverse.Multiply (clipData.m_origin);
            clipData.m_frontClipZ += clipData.m_origin.z;
            clipData.m_backClipZ  += clipData.m_origin.z;
            for (size_t iPoint=0; iPoint<nClipPoints; iPoint++)
                {
                if (!pClipPoints[iPoint].IsDisconnect())
                    {
                    pClipPoints[iPoint].x += clipData.m_origin.x;
                    pClipPoints[iPoint].y += clipData.m_origin.y;
                    }
                }

            clipData.m_matrix.InitProduct (matrix, clipData.m_matrix);

            refData->fd_opts.clipFront = clipData.m_frontClipOn;
            refData->fd_opts.clipBack  = clipData.m_backClipOn;
            refData->zFront = clipData.m_frontClipZ;
            refData->zBack  = clipData.m_backClipZ;
            }

        // AutoCAD's scale is not a "true" scale, but a scale between the file storage units.
        // This may differ from the UOR scale by the uorsPerStorage unit ratio between the reference and the master file.
        refData->fd_opts.scaleByUnits            = false;
        refData->fd_opts.scaleByStorageUnits     = true;
        refData->nestDepth                       = DWG_REFERENCE_NEST_DEPTH;
        refData->fd_opts.doNotDisplayAsNested    = pBlockTR->isFromOverlayReference();
        refData->fb_opts.useAnnotationScale      = this->CanUseAnnotationScale (pBlockReference, pBlockTR, refData, context);

        // extract DGN specific data from xdata created from DWG save
        ToDgnExtBlockReference::RoundTripDgnSpecificData (refData, acObject, context);

        // Extract the X Scale as the reference file scale... If non-uniform rest goes in the matrix.
        double          invScale = (0.0 == refData->scale) ? 1.0 : 1.0/refData->scale;
        matrix.ScaleColumns (matrix, invScale, invScale, invScale);
        refData->transform = matrix;

        const ACHAR*    pathName = NULL;
        pBlockTR->pathName (pathName);

        ToDgnExtBlockReference::SetElementSizeWithClippingPoints (refData, pClipPoints, (UInt16)nClipPoints);

        status = DgnAttachment::InitializeDgnAttachmentElementFromReferenceFileElm (outElement, *refData, NULL, pathName, comments,
                                                    context.GetSettings().DisallowLogicalNameFromXRefBlockNames() ? nullptr : blockName,
                                                    (int)nClipPoints, pClipPoints, 0, nullptr, nullptr, pClipRMatrix, modelRef);
        free (refData);

        if (SUCCESS != status)
            return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

        // add path linkages
        RealDwgUtil::AddPathToReferenceElement (outElement, pathName);

        // add block name string linkage
        RealDwgUtil::AppendStringLinkage (outElement, STRING_LINKAGE_KEY_DwgBlockName, blockName);
        // add boundary symbology linkage
        if (nClipPoints > 0)
            {
            UInt32      clipColor = context.GetDgnColor (pBlockReference->entityColor());
            Int32       clipStyle = context.GetDgnStyle (pBlockReference->linetypeId());
            UInt32      clipWeight = context.GetDgnWeight (pBlockReference->lineWeight());
            linkage_setSymbologyLinkage (outElement, SYMBOLOGY_LINKAGE_KEY_RefBound, false, false, false, false, clipStyle, clipWeight, clipColor, clipLevel);
            }

        /*This xattribute is going to persisted in the attachement*/
        if (outElement.IsValid())
            DgnAttachment::SetAnnotationScaleFlag(outElement);

        context.ElementHeaderFromEntity (outElement, pBlockReference);

        return RealDwgSuccess;
        }
    else
        {
        AcDbBlockTableRecordIterator*     pIterator;
        pBlockTR->newIterator (pIterator, true, true);

        bool            isEmptyBlock = pIterator->done();
        if (!isEmptyBlock && !context.GetSettings().AttributesAsTags())
            {
            // when converting attributes as item types, we may still get an empty shared cell definition:
            bool        hasOnlyAttrdef = true;
            for (pIterator->start(); !pIterator->done(); pIterator->step())
                {
                AcDbObjectId    id;
                if (Acad::eOk == pIterator->getEntityId(id) && id.objectClass()->isDerivedFrom(AcDbAttributeDefinition::desc()))
                    continue;
                hasOnlyAttrdef = false;
                break;
                }

            if (hasOnlyAttrdef)
                isEmptyBlock = true;
            }

        AcString        cellName;

        if (SUCCESS == RealDwgXDataUtil::GetMicroStationXDataStringByKey (cellName, pBlockReference, StringConstants::XDataKey_CellNameLinkage))
            {
            BlockTableRecordToCellElement (outElement, pBlockTR, pBlockReference, origin, matrix, cellName, context);
            }
        else
            {
            AcDbObjectId    blockId = pBlockTR->objectId ();

            SharedCellHandler::CreateSharedCellElement (outElement, NULL, blockName, &origin, &matrix, NULL, context.GetThreeD(), *modelRef);
            SharedCellHandler::SetDefinitionID (outElement, context.ElementIdFromObjectId(blockId));
            // set range based on correct level - TR 324625
            outElement.GetElementP()->ehdr.level = context.GetDgnLevel (pBlockReference->layerId());
            SharedCellHandler::CreateSharedCellComplete (outElement);

            /*---------------------------------------------------------------------------
            If the shared cell instance returned from above call has a range calculation
            failure, we will try to fix that in the post process.

            One such a failure is found when a block table has records ordered abnormally.
            Normally block records are ordered in such a manner that a nested block is
            always present before its owner in the block table.  Normally ordered block
            table records do not cause any problem because all definitions are created
            before their instances are seen.  When a nested block definition is defined 
            after its instance appearing in another block definition, however, the parent 
            shared cell definition would fail on range calculation due to the missing 
            nested definition.

            However, we do not attempt to fix the element range for an empty block or a block
            containing all invisible children (as a case in TFS#52838).  We ignore the invalid
            range for these shared cells, as the reversed range may be intentionally used
            in display code.

            Enough performance cases to warrant a config var to skip the range validation, TFS1033073.
            ---------------------------------------------------------------------------*/
            if (!context.SkipSharedCellRangeValidation() && !isEmptyBlock && !this->IsRangeValid(outElement) && !context.IsInvisibleBlock(blockId))
                context.AddPostProcessObject (acObject);

            /*---------------------------------------------------------------------------
            SharedCellHandler::CreateSharedCellElement seems to have properly initialized element, 
            so we should no longer need to remove symbologies as a post process here.
            ---------------------------------------------------------------------------*/
            }

        if (!outElement.IsValid())
            return  CantCreateCell;

        // if the block refernce is annotative, set it as an annotative cell:
        if (isAnnotative)
            {
            status = RealDwgUtil::SetElementAnnotative (outElement, modelRef->GetDgnModelP(), &annoScale);
            if (BSISUCCESS == status && context.IsOrientationMatchedToLayout (pBlockReference))
                StrokeAnnotationElm::SetCanOrientRefElemToMatchSheetLayout (outElement);
            }

        // symbology for children of a type-2 cell should have been set above. Do not override them - ElementPropertiesSetter changes children. TFS643187
        // still have to retain xattributes from the roundtrip - TFS 686249
        int         mask = outElement.GetElementType() == CELL_HEADER_ELM ? HEADERRESTOREMASK_XAttributes : HEADERRESTOREMASK_Default;

        bool        isMInsert = RealDwgUtil::IsValidMInsert (pBlockReference);
        if (isMInsert)
            {
            // Create a cell with the shared cell instances
            MSElementDescrP     pOriginalDescr = outElement.ExtractElementDescr();
            NormalCellHeaderHandler::CreateCellElement (outElement, L"", origin, matrix, context.GetThreeD(), *modelRef);
            if (!outElement.IsValid())
                return  CantCreateCell;

            context.ElementHeaderFromEntity (outElement, pBlockReference, mask);

            // each child will only be translated by the minsert xy spaces:
            Transform           deltaTransform = Transform::FromIdentity ();

            // each child will be independently transformed with scales removed(not just normalized but really inverted - TFS 7442):
            RotMatrix           deltaMatrix;
            deltaMatrix.InitProduct (transformToDGN, matrix);
            scaleMatrix.Invert ();
            deltaMatrix.InitProduct (deltaMatrix, scaleMatrix);

            // Type2Handler::_EditHeaderProperties does not allow level to be set:
            pOriginalDescr->el.ehdr.level    = context.GetDgnLevel (pBlockReference->layerId());
            pOriginalDescr->el.hdr.dhdr.symb = outElement.GetElementCP()->hdr.dhdr.symb;

            MSElementDescrP     pCellHeaderDescr = outElement.ExtractElementDescr ();
            MSElementDescrP     lastChild = NULL;
            AcDbMInsertBlock*   pMInsert = AcDbMInsertBlock::cast (pBlockReference);
            DPoint3d            delta;
            delta.z = 0.0;
            int                 iRow;
            for (iRow = 0, delta.y = 0.0; iRow < pMInsert->rows(); iRow++, delta.y += pMInsert->rowSpacing())
                {
                int             jCol;
                for (jCol = 0, delta.x = 0.0; jCol < pMInsert->columns(); jCol++, delta.x += pMInsert->columnSpacing())
                    {
                    DVec3d      tmpVec;
                    deltaMatrix.Multiply (tmpVec, delta);
                    deltaTransform.SetTranslation (tmpVec);

                    MSElementDescrP     pDuplicateDescr = NULL;
                    if (BSISUCCESS != pOriginalDescr->Duplicate(&pDuplicateDescr, true, false))
                        break;
                    
                    EditElementHandle   nextInstance (pDuplicateDescr, false, false, modelRef);
                    nextInstance.GetHandler(MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (nextInstance, TransformInfo(deltaTransform));

                    pCellHeaderDescr->AppendChild (&lastChild, pDuplicateDescr);
                    }
                }
            pOriginalDescr->Release ();

            outElement.SetElementDescr (pCellHeaderDescr, true, false);
            }
        else
            {
            context.ElementHeaderFromEntity (outElement, pBlockReference, mask);

            MSElementDescrP     pDescr = outElement.ExtractElementDescr();
            MSElementDescrP     pSaveFirst = pDescr->h.firstElem;
            pDescr->h.firstElem = NULL;

            if (isEmptyBlock)
                {
                // The block is empty - set range backwards to avoid loading in cache.
                pDescr->el.hdr.dhdr.range.xlowlim  = 0;
                pDescr->el.hdr.dhdr.range.ylowlim  = 0;
                pDescr->el.hdr.dhdr.range.zlowlim  = 0;
                pDescr->el.hdr.dhdr.range.xhighlim = -1;
                pDescr->el.hdr.dhdr.range.yhighlim = -1;
                pDescr->el.hdr.dhdr.range.zhighlim = -1;
                }
            delete pIterator;

            pDescr->h.firstElem = pSaveFirst;

            outElement.SetElementDescr (pDescr, true, false);
            }

        if (nClipPoints > 0)
            CellUtil::AddClipLinkage (outElement, clipData.m_matrix, clipData.m_origin, clipData.m_frontClipZ, clipData.m_backClipZ, 
                                      clipData.m_frontClipOn, clipData.m_backClipOn, nClipPoints, &clipData.m_pointArray.front(), false);

        if (context.GetSettings().AttributesAsTags())
            {
            context.GetAndAppendTagsToCellInstance (outElement, pBlockReference, isMInsert);
            }
        else
            {
            ConvertAttributesToItemTypes    attributesToItemTypes (outElement, pBlockReference, isMInsert, &context);
            if (RealDwgSuccess != attributesToItemTypes.Convert())
                DIAGNOSTIC_PRINTF ("Failed converting attributes to item types for INSERT ID=%I64d\n", outElement.GetElementId());
            }
        }

    // if it is a ViewportDrawnCell being round tripped back, validate the mater file target
    if (outElement.GetElementType() == CELL_HEADER_ELM)
        {
        ElementHandle::XAttributeIter xattr(outElement, ViewportDrawnCellHandler::GetHandlerId());
        if (xattr.IsValid())
            {
            AcDbDatabaseP   masterDwg = nullptr;
            DgnFileP    masterDgn = nullptr;
            if (DwgPlatformHost::Instance()._GetMasterDwgFile(masterDwg, &masterDgn) && nullptr != masterDgn)
                {
                auto dgnHeader = masterDgn->GetHeader ();
                if (nullptr != dgnHeader && 0 == dgnHeader->fileID.guid[0])
                    RealDwgUtil::SetDgnFileGuidFromDwg (dgnHeader, masterDwg);
                }
            }
        }

    return RealDwgSuccess;
    }

private:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsRangeValid (ElementHandleCR sharedcellElem) const
    {
    ScanRangeCP     range = sharedcellElem.GetIndexRange ();

    if ((range->xhighlim < range->xlowlim && range->yhighlim < range->ylowlim && range->zhighlim < range->zlowlim) ||
        (0 == range->xhighlim && 0 == range->yhighlim && 0 == range->zhighlim && 0 == range->xlowlim && 0 == range->ylowlim && 0 == range->zlowlim))
        return  false;

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
virtual   RealDwgStatus   FromElementDeleteObject (AcDbObject* acObject, AcDbObjectIdArray& idsToPurge, ConvertFromDgnContext& context) const override
    {
    AcDbBlockReference*         pBlockReference = AcDbBlockReference::cast (acObject);
    AcDbBlockTableRecordPointer pBlock (pBlockReference->blockTableRecord(), AcDb::kForRead);
    if (Acad::eOk == pBlock.openStatus() && pBlock->isFromExternalReference())
        context.SetXRefBlockPurgeRequired ();

    acObject->erase();
    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   ToElementPostProcess 
(
AcDbObjectP                     acObject,
ConvertToDgnContextR            context
) const override
    {
    /*-----------------------------------------------------------------------------------
    When we have a shared cell instance with an invalid range, we need to fix the ranges
    of all affected elements:

    1) The shared cell instance that has caused this post process: validate the ranges of 
        its definition as well as the instance itself.
    2) The parent/owner of this instance (NOT the instance's own definition): there are 
        two possible parents - a type 34 or a type 2.  For a type 34 parent, simply 
        validate the type 34 element at this step.  For a converted type 2 cell, however, 
        it uses block reference's ID and also acts as an instance.  There is no type 34 
        corresponding to the parent block definition, therefore this step will simply
        fail on finding a definition element.  However, the type 2 cell will be picked up
        and validated at next step.
    3) All instances (either type 35 or type 2) that reference the parent/owner block 
        definition found in step 2: loop through all block references of the parent block 
        and validate each one of the corresponding elements.  Again, keep in mind that
        this is not the block definition of the instance validated at step 1.  This is 
        the block definition of its parent/owner block.

    Note that in any of the above steps, we must handle a potential element replacement 
    failure: DgnModel::ReplaceElementDescr does not replace a shared cell nested in another
    shared cell definition.  In such a case, we only replace the parent shared cell def.

    A user config var can stop the shared cell range validation.
    -----------------------------------------------------------------------------------*/
    if (context.SkipSharedCellRangeValidation())
        return  RealDwgSuccess;

    DgnFileP            dgnFile = context.GetFile ();
    ElementId           elementId = context.ElementIdFromObject (acObject);
    if (0 == elementId || INVALID_ELEMENTID == elementId)
        return  RealDwgSuccess;

    ElementRefP         elementRef = dgnFile->FindByElementId (elementId, false);

    // Step 1 & 2: fix the range of this shared cell instance as well as the parent shared cell definition(i.e. the owner of this instance).
    if (NULL != elementRef && !this->ValidateElementRange(elementRef))
        return  RealDwgSuccess;

    // Step 3: find and fix all instances of the owner block definition - either shared cells or converted type 2 cells.
    AcDbObjectIdArray               blockRefIds;
    AcDbBlockTableRecordPointer     block (acObject->ownerId(), AcDb::kForRead);
    if (Acad::eOk == block.openStatus() && Acad::eOk == block->getBlockReferenceIds(blockRefIds, true, true))
        {
        for (int i = 0; i < blockRefIds.length(); i++)
            {
            if (blockRefIds[i].isValid())
                {
                elementId = context.ElementIdFromObjectId (blockRefIds[i]);
                if (0 == elementId || INVALID_ELEMENTID == elementId || NULL == (elementRef = dgnFile->FindByElementId(elementId, false)))
                    continue;

                ElementRefP         parentRef = elementRef->GetParentElementRef ();
                if (NULL != parentRef && SHAREDCELL_DEF_ELM == parentRef->GetElementType())
                    {
                    // the instance is nested in a shared cell definition, will need to replace the shared cell def:
                    elementRef = parentRef;
                    }
                
                if (NULL != elementRef)
                    this->ValidateElementRange (elementRef);
                }
            }
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/10
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ValidateElementRange (ElementRefP elementRef) const
    {
    bool                replaced = false;
    EditElementHandle   elemHandle(elementRef);

    if (elemHandle.IsValid())
        {
        /*-------------------------------------------------------------------------------
        We only validate element range for shared cells who have bad range.  But this post 
        process may not be initiated only from a bad element range.  TR 340953 has a case 
        in which hyperlinks cause block references to be post processed, resulted in poor
        performance.  Therefore, if the post process is not initiated from bad element range,
        do not bother to validate range.
        
        This also applies to shared cells/shared cell definitions, which might have been 
        invalid but got validated as a result of processing nested shared cells.
        -------------------------------------------------------------------------------*/
        ScanRangeCP             range = elemHandle.GetIndexRange ();
        if (NULL == range)
            return  false;

        if (range->xhighlim > range->xlowlim && range->yhighlim > range->ylowlim && range->zhighlim >= range->zlowlim)
            return  false;

        /*-----------------------------------------------------------------------------------------------------
        Now validate the instance itself: get the outermost parent cell definition, within which this instance 
        gets ultimately wrapped, then validate the wrapping shared cell definition element as a whole.
        -----------------------------------------------------------------------------------------------------*/
        PersistentElementRefP   elem2update = (PersistentElementRefP) elementRef->GetOutermostParentOrSelf ();
        replaced = this->ValidateAndReplaceElement (elem2update);
        }

    return  replaced;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ValidateAndReplaceElement (PersistentElementRefP elementRef) const
    {
    if (NULL == elementRef)
        return  false;

    DgnModelP           model = elementRef->GetDgnModelP ();
    MSElementDescrP     elmdscr = NULL;
    bool                replaced = false;

    if (NULL != model && BSISUCCESS == elementRef->GetElementDescr(elmdscr, model) && NULL != elmdscr)
        {
        // recurse the validation into all children of the cell definition
        if (this->ValidateElementRange(elmdscr, model->GetDgnFileP()))
            replaced = BSISUCCESS == model->ReplaceElementDescr(elmdscr, elementRef, true, false);

        elmdscr->Release ();
        }

    return  replaced;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ValidateElementRange (MSElementDescrP elmdscr, DgnFileP dgnFile) const
    {
    if (NULL == elmdscr)
        return  false;

    EditElementHandle eeh(elmdscr->h.elementRef);

    if (eeh.IsValid() && this->IsRangeValid(eeh))
        return false;

    bool        isValidated = false;
    for (MSElementDescrP currElmdscr = elmdscr; NULL != currElmdscr; currElmdscr = currElmdscr->h.next)
        {
        MSElementDescrP next = currElmdscr->h.next;
        MSElementDescrP previous = currElmdscr->h.previous;
        currElmdscr->h.next = currElmdscr->h.previous = NULL;

        if (SHAREDCELL_DEF_ELM == currElmdscr->el.ehdr.type || CELL_HEADER_ELM == currElmdscr->el.ehdr.type)
            {
            /*------------------------------------------------------------------------------------------------
            Recurse into the child cell definition, but only attempt to find and update nested shared cell
            instances and type-2 cells.  We leave other element types to be updated as part of the shared 
            cell definition which will be validated in the falling through code.
            -------------------------------------------------------------------------------------------------*/
            for (MSElementDescrP child = elmdscr->h.firstElem; NULL != child; child = child->h.next)
                {
                if (SHARED_CELL_ELM == child->el.ehdr.type || CELL_HEADER_ELM == child->el.ehdr.type)
                    this->ValidateElementRange (child, dgnFile);
                }
            }
        else
            {
            // If it is a shared cell instance, find and validate its definition prior to instance validation.
            ElementRefP     defElement = NULL;
            if (SHARED_CELL_ELM == currElmdscr->el.ehdr.type && NULL != dgnFile && NULL != (defElement = CellUtil::FindSharedCellDefinition(currElmdscr->el, *dgnFile)))
                this->ValidateAndReplaceElement ((PersistentElementRefP)defElement);
            }

        // validate current element
        if (BSISUCCESS == DisplayHandler::ValidateElementRange(currElmdscr, true))
            isValidated = true;
        
        currElmdscr->h.next = next;
        currElmdscr->h.previous = previous;
        }

    return  isValidated;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            GetEffectiveScale (DPoint3dR outScale, double& outAnnoscale, AcDbBlockReference* blockReference, AcDbBlockTableRecord* block, ConvertToDgnContextR context) const
    {
    DPoint3d    nonAnnoScale, overallScale;
    RealDwgUtil::DPoint3dFromGeScale3d (overallScale, blockReference->scaleFactors());
    RealDwgUtil::DPoint3dFromGeScale3d (nonAnnoScale, blockReference->nonAnnotationScaleFactors());

    /*-------------------------------------------------------------------------------------------------------------
    Only when the block reference is in modelspace it should be set annotative; all other instances should take 
    overall scale.  We shall remove this when sheet model annotation scale can be supported.

    When determining if a block reference is annotative we should check block definition, but apparently some blocks
    are not annotative while their instances are.  One such a case is seen in a "master" block definition with 
    multiple anonymous block definitions.  The master named block is annotative but its anonymous blocks are not.
    -------------------------------------------------------------------------------------------------------------*/
    if (acdbSymUtil()->blockModelSpaceId(blockReference->database()) == blockReference->ownerId() && RealDwgUtil::IsObjectAnnotative(block))
        {
        outAnnoscale = fabs (overallScale.x);

        if (fabs(nonAnnoScale.x) > TOLERANCE_ZeroScale)
            outAnnoscale /= fabs (nonAnnoScale.x);

        outScale = nonAnnoScale;
        return  true;
        }

    outScale = overallScale;
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            CanUseAnnotationScale (AcDbBlockReference* acRef, AcDbBlockTableRecord* acBlock, ReferenceFileElm* refData, ConvertToDgnContextR context) const
    {
    if (acRef->ownerId() != acdbSymUtil()->blockModelSpaceId(acRef->database()))
        return  false;

    /*-----------------------------------------------------------------------------------------------------------
    Like it does to viewport, ACAD also compounds xRef geometry scale to annotation scales, whereas MicroStation 
    applies only the annotation scale to annotative elements in a reference file.  So only when the effective 
    reference scale=1 we can display annotative elements correctly.  This means that when geometry/user scale is
    anything other than 1:1, we must turn annotation scale off for the DgnAttachment.

    The INSUNITS stored in a block can become out of sync with the xRef.  Ideally we'd check ref file storage units
    but loading an xRef or a ref file just to find its storage units is too big of a performance penalty.  We opt 
    in to set the flag based on existing block data and will let DWG file handler to check the DgnAttachment after 
    all files created.  The only drawback of this approach is that there may be some DgnAttachments that otherwise 
    can use but now be declined of annotation scales based on out of sync block units, although the display of the 
    xRef still should be correct.  On a plus side, a DwgPlatform host that does not have a file handler can benefit 
    from us setting the flag here.
    -----------------------------------------------------------------------------------------------------------*/
    StandardUnit    blockUnits = RealDwgUtil::DgnUnitsFromAcDbUnitsValue (acBlock->blockInsertUnits());
    double          geometryScale = 1.0;

    if (BSISUCCESS == context.GetTargetUnits().GetConversionFactorFrom(geometryScale, UnitDefinition::GetStandardUnit(blockUnits)) && geometryScale > TOLERANCE_ZeroScale)
        geometryScale = refData->scale / geometryScale;
    else    
        geometryScale = refData->scale;

    return  fabs(geometryScale - 1.0) < TOLERANCE_ScaleRatio;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsXRefInBlock (AcDbBlockReference*  pBlockReference, AcDbBlockTableRecord* pBlockTR) const
    {
    if (!pBlockTR->isFromExternalReference())
        return false;

    if (pBlockReference->blockId().isNull())
        return false;

    AcDbBlockTableRecordPointer  pOwnerBlock (pBlockReference->blockId(), AcDb::kForRead);
    if (Acad::eOk != pOwnerBlock.openStatus())
        return false;

    return !RealDwgUtil::IsModelOrPaperSpace(pOwnerBlock.object());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            GetClipDataFromBlockReference (DgnClipData& clipData, bool isXref, const AcGePoint3d& baseOrigin, AcDbBlockReference* blockReference, ConvertToDgnContextR context) const
    {
    AcDbSpatialFilter*  spatialFilter = this->GetSpatialFilter (blockReference, AcDb::kForRead);
    if (NULL == spatialFilter)
        return;

    double      diagonalSize = 0.0;
    AcDbExtents extents;
    if (!isXref && Acad::eOk == blockReference->getGeomExtents(extents))
        diagonalSize = extents.minPoint().distanceTo (extents.maxPoint());
 
    this->GetClipBoundaryFromSpatialFilter (clipData, isXref, diagonalSize, spatialFilter);

    size_t      nPoints = clipData.m_pointArray.size ();
    if (nPoints > 0)
        {
        if (!isXref)
            {
            DPoint3d    insertionPoint;
            clipData.m_origin.subtract (&RealDwgUtil::DPoint3dFromGePoint3d (insertionPoint, baseOrigin));
            }

        context.GetTransformToDGN().Multiply (clipData.m_origin);

        double  scaleToDGN = context.GetScaleToDGN();
        for (size_t i = 0; i < nPoints; i++)
            {
            if (!clipData.m_pointArray[i].IsDisconnect())
                clipData.m_pointArray[i].Scale (scaleToDGN);
            }

        if (false != (clipData.m_frontClipOn = (clipData.m_frontClipZ != ACDB_INFINITE_XCLIP_DEPTH)))
            clipData.m_frontClipZ *= scaleToDGN;

        if (false != (clipData.m_backClipOn = (clipData.m_backClipZ != ACDB_INFINITE_XCLIP_DEPTH)))
            clipData.m_backClipZ *= scaleToDGN;
        }

    spatialFilter->close();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
void             GetClipBoundaryFromSpatialFilter
(
DgnClipData&                clipData,
bool                        isXref,
double                      diagonalSize,
AcDbSpatialFilter*          spatialFilter
) const
    {
    Adesk::Boolean          enabled = false;
    double                  elevation = 0.0;
    AcGePoint2dArray        filterPoints;
    AcGeVector3d            normal;

    spatialFilter->getDefinition (filterPoints, normal, elevation, clipData.m_frontClipZ, clipData.m_backClipZ, enabled);

    if (!enabled)
        return;

    AcGeMatrix3d            inverseBlockMatrix;
    spatialFilter->getOriginalInverseBlockXform (inverseBlockMatrix);

    AcGeMatrix3d            clipBoundMatrix;
    spatialFilter->getClipSpaceToWCSMatrix (clipBoundMatrix);

    // The spatial filter seems to work with transpose of the actual matrix... not sure why.
    Transform               worldToBlock;
    RealDwgUtil::TransformFromGeMatrix3d (worldToBlock, inverseBlockMatrix);

    Transform               clipToWorld;
    RealDwgUtil::TransformFromGeMatrix3d (clipToWorld, clipBoundMatrix);

    Transform               clipToBlock = Transform::FromProduct (worldToBlock, clipToWorld);

    clipToBlock.GetTranslation (clipData.m_origin);
    clipToBlock.GetMatrix (clipData.m_matrix);

    ToDgnExtBlockReference::GetClippingPointsFromFilterPoints (clipData.m_pointArray, filterPoints);

    this->ValidateInvertedClippingPoints (clipData.m_pointArray, isXref, diagonalSize, spatialFilter);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ValidateInvertedClippingPoints (DPoint2dArrayR boundaryPoints, bool isXref, double diagonalSize, AcDbSpatialFilter* spatialFilter) const
    {
    if (!spatialFilter->isInverted())
        return  false;

    /*-------------------------------------------------------------------------------------------------------
    This is an inverted xclip.  We need to make the DGN clip of a mask clipping.  For a reference attachment,
    we just add a connect point at the top of the clipping points.  For a shared cell, we have to create a
    large enough rectangular outer clipping boundary, then add the connecting point, followed by the mask 
    clipping points.
    -------------------------------------------------------------------------------------------------------*/
    DPoint2dArray::iterator     holeStartAt = boundaryPoints.begin ();

    if (!isXref && diagonalSize > TOLERANCE_ZeroSize)
        {
        // this is a shared cell clipping - need a large rectangle shape as the outer clipping boundary
        DRange2d        range = DRange2d::From (boundaryPoints);
        double          largeSize = 2.0 * diagonalSize;
        DPoint2dArray   outerBox (5);

        outerBox[0].x = outerBox[3].x = range.low.x  - largeSize;
        outerBox[1].x = outerBox[2].x = range.high.x + largeSize;
        outerBox[0].y = outerBox[1].y = range.low.y  - largeSize;
        outerBox[2].y = outerBox[3].y = range.high.y + largeSize;
        outerBox[4] = outerBox[0];

        // insert the large rectangle shape at the start of the clipping boundary
        boundaryPoints.insert (holeStartAt, outerBox.begin(), outerBox.end());

        holeStartAt = boundaryPoints.begin() + outerBox.size ();
        }

    // insert the connect point at the top if it's an xref, or after the large rectangle shape if it's a shared cell
    DPoint2d    connectPoint;
    connectPoint.InitDisconnect ();
    boundaryPoints.insert (holeStartAt, connectPoint);

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus                   BlockTableRecordToCellElement
(
EditElementHandleR              outElement,
AcDbBlockTableRecord*           pBlockTR,
AcDbBlockReference*             pBlockReference,
DPoint3dR                       origin,
RotMatrixCR                     matrix,
AcString                        cellName,
ConvertToDgnContextR            context
) const
    {
    AcDbBlockTableRecordIterator*     pEntIter;
    if (Acad::eOk != pBlockTR->newIterator (pEntIter, true, true))
        {
        DIAGNOSTIC_PRINTF ("Block iterator failed on block ID=%I64d, name=%ls!\n", context.ElementIdFromObject(pBlockTR), cellName.kwszPtr());
        return  BlockIteratorFailure;
        }
    
    WChar             wChars[MAX_CELLNAME_LENGTH];
    if (0 != cellName.compare(NULLCELLNAME))
        RealDwgUtil::AcStringToMSWChar (wChars, cellName, MAX_CELLNAME_LENGTH);
    else
        wChars[0] = 0;

    // create an orphan cell header and do not transform the header yet - it will be done after added children.
    NormalCellHeaderHandler::CreateOrphanCellElement (outElement, wChars, context.GetThreeD(), *context.GetModel());

    MSElementDescrP cellHeader = outElement.GetElementDescrP ();
    MSElementDescrP lastChild = NULL;
    if (NULL == cellHeader)
        return  CantCreateCell;

    for (; !pEntIter->done(); pEntIter->step())
        {
        AcDbEntity*     pEntity = NULL;
        if (Acad::eOk != pEntIter->getEntity (pEntity, AcDb::kForRead))
            continue;

        if (RealDwgSuccess != ToDgnExtBlock::SaveAndAddEntityToCellDefinition(cellHeader, &lastChild, pEntity, context))
            DIAGNOSTIC_PRINTF ("Failed adding child element ID=%I64d to type 2 cell name=%ls!\n", context.ElementIdFromObject(pBlockTR), cellName.kwszPtr());

        pEntity->close ();
        }

    delete pEntIter;

    // apply transform altogether
    Transform           transform;
    transform.InitFrom (matrix, origin);
    if (!transform.IsIdentity())
        outElement.GetHandler(MISSING_HANDLER_PERMISSION_Transform).ApplyTransform (outElement, TransformInfo(transform));

    if (context.GetSettings().AttributesAsTags())
        {
        // process tags on block table record - these were not checked out for application cells causing TR# 147391
        MSElementDescrP     tagsInCell = NULL;
        if (RealDwgSuccess == context.SaveBlockAttrdefsToDgn(&tagsInCell, pBlockTR) && NULL != tagsInCell)
            outElement.GetElementDescrP()->AppendChild (&lastChild, tagsInCell);
        }
    else
        {
        // save attribute definitions to item type def's:
        context.SaveBlockAttrdefsAsItemTypedefs (pBlockTR);
        }

    // set description
    const ACHAR*    comments = nullptr;
    if (Acad::eOk == pBlockTR->comments(comments) && nullptr != comments && 0 != comments[0])
        NormalCellHeaderHandler::SetDescription (outElement, comments);
    
    NormalCellHeaderHandler::AddChildComplete (outElement);

    return  RealDwgSuccess;
    }


public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbSpatialFilter*       GetSpatialFilter (AcDbBlockReference* pBlockReference, AcDb::OpenMode mode)
    {
    AcDbFilter*         pFilter;
    Acad::ErrorStatus   status;
    if (Acad::eOk != (status = AcDbIndexFilterManager::getFilter (pBlockReference, AcDbSpatialFilter::desc(), mode, pFilter)))
        return  NULL;

    AcDbSpatialFilter*  pSpatialFilter;
    if ((NULL == (pSpatialFilter = AcDbSpatialFilter::cast (pFilter))) )
        {
        pFilter->close();
        return NULL;
        }

    return pSpatialFilter;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
static void     RoundTripDgnSpecificData (ReferenceFileElm* refData, AcDbObjectP acObject, ConvertToDgnContextR context)
    {
    // roundtrip Reference Attachment Method from xdata:
    RealDwgResBuf*  pData = RealDwgResBuf::Create (AcDb::kDxfXdInteger16);

    if (RealDwgXDataUtil::ExtractMicroStationXDataFromObject (*pData, StringConstants::XDataKey_ReferenceAttachMethod, acObject))
        refData->fb_opts.attachMethod = pData->GetInt16();

     // round trip option treatAsElement, but default it to ON, unless overridden with a user config var:
     if (RealDwgXDataUtil::ExtractMicroStationXDataFromObject (*pData, StringConstants::XDataKey_ReferenceTreatedAsElement, acObject))
         refData->fb_opts.treatAsElement = pData->GetInt16();
     else
         refData->fb_opts.treatAsElement = context.GetSettings().GetDefaultXrefTreatAsElement ();

    RealDwgResBuf::Free (pData);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
static void     GetClippingPointsFromFilterPoints (DPoint2dArrayR boundaryPoints, const AcGePoint2dArray& filterPoints)
    {
    int         nFilterPoints   = filterPoints.length ();

    if (2 == nFilterPoints)
        {
        DPoint2d            clipPoints[5];
        // Rectangle.
        clipPoints[0].x = clipPoints[3].x = filterPoints[0].x;
        clipPoints[1].x = clipPoints[2].x = filterPoints[1].x;
        clipPoints[0].y = clipPoints[1].y = filterPoints[0].y;
        clipPoints[2].y = clipPoints[3].y = filterPoints[1].y;
        clipPoints[4] = clipPoints[0];

        for (int iPoint=0; iPoint < 5; iPoint++)
            boundaryPoints.push_back (clipPoints[iPoint]);
        }
    else if (nFilterPoints > 0)
        {
        int             nTotal = nFilterPoints + ((filterPoints[0] == filterPoints[nFilterPoints-1]) ? 0 : 1);
        // Shape.
        for (int iPoint=0; iPoint<nTotal; iPoint++)
            {
            DPoint2d        point;

            point.x = filterPoints[iPoint % nFilterPoints].x;
            point.y = filterPoints[iPoint % nFilterPoints].y;
            boundaryPoints.push_back (point);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SetElementSizeWithClippingPoints (ReferenceFileElm* refData, DPoint2dCP pClipPoints, UInt16 nClipPoints)
    {
    refData->nClipPoints = nClipPoints;

    size_t      dataSize = offsetof (ReferenceFileElm, clipPoints[0]);

    refData->ehdr.elementSize = refData->ehdr.attrOffset = (UInt32)(dataSize + nClipPoints * sizeof(DPoint2d))/2;

    if (0 != nClipPoints && NULL != pClipPoints && refData->ehdr.elementSize < MAX_ELEMENT_WORDS)
        memcpy (&refData->clipPoints[0], pClipPoints, nClipPoints * sizeof(DPoint2d));
    }


};  // ToDgnExtBlockReference

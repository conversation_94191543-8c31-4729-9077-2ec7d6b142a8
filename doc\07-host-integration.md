# Host Integration

## Overview

The RealDwgFileIO framework integrates with the Bentley MicroStation platform through a sophisticated host system that provides platform-specific services, progress reporting, error handling, and application integration. The host system bridges the gap between the RealDWG library and the MicroStation environment.

## Host Architecture

### Host Class Hierarchy

```
DwgPlatformHost (Base)
    ├─ DwgMstnHost (MicroStation-specific)
    └─ DwgExampleHost (Example implementation)

Session Management
    ├─ MasterDwgSessionMonitor
    └─ DwgFileHandler
```

## Platform Host Implementation

### DwgPlatformHost

**File**: `DwgPlatformHost.cpp`

Base class providing core host services for the RealDWG library:

```cpp
class DwgPlatformHost : public AcDbHostApplicationServices
{
protected:
    DgnFileP m_dgnFile;
    AcDbProgressMeter* m_progressMeter;
    bool m_doNothingProgressMeter;

public:
    // Core host services
    virtual AcDbDatabase* workingDatabase() override;
    virtual void setWorkingDatabase(AcDbDatabase* pDatabase) override;
    
    // Progress reporting
    virtual AcDbProgressMeter* newProgressMeter() override;
    virtual void setLimit(int max) override;
    virtual void meterProgress() override;
    
    // Error handling
    virtual void warning(const ACHAR* message) override;
    virtual void auditPrintReport(AcAuditInfo* pAuditInfo, 
                                 const ACHAR* line, int both) override;
    
    // File operations
    virtual Acad::ErrorStatus findFile(ACHAR* pcFullPathOut, 
                                      int nBufferLength,
                                      const ACHAR* pcFilename,
                                      AcDbDatabase* pDb = NULL,
                                      AcDbHostApplicationServices::FindFileHint hint = kDefault) override;
};
```

### Key Host Services

#### Progress Reporting

```cpp
AcDbProgressMeter* DwgPlatformHost::newProgressMeter()
{
    if (m_doNothingProgressMeter)
        return new DoNothingProgressMeter();
    
    // Create platform-specific progress meter
    return NewProgressMeter(m_dgnFile, 0.0, 1.0);
}

void DwgPlatformHost::meterProgress()
{
    if (m_progressMeter) {
        m_progressMeter->meterProgress();
        
        // Check for user cancellation
        if (mdlSystem_getCancelFlag()) {
            throw UserCancelledException();
        }
    }
}
```

#### File Path Resolution

```cpp
Acad::ErrorStatus DwgPlatformHost::findFile(
    ACHAR* pcFullPathOut,
    int nBufferLength,
    const ACHAR* pcFilename,
    AcDbDatabase* pDb,
    FindFileHint hint)
{
    // Search in standard MicroStation paths
    WString fullPath;
    
    switch (hint) {
        case kFontFile:
            if (FindFontFile(pcFilename, fullPath))
                break;
            return Acad::eFileNotFound;
            
        case kCompiledShapeFile:
            if (FindShapeFile(pcFilename, fullPath))
                break;
            return Acad::eFileNotFound;
            
        case kTrueTypeFontFile:
            if (FindTrueTypeFont(pcFilename, fullPath))
                break;
            return Acad::eFileNotFound;
            
        case kPatternFile:
            if (FindPatternFile(pcFilename, fullPath))
                break;
            return Acad::eFileNotFound;
            
        default:
            if (FindGenericFile(pcFilename, fullPath))
                break;
            return Acad::eFileNotFound;
    }
    
    // Copy result to output buffer
    wcscpy_s(pcFullPathOut, nBufferLength, fullPath.c_str());
    return Acad::eOk;
}
```

## MicroStation Host Implementation

### DwgMstnHost

**File**: `filehandler/DwgMstnHost.h/.cpp`

MicroStation-specific host implementation:

```cpp
class DwgMstnHost : public DwgPlatformHost
{
private:
    AcDbDatabase* m_masterDwgDatabase;
    DgnFileP m_masterDgnFile;
    
public:
    // MicroStation-specific services
    void _SetMasterDwgFile(AcDbDatabase* pDatabase, DgnFileP pDgnFile);
    bool _GetMasterDwgFile(AcDbDatabase*& pDatabase, DgnFileP* ppDgnFile);
    
    // Override base services for MicroStation integration
    virtual void warning(const ACHAR* message) override;
    virtual void auditPrintReport(AcAuditInfo* pAuditInfo, 
                                 const ACHAR* line, int both) override;
    
    // Font and file resolution
    virtual Acad::ErrorStatus findFile(ACHAR* pcFullPathOut, 
                                      int nBufferLength,
                                      const ACHAR* pcFilename,
                                      AcDbDatabase* pDb = NULL,
                                      FindFileHint hint = kDefault) override;
};
```

### Master File Management

```cpp
void DwgMstnHost::_SetMasterDwgFile(
    AcDbDatabase* pDatabase, 
    DgnFileP pDgnFile)
{
    m_masterDwgDatabase = pDatabase;
    m_masterDgnFile = pDgnFile;
    
    // Update working database
    if (pDatabase) {
        setWorkingDatabase(pDatabase);
    }
}

bool DwgMstnHost::_GetMasterDwgFile(
    AcDbDatabase*& pDatabase, 
    DgnFileP* ppDgnFile)
{
    pDatabase = m_masterDwgDatabase;
    if (ppDgnFile) {
        *ppDgnFile = m_masterDgnFile;
    }
    
    return (m_masterDwgDatabase != nullptr);
}
```

## Session Management

### MasterDwgSessionMonitor

**File**: `filehandler/DwgFileHandler.cpp`

Monitors MicroStation session events for DWG file management:

```cpp
struct MasterDwgSessionMonitor : public Bentley::MstnPlatform::SessionMonitor
{
    virtual void _OnMasterFileStart(DgnFileR dgnFile) override
    {
        DgnFileFormatType format = DgnFileFormatType::Unknown;
        
        // Check if this is a DWG/DXF file
        if (BSISUCCESS == dgnFile.GetVersion(&format, nullptr, nullptr) && 
            (DgnFileFormatType::DWG == format || DgnFileFormatType::DXF == format))
        {
            // Get the DWG default model
            DgnModelP defaultModel = dgnFile.FindLoadedModelById(
                dgnFile.GetDefaultModelId());
            
            // Set up DWG host for this file
            if (g_dwgMstnHost && defaultModel) {
                RealDwgFileIO* dwgFileIO = 
                    dynamic_cast<RealDwgFileIO*>(dgnFile.GetFileIO());
                if (dwgFileIO) {
                    FileHolder* fileHolder = dwgFileIO->GetDwgFileHolder();
                    if (fileHolder) {
                        AcDbDatabase* database = fileHolder->GetDatabase();
                        g_dwgMstnHost->_SetMasterDwgFile(database, &dgnFile);
                    }
                }
            }
        }
    }
    
    virtual void _OnMasterFileStop(DgnFileR dgnFile, bool changingFiles) override
    {
        if (g_dwgMstnHost) {
            // Clear master file reference
            bool clearMaster = true;
            AcDbDatabase* masterDwg = nullptr;
            DgnFileP masterDgn = nullptr;
            
            if (changingFiles && 
                g_dwgMstnHost->_GetMasterDwgFile(masterDwg, &masterDgn) && 
                masterDgn != &dgnFile) {
                clearMaster = false;
            }
            
            if (clearMaster) {
                g_dwgMstnHost->_SetMasterDwgFile(nullptr, &dgnFile);
            }
        }
    }
};
```

## File Handler Module

### DwgFileHandler

**File**: `filehandler/DwgFileHandler.cpp`

Main module entry point for the file handler:

```cpp
extern "C" DLLEXPORT int MdlMain(int argc, WChar* argv[])
{
    // Initialize the DWG host
    g_dwgMstnHost = new DwgFileHandler::DwgMstnHost();
    
    // Register session monitor
    static MasterDwgSessionMonitor s_sessionMonitor;
    MstnPlatform::GetSessionManagerP()->AddMonitor(s_sessionMonitor);
    
    // Initialize RealDWG
    RealDwgFileIO::Initialize();
    
    return SUCCESS;
}

extern "C" DLLEXPORT int MdlUnload()
{
    // Cleanup
    if (g_dwgMstnHost) {
        delete g_dwgMstnHost;
        g_dwgMstnHost = nullptr;
    }
    
    return SUCCESS;
}
```

## Progress Reporting System

### Progress Meter Implementation

```cpp
class MstnProgressMeter : public AcDbProgressMeter
{
private:
    DgnFileP m_dgnFile;
    double m_startFraction;
    double m_endFraction;
    int m_currentStep;
    int m_totalSteps;
    WString m_currentMessage;

public:
    MstnProgressMeter(DgnFileP dgnFile, double startFraction, double endFraction)
        : m_dgnFile(dgnFile)
        , m_startFraction(startFraction)
        , m_endFraction(endFraction)
        , m_currentStep(0)
        , m_totalSteps(100)
    {
    }
    
    virtual void start(const ACHAR* displayString = NULL) override
    {
        if (displayString) {
            m_currentMessage = displayString;
        }
        
        // Initialize MicroStation progress indicator
        mdlDialog_progressBarOpen(m_currentMessage.c_str(), TRUE);
    }
    
    virtual void stop() override
    {
        // Close MicroStation progress indicator
        mdlDialog_progressBarClose();
    }
    
    virtual void meterProgress() override
    {
        m_currentStep++;
        
        // Calculate progress percentage
        double progress = m_startFraction + 
            (m_endFraction - m_startFraction) * m_currentStep / m_totalSteps;
        
        // Update MicroStation progress bar
        mdlDialog_progressBarSetPos((int)(progress * 100));
        
        // Process pending messages
        mdlSystem_processEvents();
        
        // Check for cancellation
        if (mdlSystem_getCancelFlag()) {
            throw UserCancelledException();
        }
    }
    
    virtual void setLimit(int max) override
    {
        m_totalSteps = max;
        m_currentStep = 0;
    }
};
```

### Progress Meter Factory

```cpp
AcDbProgressMeter* DwgPlatformHost::NewProgressMeter(
    DgnFileP dgnFile,
    double startFraction,
    double endFraction)
{
    if (m_doNothingProgressMeter) {
        return new DoNothingProgressMeter();
    }
    
    return new MstnProgressMeter(dgnFile, startFraction, endFraction);
}

void DwgPlatformHost::SetProgressMeterLimits(
    AcDbProgressMeter* progressMeter,
    double lowFraction,
    double highFraction,
    int approxNumObjects)
{
    if (progressMeter) {
        progressMeter->setLimit(approxNumObjects);
        
        // Update fraction range if it's our custom meter
        MstnProgressMeter* mstnMeter = 
            dynamic_cast<MstnProgressMeter*>(progressMeter);
        if (mstnMeter) {
            mstnMeter->SetFractionRange(lowFraction, highFraction);
        }
    }
}
```

## Error Handling and Logging

### Warning System

```cpp
void DwgMstnHost::warning(const ACHAR* message)
{
    // Convert to wide string
    WString wideMessage = message;
    
    // Log to MicroStation message center
    mdlOutput_messageCenter(OUTPUTMESSAGE_WARNING, 
                           wideMessage.c_str(), 
                           wideMessage.c_str(), 
                           MESSAGELISTTYPE_USER);
    
    // Also log to debug output
    mdlOutput_printf(L"DWG Warning: %ls\n", wideMessage.c_str());
}

void DwgMstnHost::auditPrintReport(
    AcAuditInfo* pAuditInfo,
    const ACHAR* line,
    int both)
{
    WString auditMessage = line;
    
    // Determine message type based on audit info
    MessageListType messageType = MESSAGELISTTYPE_USER;
    if (pAuditInfo && pAuditInfo->fixErrors()) {
        messageType = MESSAGELISTTYPE_ERROR;
    }
    
    // Log audit message
    mdlOutput_messageCenter(OUTPUTMESSAGE_INFO,
                           auditMessage.c_str(),
                           auditMessage.c_str(),
                           messageType);
}
```

## Resource Management

### Font File Resolution

```cpp
bool DwgMstnHost::FindFontFile(WCharCP fontName, WStringR fullPath)
{
    // Search MicroStation font directories
    WString fontPath;
    
    // Try TrueType fonts first
    if (FindTrueTypeFont(fontName, fontPath)) {
        fullPath = fontPath;
        return true;
    }
    
    // Try SHX fonts
    WString shxName = fontName;
    if (!shxName.EndsWithI(L".shx")) {
        shxName += L".shx";
    }
    
    if (mdlResource_findFile(fontPath, shxName.c_str(), 
                            MSRSRC_FONT, TRUE) == SUCCESS) {
        fullPath = fontPath;
        return true;
    }
    
    return false;
}
```

### Configuration Integration

```cpp
void DwgMstnHost::LoadConfiguration()
{
    // Load DWG-specific configuration variables
    ConfigurationManager& config = ConfigurationManager::GetManager();
    
    // Progress reporting settings
    m_doNothingProgressMeter = config.GetBooleanValue(
        L"MS_DWG_DISABLE_PROGRESS", false);
    
    // Error handling settings
    bool logWarnings = config.GetBooleanValue(
        L"MS_DWG_LOG_WARNINGS", true);
    
    // Performance settings
    bool enableMultiProcessing = config.GetBooleanValue(
        L"MS_DWG_SAVE_MULTIPROCESSING", false);
}
```

## Integration Points

### MicroStation API Integration

- **Message Center**: Error and warning reporting
- **Progress Dialogs**: User feedback during operations
- **Resource System**: Font and file path resolution
- **Configuration**: Settings and preferences
- **Session Management**: File lifecycle events

### Platform Services

- **Memory Management**: Integration with MicroStation memory pools
- **Threading**: Coordination with MicroStation's threading model
- **Event System**: Integration with MicroStation's event handling
- **Localization**: Support for internationalized messages

This host integration system provides seamless integration between the RealDWG library and the MicroStation platform, ensuring that DWG/DXF file operations feel native to MicroStation users while maintaining the full functionality of the AutoCAD format support.

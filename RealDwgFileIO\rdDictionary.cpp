/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdDictionary.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtDictionary : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbDictionary*     pDictionary = AcDbDictionary::cast (acObject);
    AcDbObjectId        dictionaryId = pDictionary->objectId ();
    MSElementDescrP     pDescr = NULL;
    ElementId           defaultElementId = 0;
    bool                isMainDictionary = context.GetFileHolder().GetDatabase()->namedObjectsDictionaryId() == dictionaryId;

    // We only save dictionaries we want to DGN cache. At this time, only plot style name dictionary is saved.
    if (!isMainDictionary && context.GetPlotStyleNameDictionaryId() != dictionaryId)
        return  DwgObjectUnsupported;

    AcDbDictionaryWithDefault* pDictionaryWithDefault;
    if (NULL != (pDictionaryWithDefault = AcDbDictionaryWithDefault::cast (acObject)))
        defaultElementId = context.ElementIdFromObjectId (pDictionaryWithDefault->defaultId());

    // Ignore spurious groups - File from Gary Kirk with 300,000+ groups wouldn't open.
    UInt32              entryLimit = context.GetSettings().GetMaxDictionaryItems ();
    if (pDictionary->numEntries() > entryLimit)
        {
        DIAGNOSTIC_PRINTF ("Excessive dictionary items %d (> %d) ignored.\n", pDictionary->numEntries(), entryLimit);
        return TooManyGroups;
        }

    if (SUCCESS == DgnTableUtilities::CreateTable (&pDescr, MS_DICTIONARY_TABLE_LEVEL, sizeof(DictionaryTableElm), context.ElementIdFromObject(pDictionary)))
        {
        pDescr->el.dictionaryTable.ownerFlags = 0;
        pDescr->el.dictionaryTable.cloneFlags = 0;
        pDescr->el.dictionaryTable.defaultId = defaultElementId;

        AcDbDictionaryIterator* pIterator = pDictionary->newIterator ();
        for (; !pIterator->done(); pIterator->next())
            {
            WString entryName(pIterator->name());

            // in the main dictionary, only keep the plot style entry:
            if (isMainDictionary && 0 != entryName.compare(ACDB_PLOTSTYLENAME_DICTIONARY))
                continue;

            // apply dictionary wildcard filters if user has supplied them to us:
            if (ULONG_MAX == entryLimit && !RealDwgUtil::MatchStringFilters(entryName, context.GetSettings().GetDictionaryWildcardFilters()))
                continue;

            dictionaryId = pIterator->objectId ();

            /*--------------------------------------------------------------------------------------------------
            If a child dictionary has grandchildren, use a new entry ID for the child and set the original ID
            as the lookup ID, which can be used to find the table element created from this child dictionary next
            time around.  If this child has no children, i.e. the last generation in the dictionary hierarchy,
            we will never create a new table from this child, therefore there is no need to increase element ID.  
            We directly set the entry ID from the original child dictionary ID, and do not set lookup ID, which
            can be obtained via the defaultElementId set in the table header.

            This is also partly in an effort to ensure valid roots to be set on the dictionary entry element, to 
            prevent the dependency manager from attempting to resolve invalid roots at the end of a file open.  
            Resolving roots can unnecessarily trigger off some apps(e.g. NamedRegion::Create) to add new elements 
            in a DgnFile, resulting in a state of "unsaved changes" in a DgnFile right after opening a DWG file.
            --------------------------------------------------------------------------------------------------*/
            ElementId       lookupId, entryId;
            if (this->HasChildren(dictionaryId))
                {
                lookupId = context.ElementIdFromObjectId (dictionaryId);
                entryId = context.GetAndIncrementNextId ();
                }
            else
                {
                lookupId = 0;
                entryId = context.ElementIdFromObjectId (dictionaryId);
                }

            this->AppendDictionaryEntry (pDescr, entryName, lookupId, entryId);
            }
        delete pIterator;

        if (NULL == pDescr->h.firstElem)
            pDescr->Release ();
        else
            outElement.SetElementDescr (pDescr, true, false, &context.GetFile()->GetDictionaryModel());
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool            HasChildren (const AcDbObjectId& dictionaryId) const
    {
    // check if the input dictionary is a parent of sub-dictionaries:
    AcDbDictionaryPointer   parent (dictionaryId, AcDb::kForRead);
    if (Acad::eOk == parent.openStatus())
        {
        AcDbDictionaryIterator*     iterator = parent->newIterator ();
        for (; !iterator->done(); iterator->next())
            {
            if (iterator->objectId().isValid())
                return  true;
            }
        }
    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
void            AppendDictionaryEntry (MSElementDescrP tableDescr, WStringCR itemName, ElementId itemId, ElementId entryId) const
    {
    size_t      size = sizeof (DictionaryEntryElm);
    MSElement   element;
    memset(&element, 0, size);
    
    element.ehdr.type = TABLE_ENTRY_ELM;
    element.ehdr.level = MS_DICTIONARY_TABLE_LEVEL;
    element.ehdr.nonModel = true;
    element.ehdr.elementSize = static_cast <int> (size/2);
    element.ehdr.attrOffset = static_cast <UInt32> (size/2);
    element.ehdr.uniqueId = entryId;

    if (0 != itemId)
        mdlLinkage_setElementIds (&element, &itemId, 1, DEPENDENCYAPPID_DictionaryEntry, 0);
    LinkageUtil::SetStringLinkage (&element, STRING_LINKAGE_KEY_Name, itemName.c_str());

    tableDescr->AppendElement (element);
    }

};


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       ConvertToDgnContext::SaveDictionaryApplicationElementsToDgn ()
    {
    // type-66 and type-39 elements originally saved from the DGN Dictionary model
    AcDbObjectId    msDictionaryId = this->GetMicroStationDictionaryId (false);
    if (!msDictionaryId.isValid())
        return BSIERROR;

    StatusInt               status = BSIERROR;
    AcDbDictionaryPointer   acMicroStationDictionary (msDictionaryId, AcDb::kForRead);
    if (Acad::eOk != acMicroStationDictionary.openStatus())
        return status;

    // these are the elements we saved under MicroStation Dictionary:
    size_t          nCharsModelApp = wcslen (StringConstants::UstnDictionaryItem_Application);
    size_t          nCharsDgnStore = wcslen (StringConstants::UstnDictionaryItem_DgnStore);
    size_t          nCharsNonGraphics = wcslen (StringConstants::UstnDictionaryItem_ExtendedNonGraphics);
    
    AcDbDictionaryIterator* iterator = acMicroStationDictionary->newIterator ();
    for ( ; !iterator->done(); iterator->next())
        {
        const ACHAR*    name = iterator->name ();
        if (nullptr == name)
            continue;

        if (0 == wcsncmp (name, StringConstants::UstnDictionaryItem_Application, nCharsModelApp) ||
            0 == wcsncmp (name, StringConstants::UstnDictionaryItem_ExtendedNonGraphics, nCharsNonGraphics) ||
            0 == wcsncmp (name, StringConstants::UstnDictionaryItem_DgnStore, nCharsDgnStore))
            {
            this->LoadObjectIntoCache (iterator->objectId());
            status = SUCCESS;
            }
        }
    delete iterator;
    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       ConvertToDgnContext::SaveModelApplicationElementsToDgn (int modelId)
    {
    // type-66 elements originally saved from a DgnModel and DgnModels themselves
    AcDbObjectId    msDictionaryId = this->GetMicroStationDictionaryId (false);
    if (!msDictionaryId.isValid())
        return BSIERROR;

    StatusInt       status = BSIERROR;
    size_t          nCharsType66 = wcslen (StringConstants::UstnDictionaryItem_ModelApplication);
    size_t          nCharsModel  = wcslen (StringConstants::UstnDictionaryItem_DgnModel);

    AcDbDictionaryPointer   acMicroStationDictionary (msDictionaryId, AcDb::kForRead);
    if (Acad::eOk != acMicroStationDictionary.openStatus())
        return status;

    AcDbDictionaryIterator* iterator = acMicroStationDictionary->newIterator ();
    for (; !iterator->done(); iterator->next())
        {
        const ACHAR*        entryName = iterator->name ();
        WCharP              endChar = nullptr;
        int                 entryModelId = 0;

        if (0 == wcsncmp(entryName, StringConstants::UstnDictionaryItem_ModelApplication, nCharsType66))
            {
            // a type-66 element
            entryModelId = BeStringUtilities::Wcstol (&entryName[nCharsType66], &endChar, 10);

            if (entryModelId == modelId)
                {
                this->LoadObjectIntoCache (iterator->objectId());
                status = BSISUCCESS;
                }
            }
        else if (0 == wcsncmp(entryName, StringConstants::UstnDictionaryItem_DgnModel, nCharsModel))
            {
            // a DgnModel dictionary containing specific DGN elements
            WString         idString = WString(entryName).substr (nCharsModel);

            idString.substr (0, idString.find(L" "));

            entryModelId = BeStringUtilities::Wcstol (idString.c_str(), &endChar, 10);

            if (entryModelId == modelId)
                {
                AcDbDictionaryPointer   dgnModelDictionary(iterator->objectId(), AcDb::kForRead);
                if (Acad::eOk == dgnModelDictionary.openStatus())
                    {
                    // load all children of the DgnModel dictionary into the model:
                    AcDbDictionaryIterator*     childIter = dgnModelDictionary->newIterator ();
                    for (; !childIter->done(); childIter->next())
                        this->LoadObjectIntoCache (childIter->objectId());

                    delete childIter;
                    }
                }
            }
        }

    delete iterator;
    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       ConvertToDgnContext::SaveHeaderElementToDgn ()
    {
    // type-9 element originally saved from TCB header
    StatusInt           status = BSIERROR;
    AcDbObjectId        msDictionaryId;
    if (!(msDictionaryId = this->GetMicroStationDictionaryId (false)).isNull())
        {
        AcDbDictionaryPointer   acMicroStationDictionary (msDictionaryId, AcDb::kForRead);
        if (Acad::eOk == acMicroStationDictionary.openStatus())
            {
            AcDbObjectId        savedHeaderId;
            if ( (Acad::eOk == acMicroStationDictionary->getAt (StringConstants::UstnDictionaryItem_Header, savedHeaderId)) && !savedHeaderId.isNull() )
                {
                AcDbObjectPointer<AcDbObject>   headerObject (savedHeaderId, AcDb::kForRead);
                EditElementHandle               eeh;
                if ( (Acad::eOk == headerObject.openStatus()) && (RealDwgSuccess == this->ElementFromObject (eeh, headerObject)) )
                    {
                    if (DGNFIL_HEADER_ELM == eeh.GetElementType())
                        {
                        /*---------------------------------------------------------------------------------------------
                        I have removed below call as it should have never worked anyway, because tcb::graphic is not a
                        persistent tcb variable in Dgn_header.  This call appeared to be added as part of a fix for TR 
                        157523, but its test cases do not support the need of tcb->graphic here.  Tag groups are actually
                        maintained via GetAndAppendTagsToCellInstance.

                        m_nextGraphicGroup = ((Tcb *) ((Dgn_header *) eeh.GetElementCP())->tcbinfo)->graphic;
                        ---------------------------------------------------------------------------------------------*/
                        this->LoadElementIntoCache (eeh);
                        status = BSISUCCESS;
                        }
                    }
                }
            }
        }

    if (BSISUCCESS != status)
        {
        // Graphic group is not in persistent portion of type 9 element - get it from seed model:
        m_nextGraphicGroup = m_dgnFile->GetNextGraphicGroup ();

        MSElementDescrP     pDescr = NULL;
        MSElementDescrCP    oldDescr = m_pFileHolder->GetHeaderDescr ();
        if (NULL == oldDescr || BSISUCCESS != oldDescr->Duplicate(&pDescr))
            return  OutOfMemoryError;

        EditElementHandle eeh (pDescr, true, false);
        this->LoadElementIntoCache (eeh);
        }

    return BSISUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Chris.Wu      05/2021
+---------------+---------------+---------------+---------------+---------------+------*/
void    ConvertToDgnContext::SaveGeoDataToDgn()
    {
    /*AcDbGeoData, available since 2009*/
    int  acadVersion = RealDwgUtil::MSVersionFromAcVersion(this->GetDatabase()->originalFileVersion());
    if (acadVersion <= DwgFileVersion_2010)
        return;

    AcDbObjectId geoId;
    acdbGetGeoDataObjId(this->GetDatabase(), geoId);

    if (geoId.isNull())
        return;

    AcDbObjectPointer<AcDbGeoData> pGeoData;
    pGeoData.open(geoId, AcDb::kForRead);

    AcString csId(pGeoData->coordinateSystem());

    Acad::ErrorStatus es;
    AcDbGeoCoordinateSystem* pGeoCS;
    es = AcDbGeoCoordinateSystem::create(csId, pGeoCS);

    if (Acad::eOk != es)
        return;

    DgnModelRefP modelRefP = this->GetModel();

    AcString gcsName;
    pGeoCS->getId(gcsName);

    /*Firstly, using GCS name to create the DgnGCS*/
    GeoCoordinates::DgnGCSPtr mstnGCS = GeoCoordinates::DgnGCS::CreateGCS(WString(gcsName).c_str(), modelRefP);

    /*if using GCS name fails to create dgnGCS, try wkt*/
    if (!mstnGCS.IsValid())
        {
        AcString    gcsWKT;
        pGeoCS->getWktRepresentation(gcsWKT);

        GeoCoordinates::BaseGCSPtr  baseGCS;
        baseGCS = GeoCoordinates::BaseGCS::CreateGCS();

        if (SUCCESS == baseGCS->InitFromWellKnownText(NULL, NULL, GeoCoordinates::BaseGCS::wktFlavorAutodesk, WString(gcsWKT).c_str()))
            mstnGCS = GeoCoordinates::DgnGCS::CreateGCS(baseGCS.get(), modelRefP);
        }

    /*if using wkt fails to create dgnGCS, try xml*/
    if (!mstnGCS.IsValid())
        {
        AcString    gcsXML;
        pGeoCS->getXmlRepresentation(gcsXML);

        GeoCoordinates::BaseGCSPtr  baseGCS;
        baseGCS = GeoCoordinates::BaseGCS::CreateGCS();

        if (SUCCESS == baseGCS->InitFromOSGEOXML(Utf8String(gcsXML).c_str()))
            mstnGCS = GeoCoordinates::DgnGCS::CreateGCS(baseGCS.get(), modelRefP);
        }

    /*If xml also fails, try to use GCS name mapping file*/
    if (!mstnGCS.IsValid())
        {
        WString msGCSName;
        RealDwgUtil::ReadGCSNameFromMappingFile (msGCSName, WString(gcsName), true);
        if (!WString::IsNullOrEmpty (msGCSName.c_str()))
            mstnGCS = GeoCoordinates::DgnGCS::CreateGCS(msGCSName.c_str(), modelRefP);
        }

    if (mstnGCS.IsValid())
        mstnGCS->ToModel(modelRefP, true, true, false, false);

    delete pGeoCS;
    pGeoData.close();
    }


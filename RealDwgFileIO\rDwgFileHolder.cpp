/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgFileHolder.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include    "rDwgInternal.h"
#include    <Mstn\RealDwg\rDwgDefs.h>
#include    "rDwgFileHolder.h"
#include    <Mstn\RealDwg\rDwgUtil.h>

#include    <io.h>
#include    <fcntl.h>

#include    <Bentley\BeTextFile.h>
#include    <DgnPlatform\DgnRscFont.h>
#include    <DgnPlatform\ProxyDisplayCore.h>
#include    <Raster\msraster.fdf>
#include    <Windows.h>
#include    <DgnPlatform\Tools\w32Tools.h>
USING_NAMESPACE_BENTLEY_DGNPLATFORM

BEGIN_BENTLEY_NAMESPACE

namespace RealDwg {

struct   ExternalRefTreeNode
    {
    AcDbHandleArrayPtRef    m_refPath;
    AcDbObjectId            m_xRefBlockTRObjectId;

    ExternalRefTreeNode () {}
    ExternalRefTreeNode (AcDbObjectId xRefBlockTRObjectId) : m_xRefBlockTRObjectId(xRefBlockTRObjectId) {;}
    };


#if defined (FUTUREWORK_AUDIT)
// None of the methods on AcDbAuditInfo are virtual, so we can't override them!
/*=================================================================================**//**
* @bsiclass                                                     RayBentley      07/2002
+===============+===============+===============+===============+===============+======*/
class RealDwgAuditInfo : public AcDbAuditInfo
{
private:
    bool            m_fixErrors;
    bool            m_validationHeaderPrinted;
    void            (*m_pMessageFunction) (char *);

public:
RealDwgAuditInfo
(
void                    (*pMessageFunction) (char *),
bool                    fixErrors
)
    {
    m_pMessageFunction = pMessageFunction;
    m_fixErrors = fixErrors ? true : false;
    m_validationHeaderPrinted = false;
    }

~RealDwgAuditInfo() {}

bool    fixErrors()   { return  m_fixErrors; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
void printError
(
const ACHAR*       strName,
const ACHAR*       strValue,
const ACHAR*       strValidation,
const ACHAR*       strDefaultValue
) override
    {
    if (NULLFUNC == m_pMessageFunction)
        return;

    char            outputString[1024];

    if (!m_validationHeaderPrinted)
        {
        m_validationHeaderPrinted = true;
        if (NULLFUNC != m_pMessageFunction)
            m_pMessageFunction ("DXF Name\t\t CurrentValue \t\t Validatation \t\t Default");
        }
    _snprintf_s (outputString, _countof(outputString), _TRUNCATE, "%ls \t %ls \t %ls \t %ls \t %ls", strName, strValue, strValidation, strDefaultValue);
    if (NULLFUNC != m_pMessageFunction)
        m_pMessageFunction (outputString);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
void printInfo
(
const ACHAR*   strInfo
) override
    {
    if (NULLFUNC == m_pMessageFunction)
        return;

    if (NULLFUNC != m_pMessageFunction)
        m_pMessageFunction ((char *) strInfo);
    }

};
#endif


/*----------------------------------------------------------------------+
|                                                                       |
|   Static Constant initialization.                                     |
|                                                                       |
+----------------------------------------------------------------------*/
WCharCP           StringConstants::PointBlockName                           = L"POINT_DISPLAY";
WCharCP           StringConstants::ContinousLinetypeName                    = L"CONTINUOUS";
WCharCP           StringConstants::MainDictionaryItem_MicroStation          = L"MicroStation";
WCharCP           StringConstants::MainDictionaryItem_AcadDim               = L"AcadDim";
WCharCP           StringConstants::UstnDictionaryItem_Header                = L"Header";
WCharCP           StringConstants::UstnDictionaryItem_Application           = L"ApplicationElement";
WCharCP           StringConstants::UstnDictionaryItem_ModelApplication      = L"ModelApplicationElement";
WCharCP           StringConstants::UstnDictionaryItem_DgnStore              = L"DgnStoreElement";
WCharCP           StringConstants::UstnDictionaryItem_ExtendedNonGraphics   = L"ExtendedNonGraphics";
WCharCP           StringConstants::UstnDictionaryItem_DgnModel              = L"DgnModel";
WCharCP           StringConstants::RegAppName_MicroStation                  = L"MStation";
WCharCP           StringConstants::RegAppName_PeUrl                         = L"PE_URL";
WCharCP           StringConstants::RegAppName_Acad                          = L"ACAD";
WCharCP           StringConstants::XDataKey_ApplicationLinkage              = L"Lnkg";
WCharCP           StringConstants::XDataKey_ApplicationDependency           = L"DepLnkg";
WCharCP           StringConstants::XDataKey_ApplicationDependencyOld        = L"Dep    ";
WCharCP           StringConstants::XDataKey_CellNameLinkage                 = L"Cell";
WCharCP           StringConstants::XDataKey_GraphicGroup                    = L"GG";
WCharCP           StringConstants::XDataKey_Transparency                    = L"Trnsp";
WCharCP           StringConstants::XDataKey_CompSetExpression               = L"CompSetExp";
WCharCP           StringConstants::XDataKey_CompSetExpressionSummary        = L"CompSetExpSmy";
WCharCP           StringConstants::XDataKey_NamedGroupFlags                 = L"NamedGroupFlags";
WCharCP           StringConstants::XDataKey_XAttribute                      = L"XAttribute";
WCharCP           StringConstants::XDataKey_ReferenceAttachMethod           = L"RefAttachMethod";
WCharCP           StringConstants::XDataKey_ReferenceTreatedAsElement       = L"RefTreatedAsElement";
WCharCP           StringConstants::XDataKey_DgnModelName                    = L"DgnModelName";
WCharCP           StringConstants::XData_BeginGroup                         = L"{";
WCharCP           StringConstants::XData_EndGroup                           = L"}";
WCharCP           StringConstants::XDictionaryItem_AcadDimAssoc             = L"ACAD_DIMASSOC";
WCharCP           StringConstants::XDictionaryItem_LayerFilters             = L"ACAD_LAYERFILTERS";
WCharCP           StringConstants::XDictionaryItem_AcLyDictionary           = L"ACLYDICTIONARY";
WCharCP           StringConstants::AShadeLayerName                          = L"ASHADE";


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2005
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 SetRegisterVariablesFromConfigVars
(
)
    {
    /*-----------------------------------------------------------------------------------
    NEEDS_REVIEW:

    Since R2000, PROXYSHOW and LWDEFAULT have been stored on AutoDesk's registry, they
    are no longer file based variables.  OpenDWG's setPROXYSHOW and setLWDEFAULT do not
    seem to do anything!  It does not seem to change the registry, therefore there is no
    effect on result.

    RealDWG does not even have a setProxy method, although it has a setLwdefault, which
    does not seem to change the registry either.  It causes no difference in result.
    However, I'm not sure if AcDbAppSystemVariables may have a runtime effect, so let's
    keep this call until we know for sure what it does.  Whatever we have changed here
    for RealDWG makes no difference in result when compared to what OpenDWG does.

    If we really want to set these options, we must change the registry keys.  I'm not
    all convinced just yet that we should ever change AutoDesk's registry though.
    -----------------------------------------------------------------------------------*/
    int     value;
    if (MstnInterfaceHelper::Instance().GetSettings().GetDefaultDwgLineWeight (value))
        {
        switch (value)
            {
            case  AcDb::kLnWt000:
            case  AcDb::kLnWt005:
            case  AcDb::kLnWt009:
            case  AcDb::kLnWt013:
            case  AcDb::kLnWt015:
            case  AcDb::kLnWt018:
            case  AcDb::kLnWt020:
            case  AcDb::kLnWt025:
            case  AcDb::kLnWt030:
            case  AcDb::kLnWt035:
            case  AcDb::kLnWt040:
            case  AcDb::kLnWt050:
            case  AcDb::kLnWt053:
            case  AcDb::kLnWt060:
            case  AcDb::kLnWt070:
            case  AcDb::kLnWt080:
            case  AcDb::kLnWt090:
            case  AcDb::kLnWt100:
            case  AcDb::kLnWt106:
            case  AcDb::kLnWt120:
            case  AcDb::kLnWt140:
            case  AcDb::kLnWt158:
            case  AcDb::kLnWt200:
            case  AcDb::kLnWt211:
            case  AcDb::kLnWtByLayer:
            case  AcDb::kLnWtByBlock:
            case  AcDb::kLnWtByLwDefault:
                RealDwgHostApp::Instance().workingAppSysvars()->setLwdefault ((AcDb::LineWeight) value);
                break;

            // not one of the expected values.
            default:
                BeAssert (false && L"Unexpected lineweight value from registry key LWDEFAULT!");
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/2004
+---------------+---------------+---------------+---------------+---------------+------*/
static int                  CompareRefPaths
(
const void    *pArg1,
const void    *pArg2
)
    {
    AcDbHandleArrayPtRef    &path1 = ((ExternalRefTreeNode*) pArg1)->m_refPath;
    AcDbHandleArrayPtRef    &path2 = ((ExternalRefTreeNode*) pArg2)->m_refPath;

    UInt32          length1 = path1.length(), length2 = path2.length();
    if (length1 != length2)
        return length1 < length2 ? - 1 : 1;

    for (int iPath = length1-1; iPath >= 0; iPath--)
        {
        ElementId   id1 = path1[iPath];
        ElementId   id2 = path2[iPath];
        if (id1 != id2)
            return id1 < id2 ? -1 : 1;
        }

    return 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static void                RemoveTableEntry
(
AcDbSymbolTable*            pTable,
WStringCR                   name
)
    {
    if (name.empty())
        return;

    AcDbSymbolTableRecord*  pTableRecord;

    if (Acad::eOk == pTable->getAt (name.c_str(), pTableRecord, AcDb::kForWrite, false))
        {
        pTableRecord->erase();
        pTableRecord->close();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/12
+---------------+---------------+---------------+---------------+---------------+------*/
static void     RemoveMLeaderStyleEntry (AcDbDictionary* mleaderDictionary, WStringCR dimstyleName)
    {
    // get actual style name by tripping the prefix off of the input name
    WString     mleaderStyleName;
    if (RealDwgUtil::GetMLeaderStyleNameFromDimstyleName(mleaderStyleName, dimstyleName))
        {
        // keep style "Standard"
        if (0 == mleaderStyleName.CompareToI(NAME_Standard))
            return;

        mleaderDictionary->remove (mleaderStyleName.c_str());
        }
    }

/*---------------------------------------------------------------------------------**//**
* Remove any database entries that were derived from the seed and exist in the
* incoming DGN file.
*
* @bsimethod                                                    RayBentley      07/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::RemoveRedundantSeedEntries
(
AcDbDatabase*               pDatabase,
DgnModelP                   modelRef
)
    {
    DgnFileP    dgnFile = modelRef->GetDgnFileP ();
    if (NULL == dgnFile)
        return;

    AcDbTextStyleTablePointer   acTextstyleTable (pDatabase->textStyleTableId(), AcDb::kForRead);
    if (Acad::eOk == acTextstyleTable.openStatus())
        {
        TextStyleCollection     dgnTextstyles (*dgnFile);
        for each (DgnTextStylePtr txtstyle in dgnTextstyles)
            RemoveTableEntry (acTextstyleTable, txtstyle->GetName());
        }

    AcDbDimStyleTablePointer    acDimstyleTable (pDatabase->dimStyleTableId(), AcDb::kForRead);
    if (Acad::eOk == acDimstyleTable.openStatus())
        {
        AcDbDictionaryPointer   acMLeaderStyleDictionary (pDatabase->mleaderStyleDictionaryId(), AcDb::kForWrite);
        bool                    mleaderStylesValid = Acad::eOk == acMLeaderStyleDictionary.openStatus();

        DimStyleCollection      dimensionStyles (*dgnFile);
        for (DimStyleIterator iter = dimensionStyles.begin(); iter.IsValid(); ++iter)
            {
            DimensionStyleCP    dimstyle = *iter;
            if (NULL != dimstyle)
                {
                WStringCR       dimstyleName = dimstyle->GetName ();
                // remove dup entries from dimstyle table
                RemoveTableEntry (acDimstyleTable, dimstyleName);
                // remove dup entries from mleader style dictionary
                if (mleaderStylesValid)
                    RemoveMLeaderStyleEntry (acMLeaderStyleDictionary, dimstyleName);
                }
            }
        }

    AcDbDictionaryPointer   acMLStyleDictionary (pDatabase->mLStyleDictionaryId(), AcDb::kForWrite);
    if (Acad::eOk == acMLStyleDictionary.openStatus())
        {
        MultilineStyleCollection    dgnMultilineStyles (*dgnFile);
        for each (MultilineStylePtr mlstyle in dgnMultilineStyles)
            {
            size_t      nChars = mlstyle->GetName().size ();
            if (nChars > 0)
                {
                WCharP  name = new WChar[nChars + 1];
                wcscpy (name, mlstyle->GetName().c_str());
                wcsupr (name);
                acMLStyleDictionary->remove (name);
                delete name;
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* Construct RealDwgFileHolder for DWG File.
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::Initialize
(
DgnFileP                    dgnFile,
WStringCR                   fileName
)
    {
    RealDwgFileIO::Initialize ();

    RealDwgHostApp::Instance().SetDgnFile (dgnFile);

    m_database                          = NULL;
    m_layerIndex                        = new LayerTableIndex ();
    m_linetypeIndex                     = new SignedTableIndex (true);
    m_textStyleIndex                    = new SignedTableIndex (false);
    m_multilineStyleIndex               = new SignedTableIndex (false);
    m_dwgSymbologyData                  = new DwgSymbologyData (this);
    m_dgnSymbologyData                  = new DgnSymbologyData (this);
    m_pExternalRefByAttachPathTree      = mdlAvlTree_initExtended (AVLKEY_USER, CompareRefPaths, 0, 0);
    m_pGeneratedTextStyles              = mdlAvlTree_initExtended (AVLKEY_WSTRING, RealDwgGeneratedTextStyleItem::CompareEntries, 0, 0);
    m_pBlockByIdTree                    = mdlAvlTree_init (AVLKEY_UINT64);
    m_pFontMap                          = new bmap<WString, FontMapItem>;
    m_pMaterialTree                     = NULL;
    m_nextTextNodeNumber                = 1;
    m_nextViewportNumber                = 1;
    m_nextDgnUnderlayNumber             = 1;
    m_nextPdfUnderlayNumber             = 1;
    m_nextDwfUnderlayNumber             = 1;
    m_pLayerByNameTree                  = NULL;             // Created on demand.
    m_pointDisplayBlockId               = 0;
    m_pPostProcessIdTree                = mdlAvlTree_init (AVLKEY_ELEMENTID);
    m_pHeaderDescr                      = NULL;
    m_loadToCacheMeter                  = NULL;
    m_lockedLayers                      = NULL;
    m_hasOldVersionAEC                  = false;
    m_isC3dObjectEnablerLoaded          = false;
    m_saveSheetsAsModelspace            = ConfigurationManager::IsVariableDefined(L"MS_DWG_SINGLESHEET_AS_MODELSPACE");
    m_isDwgAttrdefItemtypeLibraryDirty  = false;
    m_openCorruptedDwgFile              = ConfigurationManager::IsVariableDefined(L"MS_DWG_OPEN_CORRUPTED_FILE");
    m_openFileReadOnlyForPrinting       = ConfigurationManager::IsVariableDefined(L"MS_DWG_OPENREADONLY_FORPRINTING");
    m_bindingXrefsAttempted             = false;

    // allow attaching a corrupt file as a read-only reference - ACAD can xRef these as well.
    if (!m_openCorruptedDwgFile && DgnFilePurpose::DgnAttachment == dgnFile->GetFilePurpose())
        m_openCorruptedDwgFile = true;

    //Initialize rasterlib
    mdlRaster_initialize();

    SetRegisterVariablesFromConfigVars ();

    m_dgnFile = dgnFile;
    m_fileName = fileName;

    // initialize the default font
    DgnFontManager::GetManager().Resume();
    m_defaultFont   = DgnFontManager::GetDefaultAcadFontP();

    // read the autocad font map
    ReadAcadFontMap ();

    m_dgnUnderlayDefInfoArray.clear ();
    m_viewRotation.initIdentity ();
    m_newViewportAnnotationScales.clear ();
    m_elementsExcludedFromSaving.clear ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
void FileHolder::InitializeFromSeedModel (DgnModelP seedModelRef)
    {
    ModelInfoCP modelInfo;
    if (NULL == seedModelRef || NULL == (modelInfo = seedModelRef->GetModelInfoCP()))
        throw FileHolderException (BSIERROR);

    m_defaultModelInfo = modelInfo->MakeCopy();

    if (m_defaultModelInfo->m_uorPerStorage <= 0.0)
        {
        DIAGNOSTIC_PRINTF ("Uor Per Storage == %f - Ignored\n", m_defaultModelInfo->m_uorPerStorage);
        m_defaultModelInfo->m_uorPerStorage = 1.0;
        }
    if (m_defaultModelInfo->m_linestyleScale <= 0.0)
        m_defaultModelInfo->m_linestyleScale = 1.0;

    // get the first dimension style from the seed file
    m_seedDimensionStyle = DimensionStyle::GetSettings (*seedModelRef->GetDgnFileP());
    if (!m_seedDimensionStyle.IsValid())
        DIAGNOSTIC_PRINTF ("Failed getting active dimension style from seed file %ls\n", seedModelRef->GetDgnFileP()->GetFileName().c_str());

    PersistentElementRefP   type9ElemRef = seedModelRef->GetDgnFileP()->GetPersistentTcbElementRef ();
    if (NULL == type9ElemRef)
        throw FileHolderException (BSIERROR);

    m_pHeaderDescr = MSElementDescr::Allocate (type9ElemRef, seedModelRef);
    if (NULL == m_pHeaderDescr)
        throw FileHolderException (BSIERROR);

    m_pHeaderDescr->el.ehdr.uniqueId = 0;
    }

/*---------------------------------------------------------------------------------**//**
* Construct FileHolder from DWG File.
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
FileHolder::FileHolder (DgnFileP dgnFile, WCharCP pFileName)
    {
    m_defaultModelInfo = ModelInfo::Create (DgnModelType::Normal, NULL, false);
    Initialize (dgnFile, pFileName);
    }

/*=================================================================================**//**
*
* @bsiclass                                                     Barry.Bentley   01/09
+===============+===============+===============+===============+===============+======*/
class           LinkerReactor : public AcRxDLinkerReactor
{
private:
bool                m_loadedNewObjectEnabler;
bool                m_loadedC3dObjectEnabler;

public:
LinkerReactor ()
    {
    m_loadedNewObjectEnabler    = false;
    m_loadedC3dObjectEnabler    = false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                rxAppWillBeLoaded (const ACHAR* moduleName)
    {
    WString     rscString;
    if (SUCCESS == RmgrResource::LoadWString(rscString, DwgPlatformHost::Instance().GetRscFileHandle(), MSGLISTID_RealDwgFileIOMisc, REALDWGMESSAGE_STATUS_LoadingDBX))
        {
        WString stats;
        WString::Sprintf (stats, rscString.c_str(), moduleName);
        NotificationManager::OutputMessage (NotifyMessageDetails(OutputMessagePriority::Info, stats.c_str(), NULL, 0, OutputMessageAlert::None));
        }
    else
        {
        DIAGNOSTIC_PRINTF ("Attempting to dynamic load %ls\n", moduleName);
        }
    }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                rxAppLoadAborted (const ACHAR* moduleName)
    {
    // if prefix aecc skip 

    WString fullPath, TmpName, TmpExt, Dir;
    BeFileName::ParseName(NULL, &Dir, &TmpName, &TmpExt, moduleName);
    DWORD dwLen, dwUnUsed;
    LPTSTR lpVI = NULL;
    WString version;

    dwLen = GetFileVersionInfoSizeW(moduleName, &dwUnUsed);

    if (dwLen > 0)
    {
        lpVI = (LPTSTR)GlobalAlloc(GPTR, dwLen);
    }

    if (lpVI != NULL)
    {
        VS_FIXEDFILEINFO *lpFfi;
        wchar_t szBuffer[2048];
        UINT uLen = 0;

        GetFileVersionInfo(moduleName, NULL, dwLen, lpVI);

        if (VerQueryValue(lpVI, L"\\", (LPVOID *)&lpFfi, &uLen))
        {
            swprintf_s(szBuffer, L"%d.%d.%d.%d", HIWORD(lpFfi->dwProductVersionMS), LOWORD(lpFfi->dwProductVersionMS),
                HIWORD(lpFfi->dwProductVersionLS), LOWORD(lpFfi->dwProductVersionLS));
            version.assign(szBuffer);
            //m_majorVersion = HIWORD(lpFfi->dwProductVersionMS);
        }

        GlobalFree((HGLOBAL)lpVI);
    }

    //validation 
    //for filename prefix aec & aecc
    bool startwithAEC = TmpName.StartsWithI(L"AEC");
    bool startwithAECC = TmpName.StartsWithI(L"AECC");

    if (startwithAEC || startwithAECC)
    {
        // Dbx files in realdwg 2022 have file version 24.1.47.0
        // Dbx with 'civil 3d 2022 object enabler' are with file version 13.4.214
        if (version.Equals(L"24.1.47.0") || version.StartsWith(L"13.4.214")) 
        {
            return;
        }
    }
    //AcMapCoordSysCore is coming with 'civil 3d 2022 object enabler' and with different version
    if (version.Equals(L"25.0.45.3") && TmpName.ContainsI(L"AcMapCoordSysCore"))
        return;

    if (SUCCESS != RealDwgUtil::AddToMessageCenter(OutputMessagePriority::Error, REALDWGMESSAGECENTER_FailueLoadingDBX, false, moduleName, moduleName))
        DIAGNOSTIC_PRINTF ("Failed to to dynamic load %ls\n", moduleName);
    }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
virtual void                rxAppLoaded (const ACHAR* moduleName)
    {
    DIAGNOSTIC_PRINTF ("Successfully dynamic loaded %ls\n", moduleName);
    m_loadedNewObjectEnabler = true;
    auto filename = BeFileName::GetFileNameAndExtension (moduleName);
    if (filename.CompareToI(L"AecCivilBase.dbx") == 0)
        m_loadedC3dObjectEnabler = true;
    }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool            LoadedNewObjectEnabler() const { return m_loadedNewObjectEnabler; }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          12/21
+---------------+---------------+---------------+---------------+---------------+------*/
bool            LoadedC3dObjectEnabler() const { return m_loadedC3dObjectEnabler; }


};  // LinkerReactor

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/09
+---------------+---------------+---------------+---------------+---------------+------*/
static code_page_id         PeekCodepageFromDwgFile
(
AcDbDatabase*               pDatabase,
WCharCP                     fileName
)
    {
    // codepage started in R13.
    if (pDatabase->originalFileVersion() < AcDb::kDHL_1012)
        return  code_page_id::CODE_PAGE_UNDEFINED;

    int     fileHandle = _wsopen (fileName, _O_BINARY | _O_RDONLY, _SH_DENYNO);

    if (-1 != fileHandle)
        {
        byte    buffer[48];
        UInt8   codepage = 0;

        if (_read(fileHandle, buffer, sizeof(buffer)) > 20)
            codepage = buffer[19];

        _close (fileHandle);

        return  (code_page_id) codepage;
        }

    return  code_page_id::CODE_PAGE_UNDEFINED;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 CheckDwgCodepage
(
AcDbDatabase*               pDatabase,
WCharCP                     fileName
)
    {
    struct DwgCodepage
        {
        code_page_id    m_codepageId;
        WCharP        m_codepageName;
        };

    static DwgCodepage s_dwgCodepageList[] =
        {
        {code_page_id::CODE_PAGE_UNDEFINED,    L"?"},
        {code_page_id::CODE_PAGE_ASCII,        L"ASCII"},
        {code_page_id::CODE_PAGE_8859_1,       L"ISO8859-1"},
        {code_page_id::CODE_PAGE_8859_2,       L"ISO8859-2"},
        {code_page_id::CODE_PAGE_8859_3,       L"ISO8859-3"},
        {code_page_id::CODE_PAGE_8859_4,       L"ISO8859-4"},
        {code_page_id::CODE_PAGE_8859_5,       L"ISO8859-5"},
        {code_page_id::CODE_PAGE_8859_6,       L"ISO8859-6"},
        {code_page_id::CODE_PAGE_8859_7,       L"ISO8859-7"},
        {code_page_id::CODE_PAGE_8859_8,       L"ISO8859-8"},
        {code_page_id::CODE_PAGE_8859_9,       L"ISO8859-9"},
        {code_page_id::CODE_PAGE_DOS437,       L"DOS437"},
        {code_page_id::CODE_PAGE_DOS850,       L"DOS850"},
        {code_page_id::CODE_PAGE_DOS852,       L"DOS52"},
        {code_page_id::CODE_PAGE_DOS855,       L"DOS855"},
        {code_page_id::CODE_PAGE_DOS857,       L"DOS857"},
        {code_page_id::CODE_PAGE_DOS860,       L"DOS860"},
        {code_page_id::CODE_PAGE_DOS861,       L"DOS861"},
        {code_page_id::CODE_PAGE_DOS863,       L"DOS863"},
        {code_page_id::CODE_PAGE_DOS864,       L"DOS864"},
        {code_page_id::CODE_PAGE_DOS865,       L"DOS865"},
        {code_page_id::CODE_PAGE_DOS869,       L"DOS869"},
        {code_page_id::CODE_PAGE_DOS932,       L"DOS932"},
        {code_page_id::CODE_PAGE_MACINTOSH,    L"MAC-ROMAN"},
        {code_page_id::CODE_PAGE_BIG5,         L"BIG5"},
        {code_page_id::CODE_PAGE_KSC5601,      L"KSC5601"},
        {code_page_id::CODE_PAGE_JOHAB,        L"JOHAB"},
        {code_page_id::CODE_PAGE_DOS866,       L"DOS866"},
        {code_page_id::CODE_PAGE_ANSI_1250,    L"ANSI 1250"},
        {code_page_id::CODE_PAGE_ANSI_1251,    L"ANSI 1251"},
        {code_page_id::CODE_PAGE_ANSI_1252,    L"ANSI 1252"},
        {code_page_id::CODE_PAGE_GB2312,       L"GB2312"},
        {code_page_id::CODE_PAGE_ANSI_1253,    L"ANSI 1253"},
        {code_page_id::CODE_PAGE_ANSI_1254,    L"ANSI 1254"},
        {code_page_id::CODE_PAGE_ANSI_1255,    L"ANSI 1255"},
        {code_page_id::CODE_PAGE_ANSI_1256,    L"ANSI 1256"},
        {code_page_id::CODE_PAGE_ANSI_1257,    L"ANSI 1257"},
        {code_page_id::CODE_PAGE_ANSI_874,     L"ANSI 874"},
        {code_page_id::CODE_PAGE_ANSI_932,     L"ANSI 932"},
        {code_page_id::CODE_PAGE_ANSI_936,     L"ANSI 936"},
        {code_page_id::CODE_PAGE_ANSI_949,     L"ANSI 949"},
        {code_page_id::CODE_PAGE_ANSI_950,     L"ANSI 950"},
        {code_page_id::CODE_PAGE_ANSI_1361,    L"ANSI 1361"},
        {code_page_id::CODE_PAGE_ANSI_1200,    L"ANSI 1200"},
        {code_page_id::CODE_PAGE_ANSI_1258,    L"ANSI 1258"},
        };

    static UInt32   s_numberOfCodepages = sizeof(s_dwgCodepageList) / sizeof(DwgCodepage);
    if (s_numberOfCodepages < (int)code_page_id::CODE_PAGE_CNT)
        DIAGNOSTIC_PRINTF ("RealDWG has changed codepage count from %d to %d.\n", s_numberOfCodepages, code_page_id::CODE_PAGE_CNT);

    code_page_id    dwgCodepage = PeekCodepageFromDwgFile (pDatabase, fileName);
    UInt32          codepageAt = 0;

    for (; codepageAt < s_numberOfCodepages; codepageAt++)
        {
        if (s_dwgCodepageList[codepageAt].m_codepageId == dwgCodepage)
            break;
        }

    WChar     codepageName[48];
    if (codepageAt < s_numberOfCodepages)
        wcscpy (codepageName, s_dwgCodepageList[codepageAt].m_codepageName);
    else
        wsprintf (codepageName, L"%d", dwgCodepage);

    if (SUCCESS != RealDwgUtil::AddToMessageCenter(OutputMessagePriority::Error, REALDWGMESSAGECENTER_UnmatchingCodepage, false, codepageName, fileName))
        DIAGNOSTIC_PRINTF ("The NLS files are not found to convert texts for unmatching codepage %ls.\n", codepageName);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsExpectedOpenUnitMode (DwgOpenUnitMode unitMode)
    {
    if (DWGOpenUnitMode_NotSetYet == unitMode)
        return true;

    if (DWGOpenUnitMode_LegacyDwgAttachment == unitMode)
        return true;

    if (DWGOpenUnitMode_SeedFileMasterUnits == unitMode)
        return true;

    if (DWGOpenUnitMode_SeedFileSubUnits == unitMode)
        return true;

    if (DWG_UNITMODE_UseDesignCenter == unitMode)
        return true;

    // otherwise it must be one of our standard units.
    UnitDefinition  standardUnit = UnitDefinition::GetStandardUnit (static_cast <StandardUnit> (unitMode));
    return (standardUnit.IsValid());
    }

/*---------------------------------------------------------------------------------**//**
*  @bsimethod                                                   Don.Fu          12/21
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FileHolder::IsC3dObjectEnablerLoaded ()
    {
    return  m_isC3dObjectEnablerLoaded;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FileHolder::IsOldVersionAEC ()
    {
    return  m_hasOldVersionAEC;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/11
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            CheckOldVersionAEC (bool& hasOldVersionAEC, AcDbDatabase* pDwg)
    {
    /*-----------------------------------------------------------------------------------------------------
    Check for older version of AEC objects loaded into database:
    Autodesk has specially written a small DLL, AecVerChk.dll, for us to check for old AEC versions.
    This DLL depends on AEC modules to be already loaded.  By now, if there are AEC objects in file, the AEC
    modules should have been demand loaded.  If either AEC modules or this DLL is not loaded, we can assume 
    that there is no AEC object in file.  We then can ignore old AEC version.  Only if both AEC and the DLL
    have succeeded loading, we need to call containsPreviousVerAdtObjs which is exported from the said DLL.
    ------------------------------------------------------------------------------------------------------*/
    hasOldVersionAEC = false;
    HMODULE     dllHandle = GetModuleHandle (L"AecVerChk.dll");
    if (NULL == dllHandle)
        dllHandle = LoadLibrarySecureW (L"AecVerChk.dll");
    if (NULL != dllHandle)
        {
        static AecVerChkFuncPtr pAecVerChkFuncPtr = (bool(*)(AcDbDatabase*))GetProcAddress(dllHandle,"containsPreviousVerAdtObjs");
        if (NULL != pAecVerChkFuncPtr)
            {
            hasOldVersionAEC = (*pAecVerChkFuncPtr)(pDwg);

            if (hasOldVersionAEC)
                {
                // send a warning to the message center:
                const ACHAR*    fileName = NULL;
                if (Acad::eOk == pDwg->getFilename(fileName) && 0 != fileName[0])
                    RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_OldVersionAEC, false, NULL, fileName);
                }
            }
        }

    return  SUCCESS;
    }

#ifdef OVERRIDE_AECVIEWCONFIG
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt    OverrideAecViewConfigFromMasterDwg (AcDbDatabase* xrefDwg, DgnFileP refDgn)
    {
    /*---------------------------------------------------------------------------------------------------
    Find the default AEC view configuration object in current database and replace it with the one in the
    master file saved by the host.

        This code needs following workarounds:
    1) A non-AEC DWG file causes access violation calling any of the AEC access methods and the issue has
        been made known to Adesk.
    2) viewsDictId returns the ID of AEC_DISP_REPS, instead of AEC_DISP_REP_CONFIGURATIONS?
    3) Not sure about if defaultViewConfigId returns a correct ID to us.

        We have a test case that works but it could be because the view configuration exists in both xRef 
    and master file and they are the same, so that setDefaultViewConfigId could have just switched the 
    current view config for the xRef, not that wblockCloneObjects has replaced xRef's data correctly.
        Black & Veatch test files do not work, but we cannot varify the display via SETXREFCONFIG in 
    ACAD2015 either.
    ---------------------------------------------------------------------------------------------------*/
    if (NULL == refDgn || NULL == refDgn || DgnFilePurpose::DgnAttachment != refDgn->GetFilePurpose())
        return  BSIERROR;

    AcDbDatabase*   masterDwg = NULL;
    if (!DwgPlatformHost::Instance()._GetMasterDwgFile(masterDwg) || xrefDwg == masterDwg)
        return  BSIERROR;

    // FUTUREWORK: viewsDictId crashes on DWG files with no AEC objects - waiting for Adesk's fix!
    if (!containsPreviousVerAdtObjs(xrefDwg))
        return  BSIERROR;

#ifdef FUTUREWORK_viewsDictId
    AcDbObjectId    xrefViewsOwnerId = viewsDictId (xrefDwg);
#else
    AcDbObjectId                    xrefViewsOwnerId;
    AcDbSmartDictionaryPointer      mainDictionary(xrefDwg->namedObjectsDictionaryId(), AcDb::kForRead);
    if (Acad::eOk == mainDictionary.openStatus())
        mainDictionary->getAt (L"AEC_DISP_REP_CONFIGURATIONS", xrefViewsOwnerId);
#endif
    AcDbObjectId    masterViewConfigId = defaultViewConfigId (masterDwg);
    if (!xrefViewsOwnerId.isValid() || !masterViewConfigId.isValid())
        return  BSIERROR;

    AcDbIdMapping       idMap;
    AcDbObjectIdArray   cloneIds;
    cloneIds.append (masterViewConfigId);

    // copy and/or replace xref's view dictionaries from the master file
    Acad::ErrorStatus   es = xrefDwg->wblockCloneObjects (cloneIds, xrefViewsOwnerId, idMap, AcDb::kDrcReplace, false);

    if (Acad::eOk == es)
        {
        // find the remapped view config ID for the xref file:
        AcDbObjectId        xrefViewConfigId;
        AcDbIdMappingIter*  iter = new AcDbIdMappingIter (idMap);

        for (iter->start(); !iter->done(); iter->next())
            {
            AcDbIdPair      idPair;
            if (iter->getMap(idPair) && masterViewConfigId == idPair.key())
                {
                // this should be the new default view config copied from replaced by master file
                xrefViewConfigId = idPair.value ();
                break;
                }
            }

        delete iter;

        // reset the current default view configuration in the xRef
        if (xrefViewConfigId.isValid())
            es = setDefaultViewConfigId (xrefViewConfigId);
        }

    return  Acad::eOk == es ? BSISUCCESS : BSIERROR;
    }
#endif

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/17
+---------------+---------------+---------------+---------------+---------------+------*/
bool    FileHolder::IsCorruptedDwgFile (AcDbDatabaseP dwg)
    {
    bool    isCorrupted = false;
   
#if RealDwgVersion >= 2018
    if (nullptr != dwg)
        {
        // an input DWG is given - always check for corruption:
        dwg->closeInput (true);
        return  dwg->needsRecovery ();
        }

    // check file for open or reference attachment:
    if (DgnFileFormatType::DWG == m_dgnFile->GetOriginalFormat() && !m_openCorruptedDwgFile)
        {
        m_database->closeInput (true);
        isCorrupted = m_database->needsRecovery ();

        if (isCorrupted)
            {
            const ACHAR*   filename = nullptr;
            if (Acad::eOk != m_database->getFilename(filename))
                filename = L"??";

            RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Error, REALDWGMESSAGECENTER_DwgFileNeedsRecovery, false, nullptr, filename);
            }
        }
#endif

    return  isCorrupted;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/17
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     BindXrefsInMasterFile (AcDbDatabaseP dwg)
    {
    /*-----------------------------------------------------------------------------------
    Workaround a compatibility problem: in DWG, an xRef is an entity which participates in
    master file's entity draw order, whereas in DGN, a DgnAttachment is displayed either
    before or after master file.  A DgnAttachment itself does not have a display priority
    like other elements do.  Many users only need to print DWG files(TFS's 43829, 701158).
    In this case we open a master DWG file as read-only and bind xRef blocks into the master
    file.  The bound block inserts should retain their file draw order, hence to persist
    their display.

    To correctly do that, acdbResolveCurrentXRefs must be called before opening the block
    table of the master file.  After xRef's have been bound into the master file, do not
    set file handle read only, until at very end of file open as otherwise the database
    becomes invalid.
    -----------------------------------------------------------------------------------*/
    DIAGNOSTIC_PRINTF ("Total objects in file before binding xRef: %d\n", dwg->approxNumObjects());

    // resolve xRef's
    AcDbObjectIdArray   resolvedXrefs;
    Acad::ErrorStatus   es = acdbResolveCurrentXRefs (dwg);
    if (Acad::eOk == es)
        {
        // collect resolved xRef blocks:
        AcDbBlockTableIterator* blockIter = nullptr;
        AcDbBlockTablePointer   blockTable(dwg->blockTableId(), AcDb::kForRead);
        if (Acad::eOk == blockTable.openStatus() && Acad::eOk == blockTable->newIterator(blockIter))
            {
            for (blockIter->start(); ! blockIter->done(); blockIter->step())
                {
                AcDbObjectId    blockId;
                if (Acad::eOk == blockIter->getRecordId(blockId))
                    {
                    AcDbSmartBlockTableRecordPointer    block(blockId, AcDb::kForRead);
                    if (Acad::eOk == block.openStatus() && AcDb::kXrfResolved == block->xrefStatus())
                        resolvedXrefs.append (blockId);
                    }
                }
            delete blockIter;
            }
        }

    // bind xRef's
    if (!resolvedXrefs.isEmpty())
        es = acdbBindXrefs (dwg, resolvedXrefs, false, false, false);
    else
        es = Acad::eNotApplicable;

    DIAGNOSTIC_PRINTF ("%d xRef's are bound, %d total objects [%ls]\n", resolvedXrefs.length(), dwg->approxNumObjects(), acadErrorStatusText(es));

    return  Acad::eOk == es;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   12/08
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   FileHolder::LoadAcadDatabase ()
    {
    // The ForeignUnitMode will be set on input when we are opening a Dwg file as a reference file - see DgnAttachment::OpenAttachedDgnFile.
    m_unitMode = (DwgOpenUnitMode) m_dgnFile->GetForeignUnitMode();

    if (!IsExpectedOpenUnitMode(m_unitMode))
        {
        DIAGNOSTIC_PRINTF ("The DWG open unit mode (%d) is incorrect for file %ls (maybe a legecy type 100 element?)\n", m_unitMode, m_dgnFile->GetFileName().c_str());
        m_unitMode = DWGOpenUnitMode_NotSetYet;
        }

    // If this is an old (08.00.02.xx) reference attachment, unitMode is set to 0.
    // In this case, use the seed file storage resolution directly to match the
    // way that version worked and preserve the attachment transform.
    m_useUorPerStorageFromSeedDirectly = (DWGOpenUnitMode_LegacyDwgAttachment == m_unitMode);

    // set up the RealDwg progress meter.
    RealDwgHostAppR hostApp  = RealDwgHostApp::Instance();
    m_loadToCacheMeter       = hostApp.NewProgressMeter (m_dgnFile, 0.0, FILE_LOAD_PROGRESS_FRACTION);

    WCharCP         fileName = m_fileName.c_str();
    StatusInt       loadStatus = SUCCESS;

    try
        {
        // tell RealDWG to use the progressmeter for this file.
        hostApp.setWorkingProgressMeter (m_loadToCacheMeter);
        m_database = new AcDbDatabase (false);

        // we keep track of whether any new Object Enablers are demand loaded during the read.
        AcRxDynamicLinker*      pDynLinker      = AcRxDynamicLinker::cast(acrxSysRegistry()->at(ACRX_DYNAMIC_LINKER));
        LinkerReactor*          pReactor        = new LinkerReactor ();
        pDynLinker->addReactor (pReactor);

        // set the newly created database to be the "working" database.
        RealDwgHostApp::Instance().SetWorkingDatabase (m_database);
        ::InitializeRTextCollection ();

        Acad::ErrorStatus       status;
        if (DgnFileFormatType::DXF == m_dgnFile->GetOriginalFormat())
            status = m_database->dxfIn (fileName, NULL);
        else
            status = m_database->readDwgFile (fileName, AcDbDatabase::kForReadAndAllShare);

        // if the DWG file is so corrupted that ACAD would prompt for a recovery, don't open the file:
        if (Acad::eOk == status && this->IsCorruptedDwgFile())
            return DgnFileStatus::DWGOPEN_STATUS_BadFile;

        // if we loaded any new Object Enablers, call all of the applications that are registered to install protocol extensions before we start converting.
        if (pReactor->LoadedNewObjectEnabler())
            MstnInterfaceHelper::Instance().LoadDgnProtocolExtensions();
        m_isC3dObjectEnablerLoaded = pReactor->LoadedC3dObjectEnabler ();

        // C3D OE may be loaded in current session, or from a prior session - either way should be effective
        m_isC3dObjectEnablerLoaded = pReactor->LoadedC3dObjectEnabler ();
        if (!m_isC3dObjectEnablerLoaded)
            m_isC3dObjectEnablerLoaded = ::acrxAppIsLoaded (L"AecCivilBase.dbx");

        pDynLinker->removeReactor (pReactor);
        delete pReactor;

        // set the working progress meter to a do-nothing.
        hostApp.SetDoNothingProgressMeter ();

        switch (status)
            {
            case Acad::eOk:
                // this is redundant.
                RealDwgHostApp::Instance().SetWorkingDatabase (m_database);
                break;

            case Acad::eNLSFileNotAvailable:
                /*-----------------------------------------------------------------------
                We get here when DWG codepage does not match OS's locale and RealDWG can
                not find NLS files for character conversion.  We send a message to warn
                user about this, then we re-read the file with character conversion using
                default system conversion.  DXF file should not have this problem.  Even
                if it does, there is no way to convert characters so we continue our
                process as if nothing happens.
                -----------------------------------------------------------------------*/
                if (DgnFileFormatType::DWG == m_dgnFile->GetOriginalFormat())
                    {
                    CheckDwgCodepage (m_database, fileName);
                    hostApp.setWorkingProgressMeter (m_loadToCacheMeter);

                    // We have to delete and re-create the database, or else readDwgFile will still fail.
                    delete m_database;
                    m_database = new AcDbDatabase (false);

                    RealDwgHostApp::Instance().SetWorkingDatabase (m_database);

                    // read dwg file with character conversion:
                    status = m_database->readDwgFile (fileName, AcDbDatabase::kForReadAndAllShare, true);

                    hostApp.SetDoNothingProgressMeter ();
                    if (Acad::eOk != status)
                        {
                        RealDwgHostApp::Instance().SetWorkingDatabase (NULL);
                        return DWGOPEN_STATUS_BadFile;
                        }
                    }
                break;

            case Acad::eUndefineShapeName:
            case Acad::eMissingDxfField:
                // Do what ACAD does: allow missing shape name - TFS# 12064.  RealDWG has already sent a message to Message Center.
                status = Acad::eOk;
                break;

            default:
                RealDwgHostApp::Instance().SetWorkingDatabase (NULL);
                return DWGOPEN_STATUS_BadFile;
                break;

#if defined (FUTUREWORK_RECOVER)
            default:
                {
                // I don't see any way to invoke recover from the ObjectARX library.
                // Try a recovery.
                WString     formatString, messageString;
                bool        isReadOnly = m_dgnFile->IsReadOnly ();

                MstnPlatform::MstnResourceUtils::GetUstationString (formatString, MsgList_MESSAGES2, 1202);
                WString::Sprintf (messageString, formatString.c_str(), pFileName);

                if (isReadOnly || (PROCESSSTATE_Inactive == mdlSystem_getBatchProcessingState()  &&
                     (SUCCESS != ConfigurationManager::GetVariable (NULL, "MS_NO_OPEN_ALERTS", 0)) && ACTIONBUTTON_OK == mdlDialog_openAlert (messageString.c_str())))
                    {
                    try
                        {
                        AcDbAuditInfo        auditInfo ();

                        m_database = RealDwgHostApp::Instance().recoverFile (s_systemServices.createFile (pFileName), &auditInfo);

                        // Note that the file required recovery.
                        WString     briefMessage, detailedMessage;

                        MstnPlatform::MstnResourceUtils::GetUstationString (formatString, MsgList_MESSAGES2, isReadOnly ? 1204 : 1203);
                        WString::Sprintf (briefMessage, formatString.c_str(), pFileName);

                        MstnPlatform::MstnResourceUtils::GetUstationString (formatString, MsgList_MESSAGES2, isReadOnly ? 1206 : 1205);
                        WString::Sprintf (detailedMessage, formatString.c_str(), pFileName, auditInfo.numErrors());
                        NotificationManager::OutputMessage (NotifyMessageDetails(OutputMessagePriority::Warning, briefMessage.c_str(), detailedMessage.c_str(), 0, false);
                        }
                    catch (...)
                        {
                        throw  FileHolderException (DWGOPEN_STATUS_BadFile);
                        }
                    }
                else
                    {
                    throw  FileHolderException (DWGOPEN_STATUS_BadFile);
                    }
                break;
                }
#endif
            }
        loadStatus = Acad::eOk == status ? SUCCESS : BSIERROR;
        }
    catch (CArchiveException* e)
        {
        DIAGNOSTIC_PRINTF ("CArchiveException thrown reading file %ls", fileName);

        wchar_t     chars[500] = { 0 };
        if (e->GetErrorMessage(chars, sizeof(chars)))
            DIAGNOSTIC_PRINTF (": %ls\n", chars);
        else
            DIAGNOSTIC_PRINTF (": no error message\n");

        e->Delete ();

        return DWGOPEN_STATUS_BadFile;
        }
    catch (...)
        {
        return DWGOPEN_STATUS_BadFile;
        }

    RealDwgHostApp::Instance().SetProgressMeterLimits (m_loadToCacheMeter, FILE_LOAD_PROGRESS_FRACTION, 1.0, m_database->approxNumObjects());

    // this is the case where we are opening a master file rather than a DWG reference attachment.
    if (m_unitMode <= DWGOpenUnitMode_NotSetYet)
        {
        // we need to find the appropriate unitmode, given the DWG files' LUNITS (linear units),
        DwgLinearUnits      linearUnits = (DwgLinearUnits) m_database->lunits();
        AcDb::UnitsValue    insertUnits = m_database->insunits();
        StandardUnit        dgnUnits    = RealDwgUtil::DgnUnitsFromAcDbUnitsValue (insertUnits);
        DwgFileVersion      dwgVersion  = RealDwgUtil::MSVersionFromAcVersion (m_database->originalFileVersion());
        m_unitMode = MstnInterfaceHelper::Instance().GetSettings().GetDwgOpenUnitMode (fileName, linearUnits, dgnUnits, dwgVersion);
        BeAssert (IsExpectedOpenUnitMode (m_unitMode) && L"Unexpected open unit mode!");
        }

    m_dgnFile->SetForeignUnitMode (m_unitMode);

    if (m_openFileReadOnlyForPrinting)
        {
        // only attempt binding xRef's once per file, and only the master file:
        if (!m_bindingXrefsAttempted && DgnFilePurpose::MasterFile == m_dgnFile->GetFilePurpose())
            {
            BindXrefsInMasterFile (m_database);
            m_bindingXrefsAttempted = true;
            }

        /*-----------------------------------------------------------------------------------------
        When ANNOALLVISIBLE=1, more than one presentations of an annotative entity are displayed in
        modelspace.  This is generally useless and wrong, but some users ignore modelspace display
        and only care about layout display.  John Holland Group uses iCS through ProjectWise to 
        print layouts, thus need a way to ignore multi-annotation presenction in modelspace, TFS 779214.
        -----------------------------------------------------------------------------------------*/
        if (m_database->annoAllVisible() != 0)
            m_database->setAnnoAllVisible (0);
        }

    this->IndexAcadModels ();

    m_hasOldVersionAEC = false;
    if (SUCCESS == loadStatus)
        loadStatus = CheckOldVersionAEC (m_hasOldVersionAEC, m_database);
#ifdef OVERRIDE_AECVIEWCONFIG
    OverrideAecViewConfigFromMasterDwg (m_database, m_dgnFile);
#endif

    return loadStatus;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2005
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 RemoveExtraSeedPaperSpaces
(
AcDbDatabase*               pSeedDatabase
)
    {
    AcDbObjectId                blockTableId = pSeedDatabase->blockTableId();
    AcDbBlockTablePointer       pBlockTable (blockTableId, AcDb::kForRead);
    if (Acad::eOk != pBlockTable.openStatus())
        return;

    AcDbBlockTableIterator*     pBlkIter;
    pBlockTable->newIterator (pBlkIter, true, true);
    bvector<AcString*>*     layoutDeleteList = NULL;

    for (pBlkIter->start(); !pBlkIter->done(); pBlkIter->step())
        {
        AcDbObjectId                blockId;

        pBlkIter->getRecordId (blockId);
        if (blockId.isNull())
            continue;

        AcDbBlockTableRecordPointer pBlock (blockId, AcDb::kForRead);
        if (Acad::eOk != pBlock.openStatus())
            continue;

        const ACHAR*    blockName;
        pBlock->getName (blockName);

        // keep one paper space - "*PAPER_SPACE", but empty everything out of it..
        if (0 == wcsicmp (blockName, ACDB_PAPER_SPACE))
            {
            AcDbBlockTableRecordIterator*     pEntIter;
            pBlock->newIterator (pEntIter, true, true);
            for (pEntIter->start(); !pEntIter->done(); pEntIter->step())
                {
                AcDbObjectId    entityId;
                pEntIter->getEntityId (entityId);
                AcDbEntityPointer   pEntity (entityId, AcDb::kForWrite);
                if (Acad::eOk == pEntity.openStatus())
                    pEntity->erase();
                }
            delete pEntIter;
            }
        else if (0 != wcsicmp (blockName, ACDB_MODEL_SPACE))
            {
            AcDbObjectId            layoutId;
            if (! (layoutId = pBlock->getLayoutId()).isNull())
                {
                const ACHAR*            layoutName;
                AcDbLayoutPointer       layout (layoutId, AcDb::kForRead);
                if (Acad::eOk != layout.openStatus())
                    continue;

                layout->getLayoutName (layoutName);
                if (NULL == layoutDeleteList)
                    layoutDeleteList = new bvector<AcString*>();
                layoutDeleteList->push_back (new AcString(layoutName));
                }
            }
        }
    delete pBlkIter;
    pBlockTable.close();

    if (NULL == layoutDeleteList)
        return;
    for each (AcString* layoutString in *layoutDeleteList)
        {
        Acad::ErrorStatus deleteStatus = RealDwgHostApp::Instance().layoutManager()->deleteLayout (layoutString->kwszPtr());
        BeAssert (Acad::eOk == deleteStatus && L"Unable to delete layout from seed DWG file!");
        delete layoutString;
        }

    delete layoutDeleteList;
    }

/*---------------------------------------------------------------------------------**//**
* Construct FileHolder from DWG Seed.
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
FileHolder::FileHolder
(
DgnFileP                    dgnFile,
WCharCP                     pFileName,
DgnModelP                   defaultModelRef
)
    {
    m_defaultModelInfo  = ModelInfo::Create(DgnModelType::Normal, NULL, false);
    // this constructor is used when we are creating a new DWG design file.

    AcDb::MeasurementValue  measure     = AcDb::kEnglish;
    Initialize (dgnFile, pFileName);

    InitializeFromSeedModel (defaultModelRef);

    m_loadToCacheMeter                  = NULL;
    m_useUorPerStorageFromSeedDirectly  = false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        AllHandlesBelow (AcDbObjectId objectId, void *userData)
    {
    UInt64          highestId = *((UInt64*)userData);
    UInt64          dbHandle  = RealDwgUtil::CastDBHandle (objectId.handle());
#if defined (REALDWG_DIAGNOSTIC_VERBOSE)
    DIAGNOSTIC_PRINTF ("Checking %ls, handle %I64d\n", objectId.objectClass()->name(), RealDwgUtil::CastDBHandle (dbHandle));
#endif
    if (dbHandle >= highestId)
        {
        AcRxClass*  desc = objectId.objectClass();
        const ACHAR* objectName = desc->name();
        BeAssert (false && L"Object handle value greater than expected!");
        }
    return (dbHandle < highestId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        AllHandlesAboveHighestId (AcDbObjectId objectId, void *userData)
    {
    ElementId       highestId = *((ElementId*)userData);
    ElementId       elementId = RealDwgUtil::CastDBHandle(objectId.handle());
    BeAssert (elementId > highestId && L"Object handle value greater than the highest expected!");
    return (elementId > highestId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
UInt64                      FileHolder::SetHandseed (ElementId desiredId)
    {
    UInt64              oldHandle = RealDwgUtil::CastDBHandle (m_database->handseed ());
    Acad::ErrorStatus   es = m_database->setHandseed (AcDbHandle (desiredId));

#if !defined (NDEBUG)
    // make sure there isn't already an object with that handle.
    AcDbObjectId    objectId;
    if (Acad::eOk == m_database->getAcDbObjectId (objectId, false, AcDbHandle (desiredId), 0))
        {
        const ACHAR* className = objectId.objectClass()->name();
        DIAGNOSTIC_PRINTF ("Error setting handseed: ID=%I64d already exists in database! %ls\n", desiredId, className);
        }
#endif
    return      oldHandle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::RestoreHandseed (UInt64 restoreId)
    {
    m_database->setHandseed (AcDbHandle (restoreId));
#if !defined (NDEBUG)
    AcDbObjectId    objectId;
    if (Acad::eOk == m_database->getAcDbObjectId (objectId, false, AcDbHandle (restoreId), 0))
        {
        const ACHAR* className = objectId.objectClass()->name();
        BeAssert (false && L"Newly restored object handle should not be used by any object, yet!");
        }
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        FileHolder::VerifyDesiredHandleValue (ElementId desiredId, AcDbHandle objectHandle)
    {
    return (RealDwgUtil::CastDBHandle (objectHandle) == desiredId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        FileHolder::NoExistingElementMatchesId (ElementId proposedId)
    {
    AcDbObjectId    objectId;
    m_database->getAcDbObjectId (objectId, false, AcDbHandle (proposedId), 0);

    if (objectId.isNull())
        return true;

    if (objectId.isErased())
        return true;

    DIAGNOSTIC_PRINTF ("Attempting to add object to database when object of type: %ls, ID: %I64d already exists\n", objectId.objectClass()->name(), proposedId);

    // this handle is taken!
    return false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        FileHolder::ExactlyOneAppended (ElementId desiredId)
    {
    UInt64 currentSeed  = RealDwgUtil::CastDBHandle (m_database->handseed());
    if (currentSeed < (desiredId+1))
        {
        DIAGNOSTIC_PRINTF ("ExactlyOneAppended: No object actually added\n");
        }
    for (UInt64 extraAdded = desiredId+1; extraAdded < currentSeed; extraAdded++)
        {
        AcDbObjectId addedObjectId;
        if (Acad::eOk == m_database->getAcDbObjectId (addedObjectId, false, AcDbHandle (extraAdded), 0))
            DIAGNOSTIC_PRINTF ("ExactlyOneAppended: Also added %ls, handle %I64d\n", addedObjectId.objectClass()->name(), extraAdded);
        }
    return (currentSeed == (desiredId+1));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        FileHolder::AllHandlesBelowSeed (AcDbObjectId skipId)
    {
    static int s_calls = 0;
    // do this every hundredth time through, otherwise it slows us down too much.
    if (0 != (s_calls++ % 100))
        return true;

    UInt64 currentSeed = RealDwgUtil::CastDBHandle (m_database->handseed());
    return RealDwgUtil::TraverseAllDatabaseObjects (m_database, AllHandlesBelow, skipId, &currentSeed);
    }


/*=================================================================================**//**
* WBlockEventReactor
* @bsiclass                                                     Barry.Bentley   01/09
+===============+===============+===============+===============+===============+======*/
class           WBlockEventReactor : public AcRxEventReactor
{
ElementId       m_startingHandleForSeedObjects;

public:
WBlockEventReactor (ElementId   startingHandleForSeedObjects)
    {
    m_startingHandleForSeedObjects = startingHandleForSeedObjects;
    }

virtual void beginDeepClone (AcDbDatabase *pTo, AcDbIdMapping& idMap)
    {
    // This method is the one on AcRxEventReactor that is called at the right time - right before they
    //  start copying the AcDbObjects from the source database to the destination database. If we set
    //  handseed here, all of the objects they create have AcDbHandles greater than that value.
    pTo->setHandseed (AcDbHandle(m_startingHandleForSeedObjects));
    }
};

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      04/01
+---------------+---------------+---------------+---------------+---------------+------*/
static code_page_id         GetDwgCodePageFromAnsiCodePage
(
int                         ansiCodePage
)
    {
    switch (ansiCodePage)
        {
        case 1252:
            return  code_page_id::CODE_PAGE_ANSI_1252;
        case 1250:
            return  code_page_id::CODE_PAGE_ANSI_1250;
        case 1251:
            return  code_page_id::CODE_PAGE_ANSI_1251;
        case 1253:
            return  code_page_id::CODE_PAGE_ANSI_1253;
        case 1254:
            return  code_page_id::CODE_PAGE_ANSI_1254;
        case 1255:
            return  code_page_id::CODE_PAGE_ANSI_1255;
        case 1256:
            return  code_page_id::CODE_PAGE_ANSI_1256;
        case 1257:
            return  code_page_id::CODE_PAGE_ANSI_1257;
        case 1258:
            return  code_page_id::CODE_PAGE_ANSI_1258;
        case 874:
            return  code_page_id::CODE_PAGE_ANSI_874;
        case 932:
            return  code_page_id::CODE_PAGE_ANSI_932;
        case 936:
            return  code_page_id::CODE_PAGE_ANSI_936;
        case 949:
            return  code_page_id::CODE_PAGE_ANSI_949;
        case 950:
            return  code_page_id::CODE_PAGE_ANSI_950;
        case 1361:
            return code_page_id::CODE_PAGE_ANSI_1361;
        case 437:
            return code_page_id::CODE_PAGE_DOS437;
        case 850:
            return code_page_id::CODE_PAGE_DOS850;
        case 852:
            return code_page_id::CODE_PAGE_DOS852;
        case 855:
            return code_page_id::CODE_PAGE_DOS855;
        case 857:
            return code_page_id::CODE_PAGE_DOS857;
        case 860:
            return code_page_id::CODE_PAGE_DOS860;
        case 861:
            return code_page_id::CODE_PAGE_DOS861;
        case 863:
            return code_page_id::CODE_PAGE_DOS863;
        case 864:
            return code_page_id::CODE_PAGE_DOS864;
        case 865:
            return code_page_id::CODE_PAGE_DOS865;
        case 869:
            return code_page_id::CODE_PAGE_DOS869;
        case 10000:
            return code_page_id::CODE_PAGE_MACINTOSH;
        case 866:
            return code_page_id::CODE_PAGE_DOS866;
        case  28591:
            return  code_page_id::CODE_PAGE_8859_1;
        case  28592:
            return  code_page_id::CODE_PAGE_8859_2;
        case  28593:
            return  code_page_id::CODE_PAGE_8859_3;
        case  28594:
            return  code_page_id::CODE_PAGE_8859_4;
        case  28595:
            return  code_page_id::CODE_PAGE_8859_5;
        case  28596:
            return  code_page_id::CODE_PAGE_8859_6;
        case  28597:
            return  code_page_id::CODE_PAGE_8859_7;
        case  28598:
            return  code_page_id::CODE_PAGE_8859_8;
        case  28599:
            return  code_page_id::CODE_PAGE_8859_9;
        default :
            return  code_page_id::CODE_PAGE_ASCII;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/13
+---------------+---------------+---------------+---------------+---------------+------*/
static  bool    KeepFirstSheetInActiveFile (ModelIndex const& modelIndex, ModelId modelspaceModelId)
    {
    /*-------------------------------------------------------------------------------------------------------
    Determine if we need to keep the first sheet model, which may or may not be the defautl model, in the 
    active file when we are saving sheets to separate files:

    1) If we are NOT saving sheet models to separate files, we always keep the default sheet model in the
        active file.

    2) If we are saving sheets to separate files, we want to avoid creating an empty active DWG file whose
        modelspace & paperspace are both empty.  For example, a DGN file with only sheet models which are to
        be saved as layouts, should keep the first sheet in the active file and rest to separate files.  To 
        achieve this goal, we walk through all models to see if we'd create a modelspace from any anyone of 
        them.  If a modelspace candidate exists, we do not need to keep a sheet in the active file.
    --------------------------------------------------------------------------------------------------------*/
    // Case 1: we are not saving sheets to separate files
    if (!MstnInterfaceHelper::Instance().GetSettings().SaveSheetsToSeparateFiles())
        return  true;

    // Case 2: we are saving sheets to separate files, and also saving the default sheet as a layout, leaving us a blank modelspace:
    if (INVALID_MODELID == modelspaceModelId)
        return  true;

    // Other cases that may alse leave us a blank modelspace when saving sheets to seprate files:
    FOR_EACH (ModelIndexItemCR indexItem, modelIndex)
        {
        if (modelspaceModelId == indexItem.GetModelId())
            return  false;
        }

    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   FileHolder::LoadAcadDatabaseFromSeedDwgFile (DgnModelP defaultModelRef)
    {
    try
        {
        RealDwgHostAppR     hostApp = RealDwgHostApp::Instance();

        /*-------------------------------------------------------------------------------
        Determine which DGN model to be saved as DWG ModelSpace model:

            1) The Default model is a normal model: save the Default model as the modelspace.
            2a) The Default model is not a normal model, but there is a normal model in the file:
                save the first normal model as modelspace and the Default as a Layout.
            2b) The Default model is not a normal model, but there is a Drawing model in the file:
                save the first Drawing model as modelspace and the Default as a Layout.
            3) The Default model is a sheet model & there is no normal model in file:
                save the single sheet model as modelspace only if MS_DWG_SINGLESHEET_AS_MODELSPACE
                is present; otherwise save it as a layout.

        Cases 1 & 2 are done by RealDwgUtil::GetDwgModelSpaceId.  For case 3, we simply set
        modelSpaceModelId to INVALID_MODELID such that the seed DWG's modelspace will be used
        as is, and the only sheet model in file will be saved as a layout.
        -------------------------------------------------------------------------------*/
        ModelId     modelSpaceModelId = RealDwgUtil::GetDwgModelSpaceId (m_dgnFile);
        ModelId     defaultModelId = m_dgnFile->GetDefaultModelId ();
        if (modelSpaceModelId == defaultModelId && DgnModelType::Sheet == defaultModelRef->GetModelType() && !m_saveSheetsAsModelspace)
            modelSpaceModelId = INVALID_MODELID;

        // Below is a bit tricky - we need to read the seed file, but shift its handles
        // so that they don't conflict with the ElementIds we have in the DGN file.
        AcDbDatabase*   pSeedDatabase = new AcDbDatabase (false);
        pSeedDatabase->readDwgFile (m_fileName.c_str());

        // Remove any extra blocks that would cause wblock crash (TR: 131087)
        hostApp.SetWorkingDatabase (pSeedDatabase);
        RemoveExtraSeedPaperSpaces (pSeedDatabase);

        UInt64  seedFileHandSeed = RealDwgUtil::CastDBHandle (pSeedDatabase->handseed());

        // We need to set the new database up such that the entities within it start with handles greater than highestId, because
        //   we want to make the ACAD object handle the same as the MicroStation ElementId's for all elements in dgnFile.
        //   To do this, we set up an AcRxEventReactor, and use the "beginDeepClone" method to set the database handSeed.
        WBlockEventReactor* reactor = new WBlockEventReactor (m_dgnFile->GetHighestID()+1);
        acrxEvent->addReactor (reactor);
        Acad::ErrorStatus   wblockStatus = pSeedDatabase->wblock (m_database);
        BeAssert (Acad::eOk == wblockStatus && L"WBLOCK failed!");
        acrxEvent->removeReactor (reactor);

        hostApp.SetWorkingDatabase (m_database);

        // Remove any table entries that exist in the input file.
        RemoveRedundantSeedEntries (m_database, defaultModelRef);

        // verify that all the AcDbObjects in the database have handles below handseed.
        BeAssert (this->AllHandlesBelowSeed (AcDbObjectId::kNull) && L"Object handle verification failed!");

        // Pass on header variable Measurement to the new database.
        // ODA will investigate other header variables to see what else are missing and correct them based on ACAD's wblock behavior.
        m_database->setMeasurement (pSeedDatabase->measurement());

        delete pSeedDatabase;

        // First Create list of all of the existing model/blocks.
        AcDbObjectId                blockTableId = m_database->blockTableId();
        AcDbBlockTablePointer       pBlockTable (blockTableId, AcDb::kForRead);
        if (Acad::eOk != pBlockTable.openStatus())
            return  BSIERROR;

        AcDbObjectId                modelObjectId;
        AcDbObjectIdArray           paperSpaceIds;

        AcDbBlockTableIterator*     pBlkIter;
        pBlockTable->newIterator (pBlkIter, true, true);
        for (pBlkIter->start(); ! pBlkIter->done(); pBlkIter->step())
            {
            AcDbObjectId                    blockId;
            if (Acad::eOk != pBlkIter->getRecordId (blockId))
                continue;

            // skip all blocks that aren't either paper space or model space. Both of those have "isLayout" true.
            AcDbBlockTableRecordPointer     pBlock (blockId, AcDb::kForRead, false);
            if (Acad::eOk != pBlock.openStatus() || !RealDwgUtil::IsModelOrPaperSpace(pBlock.object()))
                continue;

            const ACHAR* blockName;
            if (Acad::eOk != pBlock.object()->getName (blockName))
                continue;

            if (0 == wcsicmp (blockName, ACDB_MODEL_SPACE))
                modelObjectId = blockId;
            else
                paperSpaceIds.append (blockId);
            }
        delete pBlkIter;
        pBlockTable.close();

        unsigned int            iSheet = 0;
        FOR_EACH (ModelIndexItemCR indexItem, m_dgnFile->GetModelIndex())
            {
            int                 modelId = indexItem.GetModelId ();
            bool                isHidden = indexItem.IsHidden ();
            WCharCP             modelName = indexItem.GetName ();
            DgnModelType        modelType = indexItem.GetModelType ();

            if (modelId == modelSpaceModelId)
                {
                AcDbBlockTableRecordPointer pBlock (modelObjectId, AcDb::kForRead, false);  // Always assign default to the main model.
                if (Acad::eOk != pBlock.openStatus())
                    continue;
                m_models.push_back (new RealDwgModelIndexItem (modelId, &*m_defaultModelInfo, pBlock.object()->objectId(), this, RDWGMODEL_TYPE_DefaultModel));
                }
            else if (DgnModelType::Sheet == modelType)
                {
                if (iSheet < 1 && KeepFirstSheetInActiveFile(m_dgnFile->GetModelIndex(), modelSpaceModelId))
                    {
                    // save the first sheet model as a layout unless the user is saving non-default sheets to separate files:
                    AcDbBlockTableRecordPointer pPaperBlock (paperSpaceIds[iSheet], AcDb::kForRead, false);
                    if (Acad::eOk != pPaperBlock.openStatus())
                        continue;
                    m_models.push_back (new RealDwgModelIndexItem (modelId, &*m_defaultModelInfo, pPaperBlock.object()->objectId(), this, RDWGMODEL_TYPE_Sheet));

                    AcDbObjectId    layoutId;

                    if (! (layoutId = pPaperBlock.object()->getLayoutId()).isNull())
                        {
                        WChar       nameChars[1024];
                        RealDwgUtil::TerminatedStringCopy (nameChars, modelName, _countof(nameChars));
                        ConvertFromDgnContext::ValidateName (nameChars, DwgFileVersion_13);

                        try
                            {
                            AcDbLayoutPointer layout (layoutId, AcDb::kForWrite, false);
                            if (Acad::eOk == layout.openStatus())
                                {
                                layout->setLayoutName (nameChars);
                                layout->setAnnoAllVisible (true);
                                }
                            }
                        catch (...)
                            {
                            DIAGNOSTIC_PRINTF ("Exception caught attempting to name sheet: %ls\n", nameChars);
                            }
                        }
                    }
                else
                    {
                    // save all other sheet models to separate files
                    this->AddModel (modelId, modelType, modelName);
                    }
                iSheet++;
                }
            else if (!isHidden)
                {
                this->AddModel (modelId, modelType, modelName);
                }
            }

        }
    catch (...)
        {
        return BSIERROR;
        }

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 FixMainViewportData (AcDbLayout* pLayout)
    {
    AcDbObjectIdArray       viewports = pLayout->getViewportArray ();

    // initialize minimum viewport data so satisfy ACAD's audit for an empty viewport:
    if (viewports.length() > 0 && viewports.first().isValid())
        {
        AcDbViewportPointer pViewport (viewports.first(), AcDb::kForWrite);
        if (Acad::eOk == pViewport.openStatus())
            {
            pViewport->setLayer (acdbSymUtil()->layerZeroId(pLayout->database()));
            pViewport->setViewDirection (AcDb::kTopView);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/2003
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        FileHolder::AddModel (int modelId, DgnModelType modelType, WCharCP modelName, int sheetTabOrder)
    {
    IDwgConversionSettings&         settings                    = MstnInterfaceHelper::Instance().GetSettings();
    DwgSaveNonDefaultModelMode      nonDefaultModelMode         = settings.GetNonDefaultModelMode();
    bool                            saveSheetsToSeparateFiles   = settings.SaveSheetsToSeparateFiles();
    WChar                           nameChars[1024];

    if (DgnModelType::Extraction_Deprecated == modelType)
        {
        // Dont create models for extractions.  These will be handled by merging.
        return false;
        }

    AcString   nameString (modelName);
    RealDwgUtil::TerminatedStringCopy (nameChars, modelName, _countof (nameChars));
    if (ConvertFromDgnContext::ValidateName (nameChars, DwgFileVersion_13))
        nameString.assign (nameChars);

    if ( (DgnModelType::Sheet == modelType && !saveSheetsToSeparateFiles) || (NonDefaultModels_CreateSheets == nonDefaultModelMode))
        {
        AcDbLayoutManager*      layoutManager   = RealDwgHostApp::Instance().layoutManager();
        AcDbObjectId            layoutId;
        AcDbObjectId            blockId;
        Acad::ErrorStatus       createStatus    = layoutManager->createLayout (nameString.kwszPtr(), layoutId, blockId, m_database);
        if (Acad::eDuplicateKey == createStatus)
            {
            if (Acad::eOk == layoutManager->deleteLayout(nameString.kwszPtr()))
                {
                // Target DWG's layout is deleted:
                createStatus = layoutManager->createLayout (nameString.kwszPtr(), layoutId, blockId, m_database);
                DIAGNOSTIC_PRINTF ("Layout %ls has been deleted and %hs.\n", nameString.kwszPtr(), Acad::eOk==createStatus ? "replaced with a new one with same name" : "but failed to create a new one");
                }
            else
                {
                // This happens when dup models are from source DGN. Try renaming the layout.
                for (UInt32 suffix = 1; Acad::eDuplicateKey == createStatus && suffix < 5000; suffix++)
                    {
                    nameString += L"_" + AcString(AcString::kUnSigned, suffix);
                    createStatus = layoutManager->createLayout(nameString.kwszPtr(), layoutId, blockId, m_database);
                    }
                }
            }

        BeAssert (Acad::eOk == createStatus && L"Failed creating new layout!");
        if (Acad::eOk != createStatus)
            return  false;

        if (sheetTabOrder < 0)
            {
            AcDbDictionaryPointer           pLayoutDictionary (m_database->layoutDictionaryId(), AcDb::kForRead);
            if (Acad::eOk == pLayoutDictionary.openStatus())
                {
                AcDbDictionaryIterator*         pIterator = pLayoutDictionary->newIterator();

                for (; !pIterator->done(); pIterator->next())
                    {
                    AcDbLayoutPointer   thisLayout (pIterator->objectId(), AcDb::kForRead);
                    int         existingTabOrder = thisLayout.object()->getTabOrder();

                    if (existingTabOrder >= sheetTabOrder)
                        sheetTabOrder = existingTabOrder + 1;
                    }
                delete pIterator;
                }
            else
                {
                sheetTabOrder = 0;
                BeAssert (Acad::eOk == pLayoutDictionary.openStatus() && L"Cannot open layout dictionary!");
                }
            }

        AcDbLayoutPointer       pLayout (layoutId, AcDb::kForWrite);
        if (Acad::eOk == pLayout.openStatus())
            {
            pLayout->initialize ();
            pLayout->setTabOrder (sheetTabOrder);
            if (DgnModelType::Sheet == modelType)
                pLayout->setAnnoAllVisible (true);

            FixMainViewportData (pLayout);
            }
        else
            {
            BeAssert (Acad::eOk == pLayout.openStatus() && L"Cannot open layout object for write!");
            }

        m_models.push_back (new RealDwgModelIndexItem (modelId, &*m_defaultModelInfo, blockId, this, RDWGMODEL_TYPE_Sheet));
        }

    else if ((DgnModelType::Sheet == modelType && saveSheetsToSeparateFiles) || (NonDefaultModels_SeparateFiles == nonDefaultModelMode) )
        {
        // This is a non-sheet, non-default model - it therefore cannot be stored in a DWG file.
        // Create a "temporary" block to represent this model - it will not be written to the
        // file, but will be used to create separate files from the models.
        AcDbObjectId            blockTableId = m_database->blockTableId();
        AcDbBlockTablePointer   pBlockTable  (blockTableId, AcDb::kForWrite);
        if (Acad::eOk == pBlockTable.openStatus())
            {
            AcDbBlockTableRecord*   pBlock       = new AcDbBlockTableRecord();
            AcString                newName      = AcString ("Model - ") + nameString;
            ConvertFromDgnContext::DeduplicateTableName (pBlockTable, newName);
            pBlock->setName (newName);
            Acad::ErrorStatus       addStatus    = pBlockTable->add (pBlock);
            AcDbObjectId            blockId;
            if (Acad::eOk == addStatus)
                {
                blockId = pBlock->objectId ();
                pBlock->close ();
                }
            else
                {
                blockId = AcDbObjectId::kNull;
                delete pBlock;
                }

            m_models.push_back (new RealDwgModelIndexItem (modelId, &*m_defaultModelInfo, blockId, this, RDWGMODEL_TYPE_NonDefaultModel));
            }
        }
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 FreeTextStyleMapItem
(
void                       *nodeToFreeP,
void                       *pUserData
)
    {
    RealDwgGeneratedTextStyleItem   *pItem = static_cast<RealDwgGeneratedTextStyleItem *>(nodeToFreeP);
    delete pItem;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FreeExternalRefItem
(
void                       *nodeToFreeP,
void                       *pUserData
)
    {
    ExternalRefTreeNode         *pItem = static_cast <ExternalRefTreeNode *> (nodeToFreeP);
    delete pItem;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
FileHolder::~FileHolder
(
)
    {
    delete  m_layerIndex;
    delete  m_linetypeIndex;
    delete  m_textStyleIndex;
    delete  m_multilineStyleIndex;
    delete  m_dwgSymbologyData;
    delete  m_dgnSymbologyData;
    delete  m_pFontMap;
    
    // remove all imageDefs before they become invalid
    m_unloadedRasterImageDefs.removeAll ();
    
    if (NULL != m_lockedLayers)
        {
        BeAssert (false && L"The locked layers should already have been restored and deleted!");
        delete m_lockedLayers;
        }

    // this closes the file held open since readDwgFile.
    RealDwgHostApp::Instance().DatabaseDeleted (m_database);

    // if this is the master file held by the host, tell the host to clear it up:
    AcDbDatabaseP   masterDwg = nullptr;
    if (DwgPlatformHost::Instance()._GetMasterDwgFile(masterDwg, nullptr) && masterDwg == m_database)
        DwgPlatformHost::Instance()._SetMasterDwgFile (nullptr, nullptr);

    delete  m_database;

    for each (RealDwgModelIndexItemP modelItem in m_models)
        delete modelItem;

    if (NULL != m_pHeaderDescr)
        {
        m_pHeaderDescr->Release ();
        m_pHeaderDescr = NULL;
        }
        
    mdlAvlTree_free (&m_pExternalRefByAttachPathTree, FreeExternalRefItem, NULL);
    mdlAvlTree_free (&m_pGeneratedTextStyles,FreeTextStyleMapItem, NULL);
    mdlAvlTree_free (&m_pPostProcessIdTree, NULLFUNC, NULL);
    mdlAvlTree_free (&m_pBlockByIdTree, NULLFUNC, NULL);
    mdlAvlTree_free (&m_pMaterialTree, NULLFUNC, NULL);
    mdlRaster_terminate();

    for each (AcDbAnnotationScale* vpAnnoscale in m_newViewportAnnotationScales)
        {
        if (nullptr != vpAnnoscale)
            delete vpAnnoscale;
        }
    m_newViewportAnnotationScales.clear ();
    m_elementsExcludedFromSaving.clear ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/2003
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   FileHolder::SaveThumbnail
(
const byte*                 pData,
int                         dataSize
)
    {
    if (dataSize < 0)
        return  BSIERROR;

    Acad::ErrorStatus es = m_database->setThumbnailBitmap ((void*)pData);

    return Acad::eOk == es ? SUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/2003
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   FileHolder::DeleteModel
(
ModelId     modelId
)
    {
    try
        {
        RealDwgModelIndexItem   *pModelItem;
        AcDbObjectId            layoutId;

        if (NULL != (pModelItem = this->GetModelItemByModelId (modelId)) &&
            ! pModelItem->GetBlockTableRecordId().isNull() )
            {
            AcDbBlockTableRecordPointer pBlockTableRecord (pModelItem->GetBlockTableRecordId(), AcDb::kForRead);
            if (Acad::eOk == pBlockTableRecord.openStatus() && !(layoutId = pBlockTableRecord.object()->getLayoutId()).isNull())
                {
                const ACHAR*            layoutName;
                AcDbLayoutPointer       layout (layoutId, AcDb::kForRead);
                if (Acad::eOk == layout.openStatus())
                    {
                    layout.object()->getLayoutName (layoutName);
                    layout.close ();
                    pBlockTableRecord.close ();
                    RealDwgHostApp::Instance().layoutManager()->deleteLayout (layoutName);
                    }
                }
            return SUCCESS;
            }
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception thrown attempting to delete model\n");
        }

    return BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/11
+---------------+---------------+---------------+---------------+---------------+------*/
static Acad::ErrorStatus    ActivateModelOrPaperSpace (AcDbObjectId& layoutId, AcDbDatabase* pDwg, bool modelspace)
    {
    /*-----------------------------------------------------------------------------------------------------------
    RealDWG R2012 has changed its logic of displaying annotative objects from context manager to directly drawing
    them in a given viewport.  This change has effectively caused a regression to us: the world or viewport draw 
    of annotative objects has become impossible in modelspace while the active model is a paperspace.

    Before we fully support annotative objects, we have little choice but to workaround this problem by activating
    modelspace before we create elements in block section and entity section.  Then we will have to re-activate 
    original paperspace.  Just providing a valid modelspace viewport Id for world/viewport draw alone, without 
    actually changing to the modelspace, does not solve the problem.  We really have to switch active model.
    -----------------------------------------------------------------------------------------------------------*/
    Acad::ErrorStatus       errorStatus = Acad::eLayoutNotCurrent;
#if RealDwgVersion >= 2012
    AcDbLayoutManager*      layoutManager = RealDwgHostApp::Instance().layoutManager();
    if (NULL == layoutManager)
        return  errorStatus;

    if (modelspace)
        {
        // activate modelspace, if not already active:
        AcDbObjectId    blockId = RealDwgUtil::FindActiveLayoutBlockId (layoutManager, pDwg);
        AcDbObjectId    modelspaceId = acdbSymUtil()->blockModelSpaceId (pDwg);
        if (blockId.isValid() && blockId != modelspaceId)
            {
            AcDbBlockTableRecordPointer block(blockId, AcDb::kForRead);
            if (Acad::eOk == block.openStatus())
                {
                layoutId = block->getLayoutId ();
                block.close ();
                block.open (modelspaceId, AcDb::kForRead);
                if (Acad::eOk == block.openStatus())
                    errorStatus = layoutManager->setCurrentLayoutId (block->getLayoutId());
                else
                    layoutId = AcDbObjectId::kNull;
                }
            }
        }
    else
        {
        // re-activate original layout:
        errorStatus = layoutManager->setCurrentLayoutId (layoutId);
        }
#endif

    return  errorStatus;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   FileHolder::ReadModelIntoCache
(
DgnModelP                   modelRef,
ModelId                     modelId,
bool                        loadDictionary
)
    {
    RealDwgModelIndexItem   *pModelItem;

    if (NULL == (pModelItem = this->GetModelItemByModelId (modelId)))
        return BSIERROR;

    /*-----------------------------------------------------------------------------------
    Prevent inadvertent recursion: if the requested model, or any of the indexed DWG models,
    is being processed, we are in a recursion. A call to ViewInfo::GetRootModelP(true) while
    a DWG file is being opened, for example, could get us here.
    -----------------------------------------------------------------------------------*/
    for (auto modelItem : this->GetModelIndexItems())
        {
        if (modelItem->GetLoadInProgress())
            return BSISUCCESS;
        }

    pModelItem->SetLoadInProgress (true);

    //TFS#1014882: Add a configuration variable to switch off the multiprocessing of the acis->Parasolid conversion for dwg
    ConvertToDgnContext* context = nullptr;
    if (ConfigurationManager::IsVariableDefinedAndTrue(L"MS_DWG_OPEN_MULTIPROCESSING"))
        context = new ConvertToDgnMultiProcessingContext (modelRef, this, pModelItem, m_loadToCacheMeter, MstnInterfaceHelper::Instance ().GetSettings ());
    else
        context = new ConvertToDgnContext (modelRef, this, pModelItem, m_loadToCacheMeter, MstnInterfaceHelper::Instance().GetSettings());
    
    //TR #300390 Fixed problem with self-reference models and rasters that have been unloaded to prevent sharing violations
    // between realDWG and our raster libraries
    int imageDefsCount = GetUnloadedRasterImageDefs().length();
    // check to see if any rasters have already been unloaded and add them to the context's array of unloaded rasters
    for (int idx=0; idx < imageDefsCount; idx++)
        {
        context->AddUnloadedRasterImageDef (GetUnloadedRasterImageDefs()[idx]);
        }

    AcDbObjectId        activeLayoutId;
    if (loadDictionary)
        {
        context->SaveDictionaryObjectsToDgn ();
        if (DEFAULTMODEL == modelId)
            ActivateModelOrPaperSpace (activeLayoutId, m_database, true);
        context->SaveSharedCellDefinitionsToDgn ();
        }
    else if (this->GetLayerIndex()->IsIdTableEmpty())
        {
        context->SetNoSaveToDgnCache (true);
        context->SaveDictionaryObjectsToDgn ();
        context->SetNoSaveToDgnCache (false);
        }

    context->SaveModelInfoToDgn ();
    context->SaveModelApplicationElementsToDgn (pModelItem->GetId());
    AcDbBlockTableRecordPointer blockTableRecord (pModelItem->GetBlockTableRecordId(), AcDb::kForRead);
    if (Acad::eOk != blockTableRecord.openStatus())
        return  BSIERROR;
    context->SaveModelEntitiesToDgn (blockTableRecord.object());
    context->PostProcess ();

    if (DEFAULTMODEL == modelId && activeLayoutId.isValid())
        ActivateModelOrPaperSpace (activeLayoutId, m_database, false);

    //TR #300390 continue
    AcDbObjectIdArray const& newImageDefs = context->GetUnloadedRasterImageDefs ();
    int newImageDefsCount = newImageDefs.length();
    // check to see if any rasters have just been unloaded in the context and add them to our array
    for (int idx=0; idx < newImageDefsCount; idx++)
        {
        if (newImageDefs[idx] == AcDbObjectId::kNull)
            break;
        if (!GetUnloadedRasterImageDefs().contains (newImageDefs[idx]))
            AddUnloadedRasterImageDefs(newImageDefs[idx]);
        }

    // make sure the cache monitor is killed off once the model is loaded.
    if (modelId >= 0 && NULL != m_loadToCacheMeter)
        {
        delete m_loadToCacheMeter;
        m_loadToCacheMeter = NULL;
        }
    pModelItem->SetLoadInProgress(false);

    delete context;

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::IndexAcadModels ()
    {
    int                         nextModelId = 1;
    AcDbBlockTablePointer       pBlockTable (m_database->blockTableId(), AcDb::kForRead);
    if (Acad::eOk != pBlockTable.openStatus())
        return;

    AcDbBlockTableIterator*     pBlkIter = nullptr;
    if (Acad::eOk != pBlockTable->newIterator(pBlkIter))
        return;

    for (pBlkIter->start(); ! pBlkIter->done(); pBlkIter->step())
        {
        AcDbObjectId                blockId;
        if (Acad::eOk != pBlkIter->getRecordId (blockId))
            continue;

        AcDbBlockTableRecordPointer     pBlock  (blockId, AcDb::kForRead);
        if (Acad::eOk == pBlock.openStatus() && RealDwgUtil::IsModelOrPaperSpace(pBlock.object()))
            {
            bool            isModel     = false;
            const ACHAR*    blockName;
            if (Acad::eOk == pBlock.object()->getName (blockName))
                isModel = (0 == wcsicmp (blockName, ACDB_MODEL_SPACE));

            m_models.push_back (new RealDwgModelIndexItem (isModel ? 0 : nextModelId++, &*m_defaultModelInfo, pBlock.object()->objectId(), this, isModel ? RDWGMODEL_TYPE_DefaultModel : RDWGMODEL_TYPE_Sheet));
            }
        }
    delete pBlkIter;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgModelIndexItemP      FileHolder::GetModelItemByModelId (ModelId modelId)
    {
    for each (RealDwgModelIndexItemP modelItem in m_models)
        {
        if (NULL != modelItem && modelItem->GetId() == modelId)
            return modelItem;
        }
    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgModelIndexItemP      FileHolder::GetDefaultModelItem ()
    {
    for each (RealDwgModelIndexItemP modelItem in m_models)
        {
        if (modelItem->GetRealDwgModelType() == RDWGMODEL_TYPE_DefaultModel)
            return modelItem;
        }
    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   05/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::RecordLockedLayers ()
    {
    // This method finds every locked layer, saves its objectId in the m_lockedLayers array,
    //  and unlocks the layer. That's so we can modify entities on those layers.
    //  When we're all done, we relock the layers that started out locked.
    AcDbDatabase*               database = GetDatabase();
    AcDbLayerTablePointer       pLayers (database->layerTableId(), AcDb::kForRead);

    // the locked layers may have already been detected and saved.
    if (NULL != m_lockedLayers)
        return;

    // we use non-NULL m_lockedLayers to tell that we've been through here before, so we don't lazy-create m_lockedLayers.
    m_lockedLayers = new AcDbObjectIdArray();

    if (Acad::eOk != pLayers.openStatus())
        {
        BeAssert (false && L"Cannot open layer table to read locked layers!");
        return;
        }

    AcDbLayerTableIterator*     pIter;
    if (Acad::eOk != pLayers->newIterator (pIter))
        {
        BeAssert (false && L"Failed creating layer iterator!");
        return;
        }

    for (pIter->start(); !pIter->done(); pIter->step())
        {
        AcDbObjectId    layerId;
        if (Acad::eOk != pIter->getRecordId (layerId))
            continue;

        AcDbLayerTableRecordPointer pLayer (layerId, AcDb::kForRead);
        if (Acad::eOk != pLayer.openStatus())
            continue;

        if (pLayer->isLocked())
            {
            if (Acad::eOk == pLayer->upgradeOpen())
                {
                m_lockedLayers->append (layerId);
                pLayer->setIsLocked (false);
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   05/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::RecallLockedLayers ()
    {
    if (NULL == m_lockedLayers)
        return;

    for (int iLocked=0, count = m_lockedLayers->length(); iLocked < count; iLocked++)
        {
        AcDbLayerTableRecordPointer layer ((*m_lockedLayers)[iLocked], AcDb::kForWrite);
        if (Acad::eOk == layer.openStatus())
            layer->setIsLocked (true);
        }
    delete m_lockedLayers;
    m_lockedLayers = NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Simon.Normand                   08/2010
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectIdArray const&  FileHolder::GetUnloadedRasterImageDefs () const
    {
    return m_unloadedRasterImageDefs;
    }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Simon.Normand                   08/2010
+---------------+---------------+---------------+---------------+---------------+------*/
void FileHolder::AddUnloadedRasterImageDefs (AcDbObjectId const& imageDef)
    {
    m_unloadedRasterImageDefs.append (imageDef);
    }

/*---------------------------------------------------------------------------------**//**
*  - Add AVL Tree for fast lookup.
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetLayerByName (WCharCP searchName)
    {
    AcDbObjectId                    foundLayerId;

    AcDbLayerTablePointer           pLayers (m_database->layerTableId(), AcDb::kForRead);
    if (Acad::eOk != pLayers.openStatus())
        return  foundLayerId;

    AcDbLayerTableIterator*         pIter;
    pLayers->newIterator (pIter);

    for (pIter->start(); !pIter->done(); pIter->step())
        {
        AcDbObjectId    layerId;
        if (Acad::eOk != pIter->getRecordId (layerId))
            continue;

        AcDbLayerTableRecordPointer  pLayer (layerId, AcDb::kForRead);
        const ACHAR*                layerName;
        if (Acad::eOk != pLayer.openStatus() || Acad::eOk != pLayer.object()->getName (layerName))
            continue;

        if (0 == _wcsicmp (searchName, layerName))
            {
            foundLayerId = layerId;
            break;
            }
        }
    delete pIter;
    return foundLayerId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/98
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId    FileHolder::GetLayerByXRefAndLevelHandle (LevelHandle const& level, AcDbBlockTableRecord* pXRefBlock, DgnModelRefP modelRef, bool savingChanges)
    {
    AcDbObjectId    layerObjectId;
    LevelId         levelId = level.GetLevelId ();

    if (LEVEL_BYCELL == levelId || nullptr == pXRefBlock)
        {
        // get layer from current DWG file:
        layerObjectId = savingChanges ? this->GetLayerByLevelHandle(level) : this->GetLayerByLevelId(levelId);
        }
    else
        {
        // NEEDS_WORK.  Store index for each XREF to avoid costly lookup.
        WString     levelName;
        WCharCP     name = level.GetName ();

        if (nullptr != name && 0 != name[0])
            {
            // majority if not all cases
            levelName.assign (name);
            }
        else
            {
            // use proxy name as that is what we used to get from mdlLevel_getName:
            DgnAttachmentCP         refAttach = nullptr;
            ProxyDisplayCacheBaseP  proxyCache = nullptr;
            WStringCP               proxyName = nullptr;

            if (nullptr != modelRef && nullptr != (refAttach = modelRef->AsDgnAttachmentCP()) &&
                nullptr != refAttach->FindProxyHandler(&proxyCache, nullptr) && nullptr != (proxyName = proxyCache->GetLevelName(modelRef, levelId)))
                levelName = *proxyName;

            if (levelName.empty())
                return  layerObjectId;
            }

        // validate the default and unique layer name "0":
        if (levelName.Equals(L"0") && LEVEL_BYCELL != levelId)
            levelName.assign (L"_0");

        AcString            layerNameString;
        RealDwgUtil::GetXRefDependentSymbolTableName (layerNameString, levelName.GetWCharCP(), pXRefBlock);
        layerObjectId = this->GetLayerByName (layerNameString.kwszPtr());
        }

    return layerObjectId;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetLayerByLevelId (UInt32 levelId)
    {
    AcDbObjectId            layerObjectId;

    if (LEVEL_BYCELL == levelId)
        return m_database->layerZero();

    AcDbHandle              layerHandle;
    if (! (layerHandle = m_layerIndex->GetDBHandle (levelId)).isNull())
        {
        Acad::ErrorStatus getStatus = m_database->getAcDbObjectId (layerObjectId, false, layerHandle);
        BeAssert (Acad::eOk == getStatus && L"Cannot find layer from levelId!");
        }

    return layerObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/17
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId    FileHolder::GetLayerByLevelHandle (LevelHandle const& level)
    {
    AcDbObjectId    layerObjectId;
    if (!level.IsValid())
        return  layerObjectId;

    if (LEVEL_BYCELL == level.GetLevelId())
        return m_database->layerZero();

    AcDbHandle      layerHandle (level.GetElementId());
    if (!layerHandle.isNull())
        m_database->getAcDbObjectId (layerObjectId, false, layerHandle);

    return  layerObjectId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetLinetypeByStyleId (Int32 styleId)
    {
    AcDbObjectId            linetypeObjectId;

    if (0 == styleId)
        return m_database->continuousLinetype();

    AcDbHandle              linetypeHandle;
    if (! (linetypeHandle = m_linetypeIndex->GetDBHandle (styleId)).isNull())
        {
        Acad::ErrorStatus getStatus = m_database->getAcDbObjectId (linetypeObjectId, false, linetypeHandle);
        BeAssert (Acad::eOk == getStatus && L"Cannot find linetype from styleId!");
        }

    return linetypeObjectId;
    }

/*---------------------------------------------------------------------------------**//**
*  - Add AVL Tree for fast lookup if this is bottleneck. - Right now,
*   I'm assumning that number of linetypes is small and mostly they will be looked
*   up by styleId.
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetLinetypeByName (WCharCP pName)
    {
    AcDbLinetypeTablePointer        pLineTypes (m_database->linetypeTableId(), AcDb::kForRead);
    if (Acad::eOk != pLineTypes.openStatus())
        return  AcDbObjectId::kNull;

    AcDbObjectId                    lineTypeId;
    AcDbLinetypeTableIterator*      pIter;
    pLineTypes->newIterator (pIter);
    for (pIter->start(); !pIter->done(); pIter->step())
        {
        AcDbObjectId                testLineTypeId;
        if (Acad::eOk != pIter->getRecordId (testLineTypeId))
            continue;

        AcDbLinetypeTableRecordPointer  pLineType (testLineTypeId, AcDb::kForRead);
        if (Acad::eOk != pLineType.openStatus())
            continue;

        const ACHAR*                    lineTypeName;
        if ( (Acad::eOk == pLineType->getName (lineTypeName)) && (0 == wcsicmp (lineTypeName, pName)) )
            {
            lineTypeId = testLineTypeId;
            break;
            }
        }
    delete pIter;

    return lineTypeId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgModelIndexItemP       FileHolder::GetActiveModelIndexItem ()
    {
    AcDbObjectId            activeBlockId;
    // Note: Their layout manager says it gets info for the "current" database or the "active" database.
    //       The only thing I saw that set anything like that is setting the "working" database in AcDbHostApplicationServices.
    AcDbLayoutManager*      layoutManager = RealDwgHostApp::Instance().layoutManager ();
    if (!(activeBlockId = layoutManager->getActiveLayoutBTRId()).isNull() ||
        !(activeBlockId = RealDwgUtil::FindActiveLayoutBlockId(layoutManager, m_database)).isNull())
        {
        for each (RealDwgModelIndexItemP modelItem in m_models)
            {
            if (activeBlockId == modelItem->GetBlockTableRecordId())
                return modelItem;
            }
        }
    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/09
+---------------+---------------+---------------+---------------+---------------+------*/
Acad::ErrorStatus           FileHolder::WriteFileAs
(
AcDbDatabase*               pDatabase,
WCharCP                     pFileName,
DgnFileFormatType           format,
AcDb::AcDbDwgVersion        dwgVersion,
int                         dxfPrecision
)
    {
    Acad::ErrorStatus       errorStatus;

    /*-----------------------------------------------------------------------------------
    RealDWG document says that if we save a file that has spatial index to an R14 we must
    call below method to update indexes.  We have seen cases that fall beyond this pre-
    condition (TR 268922).  ACAD 2009, when saving a file with spatial index to any
    version of DWG, updates the indexes.  If there is no spatial index in file, this
    method does not seem to do anything.  So, we do the same as ACAD: if saving a DWG/DXF
    to another DWG/DXF, always update indexes, regardless what version we are saving to.
    -----------------------------------------------------------------------------------*/
    if (AcDb::kDHL_1014 >= dwgVersion)
         pDatabase->setIndexctl (2);
    if (DgnFileFormatType::DWG == m_dgnFile->GetOriginalFormat() || DgnFileFormatType::DXF == m_dgnFile->GetOriginalFormat())
        AcDbIndexFilterManager::updateIndexes (pDatabase);

    if (DgnFileFormatType::DWG == format)
        errorStatus = pDatabase->saveAs (pFileName, false, dwgVersion, 0);
#if RealDwgVersion == 2009
    else if (dwgVersion <= AcDb::kDHL_1012)
        errorStatus = acdbDxfOutAsR12 (pDatabase, pFileName, dxfPrecision);
    else if (dwgVersion <= AcDb::kDHL_1015)
        errorStatus = acdbDxfOutAs2000 (pDatabase, pFileName, dxfPrecision);
    else if (dwgVersion <= AcDb::kDHL_1800)
        errorStatus = acdbDxfOutAs2004 (pDatabase, pFileName, dxfPrecision);
    else
        errorStatus = pDatabase->dxfOut (pFileName, dxfPrecision, true);
#else
    else 
        errorStatus = pDatabase->dxfOut (pFileName, dxfPrecision, dwgVersion, true);
#endif

    return  errorStatus;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void     CreateEmptyBlockForSeparateFiles (AcDbObjectId& blockId, AcDbDatabase* dwg)
    {
    AcDbBlockTablePointer   blockTable(dwg->blockTableId(), AcDb::kForWrite);
    if (Acad::eOk == blockTable.openStatus())
        {
        AcDbBlockTableRecordPointer     emptyBlock(new AcDbBlockTableRecord());
        emptyBlock->setName (L"*modelspace");
        blockTable->add (blockId, emptyBlock);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/13
+---------------+---------------+---------------+---------------+---------------+------*/
static AcDbObjectId CreateWBlockLayout (AcDbDatabase* outputDwg, WCharCP layoutName, AcDbObjectId inputBlockId)
    {
    AcString        name(layoutName);
    AcDbObjectId    outputBlockId = acdbSymUtil()->blockPaperSpaceId (outputDwg);
    AcDbObjectId    outputLayoutId;
    
    // rename existing layout or create a new one with the input name
    if (outputBlockId.isValid())
        {
        AcDbBlockTableRecordPointer outputBlock (outputBlockId, AcDb::kForRead);
        if (Acad::eOk == outputBlock.openStatus())
            outputLayoutId = outputBlock->getLayoutId ();

        if (!outputLayoutId.isValid())
            return  outputLayoutId;

        AcDbLayoutPointer layout (outputLayoutId, AcDb::kForWrite);
        if (Acad::eOk == layout.openStatus())
            layout->setLayoutName (name);
        }
    else if (Acad::eOk != RealDwgHostApp::Instance().layoutManager()->createLayout(name.kwszPtr(), outputLayoutId, outputBlockId, outputDwg))
        {
        DIAGNOSTIC_PRINTF ("Failed creating layout \"%ls\" for a separate sheet file!\n", layoutName);
        return  AcDbObjectId::kNull;
        }

    // since the modelspace is empty, set the layout active
    if (outputLayoutId.isValid())
        RealDwgHostApp::Instance().layoutManager()->setCurrentLayoutId (outputLayoutId);

    // collect entity ID array from the input block:
    AcDbBlockTableRecordPointer inputBlock (inputBlockId, AcDb::kForRead);
    if (Acad::eOk == inputBlock.openStatus())
        {
        AcDbBlockTableRecordIterator*   iterator = NULL;
        if (Acad::eOk != inputBlock->newIterator(iterator))
            return  AcDbObjectId::kNull;

        AcDbObjectIdArray   idArray;
        for (; !iterator->done(); iterator->step())
            {
            AcDbObjectId    entityId;
            if (Acad::eOk == iterator->getEntityId(entityId))
                idArray.append (entityId);
            }
        delete iterator;

        // clone all entities from the input block to wblock paperspace:
        AcDbIdMapping       idMap;
        if (Acad::eOk == outputDwg->wblockCloneObjects(idArray, outputBlockId, idMap, AcDb::kDrcReplace))
            return  outputLayoutId;
        }

    return  AcDbObjectId::kNull;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/17
+---------------+---------------+---------------+---------------+---------------+------*/
void    FileHolder::SyncHighestDgnElementIdFromHandseed ()
    {
    /*-----------------------------------------------------------------------------------
    Each file saving to DWG results in an increased handseed value.  Update DGN highest 
    element ID to ensure no overlapping elements created after each DWG save - TFS 686249
    -----------------------------------------------------------------------------------*/
    if (nullptr != m_dgnFile && nullptr != m_database)
        {
        UInt64  dgnHighestId = dgnFileObj_getHighestElementID (m_dgnFile);
        UInt64  dwgHighestId = RealDwgUtil::CastDBHandle (m_database->handseed());
        if (dwgHighestId > dgnHighestId)
            dgnFileObj_setHighestElementID (m_dgnFile, dwgHighestId);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   FileHolder::WriteFile
(
WCharCP                     pFileName,
DgnFileFormatType           format,
int                         dxfPrecision,
bool                        useVersionFromSettings,
WCharCP                     pProgressComment
)
    {
    // If source DWG file is loaded with older version of AEC, give user another chance to abort saving file:
    if (m_hasOldVersionAEC && !DwgPlatformHost::Instance()._AllowUpgradingAecObjects())
        return  DWGSAVE_STATUS_UserAbort;
    
    // prevent inadverdent call to save DWG if it has been altered and made read-only:
    if (m_openFileReadOnlyForPrinting)
        {
        // still allow SaveAs
        WString sourceFile;
        m_dgnFile->GetOriginalName (sourceFile);
        if (sourceFile.empty())
            return  DWGSAVE_STATUS_UserAbort;
        }

    Acad::ErrorStatus   errorStatus;
    RealDwgHostAppR     hostApp = RealDwgHostApp::Instance();

    try
        {
        // note: this is to try to get the AcDbLayoutMgr, which has a number of methods that work on the "current" database, to work on this database. The documentation isn't clear.
        assert (hostApp.workingDatabase() == GetDatabase());

        DwgFileVersion  originalVersion = RealDwgUtil::MSVersionFromAcVersion (this->GetDatabase()->originalFileVersion());

        /*-------------------------------------------------------------------------------
        When an old version of DXF file is opened, RealDWG always promotes the file to
        the latest version.  We have to get the "original" originalVersion from file
        header of DGN cache.
        -------------------------------------------------------------------------------*/
        if (!useVersionFromSettings && DgnFileFormatType::DXF == format)
            {
            DgnFileFormatType   cacheFormat = DgnFileFormatType::Current;
            int                 majorVersion = 0;
            if (SUCCESS == m_dgnFile->GetVersion(&cacheFormat, &majorVersion, NULL) && DgnFileFormatType::DXF == cacheFormat)
                originalVersion = (DwgFileVersion)majorVersion;
            }

        IDwgConversionSettings& settings = MstnInterfaceHelper::Instance().GetSettings();
        /*-----------------------------------------------------------------------------------------------
        Use existing DWG/DXF version if editing file, or use the version from settings if saving DGN to DWG.
        -----------------------------------------------------------------------------------------------*/
        DwgFileVersion          validVersion = useVersionFromSettings ? settings.GetDwgSaveVersion() : originalVersion;
        /*-----------------------------------------------------------------------------------------------
        Validate save version: for DWG, it must be R14 or above; For DXF, it must be R12, R2000 or above.
        -----------------------------------------------------------------------------------------------*/
        validVersion = RealDwgUtil::ValidateSaveVersion (validVersion, DgnFileFormatType::DXF == format);
        /*-----------------------------------------------------------------------------------------------
        Convert our version number to that of RealDWG's
        -----------------------------------------------------------------------------------------------*/
        AcDb::AcDbDwgVersion    saveVersion = RealDwgUtil::AcVersionFromMSVersion (validVersion);

        // prepare an empty modelspace model for saving sheet models to separate files as layouts:
        AcDbObjectId            emptyBlockId;
        if (!m_saveSheetsAsModelspace && settings.SaveSheetsToSeparateFiles() && m_models.size() > 1)
            CreateEmptyBlockForSeparateFiles (emptyBlockId, m_database);

        // First set the "erased" flag on any non-default models.
        for each (RealDwgModelIndexItemP modelItem in m_models)
            {
            if (RDWGMODEL_TYPE_NonDefaultModel == modelItem->GetRealDwgModelType())
                {
#ifndef BUILD_DWGPLATFORM
                DgnModelRefP               modelRef;
                if (SUCCESS == mdlModelRef_createWorking (&modelRef, (DgnFileP) m_dgnFile, modelItem->GetId(), false, false))
                    {
                    WChar       modelNameChars[MAX_MODEL_NAME_LENGTH];
                    RealDwgUtil::AcStringToMSWChar (modelNameChars, modelItem->GetModelName (), MAX_MODEL_NAME_LENGTH);
                    ConvertFromDgnContext::ValidateName (modelNameChars, DwgFileVersion_13);

                    WString     device, directory, name, outputName;
                    BeFileName::ParseName (&device, &directory, &name, NULL, pFileName);

                    size_t      dotAt = name.rfind (L'.');
                    if (dotAt > 1)
                        name = name.substr (0, dotAt);

                    name += WString(L"_") + WString(modelNameChars);
                    BeFileName::BuildName (outputName, device.GetWCharCP(), directory.GetWCharCP(), name.GetWCharCP(), DgnFileFormatType::DWG == format ? L"dwg" : L"dxf");

                    try
                        {
                        // wblock from the empty block for a sheet saving as a layout, or from the source model otherwise:
                        ModelInfoCR         modelInfo = modelItem->GetModelInfo ();
                        bool                wblockLayout = !m_saveSheetsAsModelspace && DgnModelType::Sheet == modelInfo.GetModelType() && emptyBlockId.isValid();
                        AcDbObjectId        wblockId = wblockLayout ? emptyBlockId : modelItem->GetBlockTableRecordId ();

                        AcDbDatabase*       pTmpDatabase = NULL;
                        Acad::ErrorStatus   wblockStatus = m_database->wblock (pTmpDatabase, wblockId);
                        BeAssert (Acad::eOk == wblockStatus && L"WBLOCK failed!");
                        if (NULL == pTmpDatabase)
                            {
                            DIAGNOSTIC_PRINTF ("Failed writing wblock file %ls for a non-default model %ls. [%ls]\n", outputName.c_str(), modelNameChars, acadErrorStatusText(wblockStatus));
                            mdlModelRef_freeWorking (modelRef);
                            continue;
                            }

                        DgnModelP               model = modelRef->AsDgnModelP ();
                        ConvertFromDgnContext  *context = nullptr;

                        if (ConfigurationManager::IsVariableDefinedAndTrue(L"MS_DWG_SAVE_MULTIPROCESSING"))
                            context = new ConvertFromDgnMultiProcessingContext  (model, modelItem->GetId(), this, format, RealDwgUtil::MSVersionFromAcVersion(saveVersion),
                                                         settings.GetDwgSaveUnitMode(), false, true, 1000, settings);
                        else
                            context = new ConvertFromDgnContext (model, modelItem->GetId(), this, format, RealDwgUtil::MSVersionFromAcVersion(saveVersion),
                                                         settings.GetDwgSaveUnitMode(), false, true, 1000, settings);

                        // create a layout in the new file if needed
                        if (wblockLayout)
                            {
                            AcDbObjectId    layoutId = CreateWBlockLayout (pTmpDatabase, modelNameChars, modelItem->GetBlockTableRecordId());
                            if (layoutId.isValid())
                                {
                                // set layout data from the sheet definition, and set current block for the context:
                                AcDbLayoutPointer   layout(layoutId, AcDb::kForWrite);
                                if (Acad::eOk == layout.openStatus())
                                    {
                                    context->SetLayoutFromSheetInfo (layout, model);
                                    context->SetCurrentBlockId (layout->getBlockTableRecordId());
                                    }
                                }
                            else
                                {
                                DIAGNOSTIC_PRINTF ("Failed adding a layout for wblock file %ls\n", outputName.c_str());
                                delete pTmpDatabase;
                                mdlModelRef_freeWorking (modelRef);
                                continue;
                                }
                            }

                        context->SaveViewGroupToDwg (pTmpDatabase, modelInfo, wblockLayout, false);
                        context->SetLayerDisplayFromViewGroup (pTmpDatabase);
                        context->SetCannoscaleFromModelInfo (modelInfo, pTmpDatabase);

                        // The non-default models may have different units (as in our examples).
                        pTmpDatabase->setInsunits ((AcDb::UnitsValue) RealDwgUtil::AcDbUnitsValueFromDgnUnits (context->GetStandardTargetUnits()));
                        // RealDWG 2009 does not seem to set lwdisplay in wblock, so below call is necessary until a fix becomes available:
                        pTmpDatabase->setLineWeightDisplay (m_database->lineWeightDisplay());

                        errorStatus = WriteFileAs (pTmpDatabase, outputName.GetWCharCP(), format, saveVersion, dxfPrecision);
                        delete context;
                        delete pTmpDatabase;
                        }
                    catch (...)
                        {
                        DIAGNOSTIC_PRINTF ("Exception Thrown Writing Block: %ls\n", outputName.c_str());
                        }

                    mdlModelRef_freeWorking (modelRef);

                    AcDbBlockTableRecordPointer pBlock (modelItem->GetBlockTableRecordId(), AcDb::kForWrite);
                    if (Acad::eOk == pBlock.openStatus())
                        pBlock->erase();
                    }
#endif
                }
            }

        // erase the empty block used for wblock:
        AcDbBlockTableRecordPointer emptyBlock (emptyBlockId, AcDb::kForWrite);
        if (Acad::eOk == emptyBlock.openStatus())
            emptyBlock->erase();

        m_models.erase (std::remove (m_models.begin(), m_models.end(), (RealDwgModelIndexItemP)NULL), m_models.end());

        // when saving a DGN as DWG, and Save Application Data is turned on, persist master fileID, such that round tripped ViewportDranCell elements will still work:
        if (DgnFileFormatType::DWG != m_dgnFile->GetOriginalFormat() && DgnFileFormatType::DXF != m_dgnFile->GetOriginalFormat() && settings.SaveApplicationData())
            {
            auto dgnHeader = m_dgnFile->GetHeader ();
            if (nullptr != dgnHeader && 0 != dgnHeader->fileID.guid[0])
                {
                wchar_t guidStr[128] = {0};
                int nChars = ::StringFromGUID2 ((REFGUID)dgnHeader->fileID.guid, guidStr, sizeof(guidStr));
                if (nChars > 16 && nChars < sizeof(guidStr))
                    errorStatus = m_database->setVersionGuid (guidStr);
                }
            }

        errorStatus = WriteFileAs (m_database, pFileName, format, saveVersion, dxfPrecision);

        // Re-sync highest DGN element ID as a save operation may add new objects that were not from DGN, as a case seen in TFS 686249:
        if (Acad::eOk == errorStatus)
            this->SyncHighestDgnElementIdFromHandseed ();
        }
    catch (RealDwgException&)
        {
        return BSIERROR;
        }
    return Acad::eOk == errorStatus ? SUCCESS : BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
void FileHolder::PostSaveToDatabase (ConvertFromDgnContext& context)
    {
    context.PostProcessTree (&m_pPostProcessIdTree);
    m_pPostProcessIdTree = mdlAvlTree_init (AVLKEY_ELEMENTID);

    // Erase table entries scheduled during the checkin - but first
    // call "purge" function to make sure that they don't have any references
    // within the database
    try
        {
        int         eraseIdCount;

        if (0 != (eraseIdCount = m_layerEraseIds.length()))
            {
            m_database->purge (m_layerEraseIds);
            if (eraseIdCount > m_layerEraseIds.length())
                DIAGNOSTIC_PRINTF ("Layer Table Erase Rejected - %d Requested, %d Allowed\n", eraseIdCount, m_layerEraseIds.length());

            for (int iErase=0, count = m_layerEraseIds.length(); iErase < count; iErase++)
                {
                AcDbLayerTableRecordPointer layer (m_layerEraseIds[iErase], AcDb::kForWrite);
                if (Acad::eOk == layer.openStatus())
                    layer.object()->erase();
                }
            m_layerEraseIds.removeAll();
            }

        if (0 != (eraseIdCount = m_tableRecordEraseIds.length()))
            {
            AcDbObjectIdArray   requestedIds (m_tableRecordEraseIds);

            m_database->purge (m_tableRecordEraseIds);
            if (eraseIdCount > m_tableRecordEraseIds.length())
                {
                DIAGNOSTIC_PRINTF ("Table Erase Rejected - %d Requested, %d Allowed\n", eraseIdCount, m_tableRecordEraseIds.length());

                // the rejected table records would have been renamed prior in ConvertFromDgnContext::DeleteSymbolTableRecord, try restoring their names:
                int             nPrefixChars = _countof (NAME_TableRecordErasePrefix) - 1;
                for (int iRequest = 0; iRequest < eraseIdCount; iRequest++)
                    {
                    AcDbObjectId    objectId = requestedIds.at (iRequest);
                    if (objectId.isValid() && !m_tableRecordEraseIds.contains(objectId))
                        {
                        AcDbSymbolTableRecordPointer<AcDbSymbolTableRecord> rejectedRecord (objectId, AcDb::kForWrite);
                        if (Acad::eOk == rejectedRecord.openStatus())
                            {
                            const ACHAR*    name = L"";
                            if (Acad::eOk == rejectedRecord->getName(name) && (int)wcslen(name) >= nPrefixChars && 0 == wcsncmp(name, NAME_TableRecordErasePrefix, nPrefixChars))
                                rejectedRecord->setName (name + nPrefixChars);
                            }
                        }
                    }
                }

            for (int iErase=0, count = m_tableRecordEraseIds.length(); iErase < count; iErase++)
                {
                AcDbObject* tableRecord;
                if (Acad::eOk == acdbOpenObject (tableRecord, m_tableRecordEraseIds[iErase], AcDb::kForWrite, false))
                    {
                    tableRecord->erase();
                    tableRecord->close();
                    }
                }
            m_tableRecordEraseIds.removeAll();
            }

        if (m_isDwgAttrdefItemtypeLibraryDirty)
            {
            context.SaveRemainingItemtypeChangeToDwg ();
            m_isDwgAttrdefItemtypeLibraryDirty = false;
            }
        m_itemTypeBlockDefList.removeAll ();

        // check modelspace annotative objects to ensure scales we added in paperspace viewports are supported
        if (m_newViewportAnnotationScales.empty())
            return;

        AcDbBlockTableRecordIterator*   iter = nullptr;
        AcDbBlockTableRecordPointer     modelspace (acdbSymUtil()->blockModelSpaceId(m_database), AcDb::kForRead);
        if (Acad::eOk == modelspace.openStatus() && Acad::eOk == modelspace->newIterator(iter, true, true) && nullptr != iter)
            {
            AcDbObjectContextInterface* objectContextInterface = nullptr;

            // iterate through all modelspace annoative objects
            for (; !iter->done(); iter->step())
                {
                AcDbEntity*             entity = nullptr;
                if (Acad::eOk == iter->getEntity(entity, AcDb::kForWrite))
                    {
                    AcDbAnnotativeObjectPE*     annotationPE = ACRX_PE_PTR (entity, AcDbAnnotativeObjectPE);

                    if (nullptr != annotationPE && annotationPE->annotative(entity) &&
                        nullptr != (objectContextInterface = ACRX_PE_PTR(entity, AcDbObjectContextInterface)))
                        {
                        // iterate through all newly added viewport annotation scales and check each of them
                        for each (AcDbAnnotationScale* vpAnnoscale in m_newViewportAnnotationScales)
                            {
                            if (nullptr != vpAnnoscale && !objectContextInterface->hasContext(entity, *vpAnnoscale))
                                {
                                // this annotative object does not support current scale - add the scale to the object
                                if (Acad::eOk != objectContextInterface->addContext(entity, *vpAnnoscale))
                                    {
                                    AcString    name;
                                    vpAnnoscale->getName (name);
                                    DIAGNOSTIC_PRINTF ("Failed adding annotation scale %ls to entity ID=%I64d\n", name.isEmpty() ? L"<null>" : name.kwszPtr(), RealDwgUtil::CastDBHandle(entity->objectId().handle()));
                                    }
                                }
                            }
                        }
                    entity->close ();
                    }
                }
            delete iter;
            }
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception caught Purging table entries\n");
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/15
+---------------+---------------+---------------+---------------+---------------+------*/
void            FileHolder::SetDwgOriginatedItemtypeLibrary (ItemTypeLibraryP attrdefLib)
    {
    if (nullptr != attrdefLib)
        m_dwgOriginatedItemtypeLibrary = attrdefLib->CloneForFile (*m_dgnFile, false);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/15
+---------------+---------------+---------------+---------------+---------------+------*/
ItemTypeLibraryP    FileHolder::GetDwgOriginatedItemtypeLibrary ()
    {
    if (m_dwgOriginatedItemtypeLibrary.IsValid())
        return  m_dwgOriginatedItemtypeLibrary.get ();

    return  nullptr;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/14
+---------------+---------------+---------------+---------------+---------------+------*/
void            FileHolder::AddViewportAnnotationScale (AcDbAnnotationScale* annoscale)
    {
    // only called once per viewport so performance is not such a great concern
    for each (AcDbAnnotationScale* savedscale in m_newViewportAnnotationScales)
        {
        if (annoscale->matchScaleId(savedscale->uniqueIdentifier()))
            return;
        }

    m_newViewportAnnotationScales.push_back (annoscale);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FileHolder::SetBlockSavedWithItemType (AcDbObjectId const& blockId)
    {
    return m_itemTypeBlockDefList.append(blockId) >= 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FileHolder::IsBlockSavedWithItemType (AcDbObjectId const& blockId)
    {
    return  m_itemTypeBlockDefList.find(blockId) >= 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
bool                        FileHolder::DisplayPointAsDot ()
    {
    Adesk::Int16 pdMode = m_database->pdmode();
    return (0 == pdMode) || (1 == pdMode);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/01
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::AddExternalRef
(
AcDbObjectId                xRefBlockId,
DgnAttachmentCP             attachment
)
    {
    ExternalRefTreeNode*  node = new ExternalRefTreeNode (xRefBlockId);

    while (NULL != attachment)
        {
        node->m_refPath.append ((AcDbHandle) attachment->GetElementId());
        attachment = attachment->GetParentModelRefP()->AsDgnAttachmentCP();
        }
    mdlAvlTree_insertNode (m_pExternalRefByAttachPathTree, node, 0);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetXRefBTRObjectIdByModelRef (DgnAttachmentCP attachment)
    {
    ExternalRefTreeNode    *pNode;
    ExternalRefTreeNode     searchNode;

    while (NULL != attachment)
        {
        searchNode.m_refPath.append ((AcDbHandle) attachment->GetElementId());
        attachment = attachment->GetParentModelRefP()->AsDgnAttachmentCP();
        }

    if (NULL != (pNode = (ExternalRefTreeNode *) mdlAvlTree_search (m_pExternalRefByAttachPathTree, &searchNode)))
        return pNode->m_xRefBlockTRObjectId;

    AcDbObjectId    nullId;
    return  nullId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/01
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetXRefBTRObjectIdByInstanceId (ElementId instanceId)
    {
    ExternalRefTreeNode     searchNode, *pNode;

    searchNode.m_refPath.append ((AcDbHandle) instanceId);
    if (NULL != (pNode = (ExternalRefTreeNode *) mdlAvlTree_search (m_pExternalRefByAttachPathTree, &searchNode)))
        return pNode->m_xRefBlockTRObjectId;

    AcDbObjectId    nullId;
    return  nullId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      06/01
*  Slow lookup.
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetBlockByName (AcString const& name)
    {
    AcDbObjectId                blockTableId = m_database->blockTableId();
    AcDbBlockTablePointer       pBlockTable (blockTableId, AcDb::kForRead);
    if (Acad::eOk != pBlockTable.openStatus())
        return  AcDbObjectId::kNull;

    AcDbObjectId                blockId;
    if (Acad::eOk == pBlockTable.object()->getAt (name.kwszPtr(), blockId, false))
        return blockId;

    return  AcDbObjectId::kNull;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetDimStyleByName (WCharCP pName)
    {
    AcDbDimStyleTablePointer pDimstyleTable (m_database->dimStyleTableId(), AcDb::kForRead);
    if (Acad::eOk != pDimstyleTable.openStatus())
        return  AcDbObjectId::kNull;

    AcDbObjectId             styleId;
    if (Acad::eOk == pDimstyleTable->getAt (pName, styleId))
        return styleId;

    return  AcDbObjectId::kNull;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/03
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetActiveDimensionStyle (bool allowDefault)
    {
    AcDbObjectId    dimstyleId = m_database->dimstyle ();

    if (!dimstyleId.isNull() || !allowDefault)
        return dimstyleId;

    // find the 1st entry in dimstyle table and return it as default
    AcDbDimStyleTablePointer        pDimStyleTable (m_database->dimStyleTableId(), AcDb::kForRead);
    if (Acad::eOk != pDimStyleTable.openStatus())
        return  AcDbObjectId::kNull;

    AcDbDimStyleTableIterator*      pDimStyleIter;

    pDimStyleTable.object()->newIterator (pDimStyleIter);
    pDimStyleIter->start();

    pDimStyleIter->getRecordId (dimstyleId);
    delete pDimStyleIter;

    return dimstyleId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      01/03
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 EndsWithIgnoreCase
(
WCharCP                   pString,
WCharCP                   pEndString
)
    {
    return  0 == _wcsicmp (pEndString, pString + (wcslen (pString) - wcslen (pEndString)));
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater  06/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::ReadAcadFontMap ()
    {
    BeFileName path;
    if (!DwgPlatformHost::Instance()._GetAcadFontmapFile(path))
        return;

    BeFileStatus    status = BeFileStatus::UnknownError;
    BeTextFilePtr   textFile = BeTextFile::Open (status, path.GetWCharCP(), TextFileOpenType::Read, TextFileOptions::None);
    if (BeFileStatus::Success != status || !textFile.IsValid())
        return;

    WString         str;
    while (TextFileReadStatus::Success == textFile->GetLine(str))
        {
        // trim spaces from line
        WString::size_type pos1 = str.find_first_not_of(L" \t");
        WString::size_type pos2 = str.find_last_not_of(L" \t");
        str = str.substr(pos1 == WString::npos ? 0 : pos1, pos2 == WString::npos ? str.length() - 1 : pos2 - pos1 + 1);

        // strip out comments
        pos1 = str.find_first_of (L'#');
        str = str.substr (0, pos1);
        if (0 != str.size ())
            {
            pos1 = str.find_first_of(L";");

            WString     key (str.substr (0, pos1).c_str ());
            WString     value (str.substr (pos1+1).c_str ());

            bool        truetype = false;

            if (EndsWithIgnoreCase (value.c_str (), L".ttf"))
                {
                DgnFontManager::GetFontFaceNameFromFileName (value, value);
                truetype = true;
                }
            else if (EndsWithIgnoreCase (value.c_str (), L".shx"))
                value = value.substr (0, value.size () - 4);

            (*m_pFontMap)[key] = FontMapItem (value, truetype);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Keith.Bentley                   06/06
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      FileHolder::GetNumberForFont (DgnFontR font)
    {
    DgnFontNumMapP   fontMap = m_dgnFile->GetDgnFontMapP();

    UInt32 fontNo;
    fontMap->GetFontNumber (fontNo, font, true);
    return  fontNo;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      FileHolder::FontIdFromNameWithoutMapSearch (WCharCP fontName, bool trueType)
    {
    DgnFontNumMapP fontMap = m_dgnFile->GetDgnFontMapP();
    DgnFontPtr  font = trueType ? DgnFontManager::GetTrueTypeFont (fontName, fontMap, true) : DgnFontManager::GetShxFont (fontName, fontMap, true);
    return  GetNumberForFont (*font);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      FileHolder::FontIdFromName (WCharCP pFontName, bool trueType)
    {
    bmap<WString, FontMapItem>::iterator iter;
    if ((iter = m_pFontMap->find (pFontName)) != m_pFontMap->end ())
        return FontIdFromNameWithoutMapSearch (iter->second.m_value.c_str (), iter->second.m_trueType);

    return FontIdFromNameWithoutMapSearch (pFontName, trueType);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      FileHolder::GetShapeFontId
(
ACHAR const*                fileName,
DgnModelP                   modelRef
)
    {
    if ( (NULL == fileName) || (0 == fileName[0]) )
        {
        return 0;
        }
    else
        {
        WString     name;
        WString     extension(L"shx");

        BeFileName::ParseName (NULL, NULL, &name, &extension, fileName);

        // trim white spaces - TFS 174231
        name.Trim ();

        bool truetype = (0 == extension.CompareToI(L"PFB")); // for "type 1" fonts, use TrueType  TR#154906
        return FontIdFromName (name.GetWCharCP(), truetype);
        }
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      FileHolder::GetTrueTypeFontId
(
AcString const&             typeFace,
DgnModelP                   modelRef
)
    {
    return FontIdFromName (typeFace, true);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::AddFontToInstallTree (DgnFontCP font)
    {
    BeAssert (nullptr != font && L"Null DgnFontCP!");
    if (nullptr != font && DgnFontType::Rsc == font->GetType ())
        m_rscFontsToExportTree.insert (font);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/08
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ValidateFontFileName (WCharP outFontFileName, size_t nChars, WStringCR fontNameIn)
    {
    for (size_t iChar = 0; iChar < nChars; iChar++)
        {
        // replace : with _
        WChar     thisChar = fontNameIn[iChar];

        if (L':' == thisChar)
            outFontFileName[iChar] = L'_';
        else
            outFontFileName[iChar] = thisChar;
        }
    outFontFileName[nChars] = 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            ExportRscFont (DgnFontCR font, WCharCP pFontPath)
    {
    WStringCR   fontNameString  = font.GetName ();
    size_t      nChars          = fontNameString.length ();
    WCharP      fontName        = (WCharP) _alloca (sizeof (WChar) * (nChars + 1));

    ValidateFontFileName (fontName, nChars, fontNameString);

    try
        {
        WString     shxFontFileName;
        bool        shxNeedsUpdate = false, bigNeedsUpdate = false;

        BeFileName::BuildName (shxFontFileName, NULL, pFontPath, fontName, L"shx");

        DgnRscFontCP    rscFont = (DgnRscFontCP) &font;
        bool            hasBigFontGlyphs = rscFont->HasBigFontGlyphs();
        WString         bigFontFileName;
        if (hasBigFontGlyphs)
            {
            WString     bigFontName = WString(fontName) + WString(L"big");
            BeFileName::BuildName (bigFontFileName, NULL, pFontPath, bigFontName.GetWCharCP(), L"shx");

            if (rscFont->DoesShxFileNeedUpdate (bigFontFileName.GetWCharCP()))
                bigNeedsUpdate = true;
            }

        if (rscFont->DoesShxFileNeedUpdate (shxFontFileName.GetWCharCP()))
            shxNeedsUpdate = true;

        // Note that the file required recovery.
        if (bigNeedsUpdate)
            {
            BeFileName  bigFontIniFile;
            WString     message, formatStr, briefMessage;

            if (!DwgPlatformHost::Instance()._GetBigfontInitFile(bigFontIniFile))
                DwgPlatformHost::Instance()._ReportMessage (DwgPlatformHost::REPORTMESSAGE_BigfontInitFileMissing, bigFontIniFile.GetName(), fontNameString.GetWCharCP());
            }

        if(shxNeedsUpdate || bigNeedsUpdate)
            {
            rscFont->ExportToSHX (shxNeedsUpdate ? shxFontFileName.GetWCharCP() : NULL, bigNeedsUpdate ? bigFontFileName.GetWCharCP() : NULL);

            // add font to master shx font list so when microstation looks for the font it finds it
            DgnFontManager::AddShxFileToMaster (shxFontFileName.GetWCharCP());
            if (hasBigFontGlyphs && bigNeedsUpdate)
                DgnFontManager::AddShxFileToMaster (bigFontFileName.GetWCharCP());
            }
        }
    catch (...)
        {
        DIAGNOSTIC_PRINTF ("Exception thrown FileHolder::visitFontTreeItem on Font %ls\n", font.GetName ().c_str ());
        }

    return  SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::ExportRscFonts
(
WStringCR       fontPath
)
    {
    // NEEDS_THOUGHT: Why do we need this?
    DgnFontP   defaultRscFont = &DgnFontManager::GetDefaultRscFont ();
    m_rscFontsToExportTree.insert (defaultRscFont);

    std::set<DgnFontCP>::iterator  rscFontIterator;
    for (rscFontIterator = m_rscFontsToExportTree.begin (); rscFontIterator != m_rscFontsToExportTree.end (); ++rscFontIterator)
        ExportRscFont (**rscFontIterator, fontPath.c_str());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgGeneratedTextStyleItem*  FileHolder::GetGeneratedTextStyle (WCharCP pStyleName)
    {
    RealDwgGeneratedTextStyleItem*           pItem;

    if (NULL != (pItem = (RealDwgGeneratedTextStyleItem *)mdlAvlTree_search (m_pGeneratedTextStyles, pStyleName)))
        {
        if (pItem->GetTextStyleObjectId().isErased())
            {
            mdlAvlTree_deleteNode (m_pGeneratedTextStyles, const_cast<WChar*>(pStyleName));
            pItem = NULL;
            }
        }

    return pItem;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::AddGeneratedTextStyle
(
RealDwgGeneratedTextStyleItem   *pItem
)
    {
    mdlAvlTree_insertNode (m_pGeneratedTextStyles, pItem, 0);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId                   FileHolder::GetTagSetDefinitionId
(
AcDbObjectId                blockObjectId
)
    {
    UInt64              handleValue = RealDwgUtil::CastDBHandle (blockObjectId.handle());
    BlockIdTreeNode     *pNode;

    return (NULL == (pNode = (BlockIdTreeNode*) mdlAvlTree_search (m_pBlockByIdTree, &handleValue))) ? 0 : pNode->tagSetDefinitionId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      04/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   FileHolder::SetTagSetDefinitionId
(
AcDbObjectId                blockObjectId,
ElementId                   tagSetDefinitionId
)
    {
    BlockIdTreeNode     node;

    node.handleValue        = RealDwgUtil::CastDBHandle (blockObjectId.handle());
    node.tagSetDefinitionId = tagSetDefinitionId;

    return mdlAvlTree_insertNode (m_pBlockByIdTree, &node, sizeof(node));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    PaulChater      12/02
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   FileHolder::Audit
(
int                 *pNumErrors,
int                 *pNumFixed,
int                 *pNumEntities,
void               (*pMessageFunction) (char *),
bool                fixErrors
)
    {
#if defined (FUTUREWORK_AUDIT)
    RealDwgAuditInfo        auditInfo (pMessageFunction, fixErrors);
    m_database->auditDatabase (&auditInfo);
    if (NULL != pNumErrors)
        *pNumErrors = auditInfo.numErrors();

    if (NULL != pNumFixed)
        *pNumFixed = auditInfo.numFixes();

    if (NULL != pNumEntities)
        *pNumEntities = auditInfo.numEntities();

    return SUCCESS;
#else
    return BSIERROR;
#endif
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2005
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 LoadMaterialTree
(
AvlTree**                   ppTree,
AcDbDatabase*               pDatabase
)
    {
    if (NULL == *ppTree)
        {
        AcDbDictionary*     materialDictionary = NULL;
        Acad::ErrorStatus   es = pDatabase->getMaterialDictionary (materialDictionary, AcDb::kForRead);
        if (Acad::eOk != es)
            {
            DIAGNOSTIC_PRINTF ("Failed opening material dictionary! [%ls]\n", acadErrorStatusText(es));
            return;
            }

        *ppTree = mdlAvlTree_init (AVLKEY_WSTRING);

        AcDbDictionaryIterator* iterator = materialDictionary->newIterator ();
        for (; !iterator->done(); iterator->next())
            {
            AcDbObjectId                        objectId = iterator->objectId ();
            AcDbObjectPointer<AcDbMaterial>     dwgMaterial(objectId, AcDb::kForRead);
            if (Acad::eOk == dwgMaterial.openStatus())
                {
                MaterialTreeNode    node (dwgMaterial->name(), objectId);
                mdlAvlTree_insertNode (*ppTree, &node, sizeof (node));
                }
            }

        delete iterator;
        materialDictionary->close ();
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2005
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                FileHolder::GetMaterialFromTree (WCharCP pName)
    {
    LoadMaterialTree (&m_pMaterialTree, m_database);

    MaterialTreeNode    *pNode;
    if (NULL != (pNode = (MaterialTreeNode*) mdlAvlTree_search (m_pMaterialTree, pName)))
        return pNode->m_objectId;

    return  AcDbObjectId::kNull;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2005
+---------------+---------------+---------------+---------------+---------------+------*/
void                        FileHolder::AddMaterialToTree (WCharCP pName, AcDbObjectId objectId)
    {
    LoadMaterialTree (&m_pMaterialTree, m_database);

    MaterialTreeNode        node (pName, objectId);
    mdlAvlTree_insertNode (m_pMaterialTree, &node, sizeof (node));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32          FileHolder::GetAndIncrementNextAvailableUnderlayNumber (RealDwgUnderlays underlayType)
    {
    switch (underlayType)
        {
        case DWGUNDERLAY_Dgn:   return  m_nextDgnUnderlayNumber++;
        case DWGUNDERLAY_Pdf:   return  m_nextPdfUnderlayNumber++;
        case DWGUNDERLAY_Dwf:   return  m_nextDwfUnderlayNumber++;
        }

    return  0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/16
+---------------+---------------+---------------+---------------+---------------+------*/
bool            FileHolder::IsElementExcludedFromSaving (ElementId id)
    {
    if (m_elementsExcludedFromSaving.empty())
        return  false;

    // if this element was cloned during saving, do not include it as part of the saving changes:
    auto    found = std::find (m_elementsExcludedFromSaving.begin(), m_elementsExcludedFromSaving.end(), id);
    return  found != m_elementsExcludedFromSaving.end();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/16
+---------------+---------------+---------------+---------------+---------------+------*/
void            FileHolder::ExcludeElementsFromSaving (ElementId from, ElementId to)
    {
    for (ElementId id = from; id <= to; id++)
        m_elementsExcludedFromSaving.push_back (id);
    std::sort (m_elementsExcludedFromSaving.begin(), m_elementsExcludedFromSaving.end());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Colin.Wang        06/2021
* Cache ItemTypeLibraries in a map for using in next time. This can avoid creating an
* ItemTypeLibrary each time, and enhance the performance.
+---------------+---------------+---------------+---------------+---------------+------*/
ItemTypeLibraryPtr  FileHolder::FindBySchemaNameWithMap ( WString SchemaName, DgnFileR dgnfile )
    {
    ItemTypeLibraryPtr  itemtypeLibrary;
    std::map<WString, ItemTypeLibraryPtr>::iterator it = m_itemLibsMap.find ( SchemaName );
    if ( it != m_itemLibsMap.end () )
        itemtypeLibrary = it->second;
    else
        {
        itemtypeLibrary = ItemTypeLibrary::FindBySchemaName ( SchemaName.GetWCharCP (), dgnfile );
        m_itemLibsMap.insert ( std::pair <WString, ItemTypeLibraryPtr> ( SchemaName, itemtypeLibrary ) );
        }
    return itemtypeLibrary;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Colin.Wang        06/2021
+---------------+---------------+---------------+---------------+---------------+------*/
void    FileHolder::ClearItemLibrariesMap ()
    {
    m_itemLibsMap.clear ();
    }

} // End RealDwg namespace

END_BENTLEY_NAMESPACE

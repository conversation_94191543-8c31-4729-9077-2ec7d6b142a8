/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSatFileConversionMPTests.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

#include <windows.h>
#include <chrono>
#include <DgnPlatform/DgnPlatformLib.h>
#include <DgnPlatform/DgnPlatformApi.h>
#include <DgnPlatform/DgnFile.h>
#include <BeSQLite/BeSQLite.h>
#include <MstnPlatformTest/ToolscriptingApi.h>

#include    <Mstn\RealDWG\rSatToPs.h>

#include <Bentley/BeDirectoryIterator.h>
#include <PSolidAcisInterop\PSolidAcisInterop.h>

#include <Mstn\PSolid\PSolidAPI.h>

#include <gtest/gtest.h>

USING_NAMESPACE_BENTLEY_SQLITE
USING_NAMESPACE_BENTLEY_DGNPLATFORM

using namespace Bentley::RealDwg;

struct MyDgnHost : DgnPlatformLib::Host
    {
public:
    MyDgnHost ()
        {
        DgnPlatformLib::Initialize (*this, true, true);
        setlocale (LC_CTYPE, "");
        }
    };

struct SatToPsFixture : public MstnBaseFixtureCovered
    {
    SatToPsFixture ()
        :MstnBaseFixtureCovered (
            TestKeywordMask::Keyword::Interchange,
            TestCategoryMask::Sanity
        )
        {}
    };

TEST_F (SatToPsFixture, NewSatFile)
    {
    BeFileName origin (L"D\\tmp\\myFile.sat");
    ASSERT_STREQ (SatFileConversionMP ().NewAcisFileName (origin.c_str ()).c_str (), L"D\\tmp\\myFile1.sat");
    }

struct SatFileConversionMPTest : SatFileConversionMP
    {
    SatFileConversionMPTest (int nProcesses = 8, SatDirectoryPtr satDir = new SatDirectory)
        :SatFileConversionMP (nProcesses, satDir)
        {}
    WString CmdLine () override
        {
        return SatFileConversionMP::CmdLine () + L" TESTING";
        }
    };

struct SatToPSProcessing
    {
private:
    bvector<PROCESS_INFORMATION> m_pis;

    BeFileName ExeFileName () const
        {
        BeFileName exeFileName =
            PathEnvAppend ().CurrentDir ()
            .AppendToPath (L"MdlSys")
            .AppendToPath (L"AsNeeded")
            .AppendToPath (L"Smartsolid")
            .AppendToPath (L"SatToPSProcessing")
            .AppendExtension (L"exe");

        EXPECT_TRUE (BeFileName::DoesPathExist (exeFileName.c_str ()));
        return exeFileName;
        }

public:
    bool SpawnSatToPSProcessing ()
        {
        // additional information
        PROCESS_INFORMATION pi;
        STARTUPINFOW si;

        // set the size of the structures
        ZeroMemory (&si, sizeof (si));
        si.cb = sizeof (si);
        ZeroMemory (&pi, sizeof (pi));

        BOOL success =
            CreateProcessW (
                ExeFileName ().c_str (),
                nullptr,
                NULL,                                           // Process handle not inheritable
                NULL,                                           // Thread handle not inheritable
                FALSE,                                          // Set handle inheritance to TRUE (for the progress bar)
                0,                                              // No creation flags
                NULL,                                           // Use parent's environment block
                NULL,                                           // Use parent's starting directory 
                &si,                                            // Pointer to STARTUPINFO structure
                &pi                                             // Pointer to PROCESS_INFORMATION structure (removed extra parentheses)
            );

        EXPECT_TRUE (success == TRUE);
        if (success != TRUE)
            return false;

        m_pis.push_back (pi);
        return true;
        }

    bool WaitForChildrenProcesses ()
        {
        bool success = true;
        for (PROCESS_INFORMATION const& pi : m_pis)
            {
            // Wait for the process to finish
            DWORD result = WaitForSingleObject (pi.hProcess, INFINITE);

            // Get and verify the exit code
            DWORD exitCode;
            EXPECT_TRUE (TRUE == GetExitCodeProcess (pi.hProcess, &exitCode));

            EXPECT_EQ (exitCode, 0);
            success = (0 == exitCode) && success;

            // Close process and thread handles. 
            CloseHandle (pi.hProcess);
            CloseHandle (pi.hThread);

            success = (WAIT_OBJECT_0 == result) && success;
            }
        return success;
        }
    };

struct FakeMutexChild final : SatMutexChild
    {
    HANDLE Open (WStringCR) override
        {
        return nullptr;
        }
    bool IsValid (HANDLE) override
        {
        return true;
        }
    DWORD WaitToAcquire (HANDLE) override
        {
        return WAIT_OBJECT_0;
        };
    bool ReleaseSatMutex (HANDLE) override 
        {
        return true;
        }
    void Close (HANDLE) {}
    };

struct LogErrors final : ILog
    {
    void Log (WCharCP message) override
        {
        ASSERT_TRUE (false) << message;
        }
    };

/*=================================================================================**//**
* Sat file processing which is designed to run in process (not in a new process)
* @bsiclass                                                     JP.Wenger       03/2019
+===============+===============+===============+===============+===============+======*/
struct InProcessSatFileProcessing : SatFileProcessing
    {
    InProcessSatFileProcessing (ILogPtr log, WStringCR guid)
        :SatFileProcessing (log, guid, new FakeMutexChild)
        {};
    bool AppendToPathEnv () final override
        {
        return m_appends.AddToPath (
            m_appends.CurrentDir ().AppendToPath (L"MdlSys").AppendToPath (L"AsNeeded").AppendToPath (L"Acis")
        );
        }
    };

struct FakeSatFileProcessing final : InProcessSatFileProcessing
    {
    FakeSatFileProcessing (ILogPtr log, WStringCR guid)
        :InProcessSatFileProcessing (log, guid)
        {};
    bool ProcessSatFile () override
        {
        return true;
        }
    };

TEST_F (SatToPsFixture, ChildProcessing)
    {
    SatFileConversionMP conversion;
    conversion.SetSatOutputStrategy (
        [this] (WCharCP fileName)
        {
        BeFile myFile;
        myFile.Create (
            fileName
        );
        return true;
        });

    for (int i = 0; i < 3; ++i)
        EXPECT_TRUE (BeFileName::DoesPathExist (conversion.OutputToSatAsynch ().c_str()));

    //Tricky: this tells the children processes that there is no more sat files to process.
    //If we don't call it, the children processes will loop indefinitely.
    conversion.SignalEnd ();

    FakeSatFileProcessing processing (
        new LogErrors,
        conversion.CmdLine ()
    );

    EXPECT_TRUE (processing.Process ());
    }

struct LogTestingInChildProcess final : SatFileConversionMP
    {
    LogTestingInChildProcess (int nProcesses, SatDirectoryPtr satDir = new SatDirectory)
        :SatFileConversionMP (nProcesses, satDir)
        {}
    WString CmdLine () override
        {
        return SatFileConversionMP::CmdLine () + L" LOGTESTING";
        }
    };

TEST_F (SatToPsFixture, TestLog)
    {
    SatDirectoryPtr satDir = new SatDirectory;
    LogTestingInChildProcess conversion (1, satDir);

    EXPECT_TRUE (conversion.SpawnSatToPsChildrenProcesses ());
    EXPECT_TRUE (conversion.WaitForFinish ());

    //Verify that a log file has been created
    bvector<BeFileName> fileNames = satDir->FilesInSessionDir ();
    ASSERT_TRUE (
        std::find_if (fileNames.begin (), fileNames.end (), [] (BeFileNameCR fileName)
        {
        BeGuid guid;
        return SUCCESS == guid.FromString (Utf8String (BeFileName::GetFileNameWithoutExtension (fileName.c_str ()).c_str ()).c_str ());
        })
        != fileNames.end ());
    }

TEST_F (SatToPsFixture, NewCachedGuid)
    {
    NewCachedGuid guid;
    ASSERT_STREQ (guid.IdAsWString ().c_str (), guid.IdAsWString ().c_str ());
    }

TEST_F (SatToPsFixture, FileProcessingSynchSingle)
    {
    SatDirectoryPtr satDir = new SatDirectory;
    SatFileConversionMPTest conversion (1, satDir);
    conversion.SetSatOutputStrategy (
        [this] (WCharCP fileName)
        {
        BeFile myFile;
        myFile.Create (
            fileName
        );
        return true;
        });

    EXPECT_TRUE (BeFileName::DoesPathExist (conversion.OutputToSatAsynch ().c_str()));

    //spawn a single child processes
    EXPECT_TRUE (conversion.SpawnSatToPsChildrenProcesses ());

    //Wait until conversions have finished
    EXPECT_TRUE (conversion.WaitForFinish ());

    auto psfilesfilter = [] (BeFileNameCR fileName) -> bool
        {
        return BeFileName::GetExtension (fileName.c_str ()).EqualsI (L"x_b");
        };

    ASSERT_FALSE (satDir->HasSatFiles ());

    //check that the number of x_b files created == number of sat files
    ASSERT_EQ (
        satDir->FilesInSessionDir (psfilesfilter).size (),
        1
    );
    }

TEST_F (SatToPsFixture, FileProcessingSynch)
    {
    SatDirectoryPtr satDir = new SatDirectory;
    SatFileConversionMPTest conversion (8, satDir);
    conversion.SetSatOutputStrategy (
        [this] (WCharCP fileName)
        {
        BeFile myFile;
        myFile.Create (
            fileName
        );
        return true;
        });
    
    for (int i = 0; i < 10; ++i)
        EXPECT_TRUE (BeFileName::DoesPathExist (conversion.OutputToSatAsynch ().c_str()));

    //spawn children processes
    EXPECT_TRUE (conversion.SpawnSatToPsChildrenProcesses ());

    //create new sat files
    for (int i = 0; i < 10; ++i)
        EXPECT_TRUE (BeFileName::DoesPathExist (conversion.OutputToSatAsynch ().c_str()));

    //Wait until conversions have finished
    EXPECT_TRUE (conversion.WaitForFinish ());

    auto psfilesfilter = [] (BeFileNameCR fileName) -> bool
        {
        return BeFileName::GetExtension (fileName.c_str ()).EqualsI (L"x_b");
        };

    ASSERT_FALSE (satDir->HasSatFiles ());

    //check that the number of x_b files created == number of sat files
    ASSERT_EQ (
        satDir->FilesInSessionDir (psfilesfilter).size (),
        20
        );
    }

TEST_F (SatToPsFixture, RealSatFile)
    {
    BeFileName satFileName (_wgetenv (L"SrcRoot"));
    satFileName.AppendToPath (L"MstnPlatform")
        .AppendToPath (L"mstn")
        .AppendToPath (L"mdlapps")
        .AppendToPath (L"RealDwgFileIO")
        .AppendToPath (L"testCase")
        .AppendToPath (L"satFile")
        .AppendExtension (L"sat");

    ASSERT_TRUE (BeFileName::DoesPathExist (satFileName.c_str ())) << "Sat file " << Utf8String (satFileName.c_str ()).c_str () << " doesn't exist";

    SatDirectoryPtr satDir = new SatDirectory;
    SatFileConversionMP conversion (1, satDir);
    conversion.SetSatOutputStrategy (
        [&satFileName] (WCharCP fileName)
        {
        return BeFileNameStatus::Success == BeFileName::BeCopyFile (satFileName.c_str (), fileName);
        });

    //create new sat files (this will move the sat file to the folder where it will be processed)
    EXPECT_TRUE (BeFileName::DoesPathExist (conversion.OutputToSatAsynch ().c_str()));

    conversion.SignalEnd ();

    InProcessSatFileProcessing processing (
        new LogErrors,
        conversion.CmdLine ()
    );

    EXPECT_TRUE (processing.Process ());

    auto psfilesfilter = [] (BeFileNameCR fileName) -> bool
        {
        return BeFileName::GetExtension (fileName.c_str ()).EqualsI (L"x_b");
        };

    ASSERT_FALSE (satDir->HasSatFiles ());

    //check that the number of x_b files created == number of sat files
    ASSERT_EQ (
        satDir->FilesInSessionDir (psfilesfilter).size (),
        1
    );
    }

BeFileName RealSatFileName ()
    {
    BeFileName satFileName (_wgetenv (L"SrcRoot"));
    satFileName.AppendToPath (L"MstnPlatform")
        .AppendToPath (L"mstn")
        .AppendToPath (L"mdlapps")
        .AppendToPath (L"RealDwgFileIO")
        .AppendToPath (L"testCase")
        .AppendToPath (L"satFile")
        .AppendExtension (L"sat");

    EXPECT_TRUE (BeFileName::DoesPathExist (satFileName.c_str ())) << "Sat file " << Utf8String (satFileName.c_str ()).c_str () << " doesn't exist";
    return satFileName;
    }

TEST_F (SatToPsFixture, RealSatFileAsynch)
    {
    BeFileName satFileName = RealSatFileName ();

    SatDirectoryPtr satDir = new SatDirectory;
    SatFileConversionMP conversion (1, satDir);
    conversion.SetSatOutputStrategy (
        [&satFileName] (WCharCP fileName)
        {
        return BeFileNameStatus::Success == BeFileName::BeCopyFile (satFileName.c_str (), fileName);
        });

    //create new sat files (this will move the sat file to the folder where it will be processed)
    EXPECT_TRUE (BeFileName::DoesPathExist (conversion.OutputToSatAsynch ().c_str()));

    //spawn children processes
    EXPECT_TRUE (conversion.SpawnSatToPsChildrenProcesses ());

    //Wait until conversions have finished
    EXPECT_TRUE (conversion.WaitForFinish ());

    ASSERT_FALSE (satDir->HasSatFiles ());

    ASSERT_EQ (conversion.PsFileNames ().size (), 1);
    }

TEST_F (SatToPsFixture, RealConvertScale)
    {
    BeFileName satFileName = RealSatFileName ();

    SatDirectoryPtr satDir = new SatDirectory;
    SatFileConversionMP conversion (1, satDir);
    conversion.SetSatOutputStrategy (
        [&satFileName] (WCharCP fileName)
        {
        return BeFileNameStatus::Success == BeFileName::BeCopyFile (satFileName.c_str (), fileName);
        });

    //create new sat files (this will move the sat file to the folder where it will be processed)
    EXPECT_TRUE (BeFileName::DoesPathExist (conversion.OutputToSatAsynch ().c_str()));

    //spawn children processes
    EXPECT_TRUE (conversion.SpawnSatToPsChildrenProcesses ());

    //Wait until conversions have finished
    EXPECT_TRUE (conversion.WaitForFinish ());

    ASSERT_FALSE (satDir->HasSatFiles ());

    auto output = conversion.PsFileNames ();
    ASSERT_EQ (output.size (), 1);

    ASSERT_GT (conversion.ConvertScaleFromPsFile (output[0]), .0);
    }

TEST_F (SatToPsFixture, PsFileIn)
    {
    //Create a real (non fake) parasolid file
    BeFileName psFileName (SatDirectory ().SessionDir ().AppendToPath (L"myPsFile").AppendExtension (L"x_b"));
    PSolidAcisInterop::SATFileToXMTFile (RealSatFileName ().c_str (), psFileName.c_str ());

    //check its validity
    TAG_ENTITY_LIST*    listP = NULL;
    mdlSolid_listCreate (&listP);
    bool ascii = true;
    EXPECT_TRUE (SUCCESS == mdlSolid_restoreParts (psFileName.c_str (), ascii, listP) || SUCCESS == mdlSolid_restoreParts (psFileName.c_str (), !ascii, listP));

    int count = 0;
    EXPECT_TRUE (mdlSolid_listCount (&count, listP) == SUCCESS);
    EXPECT_GE (count, 1);

    mdlSolid_listDelete (&listP);
    }

struct ConversionChildrenCount final : SatFileConversionMP
    {
private:
    int& m_actualProc;
    bool SpawnSatToPsChildProcess () override
        {
        ++m_actualProc;
        return true;
        }

public:
    ConversionChildrenCount (int maxProcesses, int& actualProc)
        :SatFileConversionMP (maxProcesses), m_actualProc (actualProc)
        {}
    };

TEST_F (SatToPsFixture, SpawnedCount)
    {
    int count = 0;
    ConversionChildrenCount conversion (100, count);
    conversion.SpawnSatToPsChildrenProcesses ();

    SYSTEM_INFO sysinfo;
    GetSystemInfo (&sysinfo);
    ASSERT_LE (count, (int) sysinfo.dwNumberOfProcessors);
    }

//Count that multiple calls to SpawnSatToPsChildrenProcesses doesn't spawn new processes after the first call
TEST_F (SatToPsFixture, NotSpawnedTwice)
    {
    int count = 0;
    ConversionChildrenCount conversion (100, count);
    conversion.SpawnSatToPsChildrenProcesses ();

    ASSERT_NE (count, 0);

    int const oldCount = count;
    conversion.SpawnSatToPsChildrenProcesses ();

    ASSERT_EQ (oldCount, count);
    }

int main (int argc, char** argv)
    {
    PathEnvAppend appends;
    BeFileName dir = appends.CurrentDir ().AppendToPath (L"MdlSys").AppendToPath (L"AsNeeded");
    appends.AddToPath (dir);
    appends.AddToPath (dir.AppendToPath (L"SmartSolid"));

    ::testing::InitGoogleTest (&argc, argv);
    MyDgnHost host;

    return RUN_ALL_TESTS ();
    }

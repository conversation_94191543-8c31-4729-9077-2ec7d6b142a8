/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSpline.cpp $
|
|  $Copyright: (c) 2014 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtSpline : public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement (AcDbObjectP acObject, EditElementHandleR outElement, ConvertToDgnContextR context) const override
    {
    AcDbSpline*         acSpline = AcDbSpline::cast (acObject);

    RealDwgStatus       rDwgStatus;
    if (acSpline->numFitPoints() > 1)
        rDwgStatus = this->ElementFromInterpolationCurve (outElement, acSpline, context);
    else
        rDwgStatus = this->ElementFromBCurve (outElement, acSpline, context);

    if (RealDwgSuccess != rDwgStatus)
        return rDwgStatus;

    // The (stupid) routines may have placed a linkage for style thickness if it is set for active file.
    for (ElementLinkageIterator iter = outElement.BeginElementLinkages(); iter.IsValid(); iter.ToNext())
        {
        if (STYLELINK_ID == iter.GetLinkage()->primaryID)
            {
            outElement.RemoveElementLinkage (iter);
            break;
            }
        }

    context.ElementHeaderFromEntity (outElement, acSpline);

    if (RealDwgUtil::IsViewportClippingEntity(acSpline))
        {
        // Vancouver no longer supports a bspline curve as a clipping element - wrap it inside of a complex shape:
        EditElementHandle   complexShape;
        ChainHeaderHandler::CreateChainHeaderElement (complexShape, &outElement, true, context.GetThreeD(), *context.GetModel());
        if (complexShape.IsValid() && BSISUCCESS == ChainHeaderHandler::AddComponentElement(complexShape, outElement))
            {
            complexShape.GetElementP()->ehdr.uniqueId = context.ElementIdFromObject (acObject);
            outElement.SetElementDescr (complexShape.ExtractElementDescr(), true, false, context.GetModel());
            }
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ElementFromInterpolationCurve (EditElementHandleR outElement, AcDbSpline* acSpline, ConvertToDgnContextR context) const
    {
    int                 degree;
    double              fitTolerance;
    Adesk::Boolean      tangentsExist;
    AcGeVector3d        startTangent, endTangent;
    AcGePoint3dArray    acFitPoints;
    if (Acad::eOk != acSpline->getFitData(acFitPoints, degree, fitTolerance, tangentsExist, startTangent, endTangent))
        return  EntityError;

    // always scale tolerance by default tol: values larger than zero indicate AutoCAD's wacky approximation curves
    fitTolerance = TOLERANCE_PointEqual;

    DVec3d          tangents[2];
    memset (tangents, 0, sizeof(tangents));

    if (tangentsExist)
        {
        RealDwgUtil::DVec3dFromGeVector3d (tangents[0], startTangent);
        RealDwgUtil::DVec3dFromGeVector3d (tangents[1], endTangent);
        tangents[1].Negate ();       // MDL funcs need it pointing into curve
        }

    int             nPoints = acFitPoints.length();
    DPoint3dArray   fitPoints;
    for (int iPoint=0; iPoint<nPoints; iPoint++)
        {
        DPoint3d    tmpPoint;
        fitPoints.push_back (RealDwgUtil::DPoint3dFromGePoint3d(tmpPoint, acFitPoints[iPoint]));
        }

    DPoint3d*       pPoints     = &fitPoints[0];
    double          tolerance   = fitTolerance * bsiDPoint3d_getLargestCoordinate (pPoints, nPoints);

    MSBsplineCurve  bCurve;
    StatusInt       status = bCurve.InitFromInterpolatePoints (pPoints, nPoints, 2, Adesk::kTrue==tangentsExist, &tangents[0], &tangents[1], true, 3);
    if (SUCCESS == status)
        {
        MSInterpolationCurve    interpolationCurve;

        memset (&interpolationCurve, 0, sizeof(interpolationCurve));
        interpolationCurve.params.order               =  degree + 1;
        interpolationCurve.params.isPeriodic          =  0;             // always nonperiodic
        interpolationCurve.params.isChordLenKnots     =  1;
        interpolationCurve.params.isColinearTangents  =  0;
        interpolationCurve.params.isChordLenTangents  =  1;
        interpolationCurve.params.isNaturalTangents   =  1;
        interpolationCurve.params.numPoints           =  nPoints;
        interpolationCurve.params.numKnots            =  BsplineParam::NumberAllocatedKnots (bCurve.params.numPoles, bCurve.params.order, false);

        interpolationCurve.startTangent = tangents[0];
        interpolationCurve.endTangent   = tangents[1];
        interpolationCurve.fitPoints    = pPoints;
        interpolationCurve.knots        = bCurve.knots;
        interpolationCurve.display.curveDisplay = true;

        context.GetTransformToDGN().Multiply (interpolationCurve.fitPoints, interpolationCurve.fitPoints, nPoints);

        status = BSplineCurveHandler::CreateBSplineCurveElement (outElement, NULL, interpolationCurve, context.GetThreeD(), *context.GetModel());

        bCurve.ReleaseMem ();
        }

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ElementFromBCurve (EditElementHandleR outElement, AcDbSpline* acSpline, ConvertToDgnContextR context) const
    {
    int                     degree;
    Adesk::Boolean          rational, closed, periodic;
    AcGePoint3dArray        controlPoints;
    AcGeDoubleArray         knotVector;
    AcGeDoubleArray         weights;
    double                  controlPointTolerance;
    double                  knotTolerance;
    if (Acad::eOk != acSpline->getNurbsData(degree, rational, closed, periodic, controlPoints, knotVector, weights, controlPointTolerance, knotTolerance))
        return  EntityError;

    MSBsplineCurve          bCurve;
    bCurve.rational = rational;
    bCurve.params.closed = false;
    bCurve.params.order = degree+1;
    bCurve.params.numPoles = controlPoints.length();
    // If the knot vector contains a full set of knots, use them.
    bCurve.params.numKnots = (knotVector.length() == BsplineParam::NumberAllocatedKnots (bCurve.params.numPoles, bCurve.params.order, false))
                           ? bCurve.params.numPoles - bCurve.params.order // always open
                           : 0;

    StatusInt               status;
    if (SUCCESS != (status = bCurve.Allocate()))
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    for (int iPole=0; iPole<bCurve.params.numPoles; iPole++)
        RealDwgUtil::DPoint3dFromGePoint3d (bCurve.poles[iPole], controlPoints[iPole]);

    if (0 != bCurve.params.numKnots)
        {
        const double *knotsPtr = knotVector.asArrayPtr();
        memcpy (bCurve.knots, knotsPtr, knotVector.length() * sizeof(double));
        }
    else
        {
        if (SUCCESS != (status = bspknot_computeKnotVector (bCurve.knots, &bCurve.params, NULL)))
            return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
        }

    if (bCurve.rational)
        {
        const double *weightsPtr = weights.asArrayPtr();
        memcpy (bCurve.weights, weightsPtr, bCurve.params.numPoles * sizeof (double));
        }

    bCurve.display.polygonDisplay = context.GetDatabase()->splframe();
    bCurve.display.curveDisplay = true;
    context.GetTransformToDGN().Multiply (bCurve.poles, bCurve.poles, bCurve.params.numPoles);

    if (bCurve.rational)
        bsputil_weightPoles (bCurve.poles, bCurve.poles, bCurve.weights, bCurve.params.numPoles);

    status = BSplineCurveHandler::CreateBSplineCurveElement (outElement, NULL, bCurve, context.GetThreeD(), *context.GetModel());

    bCurve.ReleaseMem ();

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

};  // ToDgnExtSpline



/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          10/11
+===============+===============+===============+===============+===============+======*/
class   ToDwgExtSpline : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* Remove redundant knots past multiplicity order and corresponding poles/weights if
* they are equal.  Shifts valid entries to starts of arrays.
* Assumes open, nondecreasing knot vector.
*
* @return true iff trimmed
* @bsimethod                                                    DavidAssaf      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       RemoveRedundantKnots
(
int*            pNewNumKnots,  // <=
Int32*          pNewNumPoles,  // <=
double*         pKnots,        // <=>
DPoint3dP       pPoles,        // <=>
double*         pWeights,      // <=>
int             numKnots,
int             numPoles,
bool            bRational,
int             order,
bool            bInteriorKnots, // true to check interior knots too
double          knotTol         // absolute tol
) const
    {
    int excess, excess0, excess1, kmult;

    if (pKnots == NULL || pNewNumKnots == NULL ||
        pPoles == NULL || pNewNumPoles == NULL ||
        (bRational && pWeights == NULL))
        return BSIERROR;

    // first knot multiplicity
    int iKnot;
    for (iKnot = kmult = 1; iKnot < numKnots && pKnots[iKnot] - pKnots[iKnot-1] < knotTol; iKnot++, kmult++)
        ;
    excess0 = kmult - order;

    // last knot multiplicity
    for (iKnot = numKnots - 1, kmult = 1; iKnot > 0 && pKnots[iKnot] - pKnots[iKnot-1] < knotTol; iKnot--, kmult++)
        ;
    excess1 = kmult - order;

    // shift past excess
    if ((excess0 > 0) || (excess1 > 0))
        {
        int numStripped = excess0 + excess1;
        numKnots -= numStripped;
        numPoles -= numStripped;

        // knot variation smaller than the tolerance can result in incorrect accessive knot count - TFS# 141091.
        if (numKnots <= 0 || numPoles <= 0)
            return  BSPLINE_STATUS_BadKnots;

        if (excess0 > 0)
            {
            memcpy (pKnots, &pKnots[excess0], numKnots * sizeof (*pKnots));
            memcpy (pPoles, &pPoles[excess0], numPoles * sizeof (*pPoles));
            if (bRational)
                memcpy (pWeights, &pWeights[excess0], numPoles * sizeof (*pWeights));
            }
        }

    // interior knot multiplicities
    if (bInteriorKnots)
        {
        int endKnot = numKnots - order;

        for (iKnot = order+1; iKnot < endKnot; iKnot += kmult)
            {
            // excess knot i-1
            int jKnot;
            for (jKnot = iKnot, kmult = 1; jKnot < endKnot && pKnots[jKnot] - pKnots[jKnot-1] < knotTol; jKnot++, kmult++)
                ;
            excess = kmult - order;

            // Note: if poles/wts[i-2] and [i-1+excess] are equal, spline is C0.
            // This holds regardless of values of poles/wts[i-1...i-2+excess].

            // shift past excess
            if (excess > 0)
                {
                int tail = iKnot-1+excess;

                memcpy (&pKnots[iKnot-1], &pKnots[tail], (numKnots - tail) * sizeof (*pKnots));
                memcpy (&pPoles[iKnot-1], &pPoles[tail], (numPoles - tail) * sizeof (*pPoles));
                if (bRational)
                    memcpy (&pWeights[iKnot-1], &pWeights[tail], (numPoles - tail) * sizeof (*pWeights));

                numKnots -= excess;
                numPoles -= excess;
                endKnot  -= excess;
                kmult    -= excess;
                }
            }
        }

    *pNewNumKnots = numKnots;
    *pNewNumPoles = numPoles;

    return SUCCESS;
    }

 /*---------------------------------------------------------------------------------**//**
* Tests if knots are invalid (decreasing, multiplicity > order).
* Assumes open knot vector.
*
* @return KNOTS_Valid, KNOTS_DECREASING or KNOTS_REDUNDANT
* @bsimethod                                                    DavidAssaf      10/01
+---------------+---------------+---------------+---------------+---------------+------*/
int             ValidateKnotVector (const double* pKnots, int numKnots, int order, double knotTol) const
    {
    int mult=1;

    for (int iKnot=1; iKnot<numKnots; iKnot++)
        {
        // decreasing knot
        if (pKnots[iKnot] < pKnots[iKnot-1])
            {
            return KNOTS_Decreasing;
            // multiple knot
            }
        else if (pKnots[iKnot] - pKnots[iKnot-1] < knotTol)
            {
            if (++mult > order)
                return KNOTS_Redundant;
            }
        else
            {
            mult = 1;
            }
        }

    return KNOTS_Valid;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/09
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt       ReplaceDegeneratedBezierWithLine (AcDbSpline* acSpline, const AcGePoint3dArray& controlPoints) const
    {
    AcDbLine*               pLine = new AcDbLine ();
    if (NULL != pLine)
        {
        pLine->setPropertiesFrom (acSpline);

        if (Acad::eObjectToBeDeleted == acSpline->handOverTo(pLine))
            {
            pLine->setStartPoint (controlPoints[0]);

            // approximate the end point of the new line at the middle of the 1st and 2nd poles:
            AcGePoint3d     midPoint;
            midPoint.setToSum (controlPoints[0], controlPoints[1].asVector());
            midPoint.scaleBy (0.5);

            pLine->setEndPoint (midPoint);

            pLine->close ();
            DIAGNOSTIC_PRINTF ("Replacing a quadratic Bezier curve of nearing start & end points with a line.\n");
            return  ReplacedObjectType;
            }
        else
            {
            delete pLine;
            DIAGNOSTIC_PRINTF ("Discarding a quadratic Bezier curve with nearing start & end points.\n");
            }
        }

    return  BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* Must pass in a DGN spline that is transformed into Dwg coords, opened (if
* previously periodic) and unweighted (if rational).
*
* @bsimethod                                                    DavidAssaf      05/01
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt        SetDwgSplineFromTransformedBsplineCurve (AcDbSpline* acSpline, MSBsplineCurve* pCurve, bool bWasPeriodic) const
    {
    // Get default tolerances
    double                  controlPointTolerance = 1.0E-8, knotTolerance = 1.0E-10;

    // This call just to get the tolerance values.
    if (0 != acSpline->numControlPoints())
        {
        int                 originalDegree;
        Adesk::Boolean      originalRational, originalClosed, originalPeriodic;
        AcGePoint3dArray    originalControlPoints;
        AcGeDoubleArray     originalKnotVector, originalWeights;

        acSpline->getNurbsData (originalDegree, originalRational, originalClosed, originalPeriodic, originalControlPoints, originalKnotVector, originalWeights,
                               controlPointTolerance, knotTolerance);
        }

    int         nKnots = BsplineParam::NumberAllocatedKnots (pCurve->params.numPoles, pCurve->params.order, false); // open
    double      largestCoordinate = bsiDPoint3d_getLargestCoordinate (pCurve->poles, pCurve->params.numPoles);
    double      dgnTolerance = controlPointTolerance * largestCoordinate;

    // validate knots
    int retVal = this->ValidateKnotVector (pCurve->knots, nKnots, pCurve->params.order, knotTolerance);
    if (retVal == KNOTS_Decreasing || (retVal == KNOTS_Redundant &&
        SUCCESS != this->RemoveRedundantKnots (&nKnots, &pCurve->params.numPoles,
                                               pCurve->knots, pCurve->poles, pCurve->weights, nKnots, pCurve->params.numPoles, 
                                               0 != pCurve->rational, pCurve->params.order, true, knotTolerance)))
        {
        DIAGNOSTIC_PRINTF ("Discarding B-spline Curve with invalid knot vector.\n");
        return BSIERROR;
        }

    // shift knots to begin with 0.0 (no need to normalize)
    double zeroKnotTolerance = 1.e-6;
    double k0 = pCurve->knots[0];
    if (k0 != 0.0)
        {
        for (int i = 0; i < nKnots; i++)
            {
            pCurve->knots[i] -= k0;
            /*---------------------------------------------------------------------------
            A small deviation in knot value causes RealDWG 2012 problem - TR 327128
            Art said that they will likely fix this bug in RealDWG, but it won't hurt to clamp the knots anyway.
            ---------------------------------------------------------------------------*/
            if (fabs(pCurve->knots[i]) <= zeroKnotTolerance)
                pCurve->knots[i] = 0.0;
            }
        }

    // validate poles:
    // R14 hangs if the 3 poles of a rational quadratic Bezier are equal within tolerance
    // but we throw out *all* degenerate B-spline curves (what use are they anyway?)
    DPoint3d    p0 = pCurve->poles[0];
    bool        bIsDegenerate = true;
    double      degenerateTolerance = 1.0E-10 * largestCoordinate;

    for (int jPole = 1; jPole < pCurve->params.numPoles; jPole++)
        {
        if (!p0.isEqual (&pCurve->poles[jPole], degenerateTolerance))
            {
            bIsDegenerate = false;
            break;
            }
        }
    if (bIsDegenerate)
        {
        DIAGNOSTIC_PRINTF ("Discarding B-spline Curve with all poles equal within tolerance: %f\n", degenerateTolerance);
        return BSIERROR;
        }

    // kludge for complex shapes that come thru with unequal start/end poles: ACAD chokes on the gap!
    if (bWasPeriodic && !p0.isEqual (&pCurve->poles[pCurve->params.numPoles-1]))
        pCurve->poles[pCurve->params.numPoles-1] = p0;

    AcGePoint3dArray            controlPoints;
    AcGeDoubleArray             weights;
    AcGeDoubleArray             knots;

    controlPoints.setLogicalLength (pCurve->params.numPoles);
    if (pCurve->rational)
        weights.setLogicalLength (pCurve->params.numPoles);

    knots.setLogicalLength (nKnots);

    for (int iPole=0; iPole<pCurve->params.numPoles; iPole++)
        {
        controlPoints[iPole] = RealDwgUtil::GePoint3dFromDPoint3d (pCurve->poles[iPole]);
        if (pCurve->rational)
            weights[iPole] = pCurve->weights[iPole];
        }

    for (int kKnot=0; kKnot<nKnots; kKnot++)
        knots[kKnot] = pCurve->knots[kKnot];

    /*-----------------------------------------------------------------------------------
    RealDWG hangs on a rastional quadratic Bezier with nearing start and end poles that
    have large coordinates. Such a degenerated spline also either hangs or crash AutoCAD.
    Work around this problem by replacing it with a line segment.
    -----------------------------------------------------------------------------------*/
    if (3 == pCurve->params.order && pCurve->rational && pCurve->poles[0].isEqual(&pCurve->poles[2], degenerateTolerance))
        return  ReplaceDegeneratedBezierWithLine(acSpline, controlPoints);

    /*-----------------------------------------------------------------------------------
    This is how we used to determine isClosed, the 3rd argument in below method:

    (bWasPeriodic || pCurve->poles[0].distance (&pCurve->poles[pCurve->params.numPoles - 1]) <= dgnTolerance) ? true : false,

    The matter of fact is that this argument is simply ignored by OpenDWG, so it has
    never had an effect on the output spline curve.  Now with RealDWG, this argument is
    used and does produce a result.  According to Art Cooney of AutoDesk, if the argument
    isClosed is true, the argument isPeriodic is ignored and it will be treated as true.
    TR 278815 shows just that.
    To keep the old behavior we set isClosed=false.  This should be correct because the
    flag bWasPeriodic was set from the curve parameter closed anyway.   ACAD's periodic
    implies closed.
    -----------------------------------------------------------------------------------*/
    bool                isClosed = false;
    Acad::ErrorStatus   es;

    es = acSpline->setNurbsData (pCurve->params.order - 1,
                           pCurve->rational ? true : false,
                           isClosed,
                           bWasPeriodic ? true : false,
                           controlPoints, knots, weights,
                           controlPointTolerance, knotTolerance);

   if (Acad::eOk != es || acSpline->isNull())
        DIAGNOSTIC_PRINTF ("Error setting NURBS data for spline entity! [%ls]\n", acadErrorStatusText(es));

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* Check for export compatibility
*
* @bsimethod                                                    DavidAssaf      09/02
+---------------+---------------+---------------+---------------+---------------+------*/
bool            IsFitCurveDWGCompatible (const MSInterpolationCurve* pFitCurve) const
    {
    bool    bCompat = true;

    // DWG fit curves are cubic.
    if (pFitCurve->params.order != 4)
        {
        bCompat = false;

        // DWG fit curves are open with tangents, but our periodic fit curves don't store tangents.
        // We could read the end tangents off the equivalent pole-based curve, and interpolate the
        // same fit points and these end tangents with an open curve for ACAD, but we would have no
        // way of telling ACAD to use the tangent magnitudes present in our periodic curve, so the
        // result is slightly off.  Summary: we cannot export periodic Interpolation Curves exactly,
        // so we must convert to pole-based.
        }
    else if (pFitCurve->params.isPeriodic != 0)
        {
        bCompat = false;
        }
    else
        {
        // A sufficient condition for an Interpolation Curve to be DWG compatible is if it has the values:
        // * isChordLenKnots    = 1
        // * isColinearTangents = 0
        // * isChordLenTangents = 1
        // * isNaturalTangents  = 1
        // This is not a necessary condition, however.  This method checks if the fit curve can still be
        // exported as a fit curve when it does not have the above values.  If not, we must convert to pole-
        // based to preserve curve shape (and necessarily lose fit-point information).

        // Use fc_nearZero (1.0e-14) zero tolerance (see setEndConditions)
        bool    bZeroStartTangent = pFitCurve->startTangent.isEqual (NULL, 1.0e-14);
        bool    bZeroEndTangent   = pFitCurve->endTangent.isEqual (NULL, 1.0e-14);

        // !isChordLenKnots:
        // In this case, the curve is incompatible iff it is periodic, but we've already ruled these out.
        // (See bspcurv_c2CubicInterpolateCurveExt: inParams is always NULL.)
        // !isNaturalTangents:
        if ((pFitCurve->params.isNaturalTangents == 0) && (bZeroStartTangent || bZeroEndTangent))
            {
            bCompat = false;

            // isColinearTangents:
            // In this case, the curve is incompatible if there is at least one 0-tangent and !isNaturalTangents.
            // There are other conditions for incompatibility in this case, but we've already ruled the above two out.

            // !isChordLenTangents:
            }
        else if ((pFitCurve->params.isChordLenTangents == 0) && (!bZeroStartTangent || !bZeroEndTangent))
            {
            bCompat = false;
            }
        }

    if (!bCompat)
        DIAGNOSTIC_PRINTF ("Incompatible Interpolation Curve is being exported as a pole-based B-spline Curve\n");

    return bCompat;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/11
+---------------+---------------+---------------+---------------+---------------+------*/
bool            AllPolesInDesignPlane (const MSBsplineCurve& curve) const
    {
    for (int i = 0; i < curve.params.numPoles; i++)
        {
        // these poles will be converted to DWG, so they are always 3D:
        if (RealDwgUtil::IsPointOffDesignPlane(&curve.poles[0], true))
            {
            DIAGNOSTIC_PRINTF ("Discarding B-Spline curve: poles out of design plane.\n");
            return  false;
            }
        }
    return  true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      01/2003
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus    SetAcDbSplineFromBsplineCurve (AcDbSpline* acSpline, CurveVectorCR curveVector, ConvertFromDgnContext& context) const
    {
    MSBsplineCurve  curve;
    StatusInt       status;
    if (SUCCESS == (status = curveVector.ToBsplineCurve(curve)))
        {
        // Although passed range check in IgnoreElement, some elements contain points out of range, TR 308491.
        if (!this->AllPolesInDesignPlane(curve))
            return  MstnElementUnacceptable;

        // save original state of curve
        bool        bWasPeriodic = curve.params.closed != 0;

        // AutoCAD only reads/writes open B-splines!
        MSBsplineCurve  openCurve;
        if (SUCCESS == (status = bspcurv_openCurve (&openCurve, &curve, 0.0)))
            {
            if (openCurve.rational)
                bsputil_unWeightPoles (openCurve.poles, openCurve.poles, openCurve.weights, openCurve.params.numPoles);

            context.GetTransformFromDGN().Multiply (openCurve.poles, openCurve.poles, openCurve.params.numPoles);

            status = SetDwgSplineFromTransformedBsplineCurve (acSpline, &openCurve, bWasPeriodic);

            bspcurv_freeCurve (&openCurve);
            }

        curve.ReleaseMem ();
        }

    return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbSplineFromInterpolationCurve (AcDbSpline* acSpline, CurveVectorCR curveVector, ConvertFromDgnContextR context) const
    {
    MSInterpolationCurveCP  fitCurve = curveVector.front()->GetInterpolationCurveCP ();
    if (NULL == fitCurve)
        return  WrongMstnElementType;

    // Cannot export exactly, so bail and produce an exact pole-based curve later
    if (!this->IsFitCurveDWGCompatible (fitCurve))
        return  CantCreateCurve;

    TransformCR             transformFromDGN = context.GetTransformFromDGN ();

    DVec3d                  tangent;
    transformFromDGN.MultiplyMatrixOnly (tangent, fitCurve->startTangent);
    AcGeVector3d            startTangent = RealDwgUtil::GeVector3dFromDPoint3d (tangent);
    startTangent = startTangent.normalize ();

    transformFromDGN.MultiplyMatrixOnly (tangent, fitCurve->endTangent);
    AcGeVector3d            endTangent = RealDwgUtil::GeVector3dFromDPoint3d (tangent);
    endTangent = endTangent.normalize ();

    AcGePoint3dArray        fitPoints;
    fitPoints.setLogicalLength (fitCurve->params.numPoints);

    DPoint3d                point;
    for (int i = 0; i < fitCurve->params.numPoints; i++)
        {
        transformFromDGN.Multiply (point, fitCurve->fitPoints[i]);
        fitPoints[i] = RealDwgUtil::GePoint3dFromDPoint3d (point);
        }

    // set DWG struct (negates dgn end tangent to pt in parametrization dir)
    acSpline->setFitData (fitPoints, fitCurve->params.order - 1, acSpline->fitTolerance(), startTangent, endTangent.negate());

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    // drop bspline curve to simple 3D polyline if user wants LWPolyline but it can't be created
    if (context.GetSettings().CreatePolylinesFromSplines() && !context.CanElementBeRepresentedByLWPolyline(inElement, false))
        return  AcDb3dPolyline::desc();
    
    DPoint3d    extrusionDirection;
    double      thickness = 0.0, width0 = 0.0, width1 = 0.0;

    // ACAD can't do B-splines with thickness/width unless they are splined polylines
    MSElementCP element = inElement.GetElementCP();
    if (context.GetEntityThicknessFromElementLinkage(thickness, extrusionDirection, NULL, inElement) || context.ExtractWidthFromElement(width0, width1, inElement))
        return AcDb2dPolyline::desc();
    else
        return AcDbSpline::desc();
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDbSplineFromCurveVector (AcDbSpline* acSpline, CurveVectorCR curveVector, ConvertFromDgnContextR context) const
    {
    ICurvePrimitive::CurvePrimitiveType curveType = curveVector.HasSingleCurvePrimitive ();

    if (ICurvePrimitive::CURVE_PRIMITIVE_TYPE_InterpolationCurve == curveType &&
        RealDwgSuccess == this->SetAcDbSplineFromInterpolationCurve (acSpline, curveVector, context))
        return  RealDwgSuccess;

    return this->SetAcDbSplineFromBsplineCurve (acSpline, curveVector, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   SetAcDb3dPolylineFromCurveVector (AcDb3dPolyline* ac3dPolyline, CurveVectorCR curveVector, ElementHandleR inElement, ConvertFromDgnContextR context) const
    {
    // default stroke options
    IFacetOptionsPtr    facetOptions = IFacetOptions::CreateForCurves ();
    DPoint3dArray       points;
    curveVector.AddStrokePoints (points, *facetOptions.get());
    if (points.size() < 2)
        return  CantCreatePolyline;

    ac3dPolyline->setPolyType (AcDb::k3dSimplePoly);
    context.GetTransformFromDGN().Multiply (&points.front(), &points.front(), (int)points.size());
    context.UpdateEntityPropertiesFromElement (ac3dPolyline, inElement);

    PolylineInfo            polylineInfo (points, 0.0, 0.0, false);
    SetFromPolylineInfo*    setFromPolyline = (SetFromPolylineInfo*) dynamic_cast <ToDgnExt3dPolyline*> (ACRX_X_CALL (ac3dPolyline, ToDgnExtension));
    assert (NULL != setFromPolyline);

    return setFromPolyline->VerticesFromPolylineInfo (ac3dPolyline, polylineInfo, inElement, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          10/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    CurveVectorPtr      curveVector = ICurvePathQuery::ElementToCurveVector (elemHandle);
    if (curveVector.IsNull() || curveVector->size() < 1)
        return  MstnElementUnacceptable;

    AcRxClass*          typeNeeded = GetTypeNeeded (elemHandle, context);
    acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);

    bool                isSolidFilled = false, isGradientFilled = false, isPatterned = false, isOutlined = false;
    bool                hasHatch = context.IsFilledOrPatterned (elemHandle, &isSolidFilled, &isGradientFilled, &isOutlined, &isPatterned);
    bool                isFilled = isSolidFilled | isGradientFilled;
    
    AcDbSpline*         acSpline = AcDbSpline::cast (acObject);
    if (NULL != acSpline)
        {
        if (hasHatch)
            context.AddHatchEntity (acSpline, elemHandle, isFilled, isPatterned, isOutlined);
        return  this->SetAcDbSplineFromCurveVector (acSpline, *curveVector.get(), context);
        }

    AcDb2dPolyline*     ac2dPolyline = AcDb2dPolyline::cast (acObject);
    if (NULL != ac2dPolyline)
        {
        if (hasHatch)
            context.AddHatchEntity (ac2dPolyline, elemHandle, isFilled, isPatterned, isOutlined);

        if (NULL != existingObject && existingObject->isKindOf(AcDb2dPolyline::desc()))
            ac2dPolyline->copyFrom (existingObject);

        Convert2dPolylineHelper plineConverter (ac2dPolyline, elemHandle, context);
        return plineConverter.SetAcDb2dPolylineFromCurve (*curveVector.get());
        }

    AcDb3dPolyline*     ac3dPolyline = AcDb3dPolyline::cast (acObject);
    if (NULL != ac3dPolyline)
        return  SetAcDb3dPolylineFromCurveVector (ac3dPolyline, *curveVector.get(), elemHandle, context);

    if (NULL != acObject && acObject->isNewObject())
        {
        delete acObject;
        acObject = NULL;
        }

    return CantCreateCurve;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/13
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObjectPostProcess (ElementHandleR inElement, AcDbObjectP acObject, ConvertFromDgnContextR context) const override
    {
    // associate hatch with boundary entities
    if (context.IsFilledOrPatterned(inElement))
        {
        ToDwgExtAssocRegion     assocRegion;
        return assocRegion.ToObjectPostProcess (inElement, acObject, context);
        }

    return  RealDwgSuccess;
    }

};  // ToDwgExtSpline

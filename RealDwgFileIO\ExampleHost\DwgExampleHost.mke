#*--------------------------------------------------------------------------------------+
#
#     $Source: mstn/mdlapps/RealDwgFileIO/ExampleHost/DwgExampleHost.mke $
#
#  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
#
#--------------------------------------------------------------------------------------*/
PolicyFile=$(_MakeFilePath)DwgExampleHost.mki

appName             = DwgExampleHost
baseDir             = $(_MakeFilePath)
SubpartLibs         = $(BuildContext)SubParts/Libs/
ContextDeliveryDir  = $(BuildContext)Delivery/
o                   = $(OutputRootDir)build/DwgExampleHost/
%if !defined (RealDwgVersion)
RealDwgVersion      = 2023
%endif

%include mdl.mki

nameToDefine =__DWGEXAMPLEHOST_BUILD__
%include cdefapnd.mki

always:
    ~mkdir $(o)
    ~mkdir $(ContextDeliveryDir)

MultiCompileDepends=$(_MakeFileSpec)
%include MultiCppCompileRule.mki

$(o)DwgExampleHost$(oext)           : $(baseDir)$(appName).cpp $(baseDir)$(appName).h ${MultiCompileDepends}

%include MultiCppCompileGo.mki
appObjects =% $(MultiCompileObjectList)

DLM_NAME                    =   $(appName)
DLM_OBJECT_DEST             =   $(o)
DLM_OBJECT_FILES            =   $(appObjects)
DLM_LIBDEF_SRC              =   $(baseDir)
DLM_EXPORT_DEST             =   $(o)
DLM_DEST                    =   $(o)
DLM_NO_DLS                  =   1     #  Used DLLEXPORT in .c file instead of using a .dls
DLM_NO_DEF                  =   1
DLM_NOENTRY                 =   1
DLM_NO_INITIALIZE_FUNCTION  =   1
DLM_NOMSBUILTINS            =   1
DLM_CREATE_LIB_CONTEXT_LINK =   1
DLM_LIB_CONTEXT_LOCATION    =   $(ContextDeliveryDir)
DLM_CONTEXT_LOCATION        =   $(ContextDeliveryDir)

LINKER_LIBRARIES            =   $(SubpartLibs)Bentley.lib   \
                                $(SubpartLibs)BentleyAllocator.lib   \
                                $(SubpartLibs)DgnPlatform.lib   \
                                $(SubpartLibs)DwgDgnIO$(RealDwgVersion).lib

%if RealDwgVersion >= 2017
LINKER_LIBRARIES_DELAYLOADED = $(SubpartLibs)AcPal.lib
%endif

%include dlmlink.mki

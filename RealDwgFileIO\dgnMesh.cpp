/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnMesh.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          08/11
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtMeshHeader : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsiclass                                                     Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          inElement,          // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    PolyfaceHeaderPtr   dgnPolyface;
    BentleyStatus       status = MeshHeaderHandler::PolyfaceFromElement (dgnPolyface, inElement);
    if (SUCCESS != status)
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    if (dgnPolyface->GetPointCount() < 3 || dgnPolyface->GetNumFacet() < 1)
        return  CantCreateMesh;

    DVec3dCP    normal = dgnPolyface->GetNormalCP ();
    if (context.GetSettings().IsZeroZCoordinateEnforced() && nullptr != normal && fabs(normal->z) > TOLERANCE_VectorEqual)
        {
        // drop mesh to shapes
        DropGeometry    dropMesh (DropGeometry::OPTION_Solids);
        dropMesh.SetSolidsOptions (DropGeometry::SOLID_Surfaces);
        return context.DropElementToDwg (acObject, NULL, inElement, dropMesh);
        }

    AcRxClass*          typeNeeded = GetTypeNeeded (dgnPolyface);
    acObject = context.InstantiateOrUseExistingObject (existingObject, typeNeeded);

    AcDbPolyFaceMesh*   acPolyfaceMesh = AcDbPolyFaceMesh::cast (acObject);
    if (NULL != acPolyfaceMesh)
        {
        RealDwgStatus   rdStatus = SetAcDbPolyfaceMeshFromMeshElement (acPolyfaceMesh, dgnPolyface, inElement, context);
        if (RealDwgSuccess == rdStatus && acPolyfaceMesh != acObject)
            {
            // polyface ID has been handeded over to a new polyface, delete the old object:
            acPolyfaceMesh->close();
            acObject = acPolyfaceMesh;
            }
        return  rdStatus;
        }

    AcDbPolygonMesh*    acPolygonMesh = AcDbPolygonMesh::cast (acObject);
    if (NULL != acPolygonMesh)
        {
        RealDwgStatus   rdStatus = SetAcDbPolygonMeshFromMeshElement (acPolygonMesh, dgnPolyface, inElement, context);
        if (ReplacedObjectType == rdStatus)
            {
            // polygon mesh has been replaced by a polyface mesh which should have been closed, just delete the polygon mesh:
            acPolygonMesh->close();
            acObject = NULL;
            }
        return  rdStatus;
        }

    AcDbFace*           ac3DFace = AcDbFace::cast (acObject);
    if (NULL != ac3DFace)
        return  SetAcDbFaceFromMeshElement (ac3DFace, dgnPolyface, context);

    return NoConversionMethod;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*              GetTypeNeeded (PolyfaceHeaderPtr dgnPolyface) const
    {
    size_t              maxPerFace = 0;
    size_t              numFaces   = dgnPolyface->GetNumFacet (maxPerFace);
    UInt32              meshStyle  = dgnPolyface->GetMeshStyle ();
    // single-triangle/quad meshes can be converted to a 3DFace
    if (1 == numFaces && (maxPerFace == 3 || maxPerFace == 4) && MESH_ELM_STYLE_QUAD_GRID != meshStyle)
        return AcDbFace::desc();

    // grid mesh if small enough in size can be converted to polygon mesh
    int                 numPerRow = dgnPolyface->Point().StructsPerRow();
    if (MESH_ELM_STYLE_QUAD_GRID == meshStyle && numPerRow <= MAX_Int16Value && numPerRow > 0 &&
        (dgnPolyface->GetPointCount() / numPerRow) <= MAX_Int16Value)
        return AcDbPolygonMesh::desc();

    // all other types of mesh will be converted to polyface mesh
    return AcDbPolyFaceMesh::desc();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbFaceFromMeshElement (AcDbFace* acFace, PolyfaceHeaderPtr dgnPolyface, ConvertFromDgnContextR context) const
    {
    size_t                  numPoints = dgnPolyface->GetPointCount ();
    if (numPoints < 3)
        return InvalidFacePointCount;

    dgnPolyface->Transform (context.GetTransformFromDGN());

    BlockedVectorDPoint3dR  pointVector = dgnPolyface->Point ();
    BlockedVectorIntR       indexVector = dgnPolyface->PointIndex ();
    PolyfaceVisitorPtr      faceVisitor = PolyfaceVisitor::Attach (*dgnPolyface.get (), true);

    faceVisitor->SetNumWrap (1);
    // expect only one face:
    faceVisitor->AdvanceToNextFace (); 

    BlockedVectorIntR       edgeIndices = faceVisitor->ClientPointIndex ();
    size_t                  numEdges = faceVisitor->NumEdgesThisFace ();

    // ACAD 3DFace triangles have equal vertices at i=2,3; the hide flag on the (nontrivial) wraparound edge is at i=3
    if (3 == numPoints)
        {
        // add the 4th point from the 3d point
        pointVector.push_back (pointVector.at(2));
        // add the 4th draw flag from the 3rd draw flag
        indexVector.push_back (indexVector.at(2));
        // add the 4th vertex from the 3rd vertex of the face visitor
        edgeIndices.push_back (static_cast<int>(numPoints));

        // apply above ACAD rule for the triangle:
        edgeIndices.at(3) = edgeIndices.at (2);
        faceVisitor->Visible().at(3) = faceVisitor->Visible().at (2);
        faceVisitor->Visible().at(2) = false;        // arbitrary flag on trivial edge
        numPoints++;
        numEdges++;
        }

    // a non-polyface mesh does not have an edge list
    bool                    hasEdgeIndices = edgeIndices.size() >= numEdges;
    bool                    zeroZ = context.GetSettings().IsZeroZCoordinateEnforced ();
    for (Adesk::Int16 i = 0; i < (Adesk::Int16)numEdges; i++)
        {
        size_t              iVertex = hasEdgeIndices ? edgeIndices.at(i) : i;

        // if user wants to remove z-coordinate in DWG file, do so now:
        if (zeroZ)
            pointVector[iVertex].z = 0.0;

        acFace->setVertexAt (i, RealDwgUtil::GePoint3dFromDPoint3d(pointVector[iVertex]));
        if (faceVisitor->Visible().at(i))
            acFace->makeEdgeVisibleAt (i);
        else
            acFace->makeEdgeInvisibleAt (i);
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SplitLargeMesh (AcDbPolyFaceMesh*& acMesh, PolyfaceHeaderPtr dgnPolyface, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    size_t      numPoints = 0, numDgnFaces = 0, numQuad = 0, numTri = 0, numFacesTriangulated = 0, numVisible = 0, numInvisible = 0;
    dgnPolyface->CollectCounts (numPoints, numDgnFaces, numQuad, numTri, numFacesTriangulated, numVisible, numInvisible);

    // set numDwgFaces and target face count according to whether or not SetAcDbPolyfaceMeshFromMeshElement will triangulate the mesh
    size_t      numDwgFaces = 0, targetFaceCount = 0;
    if (numDgnFaces == numQuad + numTri)
        {
        // dwg importer will not triangulate
        numDwgFaces = numDgnFaces;
        targetFaceCount = MAX_Int16Value;
        }
    else
        {
        // shrink the target face count so our submesh is still within dwg limits after SetAcDbPolyfaceMeshFromMeshElement will triangulate it
        numDwgFaces = numFacesTriangulated;
        targetFaceCount = (int) floor (MAX_Int16Value * (numDgnFaces / (double) numDwgFaces));
        }

    if (numPoints > MAX_Int16Value || numDwgFaces > MAX_Int16Value)
        {
        DIAGNOSTIC_PRINTF ("Splitting Mesh Element derived from element: %I64d\n", inElement.IsValid() ? inElement.GetElementCP()->ehdr.uniqueId : 0);

        // in case vertex count is too high, base the target submesh count on the vertex count
        size_t  targetSubMeshCount = (size_t) ceil ((double)numPoints / (double)MAX_Int16Value);

        // when passing 1 into PartitionByXYRange we'd get 1 mesh back, causing endless recursive calls to SplitLargeMesh!!
        targetSubMeshCount++;

        bvector<PolyfaceHeaderPtr> subMeshArray;
        if (!dgnPolyface->PartitionByXYRange(targetFaceCount, targetSubMeshCount, subMeshArray))
            return  InconsistentPolyMeshData;

        RealDwgStatus   status = RealDwgSuccess;
        bool            firstMesh = true;

        for each (PolyfaceHeaderPtr subMesh in subMeshArray)
            {
            if (firstMesh)
                {
                AcDbPolyFaceMesh*   inputMesh = acMesh;
                status = SetAcDbPolyfaceMeshFromMeshElement (acMesh, subMesh, inElement, context);

                if (RealDwgSuccess == status && acMesh != inputMesh)
                    delete inputMesh;

                firstMesh = false;
                }
            else
                {
                AcDbPolyFaceMesh*   newAcMesh = new AcDbPolyFaceMesh ();
                if (NULL == newAcMesh)
                    return  OutOfMemoryError;

                status = SetAcDbPolyfaceMeshFromMeshElement (newAcMesh, subMesh, ElementHandle(), context);
                if (RealDwgSuccess == status)
                    {
                    if (!newAcMesh->objectId().isValid())
                        context.AddEntityToCurrentBlock (newAcMesh, 0);
                        
                    context.UpdateEntityPropertiesFromElement (newAcMesh, inElement);

                    newAcMesh->close ();
                    }
                else
                    {
                    delete newAcMesh;
                    }
                }

            if (RealDwgSuccess != status)
                return  status;
            }
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/02
+---------------+---------------+---------------+---------------+---------------+------*/
static Acad::ErrorStatus    AppendVertex
(
AcDbPolygonMesh*            acMesh,
DPoint3dP                   pPoint
)
    {
    AcDbPolygonMeshVertex*  pVertex = new AcDbPolygonMeshVertex();
    pVertex->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (*pPoint));

    pVertex->setPropertiesFrom (acMesh);

    AcDbObjectId        idVertex;
    Acad::ErrorStatus   status;
    if (Acad::eOk == (status = acMesh->appendVertex (idVertex, pVertex)))
        pVertex->close ();
    else
        delete pVertex;

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
static Acad::ErrorStatus    RemoveAllVertices
(
AcDbPolygonMesh*            acMesh
)
    {
    AcDbObjectIterator* pIterator = acMesh->vertexIterator();
    for (; !pIterator->done(); )
        {
        AcDbObjectId            vertexId = pIterator->objectId ();
        AcDbPolygonMeshVertex*  pVertex  = NULL;

        Acad::ErrorStatus status;
        if (Acad::eOk != (status = acMesh->openVertex (pVertex, vertexId, AcDb::kForWrite)))
            {
            delete pIterator;
            return status;
            }

        pIterator->step();
        pVertex->erase();
        pVertex->close ();
        }
    delete pIterator;

    return Acad::eOk;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
int                     CountVertices
(
AcDbPolygonMesh*        acMesh,
AcDb::Vertex3dType      typeToSkip
) const
    {
    int                 count       = 0;
    AcDbObjectIterator* pIterator   = acMesh->vertexIterator();

    for (; !pIterator->done(); pIterator->step())
        {
        if ( (AcDb::Vertex3dType)(-1) == typeToSkip)
            count++;
        else
            {
            AcDbObjectId            vertexId = pIterator->objectId ();
            AcDbPolygonMeshVertex*  pVertex = NULL;

            Acad::ErrorStatus status;
            if (Acad::eOk != (status = acMesh->openVertex (pVertex, vertexId, AcDb::kForRead)))
                continue;

            if (pVertex->vertexType() != typeToSkip)
                count++;

            pVertex->close();
            }
        }
    delete  pIterator;

    return count;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           NewPolygonMesh
(
AcDbPolygonMesh*        acMesh,
AcDb::PolyMeshType      meshType,
DPoint3dP               pVerts,
int                     meshM,
int                     meshN,
int                     meshMDensity,
int                     meshNDensity,
Adesk::Boolean          mClosed,
Adesk::Boolean          nClosed
) const
    {
    Acad::ErrorStatus status;
    status = RemoveAllVertices (acMesh);
    BeAssert (Acad::eOk == status);
    status = acMesh->setMSize ((Adesk::Int16)meshM);
    BeAssert (Acad::eOk == status);
    status = acMesh->setNSize ((Adesk::Int16)meshN);
    BeAssert (Acad::eOk == status);
    status = acMesh->setMSurfaceDensity ((Adesk::Int16)meshMDensity);
    BeAssert (Acad::eOk == status);
    status = acMesh->setNSurfaceDensity ((Adesk::Int16)meshNDensity);
    BeAssert (Acad::eOk == status);
    status = mClosed ? acMesh->makeMClosed() : acMesh->makeMOpen();
    status = nClosed ? acMesh->makeNClosed() : acMesh->makeNOpen();

    for (int iVertex = 0; iVertex < meshM * meshN; iVertex++)
        {
        status = AppendVertex (acMesh, &pVerts[iVertex]);
        BeAssert (Acad::eOk == status);
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ReTryConvertingToPolyfaceMesh (AcDbPolygonMesh* acMesh, PolyfaceHeaderPtr dgnPolyface, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    // try converting DGN mesh to a polyface mesh, instead of a polygon mesh:
    AcDbPolyFaceMesh*   polyfaceMesh = new AcDbPolyFaceMesh ();
    // replace object handle of the input polygon object:
    if (Acad::eObjectToBeDeleted != acMesh->handOverTo(polyfaceMesh))
        return  CantCreateMesh;

    if (RealDwgSuccess == SetAcDbPolyfaceMeshFromMeshElement(polyfaceMesh, dgnPolyface, inElement, context))
        {
        polyfaceMesh->close ();

        return ReplacedObjectType;
        }

    // failure creating polyface mesh - clean up the trial polyface mesh:
    polyfaceMesh->close ();
    polyfaceMesh->handOverTo (acMesh);
    delete polyfaceMesh;
    
    return  CantCreateMesh;
    }

/*---------------------------------------------------------------------------------**//**
* Set PolyGridMesh object from a new bilinear B-spline surface element (see
* DwgFileData.newFromElement()), or a B-spline surface element or mesh element
* created with PolyGridMesh.toElement().  Other mesh elements are exported with
* PolyFaceMesh.fromElement().
*
* @param        pElmDscr    element
* @param        elementContext    info about file
* @bsimethod                                                    DavidAssaf      01/01
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbPolygonMeshFromMeshElement (AcDbPolygonMesh* acMesh, PolyfaceHeaderPtr dgnPolyface, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    // work a copy of mesh point vector as the original may need to be reused in case of failure
    BlockedVector<DPoint3d> pointArray(dgnPolyface->Point());
    size_t                  numPoints = pointArray.size ();
    size_t                  numPerRow = pointArray.StructsPerRow ();
    size_t                  numRows = 0 == numPerRow ? 0 : numPoints / numPerRow;
        
    if (MESH_ELM_STYLE_QUAD_GRID != dgnPolyface->GetMeshStyle() || 0 == numPerRow || MAX_Int16Value < numPerRow || MAX_Int16Value < numRows ||
        numPoints != (numRows * numPerRow))  // ragged row check
        return  ReTryConvertingToPolyfaceMesh (acMesh, dgnPolyface, inElement, context);

    Adesk::Boolean          mClosed = Adesk::kTrue;
    Adesk::Boolean          nClosed = Adesk::kTrue;
    AcDb::PolyMeshType      meshType = AcDb::kSimpleMesh;

    // test for closed mesh in each direction
    double                  tolSquared = TOLERANCE_RelativeDistanceRatio * dgnPolyface->LargestCoordinate();
    tolSquared *= tolSquared;
    for (size_t iRow = 0; iRow < numRows; iRow++)    // look for duplicate 1st/last col
        {
        if (pointArray[iRow*numPerRow].DistanceSquared (pointArray[(iRow+1)*numPerRow-1]) > tolSquared)
            {
            nClosed = Adesk::kFalse;
            break;
            }
        }
    for (size_t iRow = 0; iRow < numPerRow; iRow++) // look for duplicate 1st/last row
        {
        if (pointArray[iRow].DistanceSquared(pointArray[(numRows-1)*numPerRow+iRow]) > tolSquared)
            {
            mClosed = false;
            break;
            }
        }

    // adjust points
    if (nClosed)
        {
        // delete in reverse order so that array indexing is unaffected
        for (size_t iRow = numRows; iRow > 0; iRow--)
            pointArray.erase (pointArray.begin() + iRow * numPerRow - 1);
        numPerRow--;
        }
    if (mClosed)
        {
        BlockedVector<DPoint3d>::iterator   from = pointArray.begin() + (numRows-1)*numPerRow;
        pointArray.erase (from, from + numPerRow);
        numRows--;
        }

    // set data
    int             meshM = (int)numRows;
    int             meshN = (int)numPerRow;
    int             meshNDensity = 0;
    int             meshMDensity = 0;

    RealDwgStatus   status = this->SetAcDbPolygonMeshFromData (acMesh, pointArray, meshM, meshN, meshNDensity, meshMDensity, mClosed, nClosed, meshType, inElement, context);

    if (RealDwgSuccess != status)
        status = ReTryConvertingToPolyfaceMesh (acMesh, dgnPolyface, inElement, context);

    return  status;
    }

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          06/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               SetAcDbPolygonMeshFromData
(
AcDbPolygonMesh*            acMesh,
BlockedVector<DPoint3d>&    pointArray,
int                         meshM,
int                         meshN,
int                         meshNDensity,
int                         meshMDensity,
Adesk::Boolean              mClosed,
Adesk::Boolean              nClosed,
AcDb::PolyMeshType          meshType,
ElementHandleCR             inElement,
ConvertFromDgnContextR      context
) const
    {
    // have our mesh data, either from the incoming Bspline, or the incoming mesh element.
    context.GetTransformFromDGN().Multiply (pointArray.data(), (int)pointArray.size());

    // Need header as symbology template for vertices!!
    context.UpdateEntityPropertiesFromElement (acMesh, inElement);

    // acMesh->appendVertext prerequisite database residency:
    if (!acMesh->objectId().isValid())
        context.AddEntityToCurrentBlock (acMesh, inElement.GetElementCP()->ehdr.uniqueId);

    // if we're changing size, or type, or fit density, create a new surface using the RealDWG methods. This is costly.
    RealDwgStatus   rDwgStatus;
    if ( (acMesh->polyMeshType()     != meshType)        || (acMesh->mSize()           != meshM) || (acMesh->nSize() != meshN) ||
         (acMesh->mSurfaceDensity()  != meshMDensity)    || (acMesh->nSurfaceDensity() != meshNDensity) ||
         (acMesh->isMClosed()        != mClosed)         || (acMesh->isNClosed()       != nClosed) )
        {
        rDwgStatus = NewPolygonMesh (acMesh, meshType, pointArray.data(), meshM, meshN, meshMDensity, meshNDensity, mClosed, nClosed);
        }
    else
        {
        // Here, the topology of the polygonMesh has not changed, so we simply reposition any of the control points that have moved.
        // If we find that any have moved, we throw away all of the spline surface fit pts and use the RealDwg routine to re-fit the surface.
        bool                        reFitSurface    = false;
        int                         jVert           = 0;
        double                      tol             = TOLERANCE_RelativeDistanceRatio * bsiDPoint3d_getLargestCoordinate(pointArray.GetCP(), (int)pointArray.size());
        AcDbObjectIterator*         pIterator;

        for (pIterator = acMesh->vertexIterator(); !pIterator->done(); pIterator->step())
            {
            AcDbObjectId            vertexId = pIterator->objectId ();
            AcDbPolygonMeshVertex*  acVertex;

            if (Acad::eOk != acMesh->openVertex(acVertex, vertexId, AcDb::kForRead))
                {
                rDwgStatus = CantCreateMesh;
                reFitSurface = false;
                break;
                }

            if (acVertex->vertexType() != AcDb::k3dFitVertex)
                {
                DPoint3d            currentPosition;
                RealDwgUtil::DPoint3dFromGePoint3d (currentPosition, acVertex->position());

                if (!currentPosition.IsEqual(pointArray[jVert], tol))
                    {
                    // if user wants to remove z-coordinate in DWG file, do so now:
                    if (context.GetSettings().IsZeroZCoordinateEnforced())
                        pointArray[jVert].z = 0.0;

                    Acad::ErrorStatus status;
                    status = acVertex->upgradeOpen();
                    if (Acad::eOk != status)
                        DIAGNOSTIC_PRINTF ("Error upgrading AcDbVertex to reset position.  [%ls]\n", acadErrorStatusText(status));
                    BeAssert (Acad::eOk == status);

                    status = acVertex->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (pointArray[jVert]));
                    if (Acad::eOk != status)
                        DIAGNOSTIC_PRINTF ("Error resetting position for AcDbVertex.  [%ls]\n", acadErrorStatusText(status));

                    reFitSurface = true;
                    }
                jVert++;
                }
            acVertex->close();
            }
        delete pIterator;

        rDwgStatus = RealDwgSuccess;
        if (reFitSurface)
            {
            // this removes all the fit vertexes from the surface.
            Acad::ErrorStatus status;
            status = acMesh->straighten();
            if (Acad::eOk != status)
                DIAGNOSTIC_PRINTF ("Error removing fit points from a surface.  [%ls]\n", acadErrorStatusText(status));

            if (meshMDensity > 0 || meshNDensity > 0)
                {
                status = acMesh->surfaceFit (meshType, (Adesk::Int16)meshMDensity, (Adesk::Int16)meshNDensity);
                if (Acad::eOk != status)
                    DIAGNOSTIC_PRINTF ("Error fitting a surface.  [%ls]\n", acadErrorStatusText(status));
                }
            }
        }

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      08/00
+---------------+---------------+---------------+---------------+---------------+------*/
static RealDwgStatus        SetAcDbPolyFaceMeshFromPolygon (AcDbPolyFaceMesh* acMesh, DPoint3dCP inPoints, size_t numPoints)
    {
    if (NULL == inPoints || numPoints < 3)
        return  InvalidPolygonMesh;

    // Drop SDP (Stupid disconnect point).
    if (inPoints[0].IsEqual(inPoints[numPoints-1]))
        numPoints--;

    // create a single polyface header from input point array, and create a mesh of 3's and/or 4's:
    PolyfaceHeaderPtr       dgnPolyface = PolyfaceHeader::CreateVariableSizeIndexed ();

    if (!dgnPolyface->AddPolygon(inPoints, numPoints))
        return  CantCreateMesh;

    // do not check return status as it may have created a mesh with a few bad facets but rest of them are OK. Check for number of facets instead. TFS 171281.
    dgnPolyface->Triangulate (4);

    // now add DWG polyface vertices:
    BlockedVectorDPoint3dR  pointVector = dgnPolyface->Point ();
    BlockedVectorIntR       indexVector = dgnPolyface->PointIndex ();
    if (pointVector.empty() || indexVector.empty())
        return  CantCreateMesh;

    numPoints = dgnPolyface->GetPointCount ();

    for (UInt32 i = 0; i < numPoints; i++)
        {
        AcDbPolyFaceMeshVertex* acVertex = new AcDbPolyFaceMeshVertex();

        acVertex->setPosition (RealDwgUtil::GePoint3dFromDPoint3d(pointVector[i]));
        acVertex->setPropertiesFrom (acMesh);

        if (Acad::eOk == acMesh->appendVertex(acVertex))
            acVertex->close ();
        else
            delete acVertex;
        }

    // add DWG polyface faces:
    PolyfaceVisitorPtr      faceVisitor = PolyfaceVisitor::Attach (*dgnPolyface.get(), true);
    faceVisitor->SetNumWrap (1);
    for (faceVisitor->Reset(); faceVisitor->AdvanceToNextFace();)
        {
        AcDbFaceRecord* acFace = new AcDbFaceRecord ();
        acFace->setPropertiesFrom (acMesh);

        for (UInt32 i = 0; i < faceVisitor->NumEdgesThisFace(); i++)
            {
            // ACAD face uses 1-based vertex index:
            Adesk::Int16    vertexNo = (Adesk::Int16)faceVisitor->ClientPointIndex().at(i) + 1;
            acFace->setVertexAt ((Adesk::Int16)i, vertexNo);

            if (!faceVisitor->Visible().at(i))
                acFace->makeEdgeInvisibleAt ((Adesk::Int16)i);
            }

        if (Acad::eOk == acMesh->appendFaceRecord (acFace))
            acFace->close ();
        else
            delete acFace;
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    DavidAssaf    10/00
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           SetAcDbPolyfaceMeshFromMeshElement (AcDbPolyFaceMesh*& acMesh, PolyfaceHeaderPtr dgnPolyface, ElementHandleCR inElement, ConvertFromDgnContextR context) const
    {
    size_t      numPoints = 0, numFaces = 0, numQuad = 0, numTri = 0, numFacesTriangulated = 0, numVisible = 0, numInvisible = 0;
    dgnPolyface->CollectCounts (numPoints, numFaces, numQuad, numTri, numFacesTriangulated, numVisible, numInvisible);
    if (numPoints < 3 || numFaces < 1)
        return  MstnElementUnacceptable;

    // TR #177432: If this imported mesh was modified such that its vertex/face count now exceeds 32767, fail so that
    //             DgnImportContext::preprocessUnsupportedElements will be called to split this mesh, and so that
    //             DgnImportContext::SaveElementToDatabase will erase this mesh.
    if (numFacesTriangulated > 0)
        {
        // set numDwgFaces according to whether or not the dwg importer will be triangulating
        size_t  numDwgFaces = (numFaces == numQuad + numTri) ? numFaces : numFacesTriangulated;
        if (numPoints > MAX_Int16Value || numDwgFaces > MAX_Int16Value)
            return  SplitLargeMesh(acMesh, dgnPolyface, inElement, context);
        }

    BlockedVectorDPoint3dR  pointVector = dgnPolyface->Point ();
    BlockedVectorIntR       indexVector = dgnPolyface->PointIndex ();
    size_t                  maxPerFace = 0;

    // get max number of vertices per face
    numFaces = dgnPolyface->GetNumFacet (maxPerFace);

    // optimize nPerFace to avoid unnecessary triangulation (e.g., if all var-size faces are tris/quads, optimal is nPerFace = 4)
    if (maxPerFace != 3 && maxPerFace != 4 && dgnPolyface->CompactIndexArrays())
        numFaces = dgnPolyface->GetNumFacet (maxPerFace);

    // force mesh to fixed block size of 4 if not fixed at 3 or 4 already
    bool                    bTriangulated = false;
    if (maxPerFace != 3 && maxPerFace != 4)
        {
        // triangulate or quadrate the mesh:
        // do not check return status as it may have created a mesh with a few bad facets but rest of them are OK. Check for points & facets instead. TFS 171281.
        dgnPolyface->Triangulate (4);

        pointVector = dgnPolyface->Point ();
        indexVector = dgnPolyface->PointIndex ();
        if (pointVector.empty() || indexVector.empty())
            return CantCreateMesh;

        // we now have 0-terminated indices; treat 'em as 0-padded quads
        numFaces = dgnPolyface->GetNumFacet (maxPerFace);
        bTriangulated = true;
        }

    BlockedVectorInt        colorArray;
    BlockedVectorIntR       colorIndices = dgnPolyface->ColorIndex ();
    bool                    bHasColor = !colorIndices.empty() && MESH_ELM_INDEX_FAMILY_BY_FACE == colorIndices.IndexFamily();
    if (bHasColor)
        {
        /*------------------------------------------------------------------------------------------------
        Prior to Vancouver, we attempted to remap original "byface" color to newly triangulated mesh faces.
        Now PolyfaceHeader should have taken care of that during triangulation.  We can simply use its color
        index array.
        ------------------------------------------------------------------------------------------------*/
        BlockedVectorUInt32R    colorTable = dgnPolyface->ColorTable ();
        BlockedVectorUInt32R    intColors = dgnPolyface->IntColor ();
        BlockedVectorRgbFactorR doubleColors = dgnPolyface->DoubleColor ();

        // translate colors to DWG
        if (!colorTable.empty())
            {
            for each (UInt32 color in colorTable)
                colorArray.push_back ((int)context.GetColorIndexFromDgn(color, -1));
            }
        else if (!intColors.empty())
            {
            for each (UInt32 color in intColors)
                {
                RgbColorDef             rgbColor = RealDwgUtil::RGBColorDefFromIntColor (color);
                colorArray.push_back ((int)context.GetColorIndexFromRGB(&rgbColor));
                }
            }
        else if (!doubleColors.empty())
            {
            for each (RgbFactor rgbFactor in doubleColors)
                {
                RgbColorDef             rgbColor;
                ColorUtil::ConvertRgbFactorToRgbColorDef (rgbColor, rgbFactor);
                colorArray.push_back ((int)context.GetColorIndexFromRGB(&rgbColor));
                }
            }
        else if (!dgnPolyface->FloatColor().empty())
            {
            for each (FloatRgb floatRgb in dgnPolyface->FloatColor())
                {
                RgbColorDef             rgbColor;
                ColorUtil::ConvertRgbFactorToRgbColorDef (rgbColor, (RgbFactor const&)floatRgb);
                colorArray.push_back ((int)context.GetColorIndexFromRGB(&rgbColor));
                }
            }
        }

    // set verts and faces
    int             currentNFaces, currentNVertices;
    bool            bUseColor=false;
    RealDwgStatus   status = RealDwgSuccess;

    // Transform to master units
    context.GetTransformFromDGN().Multiply (pointVector.data(), (int)pointVector.size());

    // Count current number of faces and vertices (the methods for this don't seem to be reliable.
    AcDbObjectIterator*         pIterator;
    for (currentNFaces = currentNVertices = 0, pIterator = acMesh->vertexIterator(); !pIterator->done(); pIterator->step())
        {
        AcDbObjectId            vertexId = pIterator->objectId ();
        AcDbEntityPointer       acVertex (vertexId, AcDb::kForRead);

        if (Acad::eOk != acVertex.openStatus())
            continue;
        else if (acVertex->isKindOf (AcDbFaceRecord::desc()))
            currentNFaces++;
        else if (acVertex->isKindOf (AcDbPolyFaceMeshVertex::desc()))
            currentNVertices++;
        }
    delete pIterator;

    // Empty DwgVertex array if topology has most probably changed (costly)
    if (numPoints != currentNVertices || numFaces != currentNFaces)
        {
        // First remove all faces - you can't remove vertices until face list is empty. (TR# 122046).
        for (pIterator = acMesh->vertexIterator(); !pIterator->done(); pIterator->step())
            {
            AcDbObjectId            vertexId = pIterator->objectId ();
            AcDbEntityPointer       acEntity (vertexId, AcDb::kForWrite);

            if (Acad::eOk == acEntity.openStatus() && acEntity->isKindOf(AcDbFaceRecord::desc()))
                acEntity->erase();
            }
        delete pIterator;

        // Once faces have been removed, remove vertices.
        for (pIterator = acMesh->vertexIterator(); !pIterator->done(); pIterator->step())
            {
            AcDbObjectId            vertexId = pIterator->objectId ();
            AcDbEntityPointer       acEntity (vertexId, AcDb::kForWrite);

            if (Acad::eOk == acEntity.openStatus())            
                acEntity->erase();
            }
        delete pIterator;

        AcDbObjectId    objectId = acMesh->objectId ();
        if (objectId.isValid())
            {
            /*----------------------------------------------------------------------------------------------
            For an existing polyface mesh entity, even after we have erased all existing vertices and faces,
            RealDWG still does not allow us to add a new vertex.  Closing the object does not flush the object
            either.  So we workaround this problem by handing the object ID over to a new entity.
            ----------------------------------------------------------------------------------------------*/
            AcDbPolyFaceMesh*       newMesh = new AcDbPolyFaceMesh ();
            newMesh->setPropertiesFrom (acMesh);

            Acad::ErrorStatus       es = acMesh->handOverTo (newMesh, true, true);
            if (Acad::eObjectToBeDeleted != es)
                {
                DIAGNOSTIC_PRINTF ("Failed to re-open polyface %I64d, [%ls]\n", context.ElementIdFromObject(acMesh), acadErrorStatusText(es));
                return  CantOpenObject;
                }
            acMesh = newMesh;
            // leave the old object deletion to the caller
            }
        else
            {
            // this is a new polyface entity, but acMesh->appendVertext prerequisite database residency:
            context.AddEntityToCurrentBlock (acMesh, inElement.IsValid() ? inElement.GetElementCP()->ehdr.uniqueId : 0);
            }
        
        for each (DPoint3d point in pointVector)
            {
            AcDbPolyFaceMeshVertex* acVertex = new AcDbPolyFaceMeshVertex();

            acVertex->setPosition (RealDwgUtil::GePoint3dFromDPoint3d(point));
            acVertex->setPropertiesFrom (acMesh);

            Acad::ErrorStatus   es = acMesh->appendVertex (acVertex);
            if (Acad::eOk == es)
                {
                acVertex->close ();
                }
            else
                {
                delete acVertex;
                status = ErrorOpeningVertex;
                DIAGNOSTIC_PRINTF ("Failed to append vertex for polyface %I64d, [%ls]\n", context.ElementIdFromObject(acMesh), acadErrorStatusText(es));

                // can't leave an invalid mesh open - TFS 747403
                if (acMesh->objectId().isValid())
                    acMesh->erase ();
                else
                    delete acMesh;
                acMesh = nullptr;
                return  status;
                }
            }

        size_t                  numColorIndices = colorIndices.size ();
        size_t                  numColors = colorArray.size ();
        size_t                  faceCount = 0;

        PolyfaceVisitorPtr      faceVisitor = PolyfaceVisitor::Attach (*dgnPolyface.get(), true);
        faceVisitor->SetNumWrap (1);

        for (faceVisitor->Reset(); faceVisitor->AdvanceToNextFace(); faceCount++)
            {
            AcDbFaceRecord*     acFace = new AcDbFaceRecord();

            acFace->setPropertiesFrom (acMesh);
            for (UInt32 i = 0; i < faceVisitor->NumEdgesThisFace(); i++)
                {
                // DWG uses 0-based face corner index and 1-based face vertex index:
                Adesk::Int16    acIndex = (Adesk::Int16)faceVisitor->ClientPointIndex().at(i) + 1;
                Acad::ErrorStatus es = acFace->setVertexAt ((Adesk::Int16)i, acIndex);
                if (!faceVisitor->Visible().at(i))
                    acFace->makeEdgeInvisibleAt ((Adesk::Int16)i);
                }

            if (bHasColor && faceCount < numColorIndices && colorIndices[faceCount] < (ptrdiff_t)numColors)
                acFace->setColorIndex ((Adesk::UInt16)colorArray[colorIndices[faceCount] - 1]);

            Acad::ErrorStatus   es = acMesh->appendFaceRecord (acFace);
            if (Acad::eOk == es)
                {
                acFace->close ();
                }
            else
                {
                delete acFace;
                status = CantOpenObject;
                DIAGNOSTIC_PRINTF ("Failed to append face for polyface %I64d, [%ls]\n", context.ElementIdFromObject(acMesh), acadErrorStatusText(es));

                // can't leave an invalid mesh open - TFS 747403
                if (acMesh->objectId().isValid())
                    acMesh->erase ();
                else
                    delete acMesh;
                acMesh = nullptr;
                return  status;
                }

            context.ReportProgress ();
            }
        }
    else
        {
        // Otherwise, modify each DwgVertex individually if its corresponding entry in the polyface arrays is different
        size_t          numColors = colorArray.size ();
        size_t          numColorIndices = colorIndices.size ();
        size_t          faceCount = 0, pointCount = 0;

        PolyfaceVisitorPtr      faceVisitor = PolyfaceVisitor::Attach (*dgnPolyface.get(), true);
        faceVisitor->SetNumWrap (1);
        faceVisitor->Reset ();
        faceVisitor->AdvanceToNextFace();

        for (pIterator = acMesh->vertexIterator(); !pIterator->done(); pIterator->step())
            {
            AcDbObjectId            vertexId = pIterator->objectId ();
            AcDbEntityPointer       acEntity (vertexId, AcDb::kForWrite);

            AcDbPolyFaceMeshVertex* acVertex;
            AcDbFaceRecord*         acFace;
            if (Acad::eOk != acEntity.openStatus())
                {
                status = ErrorOpeningVertex;
                break;
                }
            else if (NULL != (acVertex = AcDbPolyFaceMeshVertex::cast(acEntity.object())))
                {
                acVertex->setPosition (RealDwgUtil::GePoint3dFromDPoint3d(pointVector[pointCount]));

                pointCount++;
                }
            else if (NULL != (acFace = AcDbFaceRecord::cast (acEntity.object())))
                {
                // DWG uses 0-based face corner index and 1-based face vertex index:
                for (UInt32 i = 0; i < faceVisitor->NumEdgesThisFace(); i++)
                    {
                    Adesk::Int16    acIndex = (Adesk::Int16)faceVisitor->ClientPointIndex().at(i) + 1;
                    acFace->setVertexAt ((Adesk::Int16)i, acIndex);
                    faceVisitor->Visible().at(i) ? acFace->makeEdgeVisibleAt((Adesk::UInt16)i) : acFace->makeEdgeInvisibleAt((Adesk::UInt16)i);
                    }

                if (bHasColor && faceCount < numColorIndices && colorIndices[faceCount] < (ptrdiff_t)numColors)
                    acFace->setColorIndex ((Adesk::UInt16)colorArray[colorIndices[faceCount] - 1]);
                else
                    acFace->setPropertiesFrom (acMesh);

                faceVisitor->AdvanceToNextFace();
                faceCount++;

                context.ReportProgress ();
                }
            }
        delete pIterator;
        }

    return status;
    }

};  // ToDwgExtMeshHeader



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SetAcDbPolyfaceMeshFromElement (AcDbPolyFaceMesh*& acPolyface, ElementHandleCR inElement)
    {
    bvector<PolyfaceHeaderPtr>  meshArray;
    IFacetOptionsPtr            facetOptions = IFacetOptions::Create ();
    facetOptions->SetMaxPerFace (4);
    facetOptions->SetChordTolerance (0.0);
    facetOptions->SetAngleTolerance (this->GetSettings().GetPolyfaceAngleTolerance());
    
    BentleyStatus   status = IMeshQuery::ElementToApproximateFacets (inElement, meshArray, facetOptions.get());

    if (SUCCESS == status && meshArray.size() > 0)
        {
        ToDwgExtMeshHeader  meshToDwg;
        size_t              count = 0;

        for each (PolyfaceHeaderPtr dgnPolyface in meshArray)
            {
            // clapse mesh on flat surface
            dgnPolyface->MarkInvisibleEdges (0.5 * msGeomConst_radiansPerDegree);

            ElementHandle       currElem;
            AcDbPolyFaceMesh*   currMesh = NULL;
            if (0 == count)
                {
                // use the source element id on the first polyface mesh entity:
                currElem = ElementHandle (inElement);
                currMesh = acPolyface;
                }
            else
                {
                currMesh = new AcDbPolyFaceMesh ();
                }

            if (RealDwgSuccess == meshToDwg.SetAcDbPolyfaceMeshFromMeshElement(currMesh, dgnPolyface, currElem, *this))
                {
                if (count > 0)
                    {
                    this->UpdateEntityPropertiesFromElement (currMesh, inElement);

                    if (!currMesh->objectId().isValid())
                        this->AddEntityToCurrentBlock (currMesh, 0);

                    if (currMesh != acPolyface && currMesh->objectId().isValid())
                        currMesh->close ();
                    }
                else if (acPolyface != currMesh)
                    {
                    // the input object may have handed the ID to a new object:
                    acPolyface = currMesh;
                    }
                }
            else
                {
                DIAGNOSTIC_PRINTF ("Failed converting mesh from cone element ID=%I64d\n", inElement.GetElementId());
                if (count > 0)
                    delete currMesh;
                }
            count++;
            }
        }

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/12
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SetAcDbPolygonMeshFromBSplineSurface (AcDbPolygonMesh* acMesh, ElementHandleCR inElement)
    {
    MSBsplineSurface        surface;
    // untrimmed, uniform, nonrational B-spline tensor product mesh
    if (BSISUCCESS != BSplineSurfaceHandler::SurfaceFromElement(surface, inElement))
        return  WrongMstnElementType;

    // rule out nonuniform, rational, trimmed B-spline surfaces or those with too many poles
    if (   0 != surface.uParams.numKnots
        || 0 != surface.vParams.numKnots
        || surface.rational
        || 0 != surface.numBounds
        || MAX_Int16Value < surface.vParams.numPoles
        || MAX_Int16Value < surface.uParams.numPoles)
        {
        bspsurf_freeSurface (&surface);
        return CantCreateMeshFromSurface;   // goto newFromElement and import as AcisData
        }

    // surface points logically grouped into row-order matrix:
    //  #points per row = uPoles, meshN
    //  #rows = vPoles, meshM
    int                 order = surface.uParams.order;

    // set flags
    Adesk::Boolean      mClosed = (0 != surface.vParams.closed) ? Adesk::kTrue : Adesk::kFalse;
    Adesk::Boolean      nClosed = (0 != surface.uParams.closed) ? Adesk::kTrue : Adesk::kFalse;
    AcDb::PolyMeshType  meshType = AcDb::kSimpleMesh;

    int                 meshNDensity = 0;
    int                 meshMDensity = 0;

    // bilinear, biquadratic, bicubic B-spline surface
    if (order == surface.vParams.order && order <= 4)
        {
        if (2 == order)
            {
            meshType = AcDb::kSimpleMesh;
            meshNDensity = meshMDensity = 0;
            }
        else
            {
            meshType = (order == 3) ? AcDb::kQuadSurfaceMesh : AcDb::kCubicSurfaceMesh;
            meshMDensity = meshNDensity = 6;
            if (!mClosed)
                meshMDensity++;
            if (!nClosed)
                meshNDensity++;
            }
        }
    else if (surface.uParams.numPoles == order && surface.vParams.numPoles == surface.vParams.order)
        {
        // Bezier tensor product surface
        meshType = AcDb::kBezierSurfaceMesh;
        meshNDensity = meshMDensity = 7;
        }
    else
        {
        // rule out other order surfaces
        bspsurf_freeSurface (&surface);
        return CantCreateMeshFromSurface;
        }

    // set poles and points (we have already ruled out short overflow)
    int     meshM  = surface.vParams.numPoles;
    int     meshN  = surface.uParams.numPoles;

    BlockedVector<DPoint3d> pointArray;
    pointArray.Append (surface.poles, surface.vParams.numPoles * surface.uParams.numPoles);

    bspsurf_freeSurface (&surface);

    ToDwgExtMeshHeader  meshToDwg;
    return meshToDwg.SetAcDbPolygonMeshFromData (acMesh, pointArray, meshM, meshN, 0, 0, mClosed, nClosed, meshType, inElement, *this);
    }

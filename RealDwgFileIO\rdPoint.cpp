/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdPoint.cpp $
|
|  $Copyright: (c) 2018 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp


/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtPoint : public ToDgnExtension
{

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDbPoint*          acPoint = AcDbPoint::cast (acObject);
    DSegment3d          lineSeg;
    double              thickness = acPoint->thickness ();

    RealDwgUtil::DPoint3dFromGePoint3d (lineSeg.point[0], acPoint->position());
    context.GetTransformToDGN().Multiply (&lineSeg.point[0], &lineSeg.point[0], 1);
    context.ValidatePoints (lineSeg.point, 1);
    lineSeg.point[1] = lineSeg.point[0];

    if (acPoint->layerId() == acdbSymUtil()->layerDefpointsId (context.GetDatabase()))
        {
        BentleyStatus   status;
        if (BSISUCCESS != (status = LineHandler::CreateLineElement (outElement, NULL, lineSeg, context.GetThreeD(), *context.GetModel())))
            return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);
        }
    else
        {
        RotMatrix       rotMatrix;
        Transform       extrusionTransform;

        rotMatrix.InitFromAxisAndRotationAngle (2, acPoint->ecsRotation());

        if (NULL != RealDwgUtil::GetExtrusionTransform (extrusionTransform, acPoint->normal(), 0.0))
            {
            RotMatrix   extrusionMatrix;
            extrusionTransform.GetMatrix (extrusionMatrix);
            rotMatrix.InitProduct (extrusionMatrix, rotMatrix);
            }

        WString         pointBlockName;
        ElementId       blockHandle = this->GetOrCreatePointSharedCellDefinition (pointBlockName, thickness, acPoint->normal(), context);

        SharedCellHandler::CreateSharedCellElement (outElement, NULL, pointBlockName.GetWCharCP(),
                                        &lineSeg.point[0], &rotMatrix, NULL, context.GetThreeD(), *context.GetModel());
        
        SharedCellHandler::SetDefinitionID (outElement, blockHandle);
        
        SCOverride      overrides;
        memset (&overrides, 0, sizeof(overrides));
        overrides.color     = true;
        overrides.weight    = true;

        SharedCellHandler::SetSharedCellOverrides (outElement, &overrides);
        SharedCellHandler::CreateSharedCellComplete (outElement);
        }
    context.ElementHeaderFromEntity (outElement, acPoint);
    MSElementP elem = outElement.GetElementP();
    elem->hdr.dhdr.props.b.invisible = (AcDb::kInvisible == acPoint->visibility()) || (1 == context.GetDatabase()->pdmode());

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId       GetOrCreatePointSharedCellDefinition (WStringR pointBlockName, double thickness, const AcGeVector3d& normal, ConvertToDgnContextR context) const
    {
    ElementId   blockHandle = context.GetPointDisplayBlockId ();

    if (fabs(thickness) > TOLERANCE_ZeroSize)
        {
        WString         suffix;
        suffix.Sprintf (L"%lc%.12g", CHAR_PointCellThicknessSuffix, thickness);

        pointBlockName = WString(StringConstants::PointBlockName) + suffix;

        // try to find this thickness specific point shared cell def:
        DgnFileP        dgnFile = context.GetFile ();
        ElementRefP     sharedCelldef = NULL;
        AnonymousSharedCellDefCollection    scdefs(*dgnFile);
        for each (sharedCelldef in scdefs)
            {
            EditElementHandle   scdefEeh (sharedCelldef);
            WChar               cellName[MAX_CELLNAME_LENGTH] = { 0 };

            if (BSISUCCESS == CellUtil::ExtractName(cellName, sizeof(cellName), scdefEeh) && 0 == pointBlockName.CompareToI(cellName))
                return  sharedCelldef->GetElementId ();
            }

        // create a new point shared cell that displays this new thickness
        if (NULL != (sharedCelldef = dgnFile->FindByElementId(blockHandle, true)))
            return  this->CreatePointSharedCellDefinition (sharedCelldef, pointBlockName, thickness, normal, context);
        }

    pointBlockName.assign (StringConstants::PointBlockName);

    return  blockHandle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId       CreatePointSharedCellDefinition (ElementRefP existingDef, WStringCR name, double thickness, const AcGeVector3d& normal, ConvertToDgnContextR context) const
    {
    // create a new thickness specific point shared cell definition
    DgnModelR               dictionaryModel = context.GetFile()->GetDictionaryModel ();
    EditElementHandle       oldDef (existingDef, &dictionaryModel);
    EditElementHandle       newDef, newChild;

    SharedCellDefHandler::CreateSharedCellDefElement (newDef, name.GetWCharCP(), context.GetThreeD(), dictionaryModel);
    SharedCellDefHandler::SetAnonymous (newDef, true);
    SharedCellDefHandler::SetPointCell (newDef, false);

    // copy all children and add thickness to them
    for (ChildEditElemIter child(oldDef, ExposeChildrenReason::Count); child.IsValid(); child = child.ToNext())
        {
        newChild.Duplicate (child);

        // the extrusion direction of a child element always goes by shared cell's normal, so set it to {0, 0, 1} - TFS# 128314.
        context.ApplyThickness (newChild, thickness, AcGeVector3d::kZAxis, false);

        newChild.GetElementP()->ehdr.uniqueId = 0;

        SharedCellDefHandler::AddChildElement (newDef, newChild);

        newChild.Invalidate ();
        }

    SharedCellDefHandler::AddChildComplete (newDef);

    context.LoadElementIntoCache (newDef);
    context.GetFile()->AddSharedCellDefinition ((PersistentElementRefP)newDef.GetElementRef());

    return  newDef.GetElementId ();
    }

};      // ToDgnExtPoint


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          05/14
+===============+===============+===============+===============+===============+======*/
class           PointSharedCellToAcDbPoint
{
private:
        ElementHandleCP     m_sharedCell;
        WString             m_cellName;

public:
        PointSharedCellToAcDbPoint (ElementHandleCR sharedCell, WCharCP name)
            {
            m_sharedCell = &sharedCell;
            m_cellName.assign (name);
            }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/14
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDwg (AcDbObjectP& acObject, AcDbObjectP existingObject, ConvertFromDgnContextR context)
    {
    acObject = context.InstantiateOrUseExistingObject (existingObject, AcDbPoint::desc());

    AcDbPoint*  acPoint = AcDbPoint::cast (acObject);
    if (NULL == acPoint)
        return  NullObject;

    RotMatrix   matrix;
    DPoint3d    origin;
    if (BSISUCCESS != CellUtil::ExtractOrigin(origin, *m_sharedCell) || BSISUCCESS != CellUtil::ExtractRotation(matrix, *m_sharedCell))
        return  CantExtractPoints;

    // ignore z-coordinate per setting
    if (context.GetSettings().IsZeroZCoordinateEnforced())
        origin.z = 0.0;
    context.GetTransformFromDGN().Multiply (origin);

    // get normal
    DVec3d      zAxis;
    matrix.GetColumn (zAxis, 2);
    zAxis.Normalize ();

    // roundtrip thickness from cell name
    double      thickness = 0.0;
    WString     suffix = m_cellName.substr (wcslen(StringConstants::PointBlockName));
    if (!suffix.empty())
        {
        WCharCP     startAt = suffix.GetWCharCP() + 1;
        if (NULL != startAt && 0 != startAt[0])
            {
            WCharP  endAt = NULL;
            thickness = wcstod (startAt, &endAt);
            }
        }

    acPoint->setPosition (RealDwgUtil::GePoint3dFromDPoint3d(origin));
    acPoint->setNormal (RealDwgUtil::GeVector3dFromDVec3d(zAxis));
    acPoint->setThickness (thickness);

    return  RealDwgSuccess;
    }

};      // PointSharedCellToPoint


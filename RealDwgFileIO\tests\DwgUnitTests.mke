#*--------------------------------------------------------------------------------------+
#
#     $Source: mstn/mdlapps/RealDwgFileIO/tests/DwgUnitTests.mke $
#
#  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
#
#--------------------------------------------------------------------------------------*/

ExampleHostDir = $(SrcRoot)PowerPlatform\MstnPlatform\mstn\mdlapps\RealDwgFileIO\ExampleHost/
#PolicyFile = $(ExampleHostDir)DwgExampleHost.mki

consoleAppUnicode = 1

appName             = DwgUnitTests
baseDir             = $(_MakeFilePath)
SubpartLibs         = $(BuildContext)SubParts/Libs/
ContextDeliveryDir  = $(BuildContext)Delivery/
binDest             = $(OutputRootDir)build/DwgUnitTests/
o                   = $(binDest)
productDir          = $(OutputRootDir)Product/DwgUnitTests/
%if !defined (RealDwgVersion)
RealDwgVersion      = 2023
%endif

%include mdl.mki

dirToSearch=$(ExampleHostDir)
%include cincapnd.mki

always:
    !~@mkdir $(o)

MultiCompileDepends = $(_MakeFileSpec) $(baseDir)DwgUnitTestsPCH.h $(baseDir)DwgFileOpen.h

#----------------------------------------------------------------------
# Precompile header
#----------------------------------------------------------------------

PchCompiland        = $(baseDir)DwgUnitTestsPCH.cpp
PchOutputDir        = $(o)
PchArgumentsDepends = $(MultiCompileDepends)
PchExtraOptions     + -I$(ExampleHostDir) -DUNICODE -D_UNICODE
%include $(SharedMki)PreCompileHeader.mki

#----------------------------------------------------------------------
# Sources
#----------------------------------------------------------------------

%include MultiCppCompileRule.mki

$(o)DgnPlatformHosts$(oext)         : $(ExampleHostDir)DgnPlatformHosts.cpp $(ExampleHostDir)DgnPlatformHosts.h ${MultiCompileDepends}

$(o)DwgFileOpen$(oext)				: $(baseDir)DwgFileOpen.cpp $(ExampleHostDir)DwgExampleHost.h ${MultiCompileDepends}

$(o)DwgFileOpenParameterized$(oext)	: $(baseDir)DwgFileOpenParameterized.cpp $(ExampleHostDir)DwgExampleHost.h ${MultiCompileDepends}

$(o)DwgUnitTests$(oext)             : $(baseDir)$(appName).cpp $(ExampleHostDir)DwgExampleHost.h ${MultiCompileDepends}

%include MultiCppCompileGo.mki
appObjects =% $(MultiCompileObjectList)

EXE_DEST            = $(binDest)
EXE_NAME            = $(appName)
EXE_OBJS            = $(appObjects)
EXE_TMP_DIR         = $(o)
EXE_SYMB_DEST       = $(o)
EXE_NO_SIGN         = 1

always:
    !~@mkdir $(EXE_DEST)

LINKER_LIBRARIES    =   $(SubpartLibs)Bentley.lib                           \
                        $(SubpartLibs)BentleyAllocator.lib                  \
                        $(SubpartLibs)DgnPlatform.lib                       \
						$(SubpartLibs)BentleyGeom.lib                          \
                        $(SubpartLibs)DwgDgnIO$(RealDwgVersion).lib         \
                        $(ContextDeliveryDir)DwgExampleHost.lib				\
						$(SubpartLibs)gtest.lib								\
						$(SubpartLibs)BeXml.lib								\
						advapi32.lib

LINKER_LIBRARIES_DELAYLOADED =\
%if RealDwgVersion >= 2017
                        $(SubpartLibs)AcPal.lib                             \
%endif
                        $(SubpartLibs)PSolidCore.lib                        \
                        $(SubpartLibs)PSolidAcisInterop.lib                 \
                        $(SubpartLibs)RasterCore.lib

LinkWarningsToErrorsOptions + -Ignore:4204  # Ignoring LNK4204 as it is trying to link objects.pdb (ideally we should remove the linkning of this pdb from compilation while consuming parts as LKGs)

%include $(SharedMki)linktool.mki

$(ContextDeliveryDir)$(appName).exe : $(binDest)$(appName).exe
    $(LinkFirstDepToFirstTarget)

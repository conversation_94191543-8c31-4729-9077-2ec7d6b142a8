/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgInternal.h $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once

#define UNICODE

#define     _AFXDLL
#include    <afxole.h>

#include    <vector>
#include    <map>

// needed for fields:
#include    <oaidl.h>

// this is needed to allow the AcDbObjectPointer class to be used like this: AcDbFieldPointer pFieldPointer (pFieldObj);
#define DBOBJPTR_EXPOSE_PTR_REF 1

#pragma warning(disable:4100)
#pragma warning(disable:4239)

#include    <realdwg\base\adesk.h>
#include    <realdwg\base\dbmain.h>
#include    <realdwg\base\dbapserv.h>
#include    <realdwg\base\rxevent.h>
#include    <realdwg\base\rxregsvc.h>
#include    <realdwg\base\dbsymtb.h>
#include    <realdwg\base\dbsymutl.h>
#include    <realdwg\base\dbobjptr.h>
#include    <realdwg\base\dbobjptr2.h>
#include    <realdwg\base\acstring.h>
#include    <realdwg\base\AcDbLMgr.h>
#include    <realdwg\base\AdCharFmt.h>
#include    <realdwg\base\dbdimptref.h>
#include    <realdwg\base\dbents.h>
#include    <realdwg\base\dblead.h>
#include    <realdwg\base\dbgroup.h>
#include    <realdwg\base\dbhatch.h>
#include    <realdwg\base\dbdimassoc.h>
#include    <realdwg\base\dbray.h>
#include    <realdwg\base\dbxline.h>
#include    <realdwg\base\dbmline.h>
#include    <realdwg\base\dbmstyle.h>
#include    <realdwg\base\dbcurve.h>
#include    <realdwg\base\dbelipse.h>
#include    <realdwg\base\dbwipe.h>
#include    <realdwg\base\dbidmap.h>
#include    <realdwg\base\dbindex.h>
#include    <realdwg\base\dbxrecrd.h>
#include    <realdwg\base\dbdictdflt.h>
#include    <realdwg\base\dbspfilt.h>
#include    <realdwg\base\dbspline.h>
#include    <realdwg\base\dbole.h>
#include    <realdwg\base\dbsymutl.h>
#include    <realdwg\base\acappvar.h>
#include    <realdwg\base\dbapserv.h>
#include    <realdwg\base\acgi.h>
#include    <realdwg\base\acutil.h>
#include    <realdwg\base\acutmem.h>
#include    <realdwg\base\imgvars.h>
#include    <realdwg\base\dbfield.h>
#include    <realdwg\base\acfield.h>
#include    <realdwg\base\acestext.h>
#include    <realdwg\base\dbfcf.h>
#include    <realdwg\base\dbmaterial.h>
#include    <realdwg\base\dblight.h>
#include    <realdwg\base\dbsun.h>
#include    <realdwg\base\dbsubd.h>
#include    <realdwg\base\dbmleader.h>
#include    <realdwg\base\dbmleaderstyle.h>
#include    <realdwg\base\dbunderlaydef.h>
#include    <realdwg\base\dbunderlayref.h>
#include    <realdwg\base\dbunderlayhost.h>
#include    <realdwg\base\dbtable.h>
#include    <realdwg\base\dbdynblk.h>
#include    <realdwg\base\idver.h>
#include    <realdwg\base\dbdate.h>
#if RealDwgVersion >= 2017
#include    <realdwg\base\dbbody.h>
#include    <realdwg\base\acdbimageutils.h>
#endif

// General geometry module
#include    <realdwg\base\gemat3d.h>
#include    <realdwg\base\genurb2d.h>
#include    <realdwg\base\genurb3d.h>
#include    <realdwg\base\geell2d.h>
#include    <realdwg\base\geell3d.h>
#include    <realdwg\base\geextc2d.h>
#include    <realdwg\base\geextc3d.h>
#include    <realdwg\base\gecomp3d.h>
#include    <realdwg\base\geextsf.h>
#include    <realdwg\base\gekvec.h>
#include    <realdwg\base\gecone.h>
#include    <realdwg\base\gecylndr.h>
#include    <realdwg\base\genurbsf.h>
#include    <realdwg\base\gesphere.h>
#include    <realdwg\base\getorus.h>
#include    <realdwg\base\gexbndsf.h>

// ASM/ACIS based solid & surfaces:
#include    <realdwg\base\dbsol3d.h>
#include    <realdwg\base\dbregion.h>
#include    <realdwg\base\dbsweptsurf.h>
#include    <realdwg\base\dbextrudedsurf.h>
#include    <realdwg\base\dbrevolvedsurf.h>
#include    <realdwg\base\dbloftedsurf.h>
#include    <realdwg\base\dbplanesurf.h>

#include    <realdwg\base\dbplotsetval.h>
#include    <realdwg\base\dbplhldr.h>
#include    <realdwg\base\dbTableStyle.h>
#include    <realdwg\base\dbvisualstyle.h>
#include    <realdwg\base\sorttab.h>
#include    <realdwg\base\dbxutil.h>

// object extensions
#include    <realdwg\base\dbannotationscale.h>
#include    <realdwg\base\dbAnnotativeObjectPE.h>
#include    <realdwg\base\dbObjectContextInterface.h>
#include    <realdwg\base\dbObjectContextCollection.h>
#include    <realdwg\base\dbObjectContextManager.h>

// Atil include files
#include    <FileSpecifier.h>
#include    <FileReadDescriptor.h>
#include    <ATILException.h>
#include    <BmpFormatCodec.h>
#include    <JFIFFormatCodec.h>
#include    <PngFormatCodec.h>
#include    <TiffFormatCodec.h>

// BRep module
#include    <realdwg\brep\brgbl.h>
#include    <realdwg\brep\brbrep.h>
#include    <realdwg\brep\brcplx.h>
#include    <realdwg\brep\bredge.h>
#include    <realdwg\brep\brloop.h>
#include    <realdwg\brep\brface.h>
#include    <realdwg\brep\brshell.h>
#include    <realdwg\brep\brbctrav.h>
#include    <realdwg\brep\brcstrav.h>
#include    <realdwg\brep\brbetrav.h>
#include    <realdwg\brep\brbftrav.h>
#include    <realdwg\brep\brfltrav.h>
#include    <realdwg\brep\brletrav.h>
#include    <realdwg\brep\brlvtrav.h>
#include    <realdwg\brep\brsftrav.h>
#include    <realdwg\brep\brvtx.h>

// PointCloud
#include    <realdwg\base\AcDbPointCloudApi.h>
#include    <realdwg\base\AcDbPointCloudEx.h>

// GCS
#include    <realdwg\base\dbGeoData.h>
#include    <realdwg\base\AcDbGeoCoordinateSystem.h>

#ifdef OVERRIDE_AECVIEWCONFIG
// AEC access - specifically provided to us by Autodesk (i.e. not in standard RealDWG delivery)
#include    <realdwg\base\AecVerChk.h>
#endif

// WinUser.h #defines GetClassName to GetClassNameW under UNICODE, resulting in a link error of not finding TextField::GetClassNameW:
#ifdef  GetClassName
#undef  GetClassName
#endif

#pragma warning(default:4100)
#pragma warning(default:4239)

#include    <Mstn/RealDWG/rDwgAPI.h>
#include    "rDwgFileIO.h"
#include    "rDwgFileIds.h"
#include    "RTextDbx\RText.h"

#include    <DgnPlatform\DgnPlatformApi.h>
#include    <DgnPlatform\Tcb\tcb.r.h>

// This is needed while working with VC10.  When we go to newer compiler, use the for : syntax and get rid of all this.
#include    <boost/foreach.hpp>

#define FOR_EACH(VAR,COL) BOOST_FOREACH(VAR,COL)

#define BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(C)  namespace boost {                     template<> struct range_const_iterator<C>   {typedef C::const_iterator type;}; \
                                                                                              template<> struct range_mutable_iterator<C> {typedef C::const_iterator type;}; }
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::ECN::ECCustomAttributeInstanceIterable)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::ECN::ECPropertyIterable)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::ECN::ECClassContainer)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::ECN::ECValuesCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::DgnFile::AllElementsCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::RootModelList)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::DgnFileArray)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::PersistentElementRefList)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::DgnModel::ElementsCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::DgnAttachmentArray)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::LevelCache)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::FileLevelCache)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::DgnAttachmentLevelCache)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::LsMap)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::LsResourceFileMap)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::ViewGroupCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::XAttributeCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::DgnHistory::ElementQueryResultsForModel)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::DgnHistory::ElementQueryResultsForFile)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::DgnElementInstanceIterable)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::InstanceCountIterable)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::TagSetCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::TextStyleCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::FileSignatureCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::ModelSignatureCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::ApplicableSignaturesCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::DimStyleCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::RasterElementTypesCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::RasterExtensionElementCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::RasterReferenceElementCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::MultilineStyleCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::RasterFrameElementCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::NamedSharedCellDefCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::AnonymousSharedCellDefCollection)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::ParagraphRange)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::LineRange)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::DgnPlatform::RunRange)

#ifndef ENTITY_TAG
typedef     int                     ENTITY_TAG;
#endif

typedef     AcDbSmartObjectPointer<AcDbLayout>                  AcDbLayoutPointer;
typedef     AcDbSmartObjectPointer<AcDbViewport>                AcDbViewportPointer;
typedef     AcDbSmartObjectPointer<AcDbLayout>                  AcDbLayoutPointer;
typedef     AcDbSmartObjectPointer<AcDbBlockTableRecord>        AcDbSmartBlockTableRecordPointer;
typedef     AcDbSmartObjectPointer<AcDbBlockReference>          AcDbBlockReferencePointer;
typedef     AcDbSmartObjectPointer<AcDbColor>                   AcDbColorPointer;
typedef     AcDbSmartObjectPointer<AcDbField>                   AcDbFieldPointer;
typedef     AcDbSmartObjectPointer<AcDbRasterImageDef>          AcDbRasterImageDefPointer;
typedef     AcDbSmartObjectPointer<AcDbPolyline>                AcDbPolylinePointer;
typedef     AcDbSmartObjectPointer<AcDbLeader>                  AcDbLeaderPointer;
typedef     AcDbSmartObjectPointer<AcDbMText>                   AcDbMTextPointer;
typedef     AcDbSmartObjectPointer<AcDbAttribute>               AcDbAttributePointer;
typedef     AcDbSmartObjectPointer<AcDbAttributeDefinition>     AcDbAttributeDefinitionPointer;
typedef     AcDbSmartObjectPointer<AcDbMlineStyle>              AcDbMlineStylePointer;
typedef     AcDbSmartObjectPointer<AcDbMLeaderStyle>            AcDbMLeaderStylePointer;
typedef     AcDbSmartObjectPointer<AcDbXrecord>                 AcDbXrecordPointer;
typedef     AcDbSmartObjectPointer<AcDbDictionary>              AcDbSmartDictionaryPointer;
typedef     AcDbSmartObjectPointer<AcDbPdfDefinition>           AcDbPdfDefinitionPointer;
typedef     AcDbSmartObjectPointer<AcDbDwfDefinition>           AcDbDwfDefinitionPointer;
typedef     AcDbSmartObjectPointer<AcDbDgnDefinition>           AcDbDgnDefinitionPointer;
typedef     AcDbSmartObjectPointer<AcDbVisualStyle>             AcDbVisualStylePointer;
typedef     AcDbSmartObjectPointer<AcDbTableStyle>              AcDbTableStylePointer;

BEGIN_BENTLEY_NAMESPACE

namespace RealDwg {
typedef     std::pair<AcDbHandle, AcDbSoftPointerId>    HandlePair;
typedef     bvector<HandlePair>                         HandlePairsArray;
typedef     bvector<AcDbObjectId>                       ObjectIdArray;

typedef     bvector<int>                                IntArray;
typedef     IntArray*                                   IntArrayP;
typedef     IntArray&                                   IntArrayR;
}   // Ends RealDWG namespace

END_BENTLEY_NAMESPACE

using namespace Bentley::DgnPlatform;
#if RealDwgVersion >= 2017
using namespace Autodesk::AutoCAD::PAL::FontUtils;
#endif

#include    <Mstn\RealDwg\rDwgConvertContext.h>
#include    <Mstn\RealDwg\DwgPlatformHost.h>
#include    "rDwgFileHolder.h"

typedef void (*PFDwgObjectRegisterFunction)
(
AcRxObject      *pDwgDgnExtension
);


BEGIN_BENTLEY_NAMESPACE

namespace RealDwg {


typedef std::map  <WString, WString>                    T_StringMap;
typedef std::map  <AcDbDatabase*, COleDocument*>        T_OleDocumentMap;
typedef std::vector <Bentley::WString>                  T_MessageList;

/*=================================================================================**//**
* This class is required by RealDwg.
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class RealDwgHostApp : public ::AcDbHostApplicationServices
{
private:
    mutable T_StringMap         m_localToUrl;
    mutable T_OleDocumentMap    m_databaseToOleDocument;        // each AcDbDatabase has an associated OLE Document if there are AcDbOle2Frame objects.
    mutable T_MessageList*      m_messageList;                  // array of strings collected for an entity
    mutable WString             m_displayString;
    DgnFileP                    m_file;
    AcDbProgressMeterP          m_doNothingProgressMeter;
    bool                        m_isRunningOn64BitOS;
    COleClientItem*             m_oleClientItem;                // used in converting our OLE dgnStore element to DWG.
    static RealDwgHostApp*      s_instance;

    // private constructor.
    RealDwgHostApp();

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   09/08
+---------------+---------------+---------------+---------------+---------------+------*/
~RealDwgHostApp();

/*---------------------------------------------------------------------------------**//**
* overridden methods from ObjectARX-supplied class
+---------------+---------------+---------------+---------------+---------------+------*/
virtual Acad::ErrorStatus       findFile (WCHAR* pcFullPathOut, int nBufferLength, const WCHAR* pcFilename, AcDbDatabase* pDb = NULL, AcDbHostApplicationServices::FindFileHint hint = kDefault) override;
virtual Acad::ErrorStatus       getRoamableRootFolder (const WCHAR*& folder) override;
virtual Acad::ErrorStatus       getLocalRootFolder (const WCHAR*& folder) override;
virtual Adesk::Boolean          isURL (const WCHAR* pszURL) const override;
virtual const WCHAR*            getAlternateFontName() const override;
virtual AcDbProgressMeterP      newProgressMeter() override;
virtual bool                    getPassword (const ACHAR* dwgName, PasswordOptions options, wchar_t* password, const size_t bufSize) override;
virtual void                    fatalError (const ACHAR* format, ...) override;
virtual void                    alert (const ACHAR* message) const override;
virtual void                    displayChar (ACHAR c) const override;
virtual void                    displayString (const ACHAR* string, int length) const override;
virtual const ACHAR*            getEnv (const ACHAR* var) override;
#if RealDwgVersion < 2017
virtual LCID                    getRegistryProductLCID () override;
#endif
#if RealDwgVersion < 2013
virtual const ACHAR*            getRegistryProductRootKey () override;
#else
virtual const ACHAR*            getMachineRegistryProductRootKey () override;
virtual const ACHAR*            getUserRegistryProductRootKey () override;
#endif
virtual const ACHAR*            product() override;
virtual Adesk::Boolean          readyToDisplayMessages() override;
virtual Acad::ErrorStatus       getNewOleClientItem (COleClientItem*& pItem) override;
virtual Acad::ErrorStatus       serializeOleItem(COleClientItem* pItem, CArchive*) override;

virtual Adesk::Boolean          isRemoteFile (const ACHAR* pszLocalFile, ACHAR* pszURL, size_t urlLen) const override;
virtual Acad::ErrorStatus       getRemoteFile (const ACHAR* pszURL, ACHAR* pszLocalFile, size_t localLen, Adesk::Boolean bIgnoreCache = Adesk::kFalse) const override;
virtual bool                    notifyCorruptDrawingFoundOnOpen (AcDbObjectId id, Acad::ErrorStatus es);


/*---------------------------------------------------------------------------------**//**
* Additional methods that we need
+---------------+---------------+---------------+---------------+---------------+------*/
        void                    SetDgnFile (DgnFileP file);
        AcDbProgressMeterP      NewProgressMeter (DgnFileP dgnFile, double startFraction, double endFraction);
        void                    SetProgressMeterLimits (AcDbProgressMeterP progressMeter, double lowFraction, double highFraction, int approxNumObjects);
        void                    SetDoNothingProgressMeter ();
        Acad::ErrorStatus       Substitute32BitDBX (WCHAR* pcFullPathOut, int nBufferLength, const WCHAR* pcFilename);
        void                    SaveOrSendStringToMessageCenter (const ACHAR* stringIn, size_t lengthIn) const;

        void                    SetOleClientItem (COleClientItem* clientItem);

        void                    SetWorkingDatabase (AcDbDatabase* database);    // this just calls setWorkingDatabase, but helps with debugging.
        COleDocument*           GetWorkingOleDocument ();                       // this is the COleDocument that corresponds to the workingDatabase.
        void                    DatabaseDeleted (AcDbDatabase* database);
        void                    StartMessageListCollection (T_MessageList* newMessageList) const { m_messageList = newMessageList; }
        void                    EndMessageListCollection () const { m_messageList = NULL; }
        void                    DisplayAndFlushMessage (const ACHAR* messageIn=NULL) const;

static  RealDwgHostApp&         Instance();
static  bool                    SearchAcadPaths(WCHAR* pcFullPathOut, int nBufferLength, const WCHAR* pcFilename);
static  bool                    FindFontFile(WCHAR* fontOut, int nLength, const WCHAR* fontIn, AcDbHostApplicationServices::FindFileHint hint);
};

/*=================================================================================**//**
* This class does the work for the publicly-visible MstnInterface class.
* @bsiclass                                                     Barry.Bentley   01/09
+===============+===============+===============+===============+===============+======*/
class               MstnInterfaceHelper
{
private:
    MstnInterfaceHelper();      //singleton

    bvector<IRealDwgDgnAdapter*>            m_adapters;
    IDwgConversionSettings*                 m_dwgConversionSettings;
    static MstnInterfaceHelper*             s_instance;

public:

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
static MstnInterfaceHelper&     Instance();

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                            AddAdapter (IRealDwgDgnAdapter *adapter);

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   01/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                            LoadDgnProtocolExtensions();

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   07/09
+---------------+---------------+---------------+---------------+---------------+------*/
bool                            HasProtocolExtension (ElementHandleCR eh);

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   03/10
+---------------+---------------+---------------+---------------+---------------+------*/
IDwgConversionSettings&         GetSettings ();

};

#define REALDWG_ExceptionBase (-20000)
/*=================================================================================**//**
* Base class for exceptions that we throw while reading/writing DWG files.
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class   RealDwgException
{
public:
enum RealDwgExceptionCode
    {
    NoException                 = 0,
    };

private:
RealDwgExceptionCode        m_code;
AcString                    m_message;

public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgException (RealDwgExceptionCode code) { m_code = code; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgException (RealDwgExceptionCode code, const ACHAR* message)
    {
    m_code = code;
    m_message.assign (message);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgExceptionCode        GetCode () { return m_code; }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
const ACHAR*            ErrorMessage () { return m_message.kwszPtr(); }


};

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class   FileHolderException : public RealDwgException
{
public:
FileHolderException (int status) : RealDwgException ((RealDwgExceptionCode) status) {}
};

/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class   DiscardInvalidEntityException : public RealDwgException
{
public:
DiscardInvalidEntityException (const ACHAR* errorMsg) : RealDwgException ((RealDwgExceptionCode) 0, errorMsg) {}

};

}   // Ends RealDWG namespace

END_BENTLEY_NAMESPACE

ADD_BENTLEY_TYPEDEFS1 (RealDwg, RealDwgHostApp, RealDwgHostApp, class);

/*=================================================================================**//**
* This is a helper class intended merely to access private data via a forward desclaration 
* in classes in DgnPlatform such as DimensionStyle, PersistentElementRef, etc.
* @bsiclass                                                     Don.Fu          02/11
+===============+===============+===============+===============+===============+======*/
struct Bentley::DgnPlatform::DwgConversionDataAccessor
    {
    public: static DimStyleSettings&            GetDimStyleSettingsR  (DimensionStyleR  dimStyle) { return dimStyle.m_data; }
    public: static DimStyleSettings const&      GetDimStyleSettingsCR (DimensionStyleCR dimStyle) { return dimStyle.m_data; }
    public: static DimStyleExtensions&          GetDimStyleExtensionsR (DimensionStyleR dimStyle) { return dimStyle.m_extensions; }
    public: static DimStyleExtensions const&    GetDimStyleExtensionsCR (DimensionStyleCR dimStyle) { return dimStyle.m_extensions; }
    public: static void                         ChangeElementId (PersistentElementRefP elem, ElementId newId) { elem->ChangeID(newId); }
    };




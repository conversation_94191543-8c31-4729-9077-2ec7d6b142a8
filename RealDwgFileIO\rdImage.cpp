/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdImage.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include <RasterCore\RasterCoreAPI.h>
#include <DgnPlatform\RasterElementListener.h>
#include <RasterCore\rmenvvar.h>

USING_NAMESPACE_BENTLEY_DGNPLATFORM
USING_NAMESPACE_RASTER

typedef struct tagPercentFromSignedRange
    {
    char    range;
    double  percent;
    } PercentFromSignedRange;

typedef struct tagPercentFromRange
    {
    unsigned char   range;
    double          percent;
    } PercentFromRange;


/* this table applies to convert from range [0, 255] to percentage [0.0, 100.0] round to nearest .5%*/
static PercentFromRange LookUpPercentFromRange [] =
    {{  0,0.0},
    {  1,0.5}, {  2,0.5}, {  3,1.0}, {  4,1.5}, {  5,2.0},
    {  6,2.5}, {  7,2.5}, {  8,3.0}, {  9,3.5}, { 10,4.0},
    { 11,4.0}, { 12,4.5}, { 13,5.0}, { 14,5.5}, { 15,6.0},
    { 16,6.0}, { 17,6.5}, { 18,7.0}, { 19,7.5}, { 20,8.0},
    { 21,8.0}, { 22,8.5}, { 23,9.0}, { 24,9.5}, { 25,9.5},
    { 26,10.0},{ 27,10.5},{ 28,11.0},{ 29,11.5},{ 30,11.5},
    { 31,12.0},{ 32,12.5},{ 33,13.0},{ 34,13.0},{ 35,13.5},
    { 36,14.0},{ 37,14.5},{ 38,15.0},{ 39,15.0},{ 40,15.5},
    { 41,16.0},{ 42,16.5},{ 43,16.5},{ 44,17.0},{ 45,17.5},
    { 46,18.0},{ 47,18.5},{ 48,18.5},{ 49,19.0},{ 50,19.5},
    { 51,20.0},{ 52,20.5},{ 53,20.5},{ 54,21.0},{ 55,21.5},
    { 56,22.0},{ 57,22.0},{ 58,22.5},{ 59,23.0},{ 60,23.5},
    { 61,24.0},{ 62,24.0},{ 63,24.5},{ 64,25.0},{ 65,25.5},
    { 66,25.5},{ 67,26.0},{ 68,26.5},{ 69,27.0},{ 70,27.5},
    { 71,27.5},{ 72,28.0},{ 73,28.5},{ 74,29.0},{ 75,29.0},
    { 76,29.5},{ 77,30.0},{ 78,30.5},{ 79,31.0},{ 80,31.0},
    { 81,31.5},{ 82,32.0},{ 83,32.5},{ 84,33.0},{ 85,33.0},
    { 86,33.5},{ 87,34.0},{ 88,34.5},{ 89,34.5},{ 90,35.0},
    { 91,35.5},{ 92,36.0},{ 93,36.5},{ 94,36.5},{ 95,37.0},
    { 96,37.5},{ 97,38.0},{ 98,38.0},{ 99,38.5},{100,39.0},
    {101,39.5},{102,40.0},{103,40.0},{104,40.5},{105,41.0},
    {106,41.5},{107,41.5},{108,42.0},{109,42.5},{110,43.0},
    {111,43.5},{112,43.5},{113,44.0},{114,44.5},{115,45.0},
    {116,45.5},{117,45.5},{118,46.0},{119,46.5},{120,47.0},
    {121,47.0},{122,47.5},{123,48.0},{124,48.5},{125,49.0},
    {126,49.0},{127,49.5},{128,50.0},{129,50.5},{130,50.5},
    {131,51.0},{132,51.5},{133,52.0},{134,52.5},{135,52.5},
    {136,53.0},{137,53.5},{138,54.0},{139,54.0},{140,54.5},
    {141,55.0},{142,55.5},{143,56.0},{144,56.0},{145,56.5},
    {146,57.0},{147,57.5},{148,58.0},{149,58.0},{150,58.5},
    {151,59.0},{152,59.5},{153,59.5},{154,60.0},{155,60.5},
    {156,61.0},{157,61.5},{158,61.5},{159,62.0},{160,62.5},
    {161,63.0},{162,63.0},{163,63.5},{164,64.0},{165,64.5},
    {166,65.0},{167,65.0},{168,65.5},{169,66.0},{170,66.5},
    {171,66.5},{172,67.0},{173,67.5},{174,68.0},{175,68.5},
    {176,68.5},{177,69.0},{178,69.5},{179,70.0},{180,70.5},
    {181,70.5},{182,71.0},{183,71.5},{184,72.0},{185,72.0},
    {186,72.5},{187,73.0},{188,73.5},{189,74.0},{190,74.0},
    {191,74.5},{192,75.0},{193,75.5},{194,75.5},{195,76.0},
    {196,76.5},{197,77.0},{198,77.5},{199,77.5},{200,78.0},
    {201,78.5},{202,79.0},{203,79.0},{204,79.5},{205,80.0},
    {206,80.5},{207,81.0},{208,81.0},{209,81.5},{210,82.0},
    {211,82.5},{212,83.0},{213,83.0},{214,83.5},{215,84.0},
    {216,84.5},{217,84.5},{218,85.0},{219,85.5},{220,86.0},
    {221,86.5},{222,86.5},{223,87.0},{224,87.5},{225,88.0},
    {226,88.0},{227,88.5},{228,89.0},{229,89.5},{230,90.0},
    {231,90.0},{232,90.5},{233,91.0},{234,91.5},{235,91.5},
    {236,92.0},{237,92.5},{238,93.0},{239,93.5},{240,93.5},
    {241,94.0},{242,94.5},{243,95.0},{244,95.5},{245,95.5},
    {246,96.0},{247,96.5},{248,97.0},{249,97.0},{250,97.5},
    {251,98.0},{252,98.5},{253,99.0},{254,99.0},{255,100.0}
    };


/* this table applies to convert from range [-128, 127] to percentage [-100.0, 100.0] (round to 1%) */
static PercentFromSignedRange LookUpPercentFromSignedRange [] =
    {
        {-128,-100.0},{-127,-99.0},{-126,-99.0},{-125,-98.0},
        {-124,-97.0}, {-123,-96.0},{-122,-95.0},{-121,-95.0},{-120,-94.0},
        {-119,-93.0}, {-118,-92.0},{-117,-92.0},{-116,-91.0},{-115,-90.0},
        {-114,-89.0}, {-113,-88.0},{-112,-88.0},{-111,-87.0},{-110,-86.0},
        {-109,-85.0}, {-108,-84.0},{-107,-84.0},{-106,-83.0},{-105,-82.0},
        {-104,-81.0}, {-103,-81.0},{-102,-80.0},{-101,-79.0},{-100,-78.0},
        {-99,-77.0},  {-98,-77.0}, {-97,-76.0}, {-96,-75.0}, {-95,-74.0},
        {-94,-74.0},  {-93,-73.0}, {-92,-72.0}, {-91,-71.0}, {-90,-70.0},
        {-89,-70.0},  {-88,-69.0}, {-87,-68.0}, {-86,-67.0}, {-85,-67.0},
        {-84,-66.0},  {-83,-65.0}, {-82,-64.0}, {-81,-63.0}, {-80,-63.0},
        {-79,-62.0},  {-78,-61.0}, {-77,-60.0}, {-76,-59.0}, {-75,-59.0},
        {-74,-58.0},  {-73,-57.0}, {-72,-56.0}, {-71,-56.0}, {-70,-55.0},
        {-69,-54.0},  {-68,-53.0}, {-67,-52.0}, {-66,-52.0}, {-65,-51.0},
        {-64,-50.0},  {-63,-49.0}, {-62,-49.0}, {-61,-48.0}, {-60,-47.0},
        {-59,-46.0},  {-58,-45.0}, {-57,-45.0}, {-56,-44.0}, {-55,-43.0},
        {-54,-42.0},  {-53,-42.0}, {-52,-41.0}, {-51,-40.0}, {-50,-39.0},
        {-49,-38.0},  {-48,-38.0}, {-47,-37.0}, {-46,-36.0}, {-45,-35.0},
        {-44,-34.0},  {-43,-34.0}, {-42,-33.0}, {-41,-32.0}, {-40,-31.0},
        {-39,-31.0},  {-38,-30.0}, {-37,-29.0}, {-36,-28.0}, {-35,-27.0},
        {-34,-27.0},  {-33,-26.0}, {-32,-25.0}, {-31,-24.0}, {-30,-24.0},
        {-29,-23.0},  {-28,-22.0}, {-27,-21.0}, {-26,-20.0}, {-25,-20.0},
        {-24,-19.0},  {-23,-18.0}, {-22,-17.0}, {-21,-17.0}, {-20,-16.0},
        {-19,-15.0},  {-18,-14.0}, {-17,-13.0}, {-16,-13.0}, {-15,-12.0},
        {-14,-11.0},  {-13,-10.0}, {-12,-9.0},  {-11,-9.0},  {-10,-8.0},
        { -9,-7.0},   { -8,-6.0},  { -7,-6.0},  { -6,-5.0},  { -5,-4.0},
        { -4,-3.0},   { -3,-2.0},  { -2,-2.0},  { -1,-1.0},  {  0,0.0},
        {  1,1.0},    {  2,2.0},   {  3,3.0},   {  4,4.0},   {  5,4.0},
        {  6,5.0},    {  7,6.0},   {  8,7.0},   {  9,7.0},   { 10,8.0},
        { 11,9.0},    { 12,10.0},   { 13,11.0},  { 14,11.0},  { 15,12.0},
        { 16,13.0},  { 17,14.0},   { 18,15.0},  { 19,15.0},  { 20,16.0},
        { 21,17.0},  { 22,18.0},   { 23,19.0},  { 24,19.0},  { 25,20.0},
        { 26,21.0},  { 27,22.0},   { 28,22.0},  { 29,23.0},  { 30,24.0},
        { 31,25.0},  { 32,26.0},   { 33,26.0},  { 34,27.0},  { 35,28.0},
        { 36,29.0},  { 37,30.0},   { 38,30.0},  { 39,31.0},  { 40,32.0},
        { 41,33.0},  { 42,33.0},   { 43,34.0},  { 44,35.0},  { 45,36.0},
        { 46,37.0},  { 47,37.0},   { 48,38.0},  { 49,39.0},  { 50,40.0},
        { 51,41.0},  { 52,41.0},   { 53,42.0},  { 54,43.0},  { 55,44.0},
        { 56,44.0},  { 57,45.0},   { 58,46.0},  { 59,47.0},  { 60,48.0},
        { 61,48.0},  { 62,49.0},   { 63,50.0},  { 64,51.0},  { 65,52.0},
        { 66,52.0},  { 67,53.0},   { 68,54.0},  { 69,55.0},  { 70,56.0},
        { 71,56.0},  { 72,57.0},   { 73,58.0},  { 74,59.0},  { 75,59.0},
        { 76,60.0},  { 77,61.0},   { 78,62.0},  { 79,63.0},  { 80,63.0},
        { 81,64.0},  { 82,65.0},   { 83,66.0},  { 84,67.0},  { 85,67.0},
        { 86,68.0},  { 87,69.0},   { 88,70.0},  { 89,70.0},  { 90,71.0},
        { 91,72.0},  { 92,73.0},   { 93,74.0},  { 94,74.0},  { 95,75.0},
        { 96,76.0},  { 97,77.0},   { 98,78.0},  { 99,78.0},  {100,79.0},
        {101,80.0},  {102,81.0},   {103,81.0},  {104,82.0},  {105,83.0},
        {106,84.0},  {107,85.0},   {108,85.0},  {109,86.0},  {110,87.0},
        {111,88.0},  {112,89.0},   {113,89.0},  {114,90.0},  {115,91.0},
        {116,92.0},  {117,93.0},   {118,93.0},  {119,94.0},  {120,95.0},
        {121,96.0},  {122,96.0},   {123,97.0},  {124,98.0},  {125,99.0},
        {126,99.0},  {127,100.0}
    };


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Marc.Bedard                     09/2011
+---------------+---------------+---------------+---------------+---------------+------*/
static double  GetPercentFromRange
(            /*<= percentage [0.0, 100.0] (round to 0.5)*/
int pi_range /*=> range [0, 255]*/
)
    {
    BeAssert ((pi_range>=0) && (pi_range<=255));
    return LookUpPercentFromRange[pi_range].percent;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Marc.Bedard                     09/2011
+---------------+---------------+---------------+---------------+---------------+------*/
static double  GetPercentFromSignedRange
(                  /*<= percentage [-100.0, 100.0] (round to 0.5)*/
int pi_signedRange /*=> range [-128, 127]*/
)
    {
    BeAssert ((pi_signedRange>=-128) && (pi_signedRange<=127));
    return LookUpPercentFromSignedRange[pi_signedRange + 128].percent;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                   Marc.Bedard  04/2009
+---------------+---------------+---------------+---------------+---------------+------*/
class RasterSourceUnloadGuard
    {
    public:
    RasterSourceUnloadGuard(ElementHandleCP type94ElemHandle):m_rasterP(NULL)
        {
        m_rasterP = DgnRasterCollection::GetRastersR(type94ElemHandle->GetModelRef()).FindP(type94ElemHandle->GetElementRef());
        if (m_rasterP!=NULL)
            {
            m_isReadOnly = m_rasterP->IsReadOnly();
            if(m_rasterP->GetRasterSourceCP() == NULL || m_rasterP->GetRasterSourceCP()->GetRasterFile().GetStatus() == RASTERFILE_STATUS_Closed)
                return;

            // Close source file on disk and free corresponding loader
            DgnPlatform::Raster::DgnRasterUtilities::CloseRasterSource(*m_rasterP, false);
            }
        }
    ~RasterSourceUnloadGuard()
        {
        if (m_rasterP!=NULL)
            DgnPlatform::Raster::DgnRasterUtilities::ReloadRasterSource(*m_rasterP, false);
        }
    bool IsReadOnly() const   {return m_isReadOnly;}
    private:
        DgnRasterP m_rasterP;
        bool       m_isReadOnly;
    };

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand  02/2004
+---------------+---------------+---------------+---------------+---------------+------*/
static void     GetForegroundColor (RgbColorDef* pForegroundColor, AcDbEntity* pEntity, DwgSymbologyData* pDwgSymbologyData)
    {
    AcCmColor     trueColor = pEntity->color ();
    RgbColorDef   colorDef;

    switch (trueColor.colorMethod())
        {
        case AcCmEntityColor::kByLayer:
            {
            AcDbLayerTableRecordPointer pLayer (pEntity->layerId(), AcDb::kForRead);
            if (Acad::eOk == pLayer.openStatus())
                colorDef    = *pDwgSymbologyData->GetColor (pLayer->color().colorIndex());
            else
                colorDef    = *pDwgSymbologyData->GetColor (pEntity->colorIndex());
            break;
            }
        case AcCmEntityColor::kByColor:
            {
            colorDef.red   = trueColor.red();
            colorDef.green = trueColor.green();
            colorDef.blue  = trueColor.blue();
            break;
            }

        case AcCmEntityColor::kByBlock:
            {
            BeAssert (false && L"ByBlock color not supported in getForegroundColor!");
            // fall through
            }

        default:
            colorDef = *pDwgSymbologyData->GetColor (pEntity->colorIndex());
            break;
        }

    pForegroundColor->red   = colorDef.red;
    pForegroundColor->green = colorDef.green;
    pForegroundColor->blue  = colorDef.blue;
    }


/*=================================================================================**//**
* @bsiclass                                                     Barry.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExtRasterAttachment: public ToDgnExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    //Raster image are not supported in shared cell in MicroStation
    if (context.IsInSharedCellCreation() && !context.GetSettings().SaveRasterToSharedCell())
        {
        return DwgObjectUnsupported;
        }

    AcDbRasterImage*    pImage = AcDbRasterImage::cast (acObject);
    MSElementDescrP     pDescr = NULL;
    MSElementDescrP     pDescrOut = NULL;
    RgbColorDef         backColor, foreColor;

    AcDbObjectId        idImageDef = pImage->imageDefId();
    if( idImageDef.isNull())
        return EntityError;

    AcDbRasterImageDef* pImageDef = NULL;
    if (Acad::eOk != acdbOpenObject (pImageDef, idImageDef, AcDb::kForWrite))
        return EntityError;

    //Check if file can be found
    bool            fileInfoIsValid(false);
    RasterFileQuickInfo tRasterQuickInfo;
    memset(&tRasterQuickInfo, 0, sizeof(tRasterQuickInfo));

    
    //We want to resolve file name first
    WString filename(pImageDef->sourceFileName());
    DgnDocumentMonikerPtr pMoniker = DgnDocumentMoniker::CreateFromRawData(filename.c_str(),filename.c_str(),NULL,IRasterAttachmentQuery::GetSearchPath(context.GetModel()).c_str(),false,NULL);
    WString sourceFileName = pMoniker->ResolveFileName ();

    if (SUCCESS == mdlRaster_fileInfoMinimalGetExt (&tRasterQuickInfo, sourceFileName.c_str(), NULL))
        fileInfoIsValid=true;

    // N.B. Get Autocad compatibility invert WITHOUT checking the WORK MODE.
    int                 autocadInvertCompatibility  = false;
    ImageColorMode      iColorMode                  = ImageColorMode::RGB;
    if (SUCCESS != mdlRaster_fileColorModeAndDwgInvertCompatibilityGet (&iColorMode, &autocadInvertCompatibility, sourceFileName.c_str(), context.GetModel(), false))
        {
        /* Force the color mode to be RGB if file color mode cannot be retrieved */
        iColorMode = ImageColorMode::RGB;
        autocadInvertCompatibility = false;     // invert is OFF by default
        }

    /* Find image entry in the dictionary */
    AcString    searchKey = GetDictionarySearchKey (pImageDef, pImage->database ());

    ExtractBackgroundForegroundColors (&backColor, &foreColor, pImage, true, context);

    UInt16              displayProps(0);
    bool                isSetDisplayOpt(TO_BOOL(pImage->isSetDisplayOpt(AcDbRasterImage::kShow)));
    //Ignore loaded status (and consider it loaded) if file cannot be found (!fileinfoValid)
    bool                isLoaded(fileInfoIsValid ? TO_BOOL(pImageDef->isLoaded()) : true);
    bool                WasRasterImageDefUnloadedByUs(context.WasRasterImageDefUnloadedByUs(idImageDef));
    if (isSetDisplayOpt)
        {
        bool beforeReload = isLoaded;

        // try to load the raster if not already loaded, TR#292908
        if (!isLoaded && !WasRasterImageDefUnloadedByUs && !pImageDef->isEmbedded() && !(isLoaded = this->TryLoad(pImageDef, sourceFileName, filename)))
            isLoaded = TO_BOOL(pImageDef->isLoaded());

        if (sourceFileName.EqualsI(filename.c_str()))
            {
            if (beforeReload || WasRasterImageDefUnloadedByUs)
                displayProps |= AcDbRasterImage::kShow;
            }
        else
            {
            if (isLoaded || WasRasterImageDefUnloadedByUs)
                displayProps |= AcDbRasterImage::kShow;
            }
        }

    if (pImage->isSetDisplayOpt(AcDbRasterImage::kShowUnAligned))
        displayProps |= AcDbRasterImage::kShowUnAligned;

    if (pImage->isSetDisplayOpt(AcDbRasterImage::kClip))
        displayProps |= AcDbRasterImage::kClip;

    if (pImage->isSetDisplayOpt(AcDbRasterImage::kTransparent))
        displayProps |= AcDbRasterImage::kTransparent;

    // Get georeference in Raster Manager format
    DPoint3d            origin, uVector, vVector;
    Dpoint2d            imageSize;
    GetRasterReferenceGeoref(origin, uVector, vVector, sourceFileName.c_str(), imageSize, pImage, context.GetModel(), fileInfoIsValid, tRasterQuickInfo);

    /* Get the clipping polygon from the clipboundary */
    std::vector<DPoint2d> clipPolygon;
    int                 numClipPoints(0);
    if (IsDWGClipValid(pImage,&imageSize,*context.GetModel()))
        {
        AcGePoint2dArray    geClipBoundary;

        geClipBoundary = pImage->clipBoundary ();
        numClipPoints = geClipBoundary.length();

        /* allocate a Dpoint2d array to transform the AcGePoint2dArray */
        if (numClipPoints>0)
            clipPolygon.resize(numClipPoints);

        for (int i(0); i < numClipPoints; i++)
            {
            RealDwgUtil::DPoint2dFromGePoint2d (clipPolygon[i], geClipBoundary[i]);
            //DWG Clip polygon are in pixels but they have their 0,0 coordinate at the center of the pixel
            //Raster attachment uses lower left corner of the pixel for its 0,0 coordinate, we have to shift coord. by 0.5 pixel
            clipPolygon[i].x += 0.5;
            clipPolygon[i].y += 0.5;
            }
        }
    else
        {
        numClipPoints=0;
        }

    if (pImageDef!= NULL && pImageDef->isLoaded())
        {
        //TR #271568: Image def must be unload otherwise we have a sharing violation as imageDef keeps a ReadOnly (not sharable) handle on file.
        pImageDef->unload();
        context.AddUnloadedRasterImageDef (idImageDef);
        }

    pImageDef->close ();

    char searchKeyString[MAXFILELENGTH];
    BeStringUtilities::WCharToCurrentLocaleChar(searchKeyString,searchKey,MAXFILELENGTH);

    DVec3d              translation;
    context.GetTransformToDGN().getTranslation (&translation);

    unsigned short      ClipType= numClipPoints>2 ? AD_IMAGE_CLIPBOUND_POLYGON : AD_IMAGE_CLIPBOUND_RECT;
    unsigned short      IsClipped = pImage->isClipped() ? AD_IMAGE_CLIPPED : AD_IMAGE_NOT_CLIPPED;
    bool                IsClipInverted = pImage->isClipInverted ()==Adesk::kTrue ? true : false;
    Transform           tMatrix = Transform::FromIdentity();
    DPoint2d            extentInUOR = DPoint2d::From(0, 0);
    double              scaleToDgn = context.GetScaleToDGN ();

    /* Setup the transformation matrix */
    tMatrix.ScaleMatrixColumns (scaleToDgn, scaleToDgn, scaleToDgn);
    tMatrix.SetTranslation (translation);

    /* Set the image's origin */
    tMatrix.Multiply (origin);

    // Compute u and v vector 3d
    DPoint3d            uVectorScaled;
    DPoint3d            vVectorScaled;
    uVectorScaled.Scale (uVector, scaleToDgn);
    vVectorScaled.Scale (vVector, scaleToDgn);

    /* Compute the image's uor extent */
    extentInUOR.x = uVectorScaled.Magnitude() * imageSize.x;
    extentInUOR.y = vVectorScaled.Magnitude() * imageSize.y;

    DPoint3d            nVector;
    nVector.CrossProduct (uVectorScaled, vVectorScaled);

    Transform           matrix;
    matrix.InitFromOriginAndVectors (origin, (DVec3dCR)uVectorScaled, (DVec3dCR)vVectorScaled, (DVec3dCR)nVector);

    BentleyStatus status;

    if (BSISUCCESS != (status = IRasterAttachmentEdit::CreateRasterAttachment(outElement, NULL,*pMoniker, matrix, extentInUOR,*context.GetModel())))
        return RealDwgFileIO::ConvertMdlStatusToRealDwgStatus (status);

    IRasterAttachmentEdit* pIRasterAttachmentEdit = dynamic_cast <IRasterAttachmentEdit*> (&outElement.GetHandler ()); 
    if (NULL == pIRasterAttachmentEdit)
        return EntityError;


    pIRasterAttachmentEdit->SetTransform(outElement,matrix,&extentInUOR,true);
    pIRasterAttachmentEdit->SetScanningResolution(outElement,tRasterQuickInfo.scanningResolution);

    WChar logicalName [MAXLOGICALNAMELENGTH];
    logicalName [0] = L'\0';
    /* If the search key isn't part of the filename, set a logical name */
    if (searchKeyString && !wcsstr(pImageDef->sourceFileName(), searchKey))
        {
        wcsncpy (logicalName, searchKey, MAXLOGICALNAMELENGTH);
        logicalName [MAXLOGICALNAMELENGTH-1] = L'\0';
        }

    pIRasterAttachmentEdit->SetLogicalName(outElement,logicalName);
    pIRasterAttachmentEdit->SetLogicalBackgroundIndex(outElement,autocadInvertCompatibility?1:0);
    pIRasterAttachmentEdit->SetGeoreferencePriority(outElement,GeoreferencePriority_Attachment);
    bool isMonochrome ((ImageColorMode::Monochrome == iColorMode) || (ImageColorMode::Palette2 == iColorMode));
    /* Adjust contrast / brightness */
    if (!isMonochrome)
        {
        pIRasterAttachmentEdit->SetContrast(outElement,(short)GetSignedRangeFromPercent (pImage->contrast() * 2 - 100.0));
        pIRasterAttachmentEdit->SetBrightness(outElement,(short)GetSignedRangeFromPercent (pImage->brightness() * 2 - 100.0));
        }
    pIRasterAttachmentEdit->SetInvertState(outElement,TO_BOOL(autocadInvertCompatibility));
    UInt32 index(0);
    if (SUCCESS == IRasterAttachmentQuery::ColorIndexFromRgbInModel(index,*context.GetModel(),foreColor))
        pIRasterAttachmentEdit->SetForegroundColor(outElement,index);
    if (SUCCESS == IRasterAttachmentQuery::ColorIndexFromRgbInModel(index,*context.GetModel(),backColor))
        pIRasterAttachmentEdit->SetBackgroundColor(outElement,index);
    pIRasterAttachmentEdit->SetTransparencyState(outElement,TO_BOOL((displayProps & AD_IMAGE_DISPPROPS_TRANSON) >> AD_IMAGE_DISPPROPS_TRANSON_OFFSET));

    unsigned char   fadeRange = GetRangeFromPercent (pImage->fade());
    pIRasterAttachmentEdit->SetImageTransparencyLevel (outElement, fadeRange);

    /* Special case: if image has fading, then force the transparent flag to be true,   */
    /* otherwise the fading on the image will not be applied                            */
    /* This will have no effect on the visual aspect of the image back in Autocad,      */
    /* since these images don't have built-in transparency and applying or not the      */
    /* transparency in Autocad has no effect                                            */
    if (fadeRange > 0 && !isMonochrome)
        {
        pIRasterAttachmentEdit->SetTransparencyState(outElement,true);
        }
    // For a monochorme raster, AutoCAD always use background transparency with value of 100%
    // create a transparency list with the background color 100% transparent
    if(isMonochrome)
        {
        RasterTransparentColorsCollectionPtr pTransparentColor(RasterTransparentColorsCollection::Create());
        pTransparentColor->Init(TransparentColorType_CubeDef);
        RgbColorDef transparentColorRgba;
        transparentColorRgba.red = backColor.red;
        transparentColorRgba.green = backColor.green;
        transparentColorRgba.blue = backColor.blue;
        pTransparentColor->AddRgbTransparentColor(transparentColorRgba,transparentColorRgba,255);// 100 % transp
        pIRasterAttachmentEdit->SetTransparentColors(outElement,*pTransparentColor);
        }
    pIRasterAttachmentEdit->SetClipState(outElement,TO_BOOL((displayProps & AD_IMAGE_DISPPROPS_USECLIP) >> AD_IMAGE_DISPPROPS_USECLIP_OFFSET));
    /* if image display is ON in Autocad, then display image in */
    /* all views in MicroStation */
    bool bDisplay = TO_BOOL((displayProps & AD_IMAGE_DISPPROPS_SHOWIMAGE) >> AD_IMAGE_DISPPROPS_SHOWIMAGE_OFFSET);
    for (int view=0; view<8; view++)
        pIRasterAttachmentEdit->SetViewState(outElement,view,bDisplay);

    this->SetDisplayPriorityPlane (outElement, pIRasterAttachmentEdit);

    pIRasterAttachmentEdit->SetPrintState(outElement,true);
    RasterClipPropertiesPtr pClipProperties(RasterClipProperties::Create());
    FillRasterClipPropertiesFromDwg(*pClipProperties,&imageSize,numClipPoints,&clipPolygon[0],IsClipped,ClipType,*context.GetModel(),IsClipInverted,true);
    pIRasterAttachmentEdit->SetClipProperties(outElement,*pClipProperties);
    pIRasterAttachmentEdit->SetOpenReadWrite(outElement,false);// TR 151110 Always open DWG image in RO

    context.ElementHeaderFromEntity (outElement, AcDbEntity::cast (acObject));

    //TFS#140512; will post process element to add legacy raster elements (type 90/92) so pre-vancouver MicroStation will be able to load this new raster attachment.
    context.AddPostProcessObject(acObject);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/16
+---------------+---------------+---------------+---------------+---------------+------*/
bool            TryLoad (AcDbRasterImageDef* imageDef, WStringCR resolvedName, WStringCR originalName) const
    {
    /*-----------------------------------------------------------------------------------
    This method gets called because the raster is not loaded and reloading is needed.
    If the resolved the file name used for our own raster API is different than the original,
    we use the resolved name to load the image.  In case of TFS 562486, the resolved name 
    can be overridden by iCS for PDF.  Using resolved name increases the chance of a succesful
    loading.
    -----------------------------------------------------------------------------------*/
    bool    filenameChanged = false;
    if (!resolvedName.EqualsI(originalName.c_str()) && Acad::eOk == imageDef->setSourceFileName(resolvedName.c_str()))
        filenameChanged = true;

    Acad::ErrorStatus   status = imageDef->load ();

    // revert file name so it will look the same when file is saved:
    if (filenameChanged)
        imageDef->setSourceFileName (originalName.c_str());

    return  Acad::eOk == status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ToElementPostProcess (AcDbObjectP acObject, ConvertToDgnContext& context) const
    {
    EditElementHandle   rasterFrameEeh(context.ElementIdFromObject(acObject), context.GetModel());
    if (!rasterFrameEeh.IsValid())
        return  CantAccessMstnElement;

    ElementRefP         elementRef = rasterFrameEeh.GetElementRef();
    if (NULL == elementRef)
        return  CantAccessMstnElement;

    //TFS#140512;Add legacy raster elements (type 90/92) so pre-vancouver MicroStation will be able to load this new raster attachment.
    //Create and save corresponding type 90 and type 92 element
    StatusInt status = RasterLegacyElement::CreateAndAddType90And92FromRasterFrame(NULL, NULL, rasterFrameEeh, false);

    return  RealDwgFileIO::ConvertMdlStatusToRealDwgStatus(status);
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
virtual   RealDwgStatus   FromElementDeleteObject (AcDbObject* acObject, AcDbObjectIdArray& idsToPurge, ConvertFromDgnContext& context) const override
    {
    AcDbRasterImage*    pImage        = AcDbRasterImage::cast (acObject);

    AcDbObjectIdArray   imageSequence = context.GetModelIndexItem()->GetImageSequence();
    imageSequence.remove (acObject->objectId());
    context.GetModelIndexItem()->SetImageSequence (imageSequence);

    // Add the imagedef to the table to be purged. It will be deleted if no other objects reference it
    AcDbObjectId    imageDefId = pImage->imageDefId ();
    if (!imageDefId.isNull())
        {
        AcDbRasterImageDefPointer pImageDef (imageDefId, AcDb::kForRead);
        int index;
        // Add only if not already in the array, don't add it twice!
        if (Acad::eOk == pImageDef.openStatus() && !idsToPurge.find (imageDefId, index, 0))
            idsToPurge.append (imageDefId);
        }

    // erase the object.
    acObject->erase ();

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/02
+---------------+---------------+---------------+---------------+---------------+------*/
static void     GetBackgroundColor (RgbColorDef* pBackgroundColor, ConvertToDgnContextR context)
    {
    int                     red(0), green(0), blue(0);

    AcDbBlockTableRecordPointer pBlock (context.GetCurrentBlockId(), AcDb::kForRead);
    if (Acad::eOk == pBlock.openStatus() && pBlock->isLayout())
        {
        if (pBlock->objectId() == acdbSymUtil()->blockModelSpaceId(pBlock->database()))
            context.GetSettings().GetDesignBackgroundColor (red, green, blue);
        else
            context.GetSettings().GetSheetBackgroundColor (red, green, blue);
        }

    pBackgroundColor->red   = red;
    pBackgroundColor->blue  = blue;
    pBackgroundColor->green = green;
    }

/*---------------------------------------------------------------------------------**//**
* Sets the foreground color to white if background is black and entity color is also
* black and vice-versa.
* recorded
* @param        backColor       => active background color
* @param        foreColor       => active foreground color
* @bsimethod                                                    JeanMarcLanglois   05/01
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SynchForeColorWithBackColor (RgbColorDef* backColor, RgbColorDef* foreColor)
    {
    if (ISWHITE (backColor) && ISWHITE (foreColor))
        {
        foreColor->red = foreColor->green = foreColor->blue = 0;
        }
    else if (ISBLACK (backColor) && ISBLACK (foreColor))
        {
        foreColor->red = foreColor->green = foreColor->blue = 255;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    StephanePoulin  1/2006
+---------------+---------------+---------------+---------------+---------------+------*/
void                        GetRasterReferenceGeoref
(
DPoint3dR                   origin,
DPoint3dR                   uVector,
DPoint3dR                   vVector,
WCharCP                     pName,
Dpoint2d&                   imageSize,
AcDbRasterImage*            pImage,
DgnModelP                   model,
const bool                  fileInfoIsValid,
const RasterFileQuickInfo&  tRasterQuickInfo
) const
    {
    AcGePoint3d     geOrigin;
    AcGeVector3d    geUVector, geVVector;

    pImage->getOrientation (geOrigin, geUVector, geVVector);

    if (fileInfoIsValid)
        {
        imageSize = tRasterQuickInfo.extent; //In Pixels
        }
    else
        {
        RealDwgUtil::DPoint2dFromGeVector2d (imageSize, pImage->imageSize());
        }
    RealDwgUtil::DPoint3dFromGePoint3d  (origin,  geOrigin);
    RealDwgUtil::DPoint3dFromGeVector3d (uVector, geUVector);
    RealDwgUtil::DPoint3dFromGeVector3d (vVector, geVVector);

    /* transform U and V to one pixel */
    uVector.Scale(1.0/imageSize.x);
    vVector.Scale(1.0/imageSize.y);


    if (fileInfoIsValid)
        {
        if ((tRasterQuickInfo.fileFormat == IMAGEFILE_TIFFINTGR) || (tRasterQuickInfo.fileFormat == IMAGEFILE_TIFF))
            {
            Transform sloMatrix;
            if (SUCCESS == mdlRaster_getScanLineOrientationMatrix(&sloMatrix, pName, model))
                {
                if (sloMatrix.InverseOf(sloMatrix))
                    {
                    // build a matrix from DWG georef
                    Transform dwgMat, rmMatrix;
                    DPoint3d  nVector = {0.0, 0.0, 1.0};

                    bsiTransform_initFromOriginAndVectors(&dwgMat, &origin, &uVector, &vVector, &nVector);
                    bsiTransform_multiplyTransformTransform(&rmMatrix, &dwgMat, &sloMatrix);

                    uVector.x = rmMatrix.form3d[0][0];
                    uVector.y = rmMatrix.form3d[1][0];
                    uVector.z = rmMatrix.form3d[2][0];
                    vVector.x = rmMatrix.form3d[0][1];
                    vVector.y = rmMatrix.form3d[1][1];
                    vVector.z = rmMatrix.form3d[2][1];
                    origin.x  = rmMatrix.form3d[0][3];
                    origin.y  = rmMatrix.form3d[1][3];
                    origin.z  = rmMatrix.form3d[2][3];
                    }
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand  11/2003
+---------------+---------------+---------------+---------------+---------------+------*/
AcString                    GetDictionarySearchKey
(
AcDbRasterImageDef*         pImageDef,
AcDbDatabase*               pDb
) const
    {
    // Get Dictionary Id, or create one if not present
    AcDbObjectId imageDictId = AcDbRasterImageDef::imageDictionary (pDb);

    // Open the image dictionary.
    AcDbDictionaryPointer   pImageDict (imageDictId, AcDb::kForRead);
    if (Acad::eOk != pImageDict.openStatus())
        return  AcString();

    AcString                imageName;
    Acad::ErrorStatus       status = pImageDict->nameAt (pImageDef->objectId (), imageName);

    return (Acad::eOk == status) ? imageName : AcString();
    }


public:
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Marc.Bedard                     09/2011
+---------------+---------------+---------------+---------------+---------------+------*/
static char     GetSignedRangeFromPercent
(                 /* <= range [-128, 127] */
double pi_percent /* => percentage [-100.0, 100.0] */
)
    {
    int i;

    BeAssert ((pi_percent>=-100.0) && (pi_percent<=100));

    if (pi_percent >= 100)
        return(127);

    if (pi_percent <= -100)
        return(-128);

    for (i=1;i<256;i++ )
        {
        if (LookUpPercentFromSignedRange[i].percent > pi_percent)
            {
            /*The previous range was the one we searching for*/
            return LookUpPercentFromSignedRange[i-1].range;
            }
        }
    return(0);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Marc.Bedard                     09/2011
+---------------+---------------+---------------+---------------+---------------+------*/
static unsigned char GetRangeFromPercent
(                  /* <= range [0, 255] */
double pi_percent  /* => percentage [0.0, 100.0] */
)
    {
    int i;

    BeAssert ((pi_percent>=0.0) && (pi_percent<=100));

    if (pi_percent >= 100)
        return(255);

    if (pi_percent <= 0)
        return(0);

    for (i=1;i<256;i++ )
        {
        if (LookUpPercentFromRange[i].percent > pi_percent)
            {
            // TR 152509 return the percentage which is the closest to the input percentage
            if (LookUpPercentFromRange[i].percent - pi_percent < pi_percent - LookUpPercentFromRange[i-1].percent)
                return LookUpPercentFromRange[i].range;
            else /*The previous range was the one we searching for*/
                return LookUpPercentFromRange[i-1].range;
            }
        }
    return(0);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                  JeanMarcLanglois08/2001
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt FillRasterClipPropertiesFromDwg
(
RasterClipProperties&   clipProperties,
const DPoint2d          *pi_ptImageSize,
const int               pi_iNumClipVertices,
const DPoint2d          *pi_ptClipVertices,
const unsigned short    pi_ujClipStatus,
const unsigned short    pi_ujClipType,
DgnModelR               dgnModel,
const bool              isInverted = false,
const bool              startFromUpperLeft = true
)
    {
    StatusInt   iStatus = ERROR;
    bool        bImageIsClipped(false);
    short       iNumRealClipVertices;
    DPoint3d    rectClipVertices[RasterReference_MAXVERTICIESINCOMPONENT];

    if (pi_ptClipVertices==NULL)
        return ERROR;

    /* Polygonal clipping boundary */
    if ((AD_IMAGE_CLIPBOUND_POLYGON == pi_ujClipType)   &&
        (AD_IMAGE_CLIPPED == pi_ujClipStatus)           &&
        (RasterReference_MAXVERTICIESINCOMPONENT >= pi_iNumClipVertices))
        {
        int                     i;

        /* Adjust the clipping coordinates - in DWG, they are stored in image   */
        /* coordinates from the upper left corner and in DGN they are stored    */
        /* from the lower left corner                                           */
        for (i=0;i<pi_iNumClipVertices;i++)
            {
            if (startFromUpperLeft)
                rectClipVertices[i].y = pi_ptImageSize->y - pi_ptClipVertices[i].y;
            else
                rectClipVertices[i].y = pi_ptClipVertices[i].y;
            rectClipVertices[i].x = pi_ptClipVertices[i].x;
            rectClipVertices[i].z = 0.0;
            }
        bImageIsClipped = true;
        iNumRealClipVertices = pi_iNumClipVertices;
        }
    /* Rectangular clip polygon */
    else if ((AD_IMAGE_CLIPBOUND_RECT == pi_ujClipType) &&
             (AD_IMAGE_CLIPPED == pi_ujClipStatus))

        {
        rectClipVertices[0].x = pi_ptClipVertices[0].x;
        if (startFromUpperLeft)
            rectClipVertices[0].y = pi_ptImageSize->y - pi_ptClipVertices[1].y;
        else
            rectClipVertices[0].y = pi_ptClipVertices[1].y;
        rectClipVertices[0].z = 0.0;


        rectClipVertices[1].x = pi_ptClipVertices[1].x;
        rectClipVertices[1].y = rectClipVertices[0].y;
        rectClipVertices[1].z = 0.0;

        rectClipVertices[2].x = rectClipVertices[1].x;
        if (startFromUpperLeft)
            rectClipVertices[2].y = pi_ptImageSize->y - pi_ptClipVertices[0].y;
        else
            rectClipVertices[2].y = pi_ptClipVertices[0].y;
        rectClipVertices[2].z = 0.0;

        rectClipVertices[3].x = rectClipVertices[0].x;
        rectClipVertices[3].y = rectClipVertices[2].y;
        rectClipVertices[3].z = 0.0;

        rectClipVertices[4].x = rectClipVertices[0].x;
        rectClipVertices[4].y = rectClipVertices[0].y;
        rectClipVertices[4].z = 0.0;

        bImageIsClipped = true;
        iNumRealClipVertices = 5;
        }

    if (bImageIsClipped)
        {
        RasterClipPtr   clipper = RasterClip::Create (&rectClipVertices[0], iNumRealClipVertices, dgnModel);
        if (isInverted)
            {
            // DWG inverted clip only has one inner loop
            RasterClipCollectionPtr maskClips = RasterClipCollection::Create ();
            maskClips->push_back (clipper);
            clipProperties.SetMaskCollection (maskClips);
            }
        else
            {
            clipProperties.SetBoundary (clipper);
            }
        }

    return  iStatus;
    }

/*---------------------------------------------------------------------------------**//**
* Check if DWG clip is supported by rasterlib
* @bsimethod                                                    MarcBedard   11/2005
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     IsDWGClipValid (AcDbRasterImage* pImage, DPoint2dCP pi_ptImageSize, DgnModelR dgnModel)
    {
    bool isvalid(true);
    AcGePoint2dArray    geClipBoundary;

    /* Get the clipping polygon from the clipboundary */
    geClipBoundary = pImage->clipBoundary ();
    int numClipPoints = geClipBoundary.length ();

    //No clip is always valid
    if (numClipPoints==0)
        return true;

    /* allocate a Dpoint2d array to transform the AcGePoint2dArray */
    DPoint2d  *clipPolygon = new DPoint2d[numClipPoints];
    try
        {
        for (int i(0); i < numClipPoints; i++)
            {
            RealDwgUtil::DPoint2dFromGePoint2d (clipPolygon[i], geClipBoundary[i]);
            //DWG Clip polygon are in pixels but they have their 0,0 coordinate at the center of the pixel
            //Raster attachment uses lower left corner of the pixelfor its 0,0 coordinate, we have to shift coord. by 0.5 pixel
            clipPolygon[i].x += 0.5;
            clipPolygon[i].y += 0.5;
            }

        RasterClipPropertiesPtr pClipProperties(RasterClipProperties::Create());
        bvector<DPoint3d> strokedClip;
        pClipProperties->GetBoundary().ComputeStrokePoints(strokedClip);
        unsigned short ClipType= strokedClip.size()>2 ? AD_IMAGE_CLIPBOUND_POLYGON : AD_IMAGE_CLIPBOUND_RECT;
        unsigned short IsClipped = pImage->isClipped() ? AD_IMAGE_CLIPPED : AD_IMAGE_NOT_CLIPPED;
        bool                IsClipInverted = pImage->isClipInverted ()==Adesk::kTrue ? true : false;
        FillRasterClipPropertiesFromDwg(*pClipProperties,pi_ptImageSize,numClipPoints,clipPolygon,IsClipped,ClipType,dgnModel,IsClipInverted,true);

        if (strokedClip.size() > 0)
            {
            ElementHandle clipEh(pClipProperties->GetBoundary().GetClipElement());
            if (SUCCESS == mdlRaster_clipElementValidate(clipEh.GetElementDescrCP(),&dgnModel))
                isvalid =true;
            else
                isvalid = false;
            }
        else
            {
            isvalid=true;
            }
        }

    catch (...)
        {
        isvalid = false;
        }

    delete [] clipPolygon;

    return isvalid;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
static void     ExtractBackgroundForegroundColors (RgbColorDefP backgroundColor, RgbColorDefP foregroundColor, AcDbEntityP acEntity, bool synch, ConvertToDgnContextR context)
    {
    // This method was split from ToElement to be shared with PdfUnderlay code.

    /* Extract the view's background color - this will be used to set the raster's */
    /* background color if is a binary image */
    //*** We init raster background color with the model background color in which the raster belong,
    //    but at display time we will use active model background color
    //    (because in DWGWORKMODE we always use BINARYWORKMODE_Monochrome).
    if (NULL != backgroundColor)
        GetBackgroundColor (backgroundColor, context);
    if (NULL != foregroundColor)
        GetForegroundColor (foregroundColor, acEntity, context.GetFileHolder().GetDwgSymbologyData ());

    /* Switch foreground color to white if black background and black foreground */
    /* and vice-versa */
    if (synch && NULL != backgroundColor && NULL != foregroundColor)
        SynchForeColorWithBackColor (backgroundColor, foregroundColor);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/12
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SetDisplayPriorityPlane (EditElementHandleR outElement, IRasterAttachmentEdit* rasterEditor)
    {
    // This method was split from ToElement to be shared with PdfUnderlay code.

    //Fix TR #190990: In DWG work mode, we want to change display plane only if the MS_RASTER_DWG_UPDATESEQUENCE_MODE env. var. has been set.
    //Otherwise, keep order from file in DisplayPriority_DesignPlane
    int     dwgUpdateSequence = 0;
    WString buffer;
    if ( (SUCCESS == ConfigurationManager::GetVariable(buffer, L"MS_RASTER_DWG_UPDATESEQUENCE_MODE")) && !buffer.empty())
        dwgUpdateSequence = BeStringUtilities::Wtoi (buffer.c_str());
    
    switch(dwgUpdateSequence)
        {
        case 1:
            rasterEditor->SetDisplayPriorityPlane(outElement, DisplayPriority_BackPlane);
            break;
        case 2:
            rasterEditor->SetDisplayPriorityPlane(outElement, DisplayPriority_FrontPlane);
            break;
        default:
            rasterEditor->SetDisplayPriorityPlane(outElement, DisplayPriority_DesignPlane);
            break;
        }
    }
      

};  // ToDgnExtRasterAttachment



# RealDwgFileIO Framework Documentation

## Overview

The RealDwgFileIO framework provides comprehensive support for reading, writing, and converting between DWG/DXF and DGN file formats in Bentley MicroStation. This framework leverages Autodesk's RealDWG library to provide high-fidelity conversion capabilities.

## Framework Architecture

The RealDwgFileIO framework is built around several key architectural components:

### Core Components
- **RealDwgFileIO**: Main file I/O handler class
- **FileHolder**: Manages AutoCAD database and DGN file relationships
- **Conversion Contexts**: Handle bidirectional conversion between formats
- **Symbology Data**: Manages color, line style, and layer mappings
- **Table Indexes**: Provide ID mapping between DWG and DGN entities

### Key Features
- Bidirectional DWG ↔ DGN conversion
- DXF format support
- Comprehensive entity type support
- Symbology preservation
- Reference file handling
- Multi-processing support for large files
- Extensive testing framework

## Documentation Structure

This documentation is organized into the following sections:

0. **[Framework Overview](framework-overview.md)** - Complete framework summary and capabilities
1. **[Architecture Overview](01-architecture.md)** - Framework design and patterns
2. **[Core Components](02-core-components.md)** - Main classes and interfaces
3. **[File I/O System](03-file-io-system.md)** - File handling mechanisms
4. **[Conversion System](04-conversion-system.md)** - Conversion contexts and processes
5. **[Entity Conversion](05-entity-conversion.md)** - Specific entity type conversions
6. **[Symbology Management](06-symbology-management.md)** - Color, line styles, and layers
7. **[Host Integration](07-host-integration.md)** - Platform integration
8. **[Testing Framework](08-testing-framework.md)** - Test cases and validation
9. **[API Reference](09-api-reference.md)** - Detailed API documentation
10. **[Examples](10-examples.md)** - Usage examples and code samples

## Quick Start

For developers new to the framework, start with:
1. [Framework Overview](framework-overview.md) for a complete summary of capabilities
2. [Architecture Overview](01-architecture.md) to understand the overall design
3. [Core Components](02-core-components.md) to learn about key classes
4. [Examples](10-examples.md) to see practical usage

## File Organization

The RealDwgFileIO framework source code is organized as follows:

```
RealDwgFileIO/
├── Core Framework Files
│   ├── rDwgFileIO.h/.cpp          # Main file I/O class
│   ├── rDwgFileHolder.h/.cpp      # Database management
│   ├── rDwgInternal.h             # Internal headers and includes
│   └── realDwgExported.h/.cpp     # Exported functions
├── Conversion Contexts
│   ├── rDwgBaseContext.cpp        # Base conversion context
│   ├── rDwgToDgnContext.cpp       # DWG to DGN conversion
│   ├── rDwgFromDgnContext.cpp     # DGN to DWG conversion
│   └── rDwgConvertEvents.cpp      # Conversion event handling
├── Entity Converters (DWG to DGN)
│   ├── rd*.cpp files              # DWG entity converters
│   └── dgnExtElement.cpp          # Extension element handling
├── Entity Converters (DGN to DWG)
│   └── dgn*.cpp files             # DGN entity converters
├── Symbology and Tables
│   ├── rDwgSymbologyData.cpp      # Color and symbology mapping
│   ├── rDwgTableIndex.cpp         # ID mapping tables
│   └── rd*Convert.cpp files       # Style and table converters
├── Host Integration
│   ├── DwgPlatformHost.cpp        # Platform host implementation
│   ├── filehandler/               # File handler module
│   └── ExampleHost/               # Example host implementations
├── Testing
│   └── tests/                     # Unit tests and validation
└── Build Configuration
    └── realDwgFileIO.mke          # Make file configuration
```

## Dependencies

The framework depends on:
- Autodesk RealDWG library
- Bentley DgnPlatform
- Bentley MstnPlatform
- Standard C++ libraries
- Windows-specific components (OLE, COM)

## Version Information

This documentation covers the current version of the RealDwgFileIO framework as integrated with Bentley MicroStation.

---

For detailed information about specific components, please refer to the individual documentation files listed above.

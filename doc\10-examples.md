# Examples and Usage Patterns

## Overview

This document provides practical examples and common usage patterns for the RealDwgFileIO framework. These examples demonstrate how to perform typical operations and implement custom functionality.

## Basic Usage Examples

### Example 1: Opening and Reading a DWG File

```cpp
#include "rDwgFileIO.h"
#include <DgnPlatform/DgnPlatform.h>

StatusInt OpenAndReadDwgFile(WCharCP fileName)
{
    // Initialize the framework
    RealDwgFileIO::Initialize();
    
    try {
        // Open the DWG file
        DgnFilePtr dgnFile;
        StatusInt status = DgnFile::CreateFromFileName(
            dgnFile, fileName, DgnFileOpenMode::ReadOnly);
        
        if (status != SUCCESS) {
            wprintf(L"Failed to open file: %ls\n", fileName);
            return status;
        }
        
        // Get file information
        DgnFileFormatType format;
        int majorVersion, minorVersion;
        bool is3D;
        dgnFile->GetVersion(&format, &majorVersion, &minorVersion, &is3D);
        
        wprintf(L"File: %ls\n", fileName);
        wprintf(L"Format: %s\n", (format == DgnFileFormatType::DWG) ? L"DWG" : L"DXF");
        wprintf(L"Version: %d.%d\n", majorVersion, minorVersion);
        wprintf(L"3D Model: %s\n", is3D ? L"Yes" : L"No");
        
        // Load the default model
        DgnModelPtr model = dgnFile->LoadRootModelForRead();
        if (!model.IsValid()) {
            wprintf(L"Failed to load default model\n");
            return BSIERROR;
        }
        
        // Count elements by type
        std::map<DgnElementType, int> elementCounts;
        DgnElementIteratorPtr iterator = model->CreateElementIterator();
        
        for (DgnElementPtr element : *iterator) {
            DgnElementType elementType = element->GetElementType();
            elementCounts[elementType]++;
        }
        
        // Print statistics
        wprintf(L"\nElement Statistics:\n");
        for (const auto& pair : elementCounts) {
            wprintf(L"  Type %d: %d elements\n", 
                   static_cast<int>(pair.first), pair.second);
        }
        
        return SUCCESS;
    }
    catch (const std::exception& e) {
        wprintf(L"Exception: %hs\n", e.what());
        return BSIERROR;
    }
}
```

### Example 2: Converting DWG to DGN

```cpp
StatusInt ConvertDwgToDgn(WCharCP dwgFileName, WCharCP dgnFileName)
{
    RealDwgFileIO::Initialize();
    
    try {
        // Open source DWG file
        DgnFilePtr dwgFile;
        StatusInt status = DgnFile::CreateFromFileName(
            dwgFile, dwgFileName, DgnFileOpenMode::ReadOnly);
        
        if (status != SUCCESS) {
            wprintf(L"Failed to open DWG file: %ls\n", dwgFileName);
            return status;
        }
        
        // Create new DGN file
        DgnFilePtr dgnFile;
        status = DgnFile::CreateNewFile(
            dgnFile, dgnFileName, DgnFileFormatType::DGN);
        
        if (status != SUCCESS) {
            wprintf(L"Failed to create DGN file: %ls\n", dgnFileName);
            return status;
        }
        
        // Copy models from DWG to DGN
        DgnModelIteratorPtr modelIterator = dwgFile->CreateModelIterator();
        
        for (DgnModelPtr sourceModel : *modelIterator) {
            // Create corresponding model in DGN file
            WString modelName = sourceModel->GetModelName();
            DgnModelType modelType = sourceModel->GetModelType();
            bool is3D = sourceModel->Is3d();
            
            DgnModelPtr targetModel = dgnFile->CreateNewModel(
                modelName.c_str(), modelType, is3D);
            
            if (!targetModel.IsValid()) {
                wprintf(L"Failed to create model: %ls\n", modelName.c_str());
                continue;
            }
            
            // Copy elements
            CopyModelElements(sourceModel, targetModel);
        }
        
        // Save the DGN file
        status = dgnFile->ProcessChanges(DgnSaveReason::UserSave);
        if (status == SUCCESS) {
            wprintf(L"Successfully converted %ls to %ls\n", 
                   dwgFileName, dgnFileName);
        }
        
        return status;
    }
    catch (const std::exception& e) {
        wprintf(L"Conversion failed: %hs\n", e.what());
        return BSIERROR;
    }
}

StatusInt CopyModelElements(DgnModelPtr sourceModel, DgnModelPtr targetModel)
{
    DgnElementIteratorPtr iterator = sourceModel->CreateElementIterator();
    int copiedCount = 0;
    
    for (DgnElementPtr element : *iterator) {
        try {
            // Clone the element to the target model
            DgnElementPtr clonedElement = element->CloneForModel(targetModel);
            if (clonedElement.IsValid()) {
                targetModel->AddElement(clonedElement);
                copiedCount++;
            }
        }
        catch (...) {
            // Skip problematic elements
            wprintf(L"Warning: Failed to copy element ID %lld\n", 
                   element->GetElementId());
        }
    }
    
    wprintf(L"Copied %d elements to model: %ls\n", 
           copiedCount, targetModel->GetModelName().c_str());
    
    return SUCCESS;
}
```

### Example 3: Batch File Processing

```cpp
class DwgBatchProcessor
{
private:
    WString m_inputDirectory;
    WString m_outputDirectory;
    bvector<WString> m_processedFiles;
    
public:
    DwgBatchProcessor(WCharCP inputDir, WCharCP outputDir)
        : m_inputDirectory(inputDir), m_outputDirectory(outputDir)
    {
        RealDwgFileIO::Initialize();
    }
    
    StatusInt ProcessAllFiles()
    {
        // Find all DWG/DXF files in input directory
        bvector<WString> inputFiles;
        FindDwgFiles(m_inputDirectory, inputFiles);
        
        wprintf(L"Found %zu files to process\n", inputFiles.size());
        
        int successCount = 0;
        int failureCount = 0;
        
        for (const WString& inputFile : inputFiles) {
            WString outputFile = GenerateOutputFileName(inputFile);
            
            wprintf(L"Processing: %ls\n", inputFile.c_str());
            
            StatusInt status = ProcessSingleFile(inputFile, outputFile);
            if (status == SUCCESS) {
                successCount++;
                m_processedFiles.push_back(outputFile);
            } else {
                failureCount++;
                wprintf(L"Failed to process: %ls\n", inputFile.c_str());
            }
        }
        
        wprintf(L"\nBatch processing complete:\n");
        wprintf(L"  Successful: %d\n", successCount);
        wprintf(L"  Failed: %d\n", failureCount);
        
        return (failureCount == 0) ? SUCCESS : BSIERROR;
    }
    
private:
    StatusInt ProcessSingleFile(const WString& inputFile, const WString& outputFile)
    {
        try {
            // Open input file
            DgnFilePtr dgnFile;
            StatusInt status = DgnFile::CreateFromFileName(
                dgnFile, inputFile.c_str(), DgnFileOpenMode::ReadOnly);
            
            if (status != SUCCESS)
                return status;
            
            // Perform processing (validation, conversion, etc.)
            status = ValidateFile(dgnFile);
            if (status != SUCCESS)
                return status;
            
            // Save to output location
            return SaveProcessedFile(dgnFile, outputFile);
        }
        catch (...) {
            return BSIERROR;
        }
    }
    
    void FindDwgFiles(const WString& directory, bvector<WString>& files)
    {
        // Implementation to find DWG/DXF files
        // This would use platform-specific directory enumeration
    }
    
    WString GenerateOutputFileName(const WString& inputFile)
    {
        // Generate output file name based on input
        WString baseName = GetFileNameWithoutExtension(inputFile);
        return m_outputDirectory + baseName + L"_processed.dgn";
    }
    
    StatusInt ValidateFile(DgnFilePtr dgnFile)
    {
        // Perform validation checks
        return SUCCESS;
    }
    
    StatusInt SaveProcessedFile(DgnFilePtr dgnFile, const WString& outputFile)
    {
        // Save the processed file
        return dgnFile->ProcessChanges(DgnSaveReason::UserSave);
    }
};

// Usage
StatusInt ProcessDwgBatch()
{
    DwgBatchProcessor processor(L"C:\\Input\\", L"C:\\Output\\");
    return processor.ProcessAllFiles();
}
```

## Advanced Usage Examples

### Example 4: Custom Entity Converter

```cpp
// Custom converter for a specific entity type
class CustomPolylineConverter : public ToDgnExtension
{
public:
    StatusInt ConvertToDgn(
        AcDbEntity* pEntity,
        DgnElementP& element,
        ConvertToDgnContext* context,
        DgnModelP model) override
    {
        AcDbPolyline* pPolyline = AcDbPolyline::cast(pEntity);
        if (!pPolyline)
            return BSIERROR;
        
        // Get polyline properties
        unsigned int numVertices = pPolyline->numVerts();
        bool isClosed = pPolyline->isClosed();
        
        // Extract vertices
        bvector<DPoint3d> vertices;
        for (unsigned int i = 0; i < numVertices; ++i) {
            AcGePoint3d vertex;
            pPolyline->getPointAt(i, vertex);
            vertices.push_back(DPoint3d::From(vertex.x, vertex.y, vertex.z));
        }
        
        // Create appropriate DGN element
        if (isClosed && numVertices > 2) {
            // Create shape element for closed polylines
            ShapeElement shapeElement;
            shapeElement.SetVertices(vertices);
            
            // Map symbology
            MapEntitySymbology(pPolyline, &shapeElement, context);
            
            element = shapeElement.CreateElement(model);
        } else {
            // Create line string for open polylines
            LineStringElement lineElement;
            lineElement.SetVertices(vertices);
            
            // Map symbology
            MapEntitySymbology(pPolyline, &lineElement, context);
            
            element = lineElement.CreateElement(model);
        }
        
        return SUCCESS;
    }
    
    bool CanConvert(AcRxClass* pClass) override
    {
        return pClass == AcDbPolyline::desc();
    }
    
private:
    StatusInt MapEntitySymbology(
        AcDbEntity* pEntity,
        DgnElementP element,
        ConvertToDgnContext* context)
    {
        // Map color
        AcCmColor dwgColor = pEntity->color();
        if (dwgColor.isByACI()) {
            UInt32 dgnColor = context->GetDgnSymbologyData()->
                GetColorIndex(dwgColor.colorIndex(), context);
            element->SetColorIndex(dgnColor);
        }
        
        // Map line style
        AcDbObjectId linetypeId = pEntity->linetypeId();
        UInt32 dgnLineStyle = MapLineStyleToDgn(linetypeId, context);
        element->SetLineStyleId(dgnLineStyle);
        
        // Map line weight
        AcDb::LineWeight dwgWeight = pEntity->lineWeight();
        UInt32 dgnWeight = ConvertLineWeight(dwgWeight);
        element->SetWeight(dgnWeight);
        
        return SUCCESS;
    }
    
    UInt32 MapLineStyleToDgn(AcDbObjectId linetypeId, ConvertToDgnContext* context)
    {
        // Implementation for line style mapping
        return 0; // Default line style
    }
    
    UInt32 ConvertLineWeight(AcDb::LineWeight dwgWeight)
    {
        // Convert AutoCAD line weight to DGN weight
        return static_cast<UInt32>(dwgWeight);
    }
};

// Register the custom converter
void RegisterCustomConverters()
{
    static CustomPolylineConverter s_polylineConverter;
    RegisterToDgnExtension(&s_polylineConverter);
}
```

### Example 5: Progress Monitoring

```cpp
class ConversionProgressMonitor
{
private:
    WString m_currentOperation;
    int m_totalSteps;
    int m_currentStep;
    std::chrono::steady_clock::time_point m_startTime;
    
public:
    ConversionProgressMonitor() : m_totalSteps(0), m_currentStep(0) {}
    
    void StartOperation(WCharCP operation, int totalSteps)
    {
        m_currentOperation = operation;
        m_totalSteps = totalSteps;
        m_currentStep = 0;
        m_startTime = std::chrono::steady_clock::now();
        
        wprintf(L"Starting: %ls (0/%d)\n", operation, totalSteps);
    }
    
    void UpdateProgress(int step, WCharCP detail = nullptr)
    {
        m_currentStep = step;
        
        double percentage = (m_totalSteps > 0) ? 
            (100.0 * step / m_totalSteps) : 0.0;
        
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            currentTime - m_startTime);
        
        wprintf(L"\r%ls: %.1f%% (%d/%d) - %llds", 
               m_currentOperation.c_str(), percentage, 
               step, m_totalSteps, elapsed.count());
        
        if (detail) {
            wprintf(L" - %ls", detail);
        }
        
        fflush(stdout);
    }
    
    void CompleteOperation()
    {
        auto endTime = std::chrono::steady_clock::now();
        auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(
            endTime - m_startTime);
        
        wprintf(L"\nCompleted: %ls in %llds\n", 
               m_currentOperation.c_str(), totalTime.count());
    }
};

StatusInt ConvertWithProgress(WCharCP inputFile, WCharCP outputFile)
{
    ConversionProgressMonitor monitor;
    
    try {
        // Phase 1: Load file
        monitor.StartOperation(L"Loading file", 1);
        
        DgnFilePtr dgnFile;
        StatusInt status = DgnFile::CreateFromFileName(
            dgnFile, inputFile, DgnFileOpenMode::ReadOnly);
        
        if (status != SUCCESS) {
            wprintf(L"Failed to load file\n");
            return status;
        }
        
        monitor.UpdateProgress(1);
        monitor.CompleteOperation();
        
        // Phase 2: Count elements
        monitor.StartOperation(L"Analyzing content", 1);
        
        int totalElements = 0;
        DgnModelIteratorPtr modelIterator = dgnFile->CreateModelIterator();
        for (DgnModelPtr model : *modelIterator) {
            totalElements += model->GetElementCount();
        }
        
        monitor.UpdateProgress(1);
        monitor.CompleteOperation();
        
        // Phase 3: Process elements
        monitor.StartOperation(L"Converting elements", totalElements);
        
        int processedElements = 0;
        for (DgnModelPtr model : *modelIterator) {
            DgnElementIteratorPtr elementIterator = model->CreateElementIterator();
            
            for (DgnElementPtr element : *elementIterator) {
                // Process element
                ProcessElement(element);
                
                processedElements++;
                if (processedElements % 100 == 0) {
                    WString detail = L"Model: " + model->GetModelName();
                    monitor.UpdateProgress(processedElements, detail.c_str());
                }
            }
        }
        
        monitor.CompleteOperation();
        
        // Phase 4: Save file
        monitor.StartOperation(L"Saving file", 1);
        
        status = dgnFile->ProcessChanges(DgnSaveReason::UserSave);
        
        monitor.UpdateProgress(1);
        monitor.CompleteOperation();
        
        return status;
    }
    catch (const std::exception& e) {
        wprintf(L"\nConversion failed: %hs\n", e.what());
        return BSIERROR;
    }
}
```

### Example 6: Error Handling and Validation

```cpp
class DwgFileValidator
{
public:
    struct ValidationResult
    {
        bool isValid;
        bvector<WString> warnings;
        bvector<WString> errors;
        
        void AddWarning(WCharCP message) { warnings.push_back(message); }
        void AddError(WCharCP message) { 
            errors.push_back(message); 
            isValid = false;
        }
    };
    
    ValidationResult ValidateFile(WCharCP fileName)
    {
        ValidationResult result;
        result.isValid = true;
        
        try {
            // Open file
            DgnFilePtr dgnFile;
            StatusInt status = DgnFile::CreateFromFileName(
                dgnFile, fileName, DgnFileOpenMode::ReadOnly);
            
            if (status != SUCCESS) {
                result.AddError(L"Failed to open file");
                return result;
            }
            
            // Validate file structure
            ValidateFileStructure(dgnFile, result);
            
            // Validate models
            ValidateModels(dgnFile, result);
            
            // Validate elements
            ValidateElements(dgnFile, result);
            
            // Validate symbology
            ValidateSymbology(dgnFile, result);
            
        }
        catch (const std::exception& e) {
            WString errorMsg = L"Validation exception: ";
            errorMsg += WString(e.what(), BentleyCharEncoding::Utf8);
            result.AddError(errorMsg.c_str());
        }
        
        return result;
    }
    
private:
    void ValidateFileStructure(DgnFilePtr dgnFile, ValidationResult& result)
    {
        // Check file version
        DgnFileFormatType format;
        int majorVersion, minorVersion;
        dgnFile->GetVersion(&format, &majorVersion, &minorVersion);
        
        if (format != DgnFileFormatType::DWG && format != DgnFileFormatType::DXF) {
            result.AddError(L"Invalid file format");
        }
        
        // Check for minimum version support
        if (majorVersion < 12) {
            result.AddWarning(L"Very old file version, some features may not be supported");
        }
    }
    
    void ValidateModels(DgnFilePtr dgnFile, ValidationResult& result)
    {
        int modelCount = dgnFile->GetModelCount();
        if (modelCount == 0) {
            result.AddError(L"File contains no models");
            return;
        }
        
        // Validate each model
        DgnModelIteratorPtr iterator = dgnFile->CreateModelIterator();
        for (DgnModelPtr model : *iterator) {
            ValidateModel(model, result);
        }
    }
    
    void ValidateModel(DgnModelPtr model, ValidationResult& result)
    {
        WString modelName = model->GetModelName();
        
        // Check for empty models
        if (model->GetElementCount() == 0) {
            WString warning = L"Model '" + modelName + L"' is empty";
            result.AddWarning(warning.c_str());
        }
        
        // Check model bounds
        DRange3d modelRange;
        if (model->GetModelRange(modelRange)) {
            double size = modelRange.low.Distance(modelRange.high);
            if (size > 1e6) {
                WString warning = L"Model '" + modelName + L"' has very large extents";
                result.AddWarning(warning.c_str());
            }
        }
    }
    
    void ValidateElements(DgnFilePtr dgnFile, ValidationResult& result)
    {
        int totalElements = 0;
        int invalidElements = 0;
        
        DgnModelIteratorPtr modelIterator = dgnFile->CreateModelIterator();
        for (DgnModelPtr model : *modelIterator) {
            DgnElementIteratorPtr elementIterator = model->CreateElementIterator();
            
            for (DgnElementPtr element : *elementIterator) {
                totalElements++;
                
                if (!ValidateElement(element)) {
                    invalidElements++;
                }
            }
        }
        
        if (invalidElements > 0) {
            WString warning = L"Found " + std::to_wstring(invalidElements) + 
                             L" invalid elements out of " + std::to_wstring(totalElements);
            result.AddWarning(warning.c_str());
        }
    }
    
    bool ValidateElement(DgnElementPtr element)
    {
        // Check for valid geometry
        DRange3d elementRange;
        if (!element->GetElementRange(elementRange)) {
            return false;
        }
        
        // Check for degenerate geometry
        if (elementRange.IsEmpty()) {
            return false;
        }
        
        return true;
    }
    
    void ValidateSymbology(DgnFilePtr dgnFile, ValidationResult& result)
    {
        // Check for valid color table
        // Check for valid line styles
        // Check for valid text styles
        // etc.
    }
};

// Usage
void ValidateAndReportFile(WCharCP fileName)
{
    DwgFileValidator validator;
    auto result = validator.ValidateFile(fileName);
    
    wprintf(L"Validation Results for: %ls\n", fileName);
    wprintf(L"Status: %s\n", result.isValid ? L"VALID" : L"INVALID");
    
    if (!result.warnings.empty()) {
        wprintf(L"\nWarnings:\n");
        for (const WString& warning : result.warnings) {
            wprintf(L"  - %ls\n", warning.c_str());
        }
    }
    
    if (!result.errors.empty()) {
        wprintf(L"\nErrors:\n");
        for (const WString& error : result.errors) {
            wprintf(L"  - %ls\n", error.c_str());
        }
    }
}
```

These examples demonstrate the key patterns and techniques for working with the RealDwgFileIO framework, from basic file operations to advanced custom conversion and validation scenarios.

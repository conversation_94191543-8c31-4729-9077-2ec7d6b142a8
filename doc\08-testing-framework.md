# Testing Framework

## Overview

The RealDwgFileIO framework includes a comprehensive testing system designed to validate the conversion functionality, ensure data integrity, and verify performance characteristics. The testing framework covers unit tests, integration tests, and specialized validation scenarios.

## Test Architecture

### Test Organization

```
RealDwgFileIO/tests/
├── DwgUnitTests.cpp           # Main unit test suite
├── DwgUnitTests.mke           # Build configuration
├── DwgFileOpen.cpp/.h         # File opening tests
├── DwgFileOpenParameterized.cpp # Parameterized file tests
└── DwgUnitTestsPCH.cpp/.h     # Precompiled headers
```

### Test Categories

1. **Unit Tests**: Individual component testing
2. **Integration Tests**: End-to-end conversion testing
3. **Performance Tests**: Speed and memory usage validation
4. **Regression Tests**: Prevent functionality degradation
5. **Compatibility Tests**: Cross-version file support

## Unit Test Framework

### Test Suite Structure

**File**: `tests/DwgUnitTests.cpp`

```cpp
#include "DwgUnitTestsPCH.h"
#include <gtest/gtest.h>

class DwgFileIOTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        // Initialize test environment
        RealDwgFileIO::Initialize();
        
        // Set up test data paths
        SetupTestPaths();
        
        // Create temporary directory for test outputs
        CreateTestOutputDirectory();
    }
    
    void TearDown() override
    {
        // Clean up test files
        CleanupTestFiles();
        
        // Reset framework state
        ResetFrameworkState();
    }
    
    // Helper methods
    void SetupTestPaths();
    void CreateTestOutputDirectory();
    void CleanupTestFiles();
    void ResetFrameworkState();
    
    // Test data
    WString m_testDataPath;
    WString m_outputPath;
    bvector<WString> m_testFiles;
};
```

### Core Functionality Tests

#### File Loading Tests

```cpp
TEST_F(DwgFileIOTest, LoadDwgFile)
{
    // Test loading a simple DWG file
    WString testFile = m_testDataPath + L"simple.dwg";
    
    DgnFilePtr dgnFile;
    StatusInt status = DgnFile::CreateFromFileName(
        dgnFile, testFile.c_str(), DgnFileOpenMode::ReadOnly);
    
    ASSERT_EQ(SUCCESS, status);
    ASSERT_TRUE(dgnFile.IsValid());
    
    // Verify file format
    DgnFileFormatType format;
    dgnFile->GetVersion(&format, nullptr, nullptr);
    EXPECT_EQ(DgnFileFormatType::DWG, format);
    
    // Verify models are loaded
    EXPECT_GT(dgnFile->GetModelCount(), 0);
}

TEST_F(DwgFileIOTest, LoadDxfFile)
{
    // Test loading a DXF file
    WString testFile = m_testDataPath + L"simple.dxf";
    
    DgnFilePtr dgnFile;
    StatusInt status = DgnFile::CreateFromFileName(
        dgnFile, testFile.c_str(), DgnFileOpenMode::ReadOnly);
    
    ASSERT_EQ(SUCCESS, status);
    
    // Verify file format
    DgnFileFormatType format;
    dgnFile->GetVersion(&format, nullptr, nullptr);
    EXPECT_EQ(DgnFileFormatType::DXF, format);
}

TEST_F(DwgFileIOTest, LoadCorruptedFile)
{
    // Test handling of corrupted files
    WString testFile = m_testDataPath + L"corrupted.dwg";
    
    DgnFilePtr dgnFile;
    StatusInt status = DgnFile::CreateFromFileName(
        dgnFile, testFile.c_str(), DgnFileOpenMode::ReadOnly);
    
    // Should fail gracefully
    EXPECT_NE(SUCCESS, status);
    EXPECT_FALSE(dgnFile.IsValid());
}
```

#### Entity Conversion Tests

```cpp
TEST_F(DwgFileIOTest, ConvertLineEntity)
{
    // Load test file with line entities
    DgnFilePtr dgnFile = LoadTestFile(L"lines.dwg");
    DgnModelPtr model = dgnFile->LoadRootModelForRead();
    
    // Find line elements
    DgnElementIteratorPtr iterator = model->CreateElementIterator();
    int lineCount = 0;
    
    for (DgnElementPtr element : *iterator) {
        if (element->GetElementType() == DgnElementType::Line) {
            lineCount++;
            
            // Verify line properties
            LineStringElementPtr lineElement = 
                dynamic_cast<LineStringElementPtr>(element);
            ASSERT_TRUE(lineElement.IsValid());
            
            // Check geometry
            bvector<DPoint3d> vertices;
            lineElement->GetVertices(vertices);
            EXPECT_EQ(2, vertices.size());
            
            // Verify symbology
            EXPECT_NE(0, lineElement->GetColorIndex());
            EXPECT_NE(0, lineElement->GetLineStyleId());
        }
    }
    
    EXPECT_GT(lineCount, 0);
}

TEST_F(DwgFileIOTest, ConvertCircleEntity)
{
    DgnFilePtr dgnFile = LoadTestFile(L"circles.dwg");
    DgnModelPtr model = dgnFile->LoadRootModelForRead();
    
    DgnElementIteratorPtr iterator = model->CreateElementIterator();
    int circleCount = 0;
    
    for (DgnElementPtr element : *iterator) {
        if (element->GetElementType() == DgnElementType::Ellipse) {
            EllipseElementPtr ellipseElement = 
                dynamic_cast<EllipseElementPtr>(element);
            
            if (ellipseElement.IsValid() && ellipseElement->IsCircle()) {
                circleCount++;
                
                // Verify circle properties
                DPoint3d center = ellipseElement->GetCenter();
                double radius = ellipseElement->GetPrimaryAxis();
                
                EXPECT_GT(radius, 0.0);
                EXPECT_DOUBLE_EQ(radius, ellipseElement->GetSecondaryAxis());
            }
        }
    }
    
    EXPECT_GT(circleCount, 0);
}
```

#### Symbology Tests

```cpp
TEST_F(DwgFileIOTest, ColorMapping)
{
    // Test color conversion accuracy
    DgnFilePtr dgnFile = LoadTestFile(L"colors.dwg");
    
    // Get symbology data
    RealDwgFileIO* dwgFileIO = 
        dynamic_cast<RealDwgFileIO*>(dgnFile->GetFileIO());
    ASSERT_TRUE(dwgFileIO != nullptr);
    
    FileHolder* fileHolder = dwgFileIO->GetDwgFileHolder();
    ASSERT_TRUE(fileHolder != nullptr);
    
    DwgSymbologyData* dwgSymbology = fileHolder->GetDwgSymbologyData();
    DgnSymbologyData* dgnSymbology = fileHolder->GetDgnSymbologyData();
    
    // Test specific color mappings
    RgbColorDef testColors[] = {
        {255, 0, 0},    // Red
        {0, 255, 0},    // Green
        {0, 0, 255},    // Blue
        {255, 255, 0},  // Yellow
        {255, 0, 255},  // Magenta
        {0, 255, 255}   // Cyan
    };
    
    for (const auto& color : testColors) {
        UInt32 dgnIndex = dgnSymbology->GetColorIndex(&color, nullptr);
        EXPECT_NE(INVALID_COLOR_INDEX, dgnIndex);
        EXPECT_LT(dgnIndex, 256);  // Valid color index range
    }
}

TEST_F(DwgFileIOTest, LineStyleMapping)
{
    DgnFilePtr dgnFile = LoadTestFile(L"linestyles.dwg");
    DgnModelPtr model = dgnFile->LoadRootModelForRead();
    
    // Collect unique line styles
    std::set<UInt32> lineStyles;
    DgnElementIteratorPtr iterator = model->CreateElementIterator();
    
    for (DgnElementPtr element : *iterator) {
        UInt32 lineStyleId = element->GetLineStyleId();
        if (lineStyleId != 0) {
            lineStyles.insert(lineStyleId);
        }
    }
    
    // Verify line styles were mapped
    EXPECT_GT(lineStyles.size(), 1);  // Should have multiple line styles
    
    // Verify each line style is valid
    for (UInt32 styleId : lineStyles) {
        EXPECT_NE(0, styleId);
        // Additional validation could check if style exists in DGN file
    }
}
```

## File Opening Tests

### Parameterized File Tests

**File**: `tests/DwgFileOpenParameterized.cpp`

```cpp
class DwgFileOpenParameterizedTest : 
    public ::testing::TestWithParam<std::pair<WString, DgnFileFormatType>>
{
protected:
    void SetUp() override
    {
        RealDwgFileIO::Initialize();
    }
};

TEST_P(DwgFileOpenParameterizedTest, OpenVariousFiles)
{
    auto param = GetParam();
    WString fileName = param.first;
    DgnFileFormatType expectedFormat = param.second;
    
    DgnFilePtr dgnFile;
    StatusInt status = DgnFile::CreateFromFileName(
        dgnFile, fileName.c_str(), DgnFileOpenMode::ReadOnly);
    
    if (status == SUCCESS) {
        ASSERT_TRUE(dgnFile.IsValid());
        
        // Verify format
        DgnFileFormatType actualFormat;
        dgnFile->GetVersion(&actualFormat, nullptr, nullptr);
        EXPECT_EQ(expectedFormat, actualFormat);
        
        // Basic validation
        EXPECT_GT(dgnFile->GetModelCount(), 0);
    } else {
        // Log failure for analysis
        ADD_FAILURE() << "Failed to open file: " << fileName.c_str();
    }
}

// Test data
INSTANTIATE_TEST_SUITE_P(
    DwgFiles,
    DwgFileOpenParameterizedTest,
    ::testing::Values(
        std::make_pair(L"test_data/simple.dwg", DgnFileFormatType::DWG),
        std::make_pair(L"test_data/complex.dwg", DgnFileFormatType::DWG),
        std::make_pair(L"test_data/simple.dxf", DgnFileFormatType::DXF),
        std::make_pair(L"test_data/r14.dwg", DgnFileFormatType::DWG),
        std::make_pair(L"test_data/2018.dwg", DgnFileFormatType::DWG)
    )
);
```

## Performance Tests

### Conversion Performance

```cpp
TEST_F(DwgFileIOTest, ConversionPerformance)
{
    // Load large test file
    WString largeFile = m_testDataPath + L"large_model.dwg";
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    DgnFilePtr dgnFile;
    StatusInt status = DgnFile::CreateFromFileName(
        dgnFile, largeFile.c_str(), DgnFileOpenMode::ReadOnly);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        endTime - startTime);
    
    ASSERT_EQ(SUCCESS, status);
    
    // Performance expectations (adjust based on requirements)
    EXPECT_LT(duration.count(), 30000);  // Should load within 30 seconds
    
    // Memory usage check
    size_t memoryUsage = GetCurrentMemoryUsage();
    EXPECT_LT(memoryUsage, 1024 * 1024 * 1024);  // Less than 1GB
}

TEST_F(DwgFileIOTest, SavePerformance)
{
    // Create test DGN file with many elements
    DgnFilePtr dgnFile = CreateTestDgnFile();
    PopulateWithTestElements(dgnFile, 10000);  // 10k elements
    
    WString outputFile = m_outputPath + L"performance_test.dwg";
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    StatusInt status = dgnFile->ProcessChanges(DgnSaveReason::UserSave);
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        endTime - startTime);
    
    ASSERT_EQ(SUCCESS, status);
    
    // Performance expectations
    EXPECT_LT(duration.count(), 60000);  // Should save within 60 seconds
}
```

## Regression Tests

### Version Compatibility

```cpp
TEST_F(DwgFileIOTest, BackwardCompatibility)
{
    // Test files from different AutoCAD versions
    struct VersionTest {
        WString fileName;
        WString description;
    };
    
    VersionTest versionTests[] = {
        {L"r12.dwg", L"AutoCAD R12"},
        {L"r13.dwg", L"AutoCAD R13"},
        {L"r14.dwg", L"AutoCAD R14"},
        {L"2000.dwg", L"AutoCAD 2000"},
        {L"2004.dwg", L"AutoCAD 2004"},
        {L"2007.dwg", L"AutoCAD 2007"},
        {L"2010.dwg", L"AutoCAD 2010"},
        {L"2013.dwg", L"AutoCAD 2013"},
        {L"2018.dwg", L"AutoCAD 2018"}
    };
    
    for (const auto& test : versionTests) {
        WString testFile = m_testDataPath + test.fileName;
        
        if (FileExists(testFile)) {
            DgnFilePtr dgnFile;
            StatusInt status = DgnFile::CreateFromFileName(
                dgnFile, testFile.c_str(), DgnFileOpenMode::ReadOnly);
            
            EXPECT_EQ(SUCCESS, status) 
                << "Failed to load " << test.description.c_str();
            
            if (status == SUCCESS) {
                // Basic validation
                EXPECT_TRUE(dgnFile.IsValid());
                EXPECT_GT(dgnFile->GetModelCount(), 0);
            }
        }
    }
}
```

## Test Utilities

### Test Data Management

```cpp
class TestDataManager
{
private:
    WString m_testDataPath;
    bvector<WString> m_createdFiles;

public:
    TestDataManager(WCharCP testDataPath) 
        : m_testDataPath(testDataPath) {}
    
    ~TestDataManager()
    {
        CleanupCreatedFiles();
    }
    
    DgnFilePtr CreateTestDgnFile(WCharCP fileName)
    {
        WString fullPath = m_testDataPath + fileName;
        
        DgnFilePtr dgnFile;
        StatusInt status = DgnFile::CreateNewFile(
            dgnFile, fullPath.c_str(), DgnFileFormatType::DGN);
        
        if (status == SUCCESS) {
            m_createdFiles.push_back(fullPath);
        }
        
        return dgnFile;
    }
    
    void PopulateWithTestElements(DgnFilePtr dgnFile, int elementCount)
    {
        DgnModelPtr model = dgnFile->CreateNewModel(
            L"Test Model", DgnModelType::Normal, true);
        
        // Create various element types
        for (int i = 0; i < elementCount; ++i) {
            CreateRandomElement(model, i);
        }
    }
    
private:
    void CreateRandomElement(DgnModelPtr model, int index);
    void CleanupCreatedFiles();
};
```

### Validation Helpers

```cpp
class ConversionValidator
{
public:
    static bool ValidateGeometry(DgnElementPtr originalElement, 
                               DgnElementPtr convertedElement)
    {
        // Compare element types
        if (originalElement->GetElementType() != 
            convertedElement->GetElementType()) {
            return false;
        }
        
        // Compare geometry based on element type
        switch (originalElement->GetElementType()) {
            case DgnElementType::Line:
                return ValidateLineGeometry(originalElement, convertedElement);
            case DgnElementType::Ellipse:
                return ValidateEllipseGeometry(originalElement, convertedElement);
            // Add more cases as needed
        }
        
        return true;
    }
    
    static bool ValidateSymbology(DgnElementPtr originalElement,
                                DgnElementPtr convertedElement)
    {
        // Compare color
        if (originalElement->GetColorIndex() != 
            convertedElement->GetColorIndex()) {
            return false;
        }
        
        // Compare line style
        if (originalElement->GetLineStyleId() != 
            convertedElement->GetLineStyleId()) {
            return false;
        }
        
        // Compare weight
        if (originalElement->GetWeight() != 
            convertedElement->GetWeight()) {
            return false;
        }
        
        return true;
    }
    
private:
    static bool ValidateLineGeometry(DgnElementPtr elem1, DgnElementPtr elem2);
    static bool ValidateEllipseGeometry(DgnElementPtr elem1, DgnElementPtr elem2);
};
```

## Test Execution

### Build Configuration

**File**: `tests/DwgUnitTests.mke`

```makefile
# Test build configuration
TEST_NAME = DwgUnitTests
TEST_SOURCES = DwgUnitTests.cpp \
               DwgFileOpen.cpp \
               DwgFileOpenParameterized.cpp

# Include paths
INCLUDE_PATHS = $(REALDWG_INCLUDE) \
                $(GTEST_INCLUDE) \
                ../

# Libraries
LIBRARIES = $(REALDWG_LIBS) \
            $(GTEST_LIBS) \
            RealDwgFileIO

# Test data
TEST_DATA_DIR = test_data/

# Build targets
$(TEST_NAME): $(TEST_SOURCES)
    $(CXX) $(CXXFLAGS) $(INCLUDE_PATHS) -o $@ $^ $(LIBRARIES)

test: $(TEST_NAME)
    ./$(TEST_NAME) --gtest_output=xml:test_results.xml

clean:
    rm -f $(TEST_NAME) test_results.xml
```

### Continuous Integration

```yaml
# CI configuration example
name: RealDwgFileIO Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup test environment
      run: |
        # Setup RealDWG SDK
        # Setup test data
        
    - name: Build tests
      run: |
        cd RealDwgFileIO/tests
        make test
        
    - name: Run tests
      run: |
        ./DwgUnitTests --gtest_output=xml:test_results.xml
        
    - name: Upload test results
      uses: actions/upload-artifact@v2
      with:
        name: test-results
        path: test_results.xml
```

This comprehensive testing framework ensures the reliability and quality of the RealDwgFileIO framework through automated validation of functionality, performance, and compatibility across different file versions and scenarios.

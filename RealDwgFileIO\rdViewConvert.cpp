/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdViewConvert.cpp $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

// ================================ Conversion From Dwg to Dgn ==========================
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/07
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::SetAcsForDwgViewport (ViewInfoR viewInfo, AcDbViewportTableRecord* acViewport)
    {
    // extract UCS data
    AcGePoint3d             ucsOrigin;
    AcGeVector3d            ucsXAxis, ucsYAxis;
    ElementId               acsElementId = INVALID_ELEMENTID;

    if (acViewport->isUcsSavedWithViewport())
        {
        acViewport->getUcs (ucsOrigin, ucsXAxis, ucsYAxis);

        AcDbObjectId        ucsId = acViewport->ucsName();
        if (ucsId.isValid())
            acsElementId = this->ElementIdFromObjectId (ucsId);
        }
    else
        {
        AcDbDatabase*   pDatabase = this->GetDatabase();
        ucsXAxis  = pDatabase->ucsxdir();
        ucsYAxis  = pDatabase->ucsydir();
        ucsOrigin = pDatabase->ucsorg();
        }

    // convert UCS data to the new ACS:
    DPoint3d                origin;
    DVec3d                  xAxis, yAxis;

    RealDwgUtil::DPoint3dFromGePoint3d (origin, ucsOrigin);
    this->GetTransformToDGN().Multiply (origin);

    RealDwgUtil::DPoint3dFromGeVector3d (xAxis, ucsXAxis);
    RealDwgUtil::DPoint3dFromGeVector3d (yAxis, ucsYAxis);

    RotMatrix               matrix;
    matrix.InitFrom2Vectors (xAxis, yAxis);
    matrix.TransposeOf (matrix);

    IAuxCoordSysPtr acs = IACSManager::GetManager().CreateACS (ACSType::Rectangular, origin, matrix, 1.0, L"", L"");
    if (INVALID_ELEMENTID != acsElementId)
        acs->SetElementId (acsElementId, INVALID_MODELREF);
    viewInfo.SetAuxCoordinateSystem (acs.get());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          04/10
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SetUcsForDwgViewport
(
AcDbViewportTableRecord*    viewport,
AcDbDatabaseP               dwg,
ViewInfoCR                  viewInfo
)
    {
    IAuxCoordSysPtr acs = viewInfo.GetAuxCoordinateSystem ();
    if (acs.IsValid())
        {
        // get UCS from input DWG - TFS 739718:
        AcDbObjectId    ucsId;
        if (nullptr == dwg)
            ucsId = this->ExistingObjectIdFromElementId (acs->GetElementId());
        else if (Acad::eOk != dwg->getAcDbObjectId(ucsId, false, this->DBHandleFromElementId(acs->GetElementId()), 0))
            ucsId.setNull ();

        if (ucsId.isValid())
            {
            viewport->setUcs (ucsId);
            }
        else
            {
            DPoint3d    origin;
            acs->GetOrigin (origin);
            this->GetTransformFromDGN().multiply (&origin, &origin, 1);

            RotMatrix   matrix;
            acs->GetRotation (matrix);
            matrix.transposeOf (&matrix);
            matrix.squareAndNormalizeColumns (&matrix, 2, 0);

            DVec3d      xAxis, yAxis;
            matrix.getColumn (&xAxis, 0);
            matrix.getColumn (&yAxis, 1);

            viewport->setUcs (RealDwgUtil::GePoint3dFromDPoint3d(origin), RealDwgUtil::GeVector3dFromDVec3d(xAxis), RealDwgUtil::GeVector3dFromDVec3d(yAxis));
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/08
+---------------+---------------+---------------+---------------+---------------+------*/
static int                  AddDefaultDwgDisplayStyle (WStringCR styleName, ViewFlagsCR viewFlags, DgnFileP dgnFile)
    {
    DisplayStylePtr     displayStyle = DisplayStyle::Create (*dgnFile, styleName.c_str ());

    displayStyle->GetFlagsR().m_legacyDrawOrder = 1;
    displayStyle->SetIsUsableForViews (true);

    // synch these display style with viewport:
    displayStyle->GetFlagsR().m_displayMode                    = viewFlags.renderMode; // must be wireframe
    displayStyle->GetFlagsR().m_displayVisibleEdges            = viewFlags.renderDisplayEdges;
    displayStyle->GetFlagsR().m_displayHiddenEdges             = viewFlags.renderDisplayHidden;
    displayStyle->GetFlagsR().m_hiddenEdgeLineStyle            = viewFlags.hiddenLineStyle;
    displayStyle->GetFlagsR().m_displayShadows                 = viewFlags.renderDisplayShadows;
    displayStyle->GetFlagsR().m_overrideBackgroundColor        = viewFlags.overrideBackground;

    DisplayStyleCP      writtenStyle = DisplayStyleManager::WriteDisplayStyleToFile (*displayStyle, *dgnFile, false);

    return  writtenStyle->GetIndex ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/11
+---------------+---------------+---------------+---------------+---------------+------*/
static bool     CopyDisplayStyleToView (ViewInfoR viewInfo, int styleIndex, DgnFileP dgnFile)
    {
    if (styleIndex < 0)
        return  false;

    /*---------------------------------------------------------------------------------------------------
    A clone of display style might have been written to file instead of the one we sent in to be written,
    and the clone may have remapped colors etc.  Get the actually written style back to extract data.
    ---------------------------------------------------------------------------------------------------*/
    DisplayStyleCP  displayStyle = DisplayStyleManager::GetDisplayStyleByIndex (styleIndex, *dgnFile);

    // MstnViewport would ignore display style if it finds these parameters different via DisplayStyle::MatchesViewInfoData.
    if (NULL != displayStyle)
        {
        displayStyle->CopySettingsTo (viewInfo, *dgnFile);
        return  true;
        }

    return  false;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          11/15
+---------------+---------------+---------------+---------------+---------------+------*/
DisplayStyleCP  ConvertToDgnContext::MapDisplayStyleFromRenderMode (MSRenderMode renderMode)
    {
    DisplayStyleCP  effectiveStyle = nullptr;
    static WCharCP  styleName = nullptr;

    switch (renderMode)
        {
        case MSRenderMode::HiddenLine:      styleName = L"Hidden Line";     break;
        case MSRenderMode::SmoothShade:     styleName = L"Smooth";          break;
        default:        // other styles currently not mapped
            return  effectiveStyle;
        }

    // if active file has the style, use it:
    DisplayStyleList    styleList;
    DisplayStyleManager::PopulateFromFile (styleList, *m_dgnFile);

    effectiveStyle = styleList.FindDisplayStyleByName (styleName);
    if (nullptr != effectiveStyle)
        return  effectiveStyle;

#ifndef BUILD_DWGPLATFORM        
    // get the style from DGNLIB's and save it to current file:
    styleList.clear ();
    DisplayStyleManager::PopulateFromDgnLibs (styleList);

    effectiveStyle = styleList.FindDisplayStyleByName (styleName);
    if (nullptr != effectiveStyle)
        effectiveStyle = DisplayStyleManager::EnsureDisplayStyleIsInFileForDwg (*effectiveStyle, *m_dgnFile);
#endif

    return  effectiveStyle;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::SetDefaultDwgDisplayStyle (ViewInfoR viewInfo)
    {
    /*-----------------------------------------------------------------------------------
    We will need a wireframe display style that has legacy draw order turned on under
    following conditions:
    1) Only for viewport that has wireframe render mode.
    2) Create a new default display style named "DWG Wireframe" if it exists neither in
       seed file nor in dgnlib.
    3) If a style with the same name exists, even legacy draw order is turned off, we
       assume that's what user wants.  Do not override or create a new style.  If the
       style is from dgnlib, copy it to local file.

    An effort has been made to prevent a recursion into model filling: a call, either
    explicitly or implicitly, to ViewInfo::GetRootModelP(true) can potentially cause 
    the recursion.  TFS 717611 is among those examples that had eventually caused the
    method ViewInfo::GetRootModelP(true) to called for a paperspace model while we are
    in middle of filling the default model. To prevent such an inadvertent filling of
    a model, we check ViewInfo::PeekRootModelP() first.  Only if it already has a model 
    we can allow next step that may cause GetRootModelP(true) to be called.
    -----------------------------------------------------------------------------------*/
    int             styleIndex = this->GetDefaultDisplayStyleIndex ();
    DgnFileP        dgnFile = this->GetFile ();

    if (styleIndex < 0)
        {
        // there is no "DWG Wireframe" yet, we need to either get one from DGNLIB or create a new one:
        if (MSRenderMode::Wireframe == viewInfo.GetViewFlags().renderMode)
            {
            DisplayStylePtr styleFromHost;
            WString         styleName;
            if (!DwgPlatformHost::Instance()._GetDwgDefaultDisplayStyle(styleName, styleFromHost) && styleName.empty())
                styleName = WString(L"DWG Wireframe");

            // first we try to read the style from the active/seed file
            DisplayStyleList    styleList;
            DisplayStyleManager::PopulateFromFile (styleList, *dgnFile);
            DisplayStyleCP      styleFromFile = styleList.FindDisplayStyleByName (styleName.GetWCharCP());

            if (NULL != styleFromFile)
                {
                // style exists in seed file, just use it
                styleIndex = styleFromFile->GetIndex ();
                }
            else if (!styleFromHost.IsValid())
                {
                // the host does not provide a style - create our special display style "DWG Wireframe":
                styleIndex = AddDefaultDwgDisplayStyle (styleName, viewInfo.GetViewFlags(), dgnFile);
                }
            else
                {
                // a custom "DWG Wireframe" is provided by the host, save it to local file:
                if (!styleFromHost->IsInFile(*dgnFile))
                    styleFromFile = DisplayStyleManager::WriteDisplayStyleToFile (*styleFromHost.get(), *dgnFile, false);
                styleIndex = styleFromHost->GetIndex ();
                // override view params from the display style - needed to pass MstnViewport check:
                CopyDisplayStyleToView (viewInfo, styleIndex, dgnFile);
                }

            this->SetDefaultDisplayStyleIndex (styleIndex);
            }
        else if (NULL != viewInfo.PeekRootModelP() && NULL == DisplayStyleManager::GetDisplayStyleForViewInfo(viewInfo))
            {
            DisplayStylePtr     displayStyle = DisplayStyle::CreateHardCodedDefault (viewInfo.GetViewFlagsR().GetRenderMode(), *dgnFile);
            if (displayStyle.IsValid())
                {
                DisplayStyleManager::ApplyDisplayStyleToView (*displayStyle.get(), viewInfo);
                if ((styleIndex = displayStyle->GetIndex()) >= 0)
                    this->SetDefaultDisplayStyleIndex (styleIndex);
                }
            }
        }
    else
        {
        // use previously found or created style, and copy it to our view element:
        CopyDisplayStyleToView (viewInfo, styleIndex, dgnFile);
        }

    // also try mapping the active rendermode to a known display style for a better rendering result:
    DisplayStyleCP      activeStyle = this->MapDisplayStyleFromRenderMode (viewInfo.GetViewFlagsR().GetRenderMode());
    if (nullptr != activeStyle && nullptr != viewInfo.PeekRootModelP())
        DisplayStyleManager::ApplyDisplayStyleToView (*activeStyle, viewInfo);

    if (styleIndex < 0)
        return;

    // set the default display style to view element:
    DynamicViewSettingsR    dynamicViewSettings = viewInfo.GetDynamicViewSettingsR();
    dynamicViewSettings.SetDisplayStyleIndex (styleIndex);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/09
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::SetDefaultWorldDrawRegenType (AcDbViewportTableRecord* viewport)
    {
    if (eAcGiRegenTypeInvalid == this->GetWorldDrawRegenType())
        {
        AcGiRegenType               regenType = AcGiRegenType::kAcGiStandardDisplay;
        AcDbVisualStylePointer   visualStyle (viewport->visualStyle(), AcDb::kForRead);
        if (Acad::eOk == visualStyle.openStatus())
            {
            switch (visualStyle->type())
                {
                case AcGiVisualStyle::k2DWireframe:
                case AcGiVisualStyle::k3DWireframe:
                case AcGiVisualStyle::kEmptyStyle:
                case AcGiVisualStyle::kDim:
                    regenType = AcGiRegenType::kAcGiStandardDisplay;
                    break;
                // everything else is considered as rendered
                default:
                    regenType = AcGiRegenType::kAcGiShadedDisplay;
                }
            }

        this->SetWorldDrawRegenType (regenType);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          01/10
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::SetCustomObjectViewDirectionFromVPoint (AcDbViewportTableRecord* viewport)
    {
    AcGeVector3d    activeViewDir = viewport->viewDirection ();
    StandardView    customObjectView = (StandardView)this->GetSettings().GetCustomObjectDisplayView ();
    AcGeVector3d    viewDirection (1.0, 1.0, 1.0);

    if (customObjectView >= StandardView::Top && customObjectView <= StandardView::RightIso)
        {
        // use the view number explicitly selected by the user:
        RotMatrix dMatrix;
        if (bsiRotMatrix_getStandardRotation (&dMatrix, (int)customObjectView))
            {
            DVec3d          zRow;
            dMatrix.getRow (&zRow, 2);
            viewDirection = RealDwgUtil::GeVector3dFromDVec3d (zRow);
            }
        }
    else
        {
        // default to use system variable VPOINT - vector from the view's target to the view's camera:
        viewDirection = activeViewDir;
        }

    viewDirection.normalize ();

    this->SetCustomObjectViewDirection (viewDirection);

    // apply view rotation to camera-Up direction
    double          twist = viewport->viewTwist ();
    if (fabs(twist) > TOLERANCE_VectorEqual)
        {
        m_customObjectCameraUp.x = sin (twist);
        m_customObjectCameraUp.y = cos (twist);
        m_customObjectCameraUp.z = 0.0;
        }
    else
        {
        m_customObjectCameraUp = AcGeVector3d::kYAxis;
        }

    if (viewDirection != AcGeVector3d::kZAxis)
        {
        AcGeMatrix3d    toWorld;
        toWorld.setToPlaneToWorld (viewDirection);
        m_customObjectCameraUp.transformBy (toWorld);
        }

    m_customObjectVisualStyleId = viewport->visualStyle ();
    m_customObjectIsolines = this->GetDatabase()->isolines ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/10
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::SetDefaultFileParametersFromActiveViewport (ViewInfoR viewInfo, AcDbViewportTableRecord* acViewport)
    {
    this->SetDefaultDwgDisplayStyle (viewInfo);
    this->SetDefaultWorldDrawRegenType (acViewport);
    this->SetCustomObjectViewDirectionFromVPoint (acViewport);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 LimitViewParameter (double& pValue, double min, double max)
    {
    if (pValue < min)
        {
        DIAGNOSTIC_PRINTF ("View Value: %g limited to %g\n", pValue, min);
        pValue = min;
        }
    if (pValue > max)
        {
        DIAGNOSTIC_PRINTF ("View Value: %g limited to %g\n", pValue, max);
        pValue = max;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                    ConvertToDgnContext::CalculateDgnViewGeomFromDwgParams
(
RotMatrixR              rotMatrix,                      // <=
DPoint3dR               origin,                         // <=
DPoint3dR               delta,                          // <=
double&                 activeZ,                        // <=
DPoint3dR               cameraPosition,                 // <=
double&                 focalLength,                    // <=
double                  backClipDistance,               // =>
bool                    backClipEnabled,                // =>
const AcGePoint2d&      geCenterPoint,                  // =>
double                  frontClipDistance,              // =>
bool                    frontClipEnabled,               // =>
double                  height,                         // =>
double                  lensLength,                     // =>
bool                    perspectiveEnabled,             // =>
const AcGePoint3d&      geTarget,                       // =>
const AcGeVector3d&     geViewDirection,                // =>
double                  viewTwist,                      // =>
double                  width                           // =>
)
    {
    double              dAspect;
    dAspect = height > 0.0 ? (width / height) : 1.0;

    DPoint3d            viewDirection;
    RealDwgUtil::DPoint3dFromGeVector3d (viewDirection, geViewDirection);

    DPoint3d            target;
    RealDwgUtil::DPoint3dFromGePoint3d (target, geTarget);

    DPoint3d            viewNormal = viewDirection;
    if (0.0 == (focalLength = viewNormal.normalize()))
        viewNormal.z = 1.0;

    // Apply transform to DGN ...
    double          scaleToDGN = this->GetScaleToDGN();
    frontClipDistance *= scaleToDGN;
    backClipDistance  *= scaleToDGN;
    width             *= scaleToDGN;
    height            *= scaleToDGN;
    focalLength       *= scaleToDGN;

    DPoint2d            center;
    center.x = scaleToDGN * geCenterPoint.x;
    center.y = scaleToDGN * geCenterPoint.y;
    this->GetTransformToDGN().Multiply (target);

    // Set depths relative to the target.
    double targetDepth = target.dotProduct (&viewNormal);

    RealDwgUtil::RotMatrixFromArbitraryAxis (rotMatrix, viewNormal);

    RotMatrix           twistMatrix;
    twistMatrix.initFromPrincipleAxisRotations (NULL, 0.0, 0.0, -viewTwist);

    rotMatrix.InitProduct (rotMatrix, twistMatrix);
    rotMatrix.TransposeOf (rotMatrix);

    double              frontClip = 0.0;
    double              backClip = 0.0;
    if (perspectiveEnabled)
        {
        double      maxFrontClip = focalLength * .9999;

        if (frontClipEnabled)
            frontClip = frontClipDistance > maxFrontClip ? maxFrontClip : frontClipDistance;
        else
            frontClip = maxFrontClip;

        if (dAspect > 1.0)
            {
            delta.x = 35.0 * focalLength / lensLength;
            delta.y = delta.x / dAspect;
            }
        else
            {
            delta.y = 35.0 * focalLength / lensLength;
            delta.x = delta.y * dAspect;
            }
        }
    else
        {
        frontClip = frontClipEnabled ? frontClipDistance : height * 10.0;
        delta.x = height * dAspect;
        delta.y = height;
        }

    backClip  = backClipEnabled ? backClipDistance  : -frontClip;

    DPoint3d        viewTarget;

    rotMatrix.Multiply (&viewTarget, &target, 1);
    cameraPosition.SumOf (target, viewNormal, focalLength);

    // Dont allow a focal length that is too small - this will screw up viewing commands, walk application etc.    (TR# 215584).
    if (perspectiveEnabled)
        {
        double          minimumFocalLength = 0.0;
        static          double s_minimumFocalFeet = 1.0;
        static          double s_minimumFocalMeters = .25;

        if (UnitSystem::English == m_targetUnit.GetSystem())
            {
            UnitDefinition  feetUnits = UnitDefinition::GetStandardUnit (StandardUnit::EnglishFeet);
            m_targetUnit.ConvertDistanceFrom (minimumFocalLength, s_minimumFocalFeet * scaleToDGN, feetUnits);
            }
        else
            {
            UnitDefinition  meterUnits = UnitDefinition::GetStandardUnit (StandardUnit::MetricMeters);
            m_targetUnit.ConvertDistanceFrom (minimumFocalLength, s_minimumFocalMeters * scaleToDGN, meterUnits);
            }

        if (perspectiveEnabled && (focalLength < minimumFocalLength))
            {
            delta.x *= minimumFocalLength / focalLength;
            delta.y *= minimumFocalLength / focalLength;

            focalLength = minimumFocalLength;
            }
        }
    delta.z = frontClip - backClip;

    origin.x = viewTarget.x + center.x - delta.x/2.0;
    origin.y = viewTarget.y + center.y - delta.y/2.0;
    origin.z = viewTarget.z + backClip;

    LimitViewParameter (origin.x, RMINDESIGNRANGE, RMAXDESIGNRANGE);
    LimitViewParameter (origin.y, RMINDESIGNRANGE, RMAXDESIGNRANGE);
    LimitViewParameter (origin.z, RMINDESIGNRANGE, RMAXDESIGNRANGE);

    LimitViewParameter (delta.x, 0.0, 2.0*RMAXDESIGNRANGE);
    LimitViewParameter (delta.y, 0.0, 2.0*RMAXDESIGNRANGE);
    LimitViewParameter (delta.z, 0.0, 2.0*RMAXDESIGNRANGE);

    // Perhaps should try to set activeZ from UCS?  For now, center it.
    activeZ = delta.z / 2.0;

    rotMatrix.MultiplyTranspose (origin);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
static void                     InitializeDgnViewFlags (ViewFlagsR viewFlags, ConvertToDgnContextR context)
    {
    AcDbDatabase*   acDatabase = context.GetFileHolder().GetDatabase ();
    viewFlags.line_wghts      = true;
    viewFlags.patterns        = true;
    viewFlags.ed_fields       = true;
    viewFlags.constructs      = true;
    viewFlags.dimens          = true;
    viewFlags.fill            = true;
    viewFlags.textureMaps     = true;
    viewFlags.transparency    = context.IsAcadTransparencyDisplayed();                      // from ACAD registry
    viewFlags.fast_text       = false;                                                      // This actually turns OFF fast font.
    viewFlags.line_wghts      = acDatabase->lineWeightDisplay();
    viewFlags.fill            = viewFlags.patterns = acDatabase->fillmode();                // AutoCAD does not control fill and patterns seperately.
    viewFlags.tagsOff         = acDatabase->attmode() == ATTRIBMODE_INVISIBLE;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                                    ConvertToDgnContext::SetDgnViewFromDwgViewParams
(
ViewInfoR                               viewInfo,
ViewPortInfoR                           viewPortInfo,
double                                  backClipDistance,
bool                                    backClipEnabled,
const AcGePoint2d&                      geCenterPoint,
double                                  frontClipDistance,
bool                                    frontClipEnabled,
double                                  height,
double                                  lensLength,
bool                                    perspectiveEnabled,
const AcDbObjectId&                     visualstyleId,
const AcGePoint3d&                      geTarget,
const AcGeVector3d&                     geViewDirection,
double                                  viewTwist,
double                                  width,
const AcGePoint2d&                      screenLowerLeft,
const AcGePoint2d&                      screenUpperRight,
bool                                    gridOn,
bool                                    acsDisplayOn,
ModelId                                 modelId,
ElementId                               uniqueId,
bool                                    isOn
)
    {
    ViewFlagsR viewFlags = viewInfo.GetViewFlagsR(); 
    InitializeDgnViewFlags (viewFlags, *this);

    ViewGeomInfoR   geomInfo = viewInfo.GetGeomInfoR();
    this->CalculateDgnViewGeomFromDwgParams (geomInfo.m_rotation, geomInfo.m_origin, geomInfo.m_delta, geomInfo.m_activez, geomInfo.m_camera.m_position,
                                geomInfo.m_camera.m_focalLength, backClipDistance, backClipEnabled, geCenterPoint, frontClipDistance, frontClipEnabled, height,
                                lensLength, perspectiveEnabled, geTarget, geViewDirection, viewTwist, width);

    viewFlags.on_off        = isOn;
    viewFlags.camera        = perspectiveEnabled;
    viewFlags.noFrontClip   = ! frontClipEnabled;
    viewFlags.noBackClip    = ! backClipEnabled;
    viewFlags.auxDisplay    = acsDisplayOn;
    viewFlags.grid          = gridOn;
    viewFlags.renderMode    = (UInt32)RealDwgUtil::GetRenderModeFromVisualStyle (visualstyleId);

    // A paperspace should not have a render mode other than wireframe, TSF 717611!
    if (this->GetFile()->GetDefaultModelId() != modelId && viewFlags.renderMode != (UInt32)MSRenderMode::Wireframe)
        viewFlags.renderMode = (UInt32)MSRenderMode::Wireframe;

    DgnModelP   rootModel = viewInfo.PeekRootModelP ();
    if (NULL == rootModel || rootModel->GetModelId() != modelId)
        viewInfo.SetRootModelId (modelId);
    viewInfo.SetElementId (uniqueId);

    viewPortInfo.m_globalRelativeRect.origin.x = screenLowerLeft.x;
    viewPortInfo.m_globalRelativeRect.origin.y = 1.0 - screenUpperRight.y;
    viewPortInfo.m_globalRelativeRect.corner.x = screenUpperRight.x;
    viewPortInfo.m_globalRelativeRect.corner.y = 1.0 - screenLowerLeft.y;

    viewPortInfo.m_screenNumber = 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                    ConvertToDgnContext::SetDgnViewFromViewportEntity (ViewInfoR viewInfo, ViewPortInfoR viewPortInfo, AcDbViewport* acViewport, ModelId modelId)
    {
    AcGePoint2d         lowerLeft (0.0, 0.0), upperRight (1.0, 1.0);

    this->SetDgnViewFromDwgViewParams (viewInfo, viewPortInfo, acViewport->backClipDistance(), acViewport->isBackClipOn(), acViewport->viewCenter(), acViewport->frontClipDistance(),
                                              acViewport->isFrontClipOn(), acViewport->height(), acViewport->lensLength(), acViewport->isPerspectiveOn(),
                                              acViewport->visualStyle(), acViewport->viewTarget(), acViewport->viewDirection(), 0,
                                              (acViewport->height() <= 0.0) ? 1.0 : acViewport->viewHeight() * acViewport->width() / acViewport->height(),
                                              lowerLeft, upperRight, acViewport->isGridOn(), acViewport->isUcsIconVisible(), modelId, 
                                              this->ElementIdFromObject (acViewport), true /*on*/);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                    ConvertToDgnContext::SetDgnViewFromLayout (ViewInfoR viewInfo, ViewPortInfoR viewPortInfo, AcDbLayout* pLayout, ModelId modelId)
    {
    ViewFlagsR  viewFlags = viewInfo.GetViewFlagsR();
    InitializeDgnViewFlags (viewFlags, *this);

    viewInfo.SetRootModel (modelId, m_dgnFile);
    viewInfo.SetElementId (this->GetAndIncrementNextId());

    viewPortInfo.m_globalRelativeRect.origin.x = viewPortInfo.m_globalRelativeRect.origin.y = 0.0;
    viewPortInfo.m_globalRelativeRect.corner.x = viewPortInfo.m_globalRelativeRect.corner.y = 1.0;

    viewFlags.on_off = true;
    viewFlags.noFrontClip   = true;
    viewFlags.noBackClip    = true;

    RotMatrix   rotation;
    rotation.InitIdentity();

    AcGePoint3d     extMin;
    AcGePoint3d     extMax;
    pLayout->getExtents (extMin, extMax);
    DPoint3d        origin;
    DPoint3d        delta;
    RealDwgUtil::DPoint3dFromGePoint3d (origin, extMin);
    RealDwgUtil::DPoint3dFromGePoint3d (delta, extMax);

    // Check for invalid values.
    if ( (delta.x <= origin.x) || (delta.y <= origin.y) || (origin.Distance (delta) > MAX_ExtentDistance) )
        {
        origin.x = origin.y = origin.z = 0.0;
        delta.x = DEFAULT_ViewWidth;
        delta.y = DEFAULT_ViewHeight;
        delta.z = 0.0;
        }
    this->GetTransformToDGN().Multiply (origin);
    this->GetTransformToDGN().Multiply (delta);
    delta.DifferenceOf (delta, origin);
    viewInfo.SetGeometry (&origin, &delta, &rotation);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertToDgnContext::SetDgnViewFromViewportTableRecord (ViewInfoR viewInfo, ViewPortInfoR viewPortInfo, AcDbViewportTableRecord* acViewport, bool isFirstViewport)
    {
    this->SetDgnViewFromDwgViewParams (viewInfo,
                                       viewPortInfo,
                                       acViewport->backClipDistance(),
                                       acViewport->backClipEnabled(),
                                       acViewport->centerPoint(),
                                       acViewport->frontClipDistance(),
                                       acViewport->frontClipEnabled(),
                                       acViewport->height(),
                                       acViewport->lensLength(),
                                       acViewport->perspectiveEnabled(),
                                       acViewport->visualStyle(),
                                       acViewport->target(),
                                       acViewport->viewDirection(),
                                       acViewport->viewTwist(),
                                       acViewport->width(),
                                       acViewport->lowerLeftCorner(),
                                       acViewport->upperRightCorner(),
                                       acViewport->gridEnabled(),
                                       acViewport->iconEnabled(),
                                       0,                                           // Always default ModelId.
                                       this->ElementIdFromObject (acViewport),
                                       true);                                       // isOn

    /*---------------------------------------------------------------------------------------------------------------
    When loading a layout model as a ref attachment, there is no default model, therefore above call would have set
    ViewInfo to use explicit model ID and rootModel==NULL.  Later on when we load ViewGroup into cache, the ViewInfo 
    update will try to setup an ACS for it, but then the ACS setup code depends on a valid root model and it will call
    ViewInfo::GetTargetModelRefP to force loading the model.  We don't want it load the model while we are filling
    the cache.  Since attaching a ref file does not need an ACS, we simply skip adding the ACS to ViewInfo in this case.
    When the file is direct opened, we should always have a valid default model.
    ---------------------------------------------------------------------------------------------------------------*/
    if (NULL != viewInfo.PeekRootModelP())
        this->SetAcsForDwgViewport (viewInfo, acViewport);

    if (isFirstViewport)
        this->SetDefaultFileParametersFromActiveViewport (viewInfo, acViewport);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/11
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            TurnOffDesignLinkDisplayForView (ViewInfoR viewInfo, ConvertToDgnContextR context)
    {
    bool                    isOn = false;
    XAttributeHandlerId     xAttrId (XATTRIBUTEID_HUDMarker, XATTRIBUTE_MinorId_HUDMarker_Category);
    return viewInfo.GetDynamicViewSettingsR().GetXAttributesHolderR().AddXAttribute (xAttrId, MARKER_CATEGORY_DesignLinks, &isOn, sizeof(isOn));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
static void     SetOtherViewsFromFirstView (ViewGroupP viewGroup, int currentViewIndex)
    {
    for (; currentViewIndex < MAX_VIEWS; ++currentViewIndex)
        {
        ViewInfoPtr     copiedViewInfo = ViewInfo::CopyFrom (viewGroup->GetViewInfo (0), false, false, false);
        // turn the view off as it does not come from DWG
        copiedViewInfo->GetViewFlagsR().on_off = false;
        viewGroup->SetViewInfo (*copiedViewInfo.get(), currentViewIndex);
        ViewPortInfoPtr copiedViewPortInfo = ViewPortInfo::CopyFrom (viewGroup->GetViewPortInfo (0));
        viewGroup->SetViewPortInfo (*copiedViewPortInfo.get(), currentViewIndex);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SetDgnViewgroupFromModelspaceVPortTable (UInt32& currentViewIndex, ViewGroupP viewGroup, bool boundaryDisplay)
    {
    bool                            isFirstViewport = true;
    AcDbViewportTablePointer        acViewports (m_pFileHolder->GetDatabase()->viewportTableId(), AcDb::kForRead);
    if (Acad::eOk != acViewports.openStatus())
        return  CantOpenObject;

    AcDbViewportTableIterator*      iterator = NULL;
    if (Acad::eOk != acViewports->newIterator(iterator))
        return  OutOfMemoryError;

    for (iterator->start(); !iterator->done(); iterator->step())
        {
        AcDbObjectId    viewportId;
        if (Acad::eOk != iterator->getRecordId (viewportId))
            continue;

        AcDbViewportTableRecordPointer  acViewportTableRecord (viewportId, AcDb::kForRead);
        if (Acad::eOk != acViewportTableRecord.openStatus())
            continue;

        ACHAR const*                    viewportName;
        if (Acad::eOk != acViewportTableRecord->getName (viewportName))
            continue;
        if (0 != wcsicmp (viewportName, L"*active"))
            continue;

        ViewInfoR       viewInfo = viewGroup->GetViewInfoR (currentViewIndex);
        this->SetDgnViewFromViewportTableRecord (viewInfo, viewGroup->GetViewPortInfoR (currentViewIndex), acViewportTableRecord, isFirstViewport);
        TurnOffDesignLinkDisplayForView (viewInfo, *this);
        viewInfo.GetViewFlagsR().refBoundaryDisplay = boundaryDisplay;

        isFirstViewport = false;
        ++currentViewIndex;
        }
    delete iterator;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/13
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SetDgnViewgroupFromPaperspaceViewport (UInt32& currentViewIndex, ViewGroupP viewGroup, RealDwgModelIndexItem* modelItem, bool boundaryDisplay)
    {
    AcDbBlockTableRecordPointer     acBlock (modelItem->GetBlockTableRecordId(), AcDb::kForRead);
    if (Acad::eOk != acBlock.openStatus())
        return  CantOpenObject;

    AcDbBlockTableRecordIterator*   entityIterator = NULL;
    if (Acad::eOk != acBlock->newIterator(entityIterator))
        return  OutOfMemoryError;

    for (; !entityIterator->done(); entityIterator->step())
        {
        AcDbObjectId        entityId;
        if (Acad::eOk != entityIterator->getEntityId (entityId))
            continue;

        if (entityId.objectClass() != AcDbViewport::desc())
            continue;

        AcDbViewportPointer acViewport (entityId, AcDb::kForRead);
        if (Acad::eOk == acViewport.openStatus())
            {
            ViewInfoR       viewInfo = viewGroup->GetViewInfoR (currentViewIndex);
            this->SetDgnViewFromViewportEntity (viewInfo, viewGroup->GetViewPortInfoR (currentViewIndex), acViewport, modelItem->GetId());
            this->SetDefaultDwgDisplayStyle (viewInfo);
            TurnOffDesignLinkDisplayForView (viewInfo, *this);
            viewInfo.GetViewFlagsR().refBoundaryDisplay = boundaryDisplay;

            ++currentViewIndex;
            break;
            }
        }
    delete entityIterator;

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/15
+---------------+---------------+---------------+---------------+---------------+------*/
bool            ConvertToDgnContext::ValidateActiveViewgroupId (ElementId& activeViewgroupId)
    {
    /*------------------------------------------------------------------------------------------------------
    Workaround a scenario present as the 2nd case in TFS# 208529 - when the active viewgroup saved as cached 
    tcb->activeViewGroup is not the same as the TCB value retrieved from DgnFile header before a SaveAs starts, 
    ISessionMgr::DoSaveDesignFileAs would use the cached value tcb->activeViewGroup and pass it in a model 
    name to SwitchToNewFile, which activates the viewgroup via ViewGroupReactor::OnFileOpen, hence effectively 
    bypass a new value we'd set to file header tcb->activeViewGroup.

    To allow correct view group to be activated, we set the element ID of the active viewgroup from the cached 
    tcb->activeViewGroup, such that OnFileOpen will find this viewgroup via FindByElementId, thus correctly 
    activates it.  The cached tcb->activeViewGroup is saved by the host prior to master file close. We retrieve 
    this desired ID from the host, validate it then use it.
    ------------------------------------------------------------------------------------------------------*/
    bool        requestHonored = false;

    if (DgnFilePurpose::MasterFile == m_dgnFile->GetFilePurpose())
        {
        ElementId   requestedActiveViewgroup = DwgPlatformHost::Instance()._GetActiveViewGroup ();

        if (0 != requestedActiveViewgroup && INVALID_ELEMENTID != requestedActiveViewgroup && requestedActiveViewgroup != activeViewgroupId)
            {
            PersistentElementRefP   existingElem = m_dgnFile->FindByElementId (requestedActiveViewgroup, false);
            AcDbObjectId            existingObjId;

            // the requested element ID can't already be used anything else in DgnFile
            if ((nullptr == existingElem || VIEW_GROUP_ELM == existingElem->GetElementType()))
                {
                // the correspont object ID can't exisit in DWG file unless it is a layout
                if (Acad::eOk != m_pFileHolder->GetDatabase()->getAcDbObjectId(existingObjId, false, this->DBHandleFromElementId(requestedActiveViewgroup), 0) ||
                    existingObjId.objectClass()->isDerivedFrom(AcDbLayout::desc()))
                    requestHonored = true;
                }
            }

        if (requestHonored)
            activeViewgroupId = requestedActiveViewgroup;
        }

    return  requestHonored;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertToDgnContext::SaveViewGroupsToDgn ()
    {
    Symbology               activeSymbology;
    this->GetActiveSymbology (activeSymbology);

    // get active model/layout block ID
    AcDbObjectId            activeLayoutBlockId = RealDwgHostApp::Instance().layoutManager()->getActiveLayoutBTRId ();

    // XClipFrame is a global variable that affects all models/views: 0=no display, 1=display & plot, 2=display but no plot
    int                     xclipFrame = m_pFileHolder->GetDatabase()->xclipFrame ();
    bool                    viewgroupCreated = false;

    /*-----------------------------------------------------------------------------------------------------
    A root model is required to create a ViewGroup, but here we are in the dictionary model, so we use the
    default model for ViewGroup creation.  The default model is all good for modelspace VPORT's.  For layout
    viewports, we will reset ViewInfo model ID's later on.
    -----------------------------------------------------------------------------------------------------*/
    DgnModelP               viewgroupModel = m_dgnFile->FindLoadedModelById (m_dgnFile->GetDefaultModelId());
    if (NULL == viewgroupModel)
        viewgroupModel = m_model;

    RealDwgModelIndexItemArray&     modelIndexItems = m_pFileHolder->GetModelIndexItems();
    for each (RealDwgModelIndexItem* modelItem in modelIndexItems)
        {
        AcDbObjectId        blockId     = modelItem->GetBlockTableRecordId ();
        AcDbBlockTableRecordPointer pBlock (blockId, AcDb::kForRead);
        if (Acad::eOk != pBlock.openStatus())
            continue;

        AcDbObjectId        layoutId = pBlock->getLayoutId ();
        if (layoutId.isNull())
            continue;

        AcDbLayoutPointer   acLayout (layoutId, AcDb::kForRead);
        if (Acad::eOk != acLayout.openStatus())
            continue;

        ACHAR const *       layoutName;
        acLayout->getLayoutName (layoutName);

        ViewGroupPtr        viewGroup;
        if (VG_Success != ViewGroup::Create (viewGroup, *viewgroupModel, false, layoutName, true))
            return  CantCreateViewGroup;

        ElementId           viewgroupId = this->ElementIdFromObjectId (layoutId);
        if (activeLayoutBlockId.isValid() && activeLayoutBlockId == blockId)
            this->ValidateActiveViewgroupId (viewgroupId);

        viewGroup->SetElementId (viewgroupId);
        UInt32              currentViewIndex    = 0;

        viewGroup->SetUIDisplayOrder (acLayout->getTabOrder());

        if (0 == modelItem->GetId())
            {
            // this is the default model, might have muliple viewport table records to make into views.
            this->SetDgnViewgroupFromModelspaceVPortTable (currentViewIndex, viewGroup.get(), xclipFrame > 0);
            }
        else
            {
            // This is a layout  - have to find the first viewport entity to get the view.
            this->SetDgnViewgroupFromPaperspaceViewport (currentViewIndex, viewGroup.get(), modelItem, xclipFrame > 0);
            }

        // if we didn't find any views, create a default one.
        if (0 == currentViewIndex)
            {
            ViewInfoR       viewInfo = viewGroup->GetViewInfoR (currentViewIndex);
            this->SetDgnViewFromLayout (viewInfo, viewGroup->GetViewPortInfoR (currentViewIndex), acLayout, modelItem->GetId());
            TurnOffDesignLinkDisplayForView (viewInfo, *this);
            ++currentViewIndex;
            }

        // For all the views that we did not create, copy the first view.
        SetOtherViewsFromFirstView (viewGroup.get(), currentViewIndex);

        if (!viewgroupCreated)
            viewgroupCreated = viewGroup.IsValid();
        }

    if (!viewgroupCreated)
        {
        // if we get here because of bad layouts, try creating a default viewgroup from viewport table:
        ViewGroupPtr    defaultViewgroup;
        if (VG_Success != ViewGroup::Create(defaultViewgroup, *m_model, false, L"Model", true))
            return  CantCreateViewGroup;

        UInt32          defaultViewIndex = 0;
        if (RealDwgSuccess != this->SetDgnViewgroupFromModelspaceVPortTable(defaultViewIndex, defaultViewgroup.get(), true))
            return  CantFindViewGroup;

        SetOtherViewsFromFirstView (defaultViewgroup.get(), defaultViewIndex);
        }

    GetFile()->GetViewGroups().SaveFromCacheLoader();

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertToDgnContext::SaveViewTableRecordToDgn (AcDbViewTableRecord* acViewTableRecord)
    {
    AcGePoint2d             lowerLeft (0.0, 0.0), upperRight (1.0, 1.0);
    ModelId                 modelId = 0;

    if (acViewTableRecord->isPaperspaceView())
        {
        RealDwgModelIndexItemArray&      modelIndexItems = this->GetFileHolder().GetModelIndexItems();
        for each (RealDwgModelIndexItem* modelItem in modelIndexItems)
            {
            if (modelItem->GetBlockTableRecordId().isNull())
                continue;

            AcDbBlockTableRecordPointer acBlock (modelItem->GetBlockTableRecordId(), AcDb::kForRead);
            AcDbObjectId                layoutId;
            acViewTableRecord->getLayout (layoutId);
            if ( (Acad::eOk == acBlock.openStatus()) && (acBlock->getLayoutId() == layoutId) )
                {
                modelId = modelItem->GetId();
                break;
                }
            }
        }

    const ACHAR* viewName;
    acViewTableRecord->getName (viewName);
    
    NamedViewPtr    namedView;
    if (NamedViewStatus::Success != NamedView::Create (namedView, *GetFile(), viewName))
        return;

    ViewInfoR       viewInfo     = namedView->GetViewInfoR();
    ViewPortInfoR   viewPortInfo = namedView->GetViewPortInfoR();

    this->SetDgnViewFromDwgViewParams (viewInfo, viewPortInfo,
                                       acViewTableRecord->backClipDistance(), 
                                       acViewTableRecord->backClipEnabled(), 
                                       acViewTableRecord->centerPoint(), 
                                       acViewTableRecord->frontClipDistance(),
                                       acViewTableRecord->frontClipEnabled(), 
                                       acViewTableRecord->height(), 
                                       acViewTableRecord->lensLength(), 
                                       acViewTableRecord->perspectiveEnabled(), 
                                       acViewTableRecord->visualStyle(),
                                       acViewTableRecord->target(), 
                                       acViewTableRecord->viewDirection(), 
                                       acViewTableRecord->viewTwist(), 
                                       acViewTableRecord->width(), 
                                       lowerLeft, 
                                       upperRight, 
                                       false, 
                                       false,
                                       modelId, 
                                       this->ElementIdFromObject (acViewTableRecord), 
                                       true);

    NamedViewStatus nvStatus = namedView->WriteToFile();
    BeAssert (NamedViewStatus::Success == nvStatus && L"Failed saving named view to cache!");
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/00
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertToDgnContext::SaveViewTableToDgn ()
    {
    AcDbObjectId            tableId;
    if ( (tableId = m_pFileHolder->GetDatabase()->viewTableId()).isNull() )
        return CantIterateViewTable;

    AcDbViewTablePointer    acViewTable (tableId, AcDb::kForRead);

    AcDbViewTableIterator*  iterator;
    if (Acad::eOk != acViewTable->newIterator (iterator))
        return CantIterateViewTable;

    for (iterator->start(); !iterator->done(); iterator->step())
        {
        AcDbObjectId        entityId;
        if (Acad::eOk != iterator->getRecordId (entityId))
            continue;

        AcDbObjectPointer<AcDbViewTableRecord>   acViewTableRecord (entityId, AcDb::kForRead);
        if (Acad::eOk == acViewTableRecord.openStatus())
            this->SaveViewTableRecordToDgn (acViewTableRecord);
        else
            DIAGNOSTIC_PRINTF ("Error opening view table record! [%ls]\n", acadErrorStatusText(acViewTableRecord.openStatus()));
        }
    delete iterator;

    return RealDwgSuccess;
    }



// ================================ Conversion From Dgn to Dwg ==========================
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/03
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt                   ConvertFromDgnContext::ExtractDatabaseVariablesFromView (ViewInfoCR viewInfo)
    {
    AcDbDatabase*       acDatabase = m_pFileHolder->GetDatabase();

    ViewFlagsCR         viewFlags  = viewInfo.GetViewFlags();
    acDatabase->setLineWeightDisplay (viewFlags.line_wghts);
    acDatabase->setFillmode (viewFlags.fill || viewFlags.patterns);
    if (viewFlags.tagsOff != (0 == acDatabase->attmode()))
        acDatabase->setAttmode (viewFlags.tagsOff ? 0 : 1);

    m_viewDisplayFlags = viewFlags;
    m_pFileHolder->SetViewRotation (viewInfo.GetRotation());

    // display light glyphs based on on/off status of contruction class:
    acDatabase->setLightGlyphDisplay (viewFlags.constructs);

    // display reference boundaries - use TCB to roundtrip xclipframe=2
    int                 xclipFrame = viewFlags.refBoundaryDisplay;
    if (xclipFrame)
        {
        TcbCP           tcb = this->GetFile()->GetPersistentTcb ();
        if (NULL == tcb && 2 == tcb->ext_locks2.displayReferenceClipBoundaries)
            xclipFrame = 2;
        }

    acDatabase->setXclipFrame (xclipFrame);

    // we don't have anything to roundtrip DGNFRAME - just set to 1 if reference boundary is on
    acDatabase->setDgnframe (viewFlags.refBoundaryDisplay ? 1: 0);

    // for PDF underlay, do not display frames - we will turn it on only if a PDF attachment is found to be on
    acDatabase->setPdfframe (0);

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::SaveActiveViewGroupToDwg ()
    {
    ViewGroupCollectionCR   vgc                 = m_dgnFile->GetViewGroups();
    ViewGroupPtr            activeViewGroup     = vgc.GetActiveP ();
    if (activeViewGroup.IsNull())
        {
        ElementId           activeViewGroupId   = this->GetActiveViewGroupId();
        activeViewGroup = vgc.FindByElementId (activeViewGroupId);
        }

    BeAssert (activeViewGroup.IsValid() && L"Failed obtaining the active viewgroup in DgnFile!");

    if (activeViewGroup.IsNull())
        return;

    int     sourceView = this->GetLevelDisplayView();
    if (sourceView >= 0)
        {
        this->ExtractDatabaseVariablesFromView (activeViewGroup->GetViewInfo (sourceView));
        }
    else
        {
        // find the first view that is turned on.
        for (UInt32 iView = 0; iView < MAX_VIEWS; iView++)
            {
            ViewInfoCR  viewInfo = activeViewGroup->GetViewInfo (iView);
            if (!viewInfo.GetViewFlags().on_off)
                continue;
            this->ExtractDatabaseVariablesFromView (viewInfo);
            break;
            }
        }
    }

/*--------------------------------------------------------------------------------------
* @bsimethod                                                    RayBentley      05/04
+---------------+---------------+---------------+---------------+---------------+------*/
ElementId                   ConvertFromDgnContext::GetActiveViewGroupId  ()
    {
    ElementId       viewGroupId;
    if (SUCCESS == m_model->GetTCBData (&viewGroupId, offsetof (Tcb, activeViewGroup), sizeof(ElementId)) )
        return viewGroupId;

    BeAssert (false && L"Cannot extracting activeViewgroupId from model!");
    return 0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/2002
+---------------+---------------+---------------+---------------+---------------+------*/
void                        ConvertFromDgnContext::ExtractParamsFromView
(
double&                                     backClipDistance,   // <=
bool&                                       backClipEnabled,    // <=
double&                                     frontClipDistance,  // <=
bool&                                       frontClipEnabled,   // <=
bool&                                       frontClipAtEye,     // <=
AcGePoint2d&                                geCenterPoint,      // <=
double&                                     height,             // <=
double&                                     width,              // <=
double&                                     lensLength,         // <=
bool&                                       perspectiveEnabled, // <=
AcDbObjectId*                               visualStyleId,      // <=
AcGePoint3d&                                geTarget,           // <=
AcGeVector3d&                               geViewDirection,    // <=
double&                                     viewTwist,          // <=
AcGePoint2d&                                screenLowerLeft,    // <=
AcGePoint2d&                                screenUpperRight,   // <=
ViewFlagsR                                  viewFlags,          // <=
ModelId&                                    modelId,            // <=
ViewInfoCR                                  viewInfo,           // =>
ViewPortInfoCR                              viewPortInfo,       // =>
bool                                        allowRotation       // =>
)
    {
    double              activeZ;
    DPoint3d            origin, delta;
    RotMatrix           rMatrix;
    CameraInfo          cameraInfo;

    origin      = viewInfo.GetOrigin();
    delta       = viewInfo.GetDelta();
    rMatrix     = viewInfo.GetRotation();
    activeZ     = viewInfo.GetActiveZ();
    viewFlags   = viewInfo.GetViewFlags();
    cameraInfo  = viewInfo.GetCamera();
    modelId     = viewInfo.GetRootModelId();

    // Overall sheet view cannot be rotated.
    if (!allowRotation && !rMatrix.IsIdentity())
        {
        DPoint3d                oldOrigin = origin, worldDelta = delta;

        rMatrix.MultiplyTranspose (worldDelta);
        origin.SumOf (oldOrigin, worldDelta, .5, delta, -.5);
        rMatrix.InitIdentity ();
        }

    CookViewFlags (viewFlags, this->GetFileHolder().GetFile(), modelId);

    this->CalculateDwgViewParams (backClipDistance, backClipEnabled, frontClipDistance, frontClipEnabled, frontClipAtEye, geCenterPoint, // outputs
                                  height, width, lensLength, perspectiveEnabled, geTarget, geViewDirection, viewTwist,                   // outputs
                                  origin, delta, rMatrix, viewFlags.camera, cameraInfo.m_position, cameraInfo.m_focalLength,             // inputs
                                  viewFlags.noFrontClip, viewFlags.noBackClip, this->GetScaleFromDGN(), this->GetTransformFromDGN());    // inputs

    DPoint2d        globalRelativeOrigin = viewPortInfo.m_globalRelativeRect.origin;
    DPoint2d        globalRelativeCorner = viewPortInfo.m_globalRelativeRect.corner;

    screenLowerLeft.x   = globalRelativeOrigin.x;
    screenUpperRight.x  = globalRelativeCorner.x;
    screenUpperRight.y  = 1.0 - globalRelativeOrigin.y;
    screenLowerLeft.y   = 1.0 - globalRelativeCorner.y;

    // AutoCrap doesn't handle out of range values well.
    if (screenLowerLeft.x < 0.0)
        screenLowerLeft.x = 0.0;

    if (screenUpperRight.x > 1.0)
        screenUpperRight.x = 1.0;

    if (screenLowerLeft.y < 0.0)
        screenLowerLeft.y = 0.0;

    if (screenUpperRight.y > 1.0)
        screenUpperRight.y = 1.0;

    if (NULL != visualStyleId)
        *visualStyleId = this->GetVisualStyleFromRenderMode (viewFlags.GetRenderMode(), this->GetDatabase());
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    01/01
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ExpandYRange
(
DRange2dR                   range,
ViewGroupCR                 viewGroup,
bool                        validView[MAX_VIEWS], 
UInt32                      thisView
)
    {
    double      yLow = 0.0, yHigh = 1.0;

    for (UInt32 jView = 0; jView < MAX_VIEWS; jView++)
        {
        if (!validView[jView] || jView == thisView)
            continue;

        ViewPortInfoCR  testViewPortInfo = viewGroup.GetViewPortInfo (jView);
        DRange2d        testRange = DRange2d::From (testViewPortInfo.m_globalRelativeRect.origin, testViewPortInfo.m_globalRelativeRect.corner);
        // Allow small overlap.
        testRange.Extend (-TOLERANCE_Overlap, -TOLERANCE_Overlap);

        if (testRange.low.x < range.high.x && testRange.high.x > range.low.x)
            {
            if (testRange.low.y < range.low.y &&
                testRange.high.y > yLow)
                yLow  = testRange.high.y;

            if (testRange.high.y > range.high.y &&
                testRange.low.y < yHigh)
                yHigh = testRange.low.y;
            }
        }

    if (yLow < range.low.y)
        range.low.y = yLow;

    if (yHigh > range.high.y)
        range.high.y = yHigh;


    if (range.low.y < 0.0)
        range.low.y = 0.0;

    if (range.high.y > 1.0)
        range.high.y = 1.0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    01/01
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 ExpandXRange
(
DRange2dR                   range,
ViewGroupCR                 viewGroup,
bool                        validView[MAX_VIEWS], 
UInt32                      thisView
)
    {
    double      xLow = 0.0, xHigh = 1.0;

    for (UInt32 jView = 0; jView < MAX_VIEWS; jView++)
        {
        if (!validView[jView] || jView == thisView)
            continue;

        ViewPortInfoCR  testViewPortInfo = viewGroup.GetViewPortInfo (jView);
        DRange2d        testRange = DRange2d::From (testViewPortInfo.m_globalRelativeRect.origin, testViewPortInfo.m_globalRelativeRect.corner);
        // Allow small overlap.
        testRange.Extend (-TOLERANCE_Overlap, -TOLERANCE_Overlap);

        if (testRange.low.y < range.high.y && testRange.high.y > range.low.y)
            {
            if (testRange.low.x < range.low.x &&
                testRange.high.x > xLow)
                xLow  = testRange.high.x;

            if (testRange.high.x > range.high.x &&
                testRange.low.x < xHigh)
                xHigh = testRange.low.x;
            }
        }

    if (xLow < range.low.x)
        range.low.x = xLow;

    if (xHigh > range.high.x)
        range.high.x = xHigh;

    if (range.low.x < 0.0)
        range.low.x = 0.0;

    if (range.high.x > 1.0)
        range.high.x = 1.0;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    01/01
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 RemoveOverlap
(
double*                     pLow,
double*                     pHigh,
double                      testLow,
double                      testHigh
)
    {
    if (*pLow < testLow && *pHigh > testLow && *pHigh < testHigh)
        *pHigh = testLow;
    else if (*pLow < testHigh && *pHigh > testHigh && *pLow > testLow)
        *pLow = testHigh;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    01/01
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsValidView (ViewInfoCR viewInfo, ViewPortInfoCR viewPortInfo)
    {
    // Sanity check...  could do more here.
    if (0 == viewInfo.GetViewFlags().on_off)
        return false;

    if (viewPortInfo.GetScreenNumber() >= DwgPlatformHost::Instance()._GetNumberOfScreens())
        return false;

    DPoint3d    delta = viewInfo.GetDelta();
    return ( (delta.x > 0.0) && (delta.y > 0.0) && (delta.z >= 0.0) );
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    01/01
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 GetNonOverlappingRangesFromViewGroup (bool validView[MAX_VIEWS], DRange2d viewRange[MAX_VIEWS], ViewGroupCR viewGroup)
    {
    // this method calculates the view ranges such that no views overlap, and might invalidate a view if necessary.
    // the result of its calculations are stored in the validView and viewRange arrays.
    bool                    expanded = false;

    // work from the highest view to the lowest.
    for (Int32 iView = MAX_VIEWS-1; iView >= 0 ; iView--)
        {
        if (!validView[iView])
            continue;

        ViewPortInfoCR      thisViewPortInfo = viewGroup.GetViewPortInfo (iView);
        DRange2d            thisRange = DRange2d::From (thisViewPortInfo.m_globalRelativeRect.origin, thisViewPortInfo.m_globalRelativeRect.corner);

        // Check for invalid values - else we may let overlapping views pass through and crash AutoCrap (TR#
        if (thisRange.low.x < 0.0)
            thisRange.low.x = 0.0;

        if (thisRange.low.y < 0.0)
            thisRange.low.y = 0.0;

        if (thisRange.high.x > 1.0)
            thisRange.high.x = 1.0;

        if (thisRange.high.y > 1.0)
            thisRange.high.y = 1.0;

        double              thisArea = thisRange.Area ();

        // look at all the preceding views.
        for (Int32 jView = iView-1; jView >= 0; jView--)
            {
            if (!validView[jView])
                continue;

            ViewPortInfoCR  testViewPortInfo = viewGroup.GetViewPortInfo (jView);
            DRange2d        testRange = DRange2d::From (testViewPortInfo.m_globalRelativeRect.origin, testViewPortInfo.m_globalRelativeRect.corner);

            DRange2d        intersection;
            if (intersection.IntersectionOf(thisRange, testRange))
                {
                double      testArea         = testRange.Area ();
                double      intersectionArea = intersection.Area ();

                if (intersectionArea > MAX_Overlap * thisArea)
                    {
                    validView[iView] = false;
                    break;
                    }
                else if (intersectionArea > MAX_Overlap * testArea)
                    {
                    validView[jView] = false;
                    }
                else
                    {
                    if (thisRange.low.x < testRange.low.x && thisRange.high.x < testRange.high.x)
                        thisRange.high.x = testRange.low.x;
                    else if (thisRange.low.x > testRange.low.x && thisRange.high.x > testRange.high.x)
                        thisRange.low.x = testRange.high.x;
                    else if (thisRange.low.y < testRange.low.y && thisRange.high.y < testRange.high.y)
                        thisRange.high.y = testRange.low.y;
                    else if (thisRange.low.y > testRange.low.y && thisRange.high.y > testRange.high.y)
                        thisRange.low.y = testRange.high.y;
                    else
                        {
                        validView[iView] = false;
                        break;
                        }
                    }
                }
            }

        if (validView[iView])
            {
            // We know the view is not overlapping any lower views.
            if ( (thisRange.high.x - thisRange.low.x > MIN_WindowSize) && (thisRange.high.y - thisRange.low.y > MIN_WindowSize) )
                viewRange[iView] = thisRange;
            else
                validView[iView] = false;
            }
        }

    // Next attempt to expand views (without adding overlaps)
    for (UInt32 iView = 0; iView < MAX_VIEWS; iView++)
        {
        if (!validView[iView])
            continue;

        ViewPortInfoCR  thisViewPortInfo = viewGroup.GetViewPortInfo (iView);
        DRange2d        thisRange = DRange2d::From (thisViewPortInfo.m_globalRelativeRect.origin, thisViewPortInfo.m_globalRelativeRect.corner);

        ExpandXRange (thisRange, viewGroup, validView, iView);
        ExpandYRange (thisRange, viewGroup, validView, iView);

        viewRange[iView] = thisRange;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/01
+---------------+---------------+---------------+---------------+---------------+------*/
static     int              IsoPairFromDGNIsoPlane (int isoPlane)
    {
    int         isoPairRemap[3] = {1, 0, 2};

    return (isoPlane < 0 || isoPlane > 2) ? 0 : isoPairRemap[isoPlane];
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          08/09
+---------------+---------------+---------------+---------------+---------------+------*/
static void                 FixUpVisualStyleInViewportTableRecord
(
AcDbViewportTableRecord*    acViewportTableRecord,
ViewGroupCR                 viewGroup,
UInt32                      viewIndex,
AcDbDatabase*               acDatabase,
ConvertFromDgnContextR      context
)
    {
    /*-----------------------------------------------------------------------------------
    ConvertFromDgnContext::SaveViewportTableToDwg is called for both default database and
    wblock'ed database.  When we wblock a database, we still keep default database in
    the file holder.  The FromElement method of viewport table record has no knowledge of
    which database should be used, and it uses file holder's database.  Therefore the
    visual style ID we set in the viewport is incorrect.  It causes RealDWG to display a
    message:"Warning: An error occurred during save. We recommend that you run RECOVER on
    the drawing." (TR 280208).  To fix this problem, we re-do the visual style again using
    correct database.

    We probably should reset file holder to use wblock'ed database such that we do not
    have to reset the visual style.
    -----------------------------------------------------------------------------------*/
    if (!acViewportTableRecord->visualStyle().isNull() && acDatabase != acViewportTableRecord->visualStyle().database())
        {
        // remove previously set visual style from the viewport:
        acViewportTableRecord->setVisualStyle (AcDbObjectId::kNull);

        // try to reset visual style in target database:
        ViewFlagsCR     viewFlags = viewGroup.GetViewInfo (viewIndex).GetViewFlags();
        context.SetVisualStyleFromRenderMode (acViewportTableRecord, (MSRenderMode)viewFlags.renderMode, acDatabase);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static bool                 IsSameRenderMode
(
AcDbObjectId                sourceVisualStyle,
MSRenderMode                targetRenderMode
)
    {
    MSRenderMode    sourceRenderMode = RealDwgUtil::GetRenderModeFromVisualStyle (sourceVisualStyle);
    return  targetRenderMode == sourceRenderMode ||
            (MSRenderMode::SmoothShade == sourceRenderMode && MSRenderMode::RenderLuxology == targetRenderMode) ||
            (MSRenderMode::SmoothShade == targetRenderMode && MSRenderMode::RenderLuxology == sourceRenderMode);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          07/09
+---------------+---------------+---------------+---------------+---------------+------*/
static StatusInt            SetViewportVisualStyle
(
AcDbObject*                 pViewport,
const AcDbDatabase*         viewportDatabase,
MSRenderMode                targetRenderMode,
const AcDbObjectId&         visualStyleId
)
    {
    /*-----------------------------------------------------------------------------------
    Reset new visual style if the render mode appears changed, or if we are doing wblock
    cloning a new viewport table record.
    -----------------------------------------------------------------------------------*/
    AcDbViewportTableRecord*    viewportRecord = AcDbViewportTableRecord::cast(pViewport);
    AcDbViewport*               viewportEntity = AcDbViewport::cast(pViewport);
    if (NULL != viewportEntity)
        {
        if (viewportDatabase != visualStyleId.database() || !IsSameRenderMode(viewportEntity->visualStyle(), targetRenderMode))
            viewportEntity->setVisualStyle (visualStyleId);
        return  SUCCESS;
        }
    else if (NULL != viewportRecord)
        {
        if (viewportDatabase != visualStyleId.database() || !IsSameRenderMode(viewportRecord->visualStyle(), targetRenderMode))
            viewportRecord->setVisualStyle (visualStyleId);
        return  SUCCESS;
        }

    return  BSIERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/09
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId    ConvertFromDgnContext::GetVisualStyleFromRenderMode (MSRenderMode renderMode, AcDbDatabase* pDatabase)
    {
    /*-----------------------------------------------------------------------------------
    FUTUREWORK: we will eventually support visual style vs display style. Until then we
    have this workaround to set visual style from render mode on viewports.  This may
    not work when saving DGN to DWG because visual styles may not exist.
    -----------------------------------------------------------------------------------*/
    AcDbObjectId            foundStyleId = AcDbObjectId::kNull;
    AcDbDictionaryPointer   visualStyleDictionary (pDatabase->visualStyleDictionaryId(), AcDb::kForRead);
    if (Acad::eOk == visualStyleDictionary.openStatus())
        {
        AcDbObjectId                realisticStyleId;
        AcDbDictionaryIterator*     iter = visualStyleDictionary->newIterator ();

        for (; !iter->done(); iter->next())
            {
            bool            isRealistic = false;
            MSRenderMode    renderModeFromVisualStyle = RealDwgUtil::GetRenderModeFromVisualStyle (iter->objectId(), &isRealistic);
            const ACHAR*    visualStyleName = iter->name ();
            bool            isNamedStyle = NULL != visualStyleName && '*' != visualStyleName[0] && 0 != visualStyleName[0];
            if (renderMode == renderModeFromVisualStyle && isNamedStyle)
                {
                foundStyleId = iter->objectId ();
                break;
                }

            if (isRealistic)
                realisticStyleId = iter->objectId ();
            }

        delete iter;

        /*-------------------------------------------------------------------------------
        If the DWG file does not contain a matching style and we are in a rendered mode,
        default it to visual style Realistic.  It would be nice to add needed visual
        styles in our DWG seed file, but wblock does not clone these.  Wblock only has 3
        VS's: 2D, 3D and Realistic.  However, this will become a none issue when we fully
        support visual styles in next release.
        -------------------------------------------------------------------------------*/
        if (!foundStyleId.isValid() && realisticStyleId.isValid() && renderMode >= MSRenderMode::HiddenLine)
            {
            foundStyleId = realisticStyleId;
            }
        }
    return  foundStyleId;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          05/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus   ConvertFromDgnContext::SetVisualStyleFromRenderMode(AcDbObject* pViewport, MSRenderMode renderMode, AcDbDatabase* acDatabase)
    {
    AcDbObjectId    visualstyleId = this->GetVisualStyleFromRenderMode (renderMode, acDatabase);
    if (visualstyleId.isNull())
        return  VisualStyleError;

    SetViewportVisualStyle (pViewport, this->GetDatabase(), renderMode, visualstyleId);

    return  RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          02/13
+---------------+---------------+---------------+---------------+---------------+------*/
void            ValidateModelspaceViewportWindow (AcGePoint2d& lowerLeftCorner, AcGePoint2d& upperRightCorner)
    {
    // apparently AutoCAD does not allow screen size smaller than 1.0 for a model space viewport - TFS 1462.
    DPoint2d    delta = DPoint2d::From(upperRightCorner.x - lowerLeftCorner.x, upperRightCorner.y - lowerLeftCorner.y);
    if (fabs(delta.x - 1.0) <= TOLERANCE_ViewportFillSize)
        {
        lowerLeftCorner.x = 0.0;
        upperRightCorner.x = 1.0;
        }
    if (fabs(delta.y - 1.0) <= TOLERANCE_ViewportFillSize)
        {
        lowerLeftCorner.y = 0.0;
        upperRightCorner.y = 1.0;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      12/2002
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SetViewportTableRecordFromView 
(
AcDbViewportTableRecord*    acViewport, 
AcDbDatabaseP               dwg,
ViewGroupCR                 viewGroup, 
UInt32                      viewIndex
)
    {
    double                                  frontClipDistance, backClipDistance, height, lensLength, viewTwist, width;
    bool                                    frontClipEnabled, backClipEnabled, perspectiveEnabled, frontClipAtEye;
    AcGePoint2d                             centerPoint, screenLowerLeft, screenUpperRight;
    AcGePoint3d                             target;
    AcGeVector3d                            viewDirection;
    ViewFlags                               viewFlags;
    ModelId                                 modelId;

    ViewInfoCR                              viewInfo = viewGroup.GetViewInfo (viewIndex);
    ViewPortInfoCR                          viewPortInfo = viewGroup.GetViewPortInfo (viewIndex);
    this->ExtractParamsFromView (backClipDistance, backClipEnabled, frontClipDistance, frontClipEnabled, frontClipAtEye, centerPoint, height, width, 
                                   lensLength, perspectiveEnabled, NULL, target, viewDirection, viewTwist, screenLowerLeft, screenUpperRight, viewFlags, modelId, 
                                   viewInfo, viewPortInfo, true);

    ValidateModelspaceViewportWindow (screenLowerLeft, screenUpperRight);

    acViewport->setCenterPoint (centerPoint);
    acViewport->setBackClipDistance (backClipDistance);
    acViewport->setFrontClipDistance (frontClipDistance);
    if (this->GetSettings().SaveFrontBackClip())
        {
        acViewport->setBackClipEnabled (backClipEnabled ? true : false);
        acViewport->setFrontClipEnabled (frontClipEnabled ? true : false);
        }
    acViewport->setFrontClipAtEye (frontClipAtEye ? true : false);
    acViewport->setHeight (height);
    acViewport->setLensLength (lensLength);
    acViewport->setPerspectiveEnabled (perspectiveEnabled ? true : false);
    this->SetVisualStyleFromRenderMode (acViewport, (MSRenderMode)viewFlags.renderMode, nullptr == dwg ? m_pFileHolder->GetDatabase() : dwg);
    acViewport->setTarget (target);
    acViewport->setViewDirection (viewDirection);
    acViewport->setViewTwist (viewTwist);
    acViewport->setWidth (width);
    acViewport->setLowerLeftCorner (screenLowerLeft);
    acViewport->setUpperRightCorner (screenUpperRight);
    acViewport->setGridEnabled (viewFlags.grid);
    acViewport->setIconEnabled (viewFlags.auxDisplay);
    acViewport->setName (L"*ACTIVE");
    acViewport->setDefaultLightingOn (true==viewFlags.ignoreSceneLights);

    if (this->GetSettings().SetUCSFromCurrentACS())
        this->SetUcsForDwgViewport (acViewport, dwg, viewInfo);

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/10
+---------------+---------------+---------------+---------------+---------------+------*/
static void                UpdateSunObjectInViewport
(
AcDbViewportTableRecord*    viewport,
AcDbSun*                    sunFromDgn,
RenderFlags const&          renderFlags,
RgbFactor const&            ambientIntensity,
double                      ambientIntensityScale
)
    {
    if (renderFlags.ambientLight)
        {
        viewport->setBrightness (ambientIntensityScale);
        viewport->setAmbientLightColor (RealDwgUtil::AcCmColorFromRGBFactor(ambientIntensity));
        }

    AcDbSun*            newSun = NULL;
    Acad::ErrorStatus   errorStatus = acdbOpenObject (newSun, viewport->sunId(), AcDb::kForWrite);
    if (Acad::eOk == errorStatus)
        {
        // scenario 1: a sun exists in DWG - update it with input sun.
        if (NULL == sunFromDgn)
            newSun->setOn (false);
        else
            newSun->copyFrom (sunFromDgn);
        newSun->close ();
        return;
        }

    if (NULL != sunFromDgn)
        {
        // scenario 2: a sun exists in DGN but not in DWG - create a new one from the input non-database resident sun.
        AcDbObjectId    sunId;
        newSun = new AcDbSun ();
        errorStatus = viewport->setSun (sunId, newSun, true);
        if (Acad::eOk == errorStatus)
            {
            newSun->copyFrom (sunFromDgn);
            newSun->close ();
            }
        else
            {
            delete newSun;
            DIAGNOSTIC_PRINTF ("Failed setting sun for viewport. [%ls]\n", acadErrorStatusText(errorStatus));
            }
        return;
        }

    // scenario 3: neither DWG nor DGN has a sun - we are done.
    return;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SaveViewportTableToDwg
(
AcDbDatabase*               acDatabase,
ViewGroupCR                 viewGroup,
ModelInfoCR                 modelInfo,
bool                        changesOnly
)
    {
    AcDbObjectId                    tableId     = acDatabase->viewportTableId();
    AcDbViewportTablePointer        acViewportTable (tableId, AcDb::kForWrite);
    if (Acad::eOk != acViewportTable.openStatus())
        return  CantOpenObject;

    // Initial pass to turn off invalid views.
    bool                            validView[MAX_VIEWS];
    for (UInt32 iView = 0; iView < MAX_VIEWS; iView++)
        {
        ViewInfoCR      viewInfo     = viewGroup.GetViewInfo (iView);
        ViewPortInfoCR  viewPortInfo = viewGroup.GetViewPortInfo (iView);
        validView[iView] = IsValidView (viewInfo, viewPortInfo);
        }

    DRange2d    viewRange[MAX_VIEWS];
    GetNonOverlappingRangesFromViewGroup (validView, viewRange, viewGroup);

    // get tcb data
    RenderFlags renderFlags;
    RgbFactor   ambientIntensity;
    double      ambientIntensityScale;
    TcbCP       pTcb = this->GetFile()->GetPersistentTcb ();
    if (NULL != pTcb)
        {
        renderFlags = pTcb->renderFlags;
        ambientIntensity = pTcb->ambientIntensity;
        ambientIntensityScale = pTcb->ambientIntensityScale;
        }
    else
        {
        BeAssert (false && L"Missing TCB in active file!");
        memset (&renderFlags, 0, sizeof renderFlags);
        memset (&ambientIntensity, 0, sizeof ambientIntensity);
        ambientIntensityScale = 1.0;
        }

    /*-----------------------------------------------------------------------------------
    Create sun from DGN and set it to all rendered viewports. AcDbAbstractViewport::setSun
    requires viewport to be a database resident, so we have to do it after viewports have
    been added to viewport table.
    Only when solor light or sky dome is on we need to create a new sun.
    -----------------------------------------------------------------------------------*/
    AcDbSun*    sun = NULL;
    bool        needSun = this->GetModel()->Is3d() && this->GetSettings().CreateDWGLights();
    if (needSun && !renderFlags.solarLight)
        {
        LightSetupCP    lightSetup = LightManager::GetManagerR().GetActiveLightSetupForModel (false, *m_model);
        if (NULL != lightSetup)
            {
            SkyDomeLightCR  skyDomeLight = lightSetup->GetSkyDomeLight ();
            needSun = skyDomeLight.IsEnabled ();
            }
        }
    if (needSun)
        this->ExtractSunLightFromActiveSolarLight (sun = new AcDbSun(), acDatabase);

    bool        foundMatch[MAX_VIEWS];
    memset (foundMatch, 0, sizeof(foundMatch));

    AcDbViewportTableIterator*        iterator;
    if (Acad::eOk != acViewportTable->newIterator(iterator))
        return CantIterateViewports;

    int         seedViewres = 11000;

    for (iterator->start(); !iterator->done(); iterator->step())
        {
        AcDbObjectId    recordId;
        if (Acad::eOk != iterator->getRecordId (recordId))
            continue;

        AcDbViewportTableRecordPointer  acViewportTableRecord (recordId, AcDb::kForWrite);
        if (Acad::eOk != acViewportTableRecord.openStatus())
            continue;

        const ACHAR*    viewportName;
        acViewportTableRecord->getName (viewportName);
        if (0 != _wcsicmp (viewportName, L"*Active"))          // TR# 135938 - dont remove named viewports.
            continue;

        // try to find the corresponding view.
        ElementId       viewportId = this->ElementIdFromObject (acViewportTableRecord);
        bool            foundViewOn = false;
        UInt32          matchingView;
        MSRenderMode    renderMode = MSRenderMode::Wireframe;
        for (UInt32 iView=0; iView < MAX_VIEWS; iView++)
            {
            ViewInfoCR      viewInfo    = viewGroup.GetViewInfo (iView);
            if (viewportId == viewInfo.GetElementId())
                {
                foundViewOn = (0 != viewInfo.GetViewFlags().on_off);
                foundMatch[iView] = true;
                matchingView      = iView;
                renderMode        = (MSRenderMode)viewInfo.GetViewFlags().renderMode;
                break;
                }
            }

        // save viewres from seed file
        seedViewres = acViewportTableRecord->circleSides ();

        if (!foundViewOn)
            {
            acViewportTableRecord->erase ();
            }
        else
            {
            SetViewportTableRecordFromView (acViewportTableRecord, acDatabase, viewGroup, matchingView);
            FixUpVisualStyleInViewportTableRecord (acViewportTableRecord, viewGroup, matchingView, acDatabase, *this);
            acViewportTableRecord->setAmbientLightColor (renderFlags.ambientLight ? RealDwgUtil::AcCmColorFromRGBFactor(ambientIntensity) : AcCmColor());
            if (NULL != sun && renderMode >= MSRenderMode::SmoothShade)
                UpdateSunObjectInViewport (acViewportTableRecord, sun, renderFlags, ambientIntensity, ambientIntensityScale);
            }
        }

    // go through and save the views we did not find a match for in the DWG file.
    for (UInt32 iView=0; iView < MAX_VIEWS; iView++)
        {
        if (!validView[iView] || foundMatch[iView])
            continue;

        AcDbViewportTableRecordPointer  newViewport = new AcDbViewportTableRecord();

        if (RealDwgSuccess == this->SetViewportTableRecordFromView (newViewport, acDatabase, viewGroup, iView))
            {
            FixUpVisualStyleInViewportTableRecord (newViewport, viewGroup, iView, acDatabase, *this);
            newViewport->setAmbientLightColor (renderFlags.ambientLight ? RealDwgUtil::AcCmColorFromRGBFactor(ambientIntensity) : AcCmColor());
            // If we are adding the viewport to a "wblocked" database then the Ids are not going to be in the same
            // range and we need to give this viewport a new ID. (TR# 112753).
            ElementId           newId = (acDatabase == m_pFileHolder->GetDatabase()) ? viewGroup.GetViewInfo (iView).GetElementId() : 0;
            this->AddRecordToSymbolTable (acViewportTable, newViewport, newId);
            if (NULL != sun && (MSRenderMode)viewGroup.GetViewInfo(iView).GetViewFlags().renderMode >= MSRenderMode::SmoothShade)
                UpdateSunObjectInViewport (newViewport, sun, renderFlags, ambientIntensity, ambientIntensityScale);
            // set viewres from seed file
            newViewport->setCircleSides (seedViewres);
            }
        }

    if (NULL != sun)
        delete sun;

    // we are done if we are only updating default model view group
    if (NULL == m_pModelIndexItem)
        return  RealDwgSuccess;

    // Each AutoCAD view contains information that we store on a per-model basis such as grid and UCS definitions.
    // We don't want to set these unless they have been changed on the MicroStation side to avoid destroying settings
    // that vary per view - so when we are saving changes we impose the model settings only when the values have
    // changed in MicroStation.
    ModelInfoCR     oldModelInfo = m_pModelIndexItem->GetModelInfo ();
    for (iterator->start(); !iterator->done(); iterator->step())
        {
        AcDbObjectId    recordId;
        if (Acad::eOk != iterator->getRecordId (recordId))
            continue;

        AcDbViewportTableRecordPointer  acViewportTableRecord (recordId, AcDb::kForWrite);
        if (Acad::eOk != acViewportTableRecord.openStatus())
            continue;

        if (!changesOnly ||
            modelInfo.m_uorPerGrid != oldModelInfo.m_uorPerGrid ||
            modelInfo.m_refGrid    != oldModelInfo.m_refGrid ||
            modelInfo.m_gridRatio  != oldModelInfo.m_gridRatio ||
            modelInfo.m_gridAngle  != oldModelInfo.m_gridAngle)
            {
            DPoint2d        gridDist;

            gridDist.x = gridDist.y = fabs (modelInfo.m_uorPerGrid) / this->GetScaleToDGN();        // TR# 160946 - negative grid causes audit error.

            // An invalid Grid distance will cause AutoCRAP to hang.
            if (!RealDwgUtil::ValidateDoubleValue (&gridDist.x, MIN_GridDistance, MAX_GridDistance, DEFAULT_GridDistance) ||
                !RealDwgUtil::ValidateDoubleValue (&gridDist.y, MIN_GridDistance, MAX_GridDistance, DEFAULT_GridDistance))
                {
                acViewportTableRecord->setGridEnabled (false);
                }

            if (0.0 != modelInfo.m_gridRatio)
                gridDist.y *= modelInfo.m_gridRatio;

            acViewportTableRecord->setGridIncrements (RealDwgUtil::GePoint2dFromDPoint2d (gridDist));
            acViewportTableRecord->setSnapAngle (modelInfo.m_gridAngle);
            // When gridMajor=0, ACAD does not display grids - change it to 1
            acViewportTableRecord->setGridMajor (modelInfo.m_refGrid < 1 ? 1 : modelInfo.m_refGrid);
            }

        if (!changesOnly ||
            modelInfo.m_roundoffUnit != oldModelInfo.m_roundoffUnit ||
            modelInfo.m_roundoffRatio != oldModelInfo.m_roundoffRatio)
            {
            DPoint2d        snapDist;

            snapDist.x = snapDist.y = modelInfo.m_roundoffUnit / this->GetScaleToDGN();
            if (0.0 != modelInfo.m_roundoffRatio)
                snapDist.y *= modelInfo.m_roundoffRatio;

            try
                {
                acViewportTableRecord->setSnapIncrements (RealDwgUtil::GePoint2dFromDPoint2d (snapDist));
                }
            catch (...)
                {
                DIAGNOSTIC_PRINTF ("Exception caught setting snap increments to: (%f, %f)\n", snapDist.x, snapDist.y);
                }
            }

        if (!changesOnly || !modelInfo.m_gridBase.isEqual (&oldModelInfo.m_gridBase))
            {
            DPoint2d        gridBase;

            gridBase.scale (&modelInfo.m_gridBase, 1.0 / this->GetScaleToDGN());
            acViewportTableRecord->setSnapBase (RealDwgUtil::GePoint2dFromDPoint2d (gridBase));
            }

        if (!changesOnly || modelInfo.m_settingFlags.unitLock != oldModelInfo.m_settingFlags.unitLock)
            acViewportTableRecord->setSnapEnabled (modelInfo.m_settingFlags.unitLock);

        if (!changesOnly || modelInfo.m_settingFlags.isoGrid != oldModelInfo.m_settingFlags.isoGrid)
            acViewportTableRecord->setIsometricSnapEnabled (modelInfo.m_settingFlags.isoGrid);

        if (!changesOnly || modelInfo.m_settingFlags.isoPlane != oldModelInfo.m_settingFlags.isoPlane)
         acViewportTableRecord->setSnapPair (IsoPairFromDGNIsoPlane(modelInfo.m_settingFlags.isoPlane));
        }
    delete iterator;

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SaveSheetViewToDwg
(
AcDbDatabaseP               acDatabase,
ViewGroupCR                 viewGroup,
ModelInfoCR                 modelInfo,
bool                        changesOnly
)
    {
    AcDbObjectId    layoutId;

    if ( (layoutId = this->GetCurrentLayoutId()).isNull())
        return CantFindCurrentLayout;

    AcDbLayoutPointer   pLayout (layoutId, AcDb::kForRead);
    if (Acad::eOk != pLayout.openStatus())
        return CantFindCurrentLayout;

    // First try to get the source view specified by the user:
    ViewInfoPtr     viewInfo;
    Int32           viewSelected = this->GetLevelDisplayView ();
    if (viewSelected >= 0 && viewSelected < MAX_VIEWS)
        viewInfo = ViewInfo::CopyFrom (viewGroup.GetViewInfo(viewSelected), false, false, false);

    // then try getting the 1st open view in viewGroup:
    if (!viewInfo.IsValid() || !viewInfo->GetViewFlags().on_off)
        {
        for (viewSelected = 0; viewSelected < MAX_VIEWS; viewSelected++)
            {
            ViewInfoCR      checkView = viewGroup.GetViewInfo (viewSelected);
            if (checkView.GetViewFlags().on_off)
                {
                viewInfo = ViewInfo::CopyFrom (checkView, false, false, false);
                break;
                }
            }
        }

    if (!viewInfo.IsValid())
        return  CantFindViewGroup;

    // if reach here, we have a valid view from which we can create or set the overall viewport for the layout:
    AcDbObjectIdArray       viewports = pLayout->getViewportArray();
    bool                    noActiveViewport = (viewports.length() <= 0) || viewports[0].isNull();
    bool                    addedActiveViewport = false;
    AcDbObjectId            viewportId;
    AcDbViewportPointer     acViewport;

    if (!noActiveViewport)
        {
        viewportId = viewports[0];
        if (Acad::eOk != acViewport.open (viewportId, AcDb::kForWrite))
            noActiveViewport = true;
        }
    else
        {
        /*-------------------------------------------------------------------------------------------------------------------------
        Before we create a new viewport with the first view element ID, check possible existing viewport with the same ID.
        This is because when AcDbLayout::getViewportArray returns an empty list, it can also mean that the layout viewport actually
        exists in case of editing a DWG file, just that the layout has never been visited.  TR 314592
        We have saved layout ID as ViewGroup ID and the overall viewport ID as the 1st view element ID.  TFS# 150173
        --------------------------------------------------------------------------------------------------------------------------*/
        viewportId = this->ExistingObjectIdFromElementId (viewGroup.GetViewInfo(0).GetElementId());
        if (viewportId.isValid() && Acad::eOk == acViewport.open(viewportId, AcDb::kForWrite))
            noActiveViewport = false;
        }

    if (noActiveViewport)
        {
        AcDbViewport*   newViewport = new AcDbViewport ();

        // initialize minimum viewport data so satisfy ACAD's audit for an empty viewport:
        newViewport->setLayer (acdbSymUtil()->layerZeroId(acDatabase));
        newViewport->setViewDirection (AcDb::kTopView);

        pLayout->upgradeOpen();

        // if we are saving to a separate file, attempting to persist element ID will result in a wrong database error:
        ElementId       newId = (acDatabase == m_pFileHolder->GetDatabase()) ? viewInfo->GetElementId() : 0;

        viewportId = this->AddEntityToCurrentBlock (newViewport, newId);
        viewports.append (viewportId);
        noActiveViewport = false;
        addedActiveViewport = true;
        acViewport.acquire (newViewport);
        }

    if (Acad::eOk != acViewport.openStatus())
        return  CantOpenObject;

    // these are the variables calculated by ExtractParamsFromView.
    double          frontClipDistance, backClipDistance, height, lensLength, viewTwist, width;
    bool            frontClipEnabled, backClipEnabled, perspectiveEnabled, frontClipAtEye;
    AcGePoint2d     centerPoint, screenLowerLeft, screenUpperRight;
    AcGePoint3d     target;
    AcGeVector3d    viewDirection;
    ViewFlags       viewFlags;
    ModelId         modelId;

    // the inputs are the viewInfo and viewPortInfo.
    ViewPortInfoCR  viewPortInfo = viewGroup.GetViewPortInfo (viewSelected);

    this->ExtractParamsFromView (backClipDistance, backClipEnabled, frontClipDistance, frontClipEnabled, frontClipAtEye,
                                 centerPoint, height, width, lensLength, perspectiveEnabled, NULL, target, viewDirection,
                                 viewTwist, screenLowerLeft, screenUpperRight, viewFlags, modelId, *viewInfo.get(), viewPortInfo, true);

    acViewport->setCenterPoint (AcGePoint3d (centerPoint.x, centerPoint.y, 0.0));
    acViewport->setViewCenter (centerPoint);
    acViewport->setViewDirection (viewDirection);
    acViewport->setViewHeight (height);
    acViewport->setHeight (height);
    acViewport->setWidth (width);
    acViewport->setLensLength (50.0);           // Bogus view otherwise.  (TR# 129952).
    viewFlags.grid ? acViewport->setGridOn() : acViewport->setGridOff();
    viewFlags.auxDisplay ? acViewport->setUcsIconVisible() : acViewport->setUcsIconInvisible();

    AcGeVector2d    grid;
    grid.x = grid.y = fabs(modelInfo.m_uorPerGrid) / this->GetScaleToDGN();
    if (0.0 != modelInfo.m_gridRatio)
        grid.y *= modelInfo.m_gridRatio;
    acViewport->setGridIncrement (grid);
    // Apparently pViewport->setGridMajor does not allow 0 for major grid lines - TR#283386 - change 0 to 1:
    Adesk::UInt16   gridMajor = (0 == modelInfo.GetGridPerReference() && acViewport->gridMajor() > 1) ? 1 : modelInfo.GetGridPerReference();
    acViewport->setGridMajor (gridMajor);

    if (addedActiveViewport)
        {
        /*---------------------------------------------------------------------------
        Adding the main viewport entity into database causes an extra viewport entity
        to be added when the main viewport is closed.  Get and delete this extra
        viewport from the paperspace block.
        ---------------------------------------------------------------------------*/
        acViewport.close ();

        AcDbBlockTableRecordPointer     block (m_currentBlockId, AcDb::kForRead);
        if (Acad::eOk == block.openStatus())
            {
            AcDbBlockTableRecordIterator*   iterator = NULL;
            // the extra viewport is the last entity in the block:
            if (Acad::eOk == block->newIterator(iterator, false))
                {
                AcDbEntity*             viewport1 = NULL;
                if (Acad::eOk == iterator->getEntity(viewport1, AcDb::kForWrite))
                    {
                    if (viewport1->isKindOf(AcDbViewport::desc()) && viewport1->objectId() != viewportId)
                        viewport1->erase ();
                    else
                        viewport1->close ();
                    }
                delete iterator;
                }
            }
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SaveViewGroupToDwg
(
AcDbDatabase*               acDatabase,
ModelInfoCR                 modelInfo,
bool                        separateLayout,
bool                        changesOnly
)
    {
    ViewGroupCollectionCR   vgc = m_model->GetDgnFileP()->GetViewGroups();
    ElementId               activeViewGroupId = this->GetActiveViewGroupId();
    ViewGroupPtr            foundViewGroup;
    for each (ViewGroupPtr viewGroup in vgc)
        {
        ViewInfoCR  viewInfo = viewGroup->GetViewInfo (0);
        if (viewInfo.GetRootModelId() == m_modelId)
            {
            // If this is the active viewgroup, take it.  Else (arbitrarily) will end up with the first view group pointing to this model.
            if (viewGroup->GetElementId() == activeViewGroupId)
                {
                foundViewGroup = viewGroup;
                break;
                }
            if (foundViewGroup.IsNull())
                foundViewGroup = viewGroup;
            }
        }
    if (foundViewGroup.IsNull())
        return CantFindViewGroup;

    // We want the disc version of the ViewGroup if File->SaveSettings has NOT been invoked, to revert the view back to the disc version:
    if (changesOnly)
        {
        ViewGroupPtr    originalViewgroup;
        ElementHandle   originalElement(foundViewGroup->GetElementRef());
        if (originalElement.IsValid() && VG_Success == ViewGroup::Create(originalViewgroup, originalElement, false))
            foundViewGroup = originalViewgroup;
        }

    // when saving a sheet model to a separate file as a layout, we want to convert the sheet view:
    RealDwgModelType    saveModelType = m_pModelIndexItem->GetRealDwgModelType ();
    if (RDWGMODEL_TYPE_NonDefaultModel == saveModelType && separateLayout)
        saveModelType = RDWGMODEL_TYPE_Sheet;

    RealDwgStatus       status = BadModel;
    switch (saveModelType)
        {
        case RDWGMODEL_TYPE_Sheet:
            status = this->SaveSheetViewToDwg (acDatabase, *foundViewGroup.get(), modelInfo, changesOnly);
            break;

        case RDWGMODEL_TYPE_DefaultModel:
        default:
            status = this->SaveViewportTableToDwg (acDatabase, *foundViewGroup.get(), modelInfo, changesOnly);
            break;
        }

    /*----------------------------------------------------------------------------------------------------
    Prior to Vancouver, we used to set active layout if foundViewGroup is the active view group.  I have 
    removed this duplicate code because the active layout in the active file is set later on in method
    ExtractDatabaseVariablesFromDgnHeader.  If this is a layout in a wblock'ed file, the active layout is
    set in CreateWBlockLayout.  If this is a non-default design model being saved to a separate file, the
    modelspace in the wblocked file is active by default and there is no need to re-activate it.
    ----------------------------------------------------------------------------------------------------*/

    return status;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SaveDgnNamedViewToDatabase (NamedViewR namedView)
    {
    AcDbViewTableRecordPointer      acViewTableRecord;

    // get a local copy of the name.
    WChar                         nameWChars[2048];
    wcscpy (nameWChars, namedView.GetName().c_str());
    this->ValidateName (nameWChars);

    // see if is an existing object or if we have to create a new one.
    AcDbObjectId                    existingObjectId = this->ExistingObjectIdFromElementId (namedView.GetElementId());
    if (existingObjectId.isNull())
        {
        AcDbSymbolTable*   acViewTable;

        if (Acad::eOk != this->GetFileHolder().GetDatabase()->getViewTable (acViewTable, AcDb::kForWrite))
            return  CantOpenObject;

        // see if we can find an existing one that has the same name 
        AcDbSymbolTableRecord*    foundRecord;
        if (this->SavingChanges() && (Acad::eOk == acViewTable->getAt (nameWChars, foundRecord, AcDb::kForWrite)))
            {
            AcDbViewTableRecord*  foundView = AcDbViewTableRecord::cast (foundRecord);
            acViewTableRecord.acquire (foundView);

            acViewTable->close();
            }
        else
            {
            // couldn't find one, create a new one.
            acViewTableRecord.create();

            // set the name.
            acViewTableRecord->setName (nameWChars);

            AcDbObjectId newObjectId = AddRecordToSymbolTable (acViewTable, acViewTableRecord, namedView.GetElementId());
            acViewTable->close();

            if (newObjectId.isNull())
                return CantCreateViewTableRecord;

            // open the view table record we created.
            acViewTableRecord.open (newObjectId, AcDb::kForWrite);
            }
        }
    else
        {
        // see if the name has changed.
        const ACHAR* currentName;
        acViewTableRecord.open (existingObjectId, AcDb::kForWrite);
        if (Acad::eOk != acViewTableRecord.openStatus())
            return CantOpenObject;

        acViewTableRecord->getName (currentName);
        if (0 != wcscmp (currentName, nameWChars))
            {
            // name changed.
            AcDbSymbolTable*   acViewTable;

            if (Acad::eOk != this->GetFileHolder().GetDatabase()->getViewTable (acViewTable, AcDb::kForWrite))
                {
                DIAGNOSTIC_PRINTF ("Error opening view table for write!\n");
                return  CantOpenObject;
                }

            AcString    name = AcString (nameWChars);
            this->DeduplicateTableName (acViewTable, name);

            if (Acad::eOk != acViewTableRecord->setName (name.kwszPtr()))
                DIAGNOSTIC_PRINTF ("Error setting view table record <%ls>\n", name.kwszPtr());

            acViewTable->close ();
            }
        }

    double                                  frontClipDistance, backClipDistance, height, lensLength, viewTwist, width;
    bool                                    frontClipEnabled, backClipEnabled, perspectiveEnabled, frontClipAtEye;
    AcGePoint2d                             centerPoint, screenLowerLeft, screenUpperRight;
    AcGePoint3d                             target;
    AcGeVector3d                            viewDirection;
    AcDbObjectId                            visualstyleId;
    ViewFlags                               viewFlags;
    ModelId                                 modelId;

    // get the parameters from the view information.
    this->ExtractParamsFromView (backClipDistance, backClipEnabled, frontClipDistance, frontClipEnabled, frontClipAtEye, centerPoint, height, width, lensLength,
                                 perspectiveEnabled, &visualstyleId, target, viewDirection, viewTwist, screenLowerLeft, screenUpperRight, viewFlags, modelId, namedView.GetViewInfo(), namedView.GetViewPortInfo(), true);

    bool                                    isSheetModelView = false;
    RealDwgModelIndexItem*                  pModelIndexItem;
    if (NULL != (pModelIndexItem = this->GetFileHolder().GetModelItemByModelId (modelId)) &&
        !pModelIndexItem->GetBlockTableRecordId().isNull())
        {
        isSheetModelView = RDWGMODEL_TYPE_Sheet == pModelIndexItem->GetRealDwgModelType();
        acViewTableRecord->setIsPaperspaceView (isSheetModelView);
        AcDbBlockTableRecordPointer pBlock (pModelIndexItem->GetBlockTableRecordId(), AcDb::kForRead);
        if (Acad::eOk == pBlock.openStatus());
            acViewTableRecord->setLayout (pBlock->getLayoutId());
        }

    acViewTableRecord->setCenterPoint (centerPoint);
    acViewTableRecord->setHeight (height);
    acViewTableRecord->setWidth (width);
    // don't try setting 3D parameters in paperspace - doing so causes RealDWG to fail close the view object - TR 309005!
    if (!isSheetModelView)
        {
        acViewTableRecord->setBackClipDistance (backClipDistance);
        acViewTableRecord->setFrontClipDistance (frontClipDistance);
        if (this->GetSettings().SaveFrontBackClip())
            {
            acViewTableRecord->setBackClipEnabled (backClipEnabled);
            acViewTableRecord->setFrontClipEnabled (frontClipEnabled);
            }
        acViewTableRecord->setFrontClipAtEye (frontClipAtEye);
        acViewTableRecord->setLensLength (lensLength);
        acViewTableRecord->setVisualStyle (visualstyleId);
        acViewTableRecord->setTarget (target);
        acViewTableRecord->setViewDirection (viewDirection);
        acViewTableRecord->setViewTwist (viewTwist);
        }

    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus               ConvertFromDgnContext::SaveDgnNamedViewsToDatabase ()
    {
    NamedViewCollectionCR   nvc = GetFile()->GetNamedViews();
    for each (NamedViewPtr namedView in nvc)
        {
        this->SaveDgnNamedViewToDatabase (*namedView.get());
        }
    return RealDwgSuccess;
    }


/*=================================================================================**//**
* @bsiclass                                                     Don.Fu          03/14
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtViewElement : public ToDwgExtension
{
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          03/14
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    NamedViewPtr        namedView = context.GetFile()->GetNamedViews().FindByElementId (elemHandle.GetElementId());
    if (!namedView.IsValid())
        return  CantAccessMstnElement;

    return  context.SaveDgnNamedViewToDatabase (*namedView.get());
    }

};  // ToDwgExtViewElement


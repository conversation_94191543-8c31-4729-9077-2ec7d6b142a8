/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rd3dPolyline.cpp $
|
|  $Copyright: (c) 2015 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDgnExt3dPolyline : public ToDgnExtension, SetFromPolylineInfo
{

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Sagar.Inamdar 04/2022
+---------------+---------------+---------------+---------------+---------------+------*/
    bool ShouldForceLineStyle0(AcDb3dPolyline* acPolyline, ConvertToDgnContextR context) const
        {

        AcDbDatabase* dwg = acPolyline->database();
        AcDbObjectId  linetypeId = acPolyline->linetypeId();

        // filter out cases we do not have to force setting linestyle 0: 
        if (linetypeId == acdbSymUtil()->linetypeContinuousId(dwg))
            {
            return false;
            }
        else if (linetypeId == acdbSymUtil()->linetypeByLayerId(dwg))
            {
            AcDbLayerTableRecordPointer layer(acPolyline->layerId(), AcDb::kForRead);
            if (Acad::eOk == layer.openStatus() && (layer->linetypeObjectId() == acdbSymUtil()->linetypeContinuousId(dwg)))
                {
                return false;
                }
            }
        else if (linetypeId == acdbSymUtil()->linetypeByBlockId(dwg))
            {
            AcDbObjectId    ownerId = acPolyline->ownerId();
            // In either the ModelSpace or a PaperSpace, the effective linetype in ACAD or linestyle in MSTN shall be 0 anyway: 
            if (ownerId == acdbSymUtil()->blockModelSpaceId(dwg) || ownerId == acdbSymUtil()->blockPaperSpaceId(dwg))
                {
                return false;
                }
            }
        // all other cases are controlled by the config var: 
        return !context.PreserveLinetypeOf3dPolyline();
        }
/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToElement
(
AcDbObjectP             acObject,
EditElementHandleR      outElement,
ConvertToDgnContextR    context
) const override
    {
    AcDb3dPolyline*         acPolyline = AcDb3dPolyline::cast (acObject);
    RealDwgStatus           rDwgStatus;
    DPoint3dArray           pointArray;

    switch (acPolyline->polyType())
        {
        case AcDb::k3dSimplePoly:
            {
            if (RealDwgSuccess != (rDwgStatus = ExtractVertices (acPolyline, pointArray, AcDb::k3dSimpleVertex)))
                return rDwgStatus;

            // create a shape element only if the polyline is planar & closed - TFS# 42553:
            bool            createShape = acPolyline->isClosed() && acPolyline->isPlanar();
            DPoint3dCP      startPoint = &pointArray.front();
            DPoint3dCP      endPoint   = startPoint + (pointArray.size()-1);
            if (acPolyline->isClosed() && !startPoint->IsEqual (*endPoint))
                pointArray.push_back (*startPoint);

            if (RealDwgSuccess != (rDwgStatus = context.CreateElementFromVertices (outElement, &pointArray.front(), pointArray.size(), createShape, context.GetTransformToDGN())))
                return rDwgStatus;

            break;
            }

        case AcDb::k3dQuadSplinePoly:
        case AcDb::k3dCubicSplinePoly:
            {
            if (RealDwgSuccess != (rDwgStatus = ExtractVertices (acPolyline, pointArray, AcDb::k3dControlVertex)))
                return rDwgStatus;

            size_t  nPoles = pointArray.size();
            int     degree = (AcDb::k3dQuadSplinePoly == acPolyline->polyType()) ? 2 : 3;

            // handle the 3-pole cubic case by duplicating middle pole to satisfy nPoles >= order
            if (3 == nPoles && 3 == degree)
                {
                DPoint3d    middlePole = pointArray[1];
                pointArray.insert (pointArray.begin()+1, middlePole);
                nPoles = pointArray.size();
                }

            // TR 99592 & TR 115926: make a linear spline from other points if not enough poles
            else if ((int)nPoles < degree + 1)
                {
                pointArray.clear();
                if (RealDwgSuccess != (rDwgStatus = ExtractVertices (acPolyline, pointArray, AcDb::k3dFitVertex)))
                    return rDwgStatus;

                degree = 1;
                nPoles = pointArray.size();
                }

            if (RealDwgSuccess != (rDwgStatus = context.CreateElementFromDwgSplineParamsAndTransform (outElement, degree, nPoles, 0, (Adesk::kFalse != acPolyline->isClosed()), false,
                                                                 &pointArray.front(), NULL, NULL, context.GetTransformToDGN())))
                return rDwgStatus;

            break;
            }
        default:
            {
            assert (false);
            return EntityError;
            }
        }

    context.ElementHeaderFromEntity (outElement, acPolyline);


    if (ShouldForceLineStyle0(acPolyline, context))
        {
        // ACAD does not display linetype on a 3D polyline: it confuses user - TFS# 14995.
        outElement.GetElementP()->hdr.dhdr.symb.style = 0;
        }


    return RealDwgSuccess;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           ExtractVertices
(
AcDb3dPolyline*         acPolyline,
DPoint3dArrayR          points,
AcDb::Vertex3dType      vertexType
) const
    {
    AcDbObjectIterator* iterator = acPolyline->vertexIterator();
    for (; !iterator->done(); iterator->step())
        {
        AcDbObjectId            vertexId = iterator->objectId ();
        AcDb3dPolylineVertex*   vertex = NULL;

        Acad::ErrorStatus status;
        if (Acad::eOk != (status = acPolyline->openVertex (vertex, vertexId, AcDb::kForRead)))
            {
            DIAGNOSTIC_PRINTF ("Error opening a vertex from a 3D polyline! [%ls]\n", acadErrorStatusText(status));
            // free the points so the caller can just return.
            points.clear();
            delete iterator;
            return ErrorOpeningVertex;
            }

        // When a simple polyline has fit vertices, AutoCAD treats these vertices as if they are simple vertices.  We've done the same in MSJ.
        AcDb::Vertex3dType      currVertexType = vertex->vertexType ();
        if (vertexType == currVertexType || (AcDb::k3dSimpleVertex == vertexType && AcDb::k3dFitVertex == currVertexType))
            {
            DPoint3d  point;
            points.push_back (RealDwgUtil::DPoint3dFromGePoint3d (point, vertex->position()));
            }

        vertex->close();
        }
    delete iterator;

    return points.size() > 0 ? RealDwgSuccess : IncorrectVertexCount;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/10
* This method is called from the ToDwgExtension::ToObject methods of several elements.
+---------------+---------------+---------------+---------------+---------------+------*/
RealDwgStatus           VerticesFromPolylineInfo (AcDbObjectP acObject, PolylineInfoCR polylineInfo, ElementHandleR sourceElement, ConvertFromDgnContext& context) const override
    {
    size_t  numPoints   = polylineInfo.GetPointCount();
    bool    closed      = polylineInfo.IsClosed();
    if (closed)
        numPoints--;
    if (numPoints < 2)
        {
#if defined (DIAGNOSTIC)
        if (com.bentley.sys.Diagnostic.INSTRUMENTED)
            System.out.println ("Ignoring 3DPolyline with less than 2 vertices");
#endif
        return InvalidPolygonVertexCount;
        }

    AcDb3dPolyline* acPolyline = AcDb3dPolyline::cast (acObject);

    // we are not expecting either bulges or widths.
    BeAssert (!polylineInfo.HasIndividualWidths() && L"Unexpected polyline width!");
    BeAssert (!polylineInfo.HasBulges() && L"Unexpected polyline bulge!");

    if ( (false != closed) != (Adesk::kFalse != acPolyline->isClosed()) )
        closed ? acPolyline->makeClosed() : acPolyline->makeOpen();

    acPolyline->setPolyType (AcDb::k3dSimplePoly);

    RealDwgStatus   rDwgStatus = RealDwgSuccess;
    DPoint3dCP      points = polylineInfo.FirstPoint();
    if (CountVertices (acPolyline, (AcDb::Vertex3dType)(-1)) == numPoints)
        {
        AcDbObjectIterator* iterator = acPolyline->vertexIterator();
        for (int iVertex=0; !iterator->done(); iterator->step(), iVertex++)
            {
            AcDbObjectId            vertexId = iterator->objectId ();
            AcDb3dPolylineVertex*   vertex = NULL;

            Acad::ErrorStatus status;
            if (Acad::eOk != (status = acPolyline->openVertex (vertex, vertexId, AcDb::kForWrite)))
                {
                DIAGNOSTIC_PRINTF ("Error opening a vertex of a 3D polyline! [%ls]\n", acadErrorStatusText(status));
                rDwgStatus = ErrorOpeningVertex;
                break;
                }

            vertex->setPosition (RealDwgUtil::GePoint3dFromDPoint3d (points[iVertex]));
            vertex->close();
            }
        delete iterator;
        }
    else
        {
        RemoveAllVertices (acPolyline);

        for (size_t iVertex = 0; iVertex < numPoints; iVertex++)
            AppendVertex (acPolyline, points[iVertex]);
        }
    return rDwgStatus;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      03/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                    AppendVertex (AcDb3dPolyline* acPolyline, DPoint3dCR pPoint) const
    {
    AcDb3dPolylineVertex*   acVertex = new AcDb3dPolylineVertex (RealDwgUtil::GePoint3dFromDPoint3d (pPoint));
    if (NULL == acVertex)
        return;

    acVertex->setPropertiesFrom (acPolyline);

    Acad::ErrorStatus       es = acPolyline->appendVertex (acVertex);
    if (Acad::eOk == es && acVertex->objectId().isValid())
        acVertex->close ();
    else if (Acad::eOk != es)
        DIAGNOSTIC_PRINTF ("Failed appending a vertex for an AcDb3dPolylineVertex. [%ls]\n", acadErrorStatusText(es));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   10/08
+---------------+---------------+---------------+---------------+---------------+------*/
void                    RemoveAllVertices (AcDb3dPolyline*  acPolyline) const
    {
    AcDbObjectIterator* iterator = acPolyline->vertexIterator();
    for (; !iterator->done(); )
        {
        AcDbObjectId            vertexId = iterator->objectId ();
        AcDb3dPolylineVertex*   acVertex = NULL;

        Acad::ErrorStatus status = acPolyline->openVertex (acVertex, vertexId, AcDb::kForWrite);
        iterator->step();

        if (Acad::eOk != status)
            {
            DIAGNOSTIC_PRINTF ("Error opening a vertex of a 3D polyline for removal! [%ls]\n", acadErrorStatusText(status));
            continue;
            }

        acVertex->erase();
        acVertex->close ();
        }
    delete iterator;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Barry.Bentley                   02/09
+---------------+---------------+---------------+---------------+---------------+------*/
size_t                  CountVertices (AcDb3dPolyline* acPolyline, AcDb::Vertex3dType typeToSkip) const
    {
    int                 count       = 0;
    AcDbObjectIterator* iterator   = acPolyline->vertexIterator();

    for (; !iterator->done(); iterator->step())
        {
        if ( (AcDb::Vertex3dType)(-1) == typeToSkip)
            count++;
        else
            {
            AcDbObjectId            vertexId = iterator->objectId ();
            AcDb3dPolylineVertex*   acVertex = NULL;

            Acad::ErrorStatus status;
            if (Acad::eOk != (status = acPolyline->openVertex (acVertex, vertexId, AcDb::kForRead)))
                continue;

            if (acVertex->vertexType() != typeToSkip)
                count++;

            acVertex->close();
            }
        }
    delete  iterator;

    return count;
    }
};

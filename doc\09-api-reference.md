# API Reference

## Overview

This document provides detailed API reference for the RealDwgFileIO framework, including class interfaces, method signatures, and usage examples. The API is organized by functional areas for easy navigation.

## Core Classes

### RealDwgFileIO

**Header**: `rDwgFileIO.h`

Main file I/O handler class that provides DWG/DXF file support.

#### Constructor

```cpp
RealDwgFileIO(DgnFileP pFile, DgnFileFormatType format);
```

**Parameters**:
- `pFile`: Pointer to the DGN file object
- `format`: File format (DWG or DXF)

#### Public Methods

##### File Operations

```cpp
virtual StatusInt LoadFile(
    DgnPlatform::DesignFileHeader* pHdr,
    bool* openedReadonly,
    StatusInt* rwStatus,
    DgnPlatform::DgnFileOpenMode openMode,
    DgnFileLoadContextP context) override;
```

Loads a DWG/DXF file into the framework.

**Parameters**:
- `pHdr`: Design file header to populate
- `openedReadonly`: Returns whether file was opened read-only
- `rwStatus`: Returns read/write status
- `openMode`: Requested open mode
- `context`: Load context for progress reporting

**Returns**: `SUCCESS` on success, error code on failure

```cpp
virtual StatusInt _WriteChanges(
    bool doFullSave,
    double timestamp,
    DgnPlatform::DesignFileHeader* hdr) override;
```

Saves changes back to the DWG/DXF file.

**Parameters**:
- `doFullSave`: Whether to perform a full save
- `timestamp`: Save timestamp
- `hdr`: Design file header

**Returns**: `SUCCESS` on success, error code on failure

##### Model Operations

```cpp
virtual StatusInt GetModelReader(
    DgnPlatform::DgnModelReader** ppStream,
    DgnModelP pCache) override;
```

Gets a model reader for streaming model data.

```cpp
virtual StatusInt _CreateCacheAndLoadHeader(
    DgnModelP& cache,
    DgnPlatform::ModelId modelIDtoRead) override;
```

Creates a model cache and loads the model header.

```cpp
virtual StatusInt DeleteModel(
    DgnPlatform::ModelId modelID) override;
```

Deletes a model from the file.

##### FileHolder Access

```cpp
FileHolder* GetDwgFileHolder();
```

Gets the associated FileHolder instance.

**Returns**: Pointer to FileHolder, or `nullptr` if not available

```cpp
StatusInt InitializeFileHolderFromSeedFile();
```

Initializes the FileHolder from a seed file.

### FileHolder

**Header**: `rDwgFileHolder.h`

Manages the AutoCAD database and provides conversion services.

#### Constructor

```cpp
FileHolder(DgnFileP dgnFile, WCharCP pName);
FileHolder(DgnFileP dgnFile, WCharCP pFileName, DgnModelP defaultModelRef);
```

**Parameters**:
- `dgnFile`: Associated DGN file
- `pName`/`pFileName`: File name
- `defaultModelRef`: Default model reference (second constructor)

#### Public Methods

##### Database Operations

```cpp
StatusInt LoadAcadDatabase();
```

Loads the AutoCAD database from file.

**Returns**: `SUCCESS` on success, error code on failure

```cpp
StatusInt LoadAcadDatabaseFromSeedDwgFile(DgnModelP defaultModel);
```

Loads database from a seed DWG file.

```cpp
StatusInt WriteFile(
    WCharCP pFileName,
    DgnFileFormatType format,
    int dxfPrecision,
    bool useVersionFromSettings,
    WCharCP pProgressComment);
```

Writes the database to a file.

**Parameters**:
- `pFileName`: Output file name
- `format`: Output format (DWG/DXF)
- `dxfPrecision`: DXF precision (for DXF format)
- `useVersionFromSettings`: Whether to use version from settings
- `pProgressComment`: Progress comment for user feedback

##### Model Management

```cpp
StatusInt ReadModelIntoCache(
    DgnModelP model,
    DgnPlatform::ModelId modelId,
    bool loadDictionary);
```

Reads a model from the AutoCAD database into DGN cache.

```cpp
bool AddModel(
    DgnPlatform::ModelId modelId,
    DgnModelType modelType,
    WCharCP modelName,
    int sheetTabOrder = -1);
```

Adds a new model to the file.

##### Symbology Access

```cpp
DwgSymbologyData* GetDwgSymbologyData();
DgnSymbologyData* GetDgnSymbologyData();
```

Gets symbology data for conversion operations.

##### Database Access

```cpp
AcDbDatabase* GetDatabase() const { return m_database; }
```

Gets the AutoCAD database instance.

## Conversion Context Classes

### ConvertToDgnContext

**Header**: `Mstn/RealDwg/rDwgConvertContext.h`

Handles conversion from DWG/DXF to DGN format.

#### Key Methods

```cpp
StatusInt ConvertDwgModelToDgn(DgnModelP model, ModelId modelId);
```

Converts a DWG model to DGN format.

```cpp
StatusInt ProcessDwgEntity(AcDbEntity* pEntity, DgnModelP model);
```

Processes an individual DWG entity for conversion.

```cpp
StatusInt MapDwgSymbologyToDgn(AcDbEntity* pEntity, DgnElementP element);
```

Maps symbology from DWG entity to DGN element.

### ConvertFromDgnContext

Handles conversion from DGN to DWG/DXF format.

#### Key Methods

```cpp
StatusInt ConvertDgnModelToDwg(DgnModelP model, ModelId modelId);
```

Converts a DGN model to DWG format.

```cpp
StatusInt ProcessDgnElement(DgnElementP element);
```

Processes an individual DGN element for conversion.

```cpp
StatusInt SaveNonCacheChanges(bool fullSave, DgnFileP dgnFile);
```

Saves non-cache changes (styles, tables, etc.) to the database.

## Symbology Classes

### BaseSymbologyData

**Header**: `rDwgSymbologyData.cpp`

Base class for symbology mapping.

#### Key Methods

```cpp
UInt32 GetColorIndex(const RgbColorDef* pColor, BaseConvertContext* context);
UInt32 GetColorIndex(UInt32 acadColorIndex, BaseConvertContext* context);
```

Maps colors between formats.

```cpp
UInt32 RemapLineStyleId(
    UInt32 currentLineStyleId,
    BaseSymbologyData* otherSymbologyData,
    BaseConvertContext* context);
```

Remaps line style IDs between symbology systems.

```cpp
void SetColorTable(const byte* colorTable);
```

Sets the color table for color mapping.

### DwgSymbologyData

DWG-specific symbology data management.

### DgnSymbologyData

DGN-specific symbology data management.

#### Specific Methods

```cpp
void ReadColorTable(DgnModelP modelRef);
```

Reads color table from a DGN model.

## Table Index Classes

### SignedTableIndex

**Header**: `rDwgTableIndex.cpp`

Provides bidirectional mapping between DGN IDs and AutoCAD handles.

#### Key Methods

```cpp
AcDbHandle GetDBHandle(Int32 dgnId);
Int32 GetDgnId(AcDbHandle dbHandle);
```

Converts between DGN IDs and AutoCAD handles.

```cpp
StatusInt AddEntry(Int32 dgnId, AcDbHandle dbHandle, bool originatedInDwg);
```

Adds a new mapping entry.

```cpp
void RemoveEntry(Int32 dgnId);
```

Removes a mapping entry.

### LayerTableIndex

Specialized table index for layer management.

```cpp
AcDbHandle GetDBHandle(UInt32 dgnId);
UInt32 GetDgnId(AcDbHandle dbHandle);
StatusInt AddEntry(UInt32 dgnId, AcDbHandle dbHandle);
```

## Extension Interfaces

### ToDgnExtension

Interface for converting DWG entities to DGN elements.

```cpp
class ToDgnExtension
{
public:
    virtual StatusInt ConvertToDgn(
        AcDbEntity* pEntity,
        DgnElementP& element,
        ConvertToDgnContext* context,
        DgnModelP model) = 0;
        
    virtual bool CanConvert(AcRxClass* pClass) = 0;
};
```

### ToDwgExtension

Interface for converting DGN elements to DWG entities.

```cpp
class ToDwgExtension
{
public:
    virtual StatusInt ConvertToDwg(
        DgnElementP element,
        AcDbEntity*& pEntity,
        ConvertFromDgnContext* context) = 0;
        
    virtual bool CanConvert(DgnElementHandlerP handler) = 0;
};
```

## Host Integration Classes

### DwgPlatformHost

**Header**: `DwgPlatformHost.h`

Base host class providing platform services.

#### Key Methods

```cpp
virtual AcDbProgressMeter* newProgressMeter() override;
virtual void warning(const ACHAR* message) override;
virtual Acad::ErrorStatus findFile(
    ACHAR* pcFullPathOut,
    int nBufferLength,
    const ACHAR* pcFilename,
    AcDbDatabase* pDb = NULL,
    FindFileHint hint = kDefault) override;
```

### DwgMstnHost

MicroStation-specific host implementation.

```cpp
void _SetMasterDwgFile(AcDbDatabase* pDatabase, DgnFileP pDgnFile);
bool _GetMasterDwgFile(AcDbDatabase*& pDatabase, DgnFileP* ppDgnFile);
```

## Utility Functions

### Framework Initialization

```cpp
RDWG_EXPORTED void RealDwgFileIO::Initialize();
```

Initializes the RealDwgFileIO framework. Must be called before using the framework.

### File Type Factory

```cpp
RDWG_EXPORTED DgnFileTypeP dwgFileIO_getFileType(int fileType);
```

Factory function for creating file type handlers.

**Parameters**:
- `fileType`: File format type (DWG or DXF)

**Returns**: Pointer to file type handler, or `nullptr` for unsupported types

## Error Codes

### Common Status Codes

```cpp
enum StatusCodes
{
    SUCCESS = 0,
    BSIERROR = -1,
    DWGOPEN_STATUS_BadFile,
    DWGOPEN_STATUS_FileNotFound,
    DWGOPEN_STATUS_AccessDenied,
    DWGOPEN_STATUS_FileLocked,
    DWGOPEN_STATUS_OutOfMemory
};
```

### AutoCAD Error Codes

The framework also uses AutoCAD's `Acad::ErrorStatus` enumeration:

```cpp
Acad::eOk                    // Success
Acad::eFileNotFound         // File not found
Acad::eFileAccessErr        // File access error
Acad::eInvalidInput         // Invalid input parameters
Acad::eOutOfMemory          // Out of memory
```

## Usage Examples

### Basic File Loading

```cpp
// Initialize framework
RealDwgFileIO::Initialize();

// Open DWG file
DgnFilePtr dgnFile;
StatusInt status = DgnFile::CreateFromFileName(
    dgnFile, L"example.dwg", DgnFileOpenMode::ReadOnly);

if (status == SUCCESS) {
    // Access models
    DgnModelPtr model = dgnFile->LoadRootModelForRead();
    // Process elements...
}
```

### File Conversion

```cpp
// Load DWG file
DgnFilePtr dwgFile;
DgnFile::CreateFromFileName(dwgFile, L"input.dwg", DgnFileOpenMode::ReadOnly);

// Save as DGN
StatusInt status = dwgFile->ProcessChanges(DgnSaveReason::UserSave);
```

### Custom Entity Conversion

```cpp
class CustomLineExtension : public ToDgnExtension
{
public:
    StatusInt ConvertToDgn(
        AcDbEntity* pEntity,
        DgnElementP& element,
        ConvertToDgnContext* context,
        DgnModelP model) override
    {
        AcDbLine* pLine = AcDbLine::cast(pEntity);
        if (!pLine) return BSIERROR;
        
        // Convert geometry
        AcGePoint3d start, end;
        pLine->getStartPoint(start);
        pLine->getEndPoint(end);
        
        // Create DGN element
        LineStringElement lineElement;
        DPoint3d points[2] = {
            {start.x, start.y, start.z},
            {end.x, end.y, end.z}
        };
        lineElement.SetVertices(points, 2);
        
        element = lineElement.CreateElement(model);
        return SUCCESS;
    }
    
    bool CanConvert(AcRxClass* pClass) override
    {
        return pClass == AcDbLine::desc();
    }
};
```

This API reference provides the essential interfaces and methods needed to work with the RealDwgFileIO framework. For detailed implementation examples, refer to the source code and test cases.

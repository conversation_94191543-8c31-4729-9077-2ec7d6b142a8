/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/dgnImage.cpp $
|
|  $Copyright: (c) 2021 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

// This file is #included in rDwgDgnExtension.cpp

/*=================================================================================**//**
* @bsiclass                                                     <PERSON>.Bentley   10/08
+===============+===============+===============+===============+===============+======*/
class           ToDwgExtRasterAttachment : public ToDwgExtension
{

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                    Marc.Bedard                     09/2011
+---------------+---------------+---------------+---------------+---------------+------*/
bool IsRasterAttachmentSupportedInDwg
(                      /*<= Return true if file type is supported in DWG workmode and should be exported*/
ConvertFromDgnContextR      context,
WCharCP                     pi_schemeTypeName,      /*=> */
WCharCP                     pi_scFilePath,          /*=> */
ImageFileFormat             pi_fileType
) const
    {
    bool                retValue = true;

    BeAssert (pi_scFilePath != nullptr && L"Null image sourece file path!");

    if (!context.GetSettings().IsImageFileFormatSupported(pi_schemeTypeName,pi_fileType))
        {
        WString     fileName, ext;

        BeFileName::ParseName (NULL, NULL, &fileName, &ext, pi_scFilePath);
        BeFileName::BuildName (fileName, NULL, NULL, fileName.GetWCharCP(), ext.GetWCharCP());

        // the file type isn't supported in DWG and we're in a DGN file, so this file shouldn't be exported
        RealDwgUtil::AddToMessageCenter (OutputMessagePriority::Warning, REALDWGMESSAGECENTER_RasterAttachmentDiscarded, false, fileName.GetWCharCP(), NULL);

        retValue = false;
        }

    return retValue;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand    03/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetUVector
(
DPoint3dP                   pio_uVector,
double                      pi_dWidth
) const
    {
    /* ACAD U and V vectors are used to set the image dimension in pixels */
    pio_uVector->x = pio_uVector->x * pi_dWidth;
    pio_uVector->y = pio_uVector->y * pi_dWidth;
    pio_uVector->z = pio_uVector->z * pi_dWidth;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand    03/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetUVector
(
DPoint3dP                   pio_uVector,
double                      pi_dWidth,
double                      pi_dUORWidth,
double                      pi_dAffinity,
double                      pi_dRotation
) const
    {
    /* WIP */
    /* For the time being, compute U vector only if the image is the XY plane */
    /* Otherwise leave as is */
    if (pio_uVector->z == (double)0)
        {
        pio_uVector->x = pi_dUORWidth;
        pio_uVector->y = 0.0;
        pio_uVector->z = 0.0;

        /* Apply rotation */
        bsiDPoint3d_rotateXY (pio_uVector, pio_uVector, pi_dRotation);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand    03/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetVVector
(
DPoint3dP                   pio_vVector,
double                      pi_dHeight
) const
    {
    /* ACAD U and V vectors are used to set the image dimension in pixels */
    pio_vVector->x = pio_vVector->x * pi_dHeight;
    pio_vVector->y = pio_vVector->y * pi_dHeight;
    pio_vVector->z = pio_vVector->z * pi_dHeight;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand    03/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetVVector
(
DPoint3dP                   pio_vVector,
double                      pi_dHeight,
double                      pi_dUORHeight,
double                      pi_dAffinity,
double                      pi_dRotation
) const
    {
    /* WIP */
    /* For the time being, compute V vector only if the image is the XY plane */
    /* Otherwise leave as is */
    if (pio_vVector->z == (double)0)
        {
        double  vecMag;

        pio_vVector->x = 0.0;
        pio_vVector->y = pi_dUORHeight;

        /* apply affinity */
        vecMag =  bsiDPoint3d_magnitude (pio_vVector);
        pio_vVector->x = -vecMag * sin (pi_dAffinity);
        pio_vVector->y = vecMag * cos (pi_dAffinity);

          /* Apply rotation */
        bsiDPoint3d_rotateXY (pio_vVector, pio_vVector, pi_dRotation);
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    StephanePoulin  1/2006
+---------------+---------------+---------------+---------------+---------------+------*/
void                        GetDwgGeorefFromRasterRefGeoref
(
DPoint3dR                   origin,
DPoint3dR                   uVector,
DPoint3dR                   vVector,
WCharCP                     pName,
RasterFileQuickInfo*        ptRasterQuickInfo,
DgnModelRefP                modelRef
) const
    {
    if (ptRasterQuickInfo!=NULL)
        {
        if (ptRasterQuickInfo->fileFormat == IMAGEFILE_TIFFINTGR || ptRasterQuickInfo->fileFormat == IMAGEFILE_TIFF)
            {
            Transform givenMatrix;
            DPoint3d  nVector = {0.0, 0.0, 1.0};
            bsiTransform_initFromOriginAndVectors(&givenMatrix, &origin, &uVector, &vVector, &nVector);

            Transform sloMatrix;
            if (SUCCESS == mdlRaster_getScanLineOrientationMatrix(&sloMatrix, pName,modelRef))
                {
                Transform delta;
                bsiTransform_multiplyTransformTransform(&delta, &givenMatrix, &sloMatrix);

                uVector.x = delta.form3d[0][0];
                uVector.y = delta.form3d[1][0];
                uVector.z = delta.form3d[2][0];
                vVector.x = delta.form3d[0][1];
                vVector.y = delta.form3d[1][1];
                vVector.z = delta.form3d[2][1];
                origin.x  = delta.form3d[0][3];
                origin.y  = delta.form3d[1][3];
                origin.z  = delta.form3d[2][3];
                }
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* Will synchronnize clip boundary from raster element to DWG image
* @bsimethod                                                    MarcBedard   11/2005
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SynchClipBoundaryInDWGImage
(
AcDbRasterImage*            pImage,
size_t                      pi_piNumClipVertices,  /* => number of clip vertices */
DPoint2dP                   pi_ptClipVertices,     /* => clip vertices in pixel coords */
unsigned short              pi_pujClipStatus,      /* => clipping present ?*/
unsigned short              pi_pujClipType,        /* => clip boundary type */
DPoint2dP                   pi_ptExtent,           /* => image extent in Pixels */
bool                        pi_isClipMask,         /* => if true, clip must be inverted*/
ConvertFromDgnContextR      context
) const
    {
    bool mustSynchClip(true);
    if (!ToDgnExtRasterAttachment::IsDWGClipValid(pImage,pi_ptExtent,*context.GetModel()))
        {
        AcGePoint2dArray    geClipBoundary;

        /* Get the clipping polygon from the clipboundary */
        geClipBoundary = pImage->clipBoundary ();
        int numClipPoints = geClipBoundary.length();
        bool DWGIsClipped = Adesk::kTrue == pImage->isClipped();
        bool DGNIsClipped = (pi_pujClipStatus == AD_IMAGE_CLIPPED);
        if (DGNIsClipped && DWGIsClipped)
            {
            //We have a clip that replace and invalid clip in DWG -> we should synch
            mustSynchClip = true;
            }
        else if (!DGNIsClipped && DWGIsClipped)
            {
            //We don't have any clip and an invalid clip in DWG -> we don't synch
            //because we remove invalid clip when converting to DGN.
            //This is an arbitrary condition, in this particular case, we don't want to "broke"
            //clip in DWG even if we cannot see it in DGN
            mustSynchClip = false;
            }
        else if (DGNIsClipped && !DWGIsClipped)
            {
            //We have a clip that replace no clip in DWG -> we should synch
            mustSynchClip = true;
            }
        else if (!DGNIsClipped && !DWGIsClipped)
            {
            //No clip to replace, -> don't synch
            mustSynchClip = false;
            }
        }
    else
        {
        //If clip was valid, it should always be replace
        mustSynchClip = true;
        }

    if (mustSynchClip)
        {
        /* TR 86867 DWG clip boundary range must be within or equal to the image's extent */
        /* Also invert from "lower left MS" to "upper left ACAD" coordinates */
        context.AdjustDWGImageClipBoundary (pi_ptClipVertices, &pi_piNumClipVertices, pi_ptExtent, pi_pujClipType, pi_pujClipStatus);

        AcGePoint2dArray clipArray;

        for (size_t i(0); i < pi_piNumClipVertices; i++)
            {
            clipArray.append (RealDwgUtil::GePoint2dFromDPoint2d (pi_ptClipVertices[i]));
            }
         if (pi_piNumClipVertices > 0)
            {
            AcDbRasterImage::ClipBoundaryType clipType(pi_piNumClipVertices>2 ? AcDbRasterImage::kPoly : AcDbRasterImage::kRect);
            pImage->setClipBoundary (clipType, clipArray);
            pImage->setDisplayOpt(AcDbRasterImage::kClip, AD_IMAGE_NOT_CLIPPED != pi_pujClipStatus);
            pImage->setClipInverted(pi_isClipMask ? Adesk::kTrue : Adesk::kFalse);
            }
        }
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand  11/2003
+---------------+---------------+---------------+---------------+---------------+------*/
AcDbObjectId                CreateNewImageDefObject
(
WCharCP                   pImageFullPath,
const WString&              imageNameWString,
AcDbDictionary*             pImageDict,
ElementHandleR                 type94ElemHandle
) const
    {
    //TR #271568; SetSourceFileName required access to image source file and should not be open by rasterlib
    // we will close and reopen the source file
    RasterSourceUnloadGuard __guardAgainSharingViolation(&type94ElemHandle);

    WString                 wImageFullPath = (wcslen(pImageFullPath)==0) ? imageNameWString : WString(pImageFullPath);
    AcDbRasterImageDef*     pImageDef = new AcDbRasterImageDef ();
    Acad::ErrorStatus       es = pImageDef->setSourceFileName (wImageFullPath.c_str());


    // Add imagedef to image dictionary using the image filename as the search key.
    AcDbObjectId        objectId;
    es = pImageDict->setAt(imageNameWString.c_str(), pImageDef, objectId);
    if (Acad::eOk != es)
        {
        delete pImageDef;
        return AcDbObjectId::kNull;
        }

    pImageDef->close ();

    return objectId;
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand    03/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        CreateDbRasterImageDef
(
AcDbRasterImage*            pImage,
WCharCP                     pImagePath,
WCharCP                     pImageFullPath,
WCharCP                     pLogicalName,
Dpoint2d                    size,
bool                        removeFirst,
ElementHandleR              type94ElemHandle,
ConvertFromDgnContextR      context
) const
    {
    AcDbObjectId            imageDefId;

    WString                 wFileName;
    WString                 wLogicalName (pLogicalName);

    // Get Dictionary Id, or create one if not present
    Acad::ErrorStatus       errorStatus = Acad::eOk;
    AcDbDatabase*           database = context.GetFileHolder().GetDatabase ();
    AcDbObjectId            imageDictId;
    if (0 == (imageDictId = AcDbRasterImageDef::imageDictionary (database)))
        errorStatus = AcDbRasterImageDef::createImageDictionary (database, imageDictId);
    if (Acad::eOk != errorStatus)
        {
        DIAGNOSTIC_PRINTF ("Failed to create image def dictionary %ls. [%ls]\n", pLogicalName, acadErrorStatusText(errorStatus));
        return;
        }

    // Open the image dictionary.
    AcDbDictionaryPointer pImageDict (imageDictId, AcDb::kForWrite);
    if (Acad::eOk != pImageDict.openStatus())
        {
        DIAGNOSTIC_PRINTF ("Failed to open image def dictionary %ls. [%ls]\n", pLogicalName, acadErrorStatusText(pImageDict.openStatus()));
        return;
        }

    if (!wLogicalName.empty() && pImageDict->has(wLogicalName.c_str()))
        {
        wFileName = wLogicalName;
        }
    else
        {
        // Get image name to look for in dictionary
        BeFileName::ParseName (NULL, NULL, &wFileName, NULL, pImagePath);
        }

    if (removeFirst)
        {
        pImageDict->removePersistentReactor (pImage->objectId ());
        pImageDict->remove (wFileName.c_str());
        }

    // dictionary name cannot end with a space character:
    if (wFileName.empty())
        {
        wFileName.assign (L"__");
        }
    else
        {
        WString::iterator   lastChar = wFileName.end() - 1;
        if (L' ' == *lastChar)
            *lastChar = L'_';
        }

    // Check if image name is already in use.
    bool created(false);
    while (!created)
        {
        if (!pImageDict->has(wFileName.c_str()))
            {// name not in use
            // Create new image def object
            imageDefId = CreateNewImageDefObject (pImageFullPath, wFileName, pImageDict,type94ElemHandle);
            created=true;
            }
        else
            {
            // Get the imageDef in the dictionary from the image name
            AcDbObject*       pTmpObject;
            AcDbRasterImageDef* pImageDef;
            if ( (Acad::eOk == pImageDict->getAt (wFileName.c_str(), pTmpObject, AcDb::kForRead)) && (NULL != (pImageDef = AcDbRasterImageDef::cast (pTmpObject))) )
                {
                WCharCP   sourceFileName = pImageDef->sourceFileName ();
                WString wSourceFilename(sourceFileName);
                WString wImagePath(pImagePath);
                WString wImageFullPath(pImageFullPath);

                bool isImagePathEqual(wImagePath.CompareToI(wSourceFilename)==0);
                bool isFullImagePathEqual(wImageFullPath.CompareToI(wSourceFilename)==0);

                // Validate this image using its size and location, with the entry in the dictionary
                if (pImageDef->size().x != size.x || pImageDef->size().y != size.y || (!isImagePathEqual && !isFullImagePathEqual)  )
                    {
                    // This is a different image with a name already in the dictionnary
                    AcString szfileNameOld (wFileName.GetWCharCP());
                    AcString szFileNameNew(RealDwgUtil::ResolveDuplicatedName (szfileNameOld));
                    wFileName = WString(szFileNameNew.kwszPtr());
                    }
                else
                    {
                    imageDefId = pImageDef->objectId ();
                    created=true;
                    }
                pImageDef->close();
                }
            }
        }

    pImage->setImageDefId(imageDefId);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand    03/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        CreateDbRasterImageDefReactor
(
AcDbRasterImage*            pImage,
ElementHandleCR             type94Element,
ConvertFromDgnContextR      context
) const
    {
    AcDbDatabase*           database = context.GetFileHolder().GetDatabase ();    
    AcDbRasterImageDef*     pImageDef = NULL;
    if (Acad::eOk == acdbOpenObject(pImageDef, pImage->imageDefId(), AcDb::kForWrite))
        {
        AcDbRasterImageDefReactor*  pDbRasterImageDefReactor = new AcDbRasterImageDefReactor();

        // Link it to the definition object.
        AcDbObjectId        reactorId;
        if (Acad::eOk == database->addAcDbObject(reactorId, pDbRasterImageDefReactor))
            {
            // Set Reactor Id.
            pImage->setReactorId (reactorId);

            // Add the database resident object to the reactor list of the object.
            pImageDef->addPersistentReactor (reactorId);

            // DWG image may not have been checked in the database yet - it has to be the owner of the new reactor:
            if (!pImage->objectId().isValid())
                context.AddEntityToCurrentBlock (pImage, type94Element.GetElementId());

            // Add image ID as the owner of the image def reactor.
            pDbRasterImageDefReactor->setOwnerId (pImage->objectId());

            pImageDef->close ();
            pDbRasterImageDefReactor->close ();
            }
        else
            {
            pImageDef->close ();
            delete pDbRasterImageDefReactor;
            DIAGNOSTIC_PRINTF ("Error creating raster image def reactor.");
            return;
            }
        }

    // add raster variables
    AcDbRasterVariables* pRVars = AcDbRasterVariables::openRasterVariables (AcDb::kForWrite, database);
    if (NULL == pRVars)
        {
        DIAGNOSTIC_PRINTF ("Failed to open AcDbRasterVariables\n");
        return;
        }
    pRVars->setImageFrame(AcDbRasterVariables::kImageFrameAbove);
    pRVars->setImageQuality(AcDbRasterVariables::kImageQualityHigh);
//    pRVars->setUserScale(AcGiRasterImage::kMeter);
    pRVars->close ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand    03/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void                        NewImageAttach
(
AcDbRasterImage*            pImage,
WCharCP                     pImagePath,
WCharCP                     pImageFullPath,
WCharCP                     pLogicalName,
Dpoint2d                    size,
bool                        removeFirst,
ElementHandleR              type94ElemHandle,
ConvertFromDgnContextR      context
) const
    {
    // set image definition object.
    CreateDbRasterImageDef(pImage, pImagePath, pImageFullPath, pLogicalName, size, removeFirst,type94ElemHandle, context);

    // Create image reactor.
    CreateDbRasterImageDefReactor (pImage, type94ElemHandle, context);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand  02/2004
+---------------+---------------+---------------+---------------+---------------+------*/
void                        SetForegroundColor
(
RgbColorDef                 *pForegroundColor,
AcDbRasterImage*            pImage,
ConvertFromDgnContextR      context
) const
    {
    int             colorIndex = pImage->colorIndex();
    int             newColorIndex = context.GetFileHolder().GetDwgSymbologyData()->GetColorIndex (pForegroundColor, context);

    if (pImage->isNewObject ())
        {
        pImage->setColorIndex ((Adesk::UInt16)newColorIndex, false);
        }
    else
        {
        RgbColorDef                 color;

        // Get the foreground color from the object
        GetForegroundColor (&color, pImage, context.GetFileHolder().GetDwgSymbologyData());

        // Compare it to the color from the type90
        if (0 != memcmp (pForegroundColor, &color, sizeof (RgbColorDef)))
            {
            pImage->setColorIndex ((Adesk::UInt16)newColorIndex, false);
            }
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                  Simon.Normand   04/2003
+---------------+---------------+---------------+---------------+---------------+------*/
static void RemoveFileSchemeType
(
WStringR pio_pFilename,
WStringR pio_pFilespec
)
    {
    WCharCP pStr;

    if ((pStr = wcsstr(pio_pFilename.c_str(), L"file://")) != NULL)
        {
        pio_pFilename = WString(pStr + wcslen(L"file://"));
        pStr = NULL;
        }
    if ((pStr = wcsstr(pio_pFilespec.c_str(), L"file://")) != NULL)
        {
        pio_pFilespec = WString(pStr + wcslen(L"file://"));
        pStr = NULL;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    SimonNormand  05/2004
+---------------+---------------+---------------+---------------+---------------+------*/
static unsigned short GetDwgShowImage
(
IRasterAttachmentQuery* pIRasterAttachmentQuery,
ElementHandleCR         elemHandle
)
    {
    unsigned short displayOn = pIRasterAttachmentQuery->GetViewState(elemHandle,0); // Init value to default, raster is ON in the first view

    // Get the env. variable and its value
    int     value;
    WString buffer;
    if ( (SUCCESS == ConfigurationManager::GetVariable(buffer, MS_RASTER_VIEWSAVEASDWG)) && !buffer.empty() && (0 != (value = BeStringUtilities::Wtoi (buffer.c_str()))) )
        {
        switch (value)
            {
            case 1:
                {
                displayOn = pIRasterAttachmentQuery->GetViewState(elemHandle,0);
                }
                break;
            case 2:
                {
                displayOn = pIRasterAttachmentQuery->GetViewState(elemHandle,1);
                }
                break;
            case 3:
                {
                displayOn = pIRasterAttachmentQuery->GetViewState(elemHandle,2);
                }
                break;
            case 4:
                {
                displayOn = pIRasterAttachmentQuery->GetViewState(elemHandle,3);
                }
                break;
            case 5:
                {
                displayOn = pIRasterAttachmentQuery->GetViewState(elemHandle,4);
                }
                break;
            case 6:
                {
                displayOn = pIRasterAttachmentQuery->GetViewState(elemHandle,5);
                }
                break;
            case 7:
                {
                displayOn = pIRasterAttachmentQuery->GetViewState(elemHandle,6);
                }
                break;
            case 8:
                {
                displayOn = pIRasterAttachmentQuery->GetViewState(elemHandle,7);
                }
                break;
            case 9:
                {
                // At least one view is ON
                displayOn = 0;
                for (int view=0; view<8; view++)
                    {
                    if (pIRasterAttachmentQuery->GetViewState(elemHandle,view))
                        {
                        displayOn = 1;
                        break;
                        }
                    }
                }
                break;
            case 10:
                {
                // All views must be ON
                displayOn = 1;
                for (int view=0; view<8; view++)
                    {
                    if (!pIRasterAttachmentQuery->GetViewState(elemHandle,view))
                        {
                        displayOn = 0;
                        break;
                        }
                    }
                }
                break;
            }
        }

    return displayOn;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          12/12
+---------------+---------------+---------------+---------------+---------------+------*/
AcRxClass*      GetTypeNeeded (DgnRasterP raster) const
    {
    RasterSourceCP      rasterSource = NULL == raster ? NULL : raster->GetRasterSourceCP ();
    if (NULL != rasterSource && IMAGEFILE_PDF == rasterSource->GetRasterFile().GetFileFormat())
        return  AcDbPdfReference::desc ();
    else
        return  AcDbRasterImage::desc ();
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Don.Fu          09/16
+---------------+---------------+---------------+---------------+---------------+------*/
void            CopyOutRasterFileAndRePath (WCharP fullPath, WStringR savePath, ConvertFromDgnContextR context) const
    {
    /*-----------------------------------------------------------------------------------
    Try to honor user request to copy out raster file to destination.  Replace the full path
    that will be used to create an AcDbRasterImageDef with the output path upon a successful
    file copy.  If the savePath is also a full path, update it as well.
    -----------------------------------------------------------------------------------*/
    if (nullptr == fullPath || 0 == fullPath[0] || !context.GetSettings().CopyRasterToOutputFolder() || savePath.EqualsI(fullPath))
        return;

    BeFileName  outputPath (BeFileName::GetDirectoryName(context.GetOutputFileName().c_str()).c_str());
    BeFileName  pathToBeSaved (savePath.c_str());

    bool    isAbsolutePath = pathToBeSaved.IsAbsolutePath ();

    bool    isNotChildFolder (false);
    
    if (wcslen (savePath.c_str()) > 3 && 0 == wcsncmp(L"..\\", savePath.c_str(), 3))
        isNotChildFolder = true;

    // build a full file spec for the output file:
    if (isAbsolutePath || isNotChildFolder)
        {
        // append only root file name to output path:
        BeFileName  rootName (BeFileName::GetFileNameAndExtension(fullPath).c_str());
        outputPath.AppendToPath (rootName.c_str());
        }
    else
        {
        // try to resolve the relative path to the output path:
        BeFileName  resolvedPath;
        if (BSISUCCESS == BeFileName::ResolveRelativePath(resolvedPath, pathToBeSaved.c_str(), outputPath.c_str()))
            {
            // ensure the resolved path exists:
            BeFileName  testPath(BeFileName::GetDirectoryName(resolvedPath.c_str()).c_str());
            if (!BeFileName::DoesPathExist(testPath.c_str()) && BeFileNameStatus::Success != BeFileName::CreateNewDirectory (testPath.c_str()))
                {
                DIAGNOSTIC_PRINTF ("Failed creating a new path, %ls, to copy out a raster file!", testPath.c_str());
                return;
                }

            outputPath = resolvedPath;
            }
        else
            {
            DIAGNOSTIC_PRINTF ("Cannot resolve raster relative path!", pathToBeSaved.c_str());
            return;
            }
        }

    // copy out the raster file if not exists and re-path proposed output file name as necessary:
    if (BeFileName::DoesPathExist(outputPath.c_str()) || BeFileNameStatus::Success == BeFileName::BeCopyFile(fullPath, outputPath.c_str()))
        {
        if (!outputPath.EqualsI(fullPath))
            wcsncpy (fullPath, outputPath.c_str(), sizeof(RasterFileQuickInfo::szFileSpec) - 1);
        if (isAbsolutePath || isNotChildFolder)
            savePath = outputPath;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      07/2002
+---------------+---------------+---------------+---------------+---------------+------*/
virtual RealDwgStatus   ToObject
(
ElementHandleR          elemHandle,         // The DGN element to be converted to DWG.
AcDbObjectP&            acObject,           // The object created, if any.
AcDbObjectP             existingObject,     // The original DWG Entity, if any.
ConvertFromDgnContextR  context             // The context
) const override
    {
    WString             fileName;
    WString             fileSpec;
    WString             sourceFileName;
    WString             savedSourceFilename;
    WString             logicalName;
    DPoint3d            pt0;
    DPoint2d            clipVertices[RasterReference_MAXVERTICIESINCOMPONENT];
    double              dgnScaleFactor(0);
    double              width(0);
    double              height(0);
    double              rotAngle (0);
    double              affAngle (0);
    RasterFileQuickInfo tRasterQuickInfo;
    short               contrast (0), brightness (0);
    unsigned short      fade(0), clipping, clipBoundType, displayProps (0);
    size_t              numClipVerts (2);
    RealDwgStatus       status;
    bool                imageExtentIsValid(true);
    DVec3d              uVector;
    DVec3d              vVector;
    RgbColorDef         foreground;

    /* Only convert changes that cames from type 94 */
    if (RASTER_FRAME_ELM != elemHandle.GetElementType())
        return WrongMstnElementType;

    memset (&foreground, 255, sizeof (foreground));

    DgnRasterP rasterP = DgnRasterCollection::GetRastersR(elemHandle.GetModelRef()).FindP(elemHandle.GetElementRef());

    //ADO #660351: External PDF attachments not displaying in DWG.
    if (NULL == rasterP)
        {
        T_HOST.GetRasterAttachmentAdmin()._LoadRasters(*elemHandle.GetModelRef());
        rasterP = DgnRasterCollection::GetRastersR(elemHandle.GetModelRef()).FindP(elemHandle.GetElementRef());
        }

    if (rasterP!=NULL)
        {
        //If the source was closed in ConvertFromDgnContext::SaveNonCacheChanges, it should be reopened here.
        if (rasterP->GetRasterSourceCP() == NULL || (rasterP->GetRasterSourceCP()->GetRasterFile().GetStatus() == RASTERFILE_STATUS_Closed))
            {
            mdlRaster_fileSourceOpen(rasterP,EVENT_SILENT);
            }
        }

    // find the type needed, and instantiate it.
    acObject = context.InstantiateOrUseExistingObject (existingObject, this->GetTypeNeeded(rasterP));

    // a PDF raster will be converted to a PDF underlay
    AcDbPdfReference*   pdfUnderlay = AcDbPdfReference::cast (acObject);
    if (NULL != pdfUnderlay)
        {
        ConvertPdfRasterToPdfUnderlay   toUnderlay (&context, &elemHandle);
        return  toUnderlay.Convert (pdfUnderlay, rasterP);
        }
    // other types of raster will be converted to AcDbRasterImage
    AcDbRasterImage*    pImage = AcDbRasterImage::cast (acObject);
    if (NULL == pImage)
        return  NullObject;

    DVec3d              translation;
    context.GetTransformToDGN().getTranslation (&translation);

    IRasterAttachmentQuery* pIRasterAttachmentQuery = dynamic_cast <IRasterAttachmentQuery*> (&elemHandle.GetHandler ()); 
    if (NULL == pIRasterAttachmentQuery)
        return MstnElementUnacceptable;

    fileName = pIRasterAttachmentQuery->GetFilename(elemHandle);
    fileSpec = pIRasterAttachmentQuery->GetFilespec(elemHandle);
    RemoveFileSchemeType(fileName,fileSpec);
    if (rasterP!=NULL)
        {
        //If raster is open, use effective value.
        fileSpec = rasterP->GetOpenParams().GetFilespec ();
        fileName = rasterP->GetOpenParams().GetFilename ();
        }

    logicalName = pIRasterAttachmentQuery->GetLogicalName(elemHandle);
    Transform matrix;
    pIRasterAttachmentQuery->GetTransform(elemHandle,matrix);
    DVec3d translation3d;
    matrix.GetTranslation(translation3d);
    /* offset the global origin*/
    pt0.x = translation3d.x - (translation.x);
    pt0.y = translation3d.y - (translation.y);
    pt0.z = translation3d.z - (translation.z);

    /* Setup the transformation matrix to apply scaling */
    Transform   tMatrix = Transform::FromIdentity ();
    double      scaleToDgn = context.GetScaleFromDGN ();
    tMatrix.ScaleMatrixColumns (scaleToDgn, scaleToDgn, scaleToDgn);
    tMatrix.Multiply (pt0);

    DPoint2d extentUOR(pIRasterAttachmentQuery->GetExtent(elemHandle));
    width = extentUOR.x * context.GetScaleFromDGN ();
    height = extentUOR.y * context.GetScaleFromDGN ();
    matrix.GetMatrixColumn(uVector,0);
    matrix.GetMatrixColumn(vVector,1);

    /* scale u and v vectors */
    uVector.Scale (context.GetScaleFromDGN());
    vVector.Scale (context.GetScaleFromDGN());


    /* Set display props */
    displayProps = GetDwgShowImage (pIRasterAttachmentQuery,elemHandle) << AD_IMAGE_DISPPROPS_SHOWIMAGE_OFFSET;

    displayProps |= 1 << AD_IMAGE_DISPPROPS_SHOWNONALIGNED_OFFSET;
    displayProps |= (unsigned short)pIRasterAttachmentQuery->GetClipState(elemHandle) << AD_IMAGE_DISPPROPS_USECLIP_OFFSET;
    displayProps |= (unsigned short)pIRasterAttachmentQuery->GetTransparencyState(elemHandle) << AD_IMAGE_DISPPROPS_TRANSON_OFFSET;

    /* Set layer, contrast, brightness, fade */
    contrast = pIRasterAttachmentQuery->GetContrast(elemHandle);
    brightness = pIRasterAttachmentQuery->GetBrightness(elemHandle);
    fade = pIRasterAttachmentQuery->GetImageTransparencyLevel(elemHandle);

    IRasterAttachmentQuery::RgbFromColorIndexInModel(foreground,*context.GetModel(),pIRasterAttachmentQuery->GetForegroundColor(elemHandle));

    clipping = AD_IMAGE_NOT_CLIPPED;
    clipBoundType = AD_IMAGE_CLIPBOUND_RECT;  /* The clip type is always rectangular even if no clip present */
    numClipVerts = 2;                         /* There are always at least 2 clip vertices */
    RasterClipPropertiesPtr pClipProperties = pIRasterAttachmentQuery->GetClipProperties(elemHandle);
    bool isClipMask;
    bool allowMaskClip(true);
    context.CreateDwgImageClipBoundary(*pClipProperties,&numClipVerts,clipVertices,&clipping,&clipBoundType,&isClipMask,allowMaskClip);


    /* Need to extract some info from raw image file, ie size in pixels, DPI, etc.  */
    /* This function call will return -1 when the image file being openened doesn't */
    /* exist (ie incomplete path+name) or is of an unsupported format           */
    /* When this happens, simply return false if it is a new image attachment, or   */
    /* if is an already existing DWG entity, don't change any of the location       */
    /* information that depends on the extent of the image in pixels...             */

    /* This was required for ProjectWise integration. When running the PW ScanRefs  */
    /* tool, we need to rewrite the attachment path so it "fits" the PW directory   */
    /* structure. If a raster image attached to a DWG file was not checked out when */
    /* running ScanRefs on the DWG file, the attachment could never be rewritten,   */
    /* since the file could not be read on disk. So if the file cannot be read on   */
    /* disk, we never change the location info but are still allowed to change the  */
    /* corresponding file name in the image dictionary                              */

    /* If fileSpec is not defined, use file name to get file properties,    */
    /* but file name will always be stored in the Dwg image insert          */

    memset(&tRasterQuickInfo,0,sizeof(tRasterQuickInfo));

    if (rasterP!=NULL)
        {
        WStringCR   openFileSpec = rasterP->GetOpenParams().GetFilespec ();
        WStringCR   openFileName = rasterP->GetOpenParams().GetFilename ();
        }

    if  (SUCCESS == mdlRaster_fileInfoMinimalGetExt (&tRasterQuickInfo,
        fileSpec.empty() ? fileName.c_str() : fileSpec.c_str(),
        context.GetModel()))
        {
        GetDwgGeorefFromRasterRefGeoref(pt0, uVector, vVector, fileSpec.empty() ? fileName.c_str() : fileSpec.c_str(),&tRasterQuickInfo,elemHandle.GetModelRef());

        /* if the file is an http, we must have its scheme in the path */
        if (tRasterQuickInfo.szFileSpec[0] && _wcsicmp(SCHEMETYPE_HTTP, tRasterQuickInfo.szSchemeType) == 0)
            {
            // Full path must have http:://
            WChar tmpStr[MAX_LINKAGE_STRING_BYTES] = {0};
            wcscpy (tmpStr,  tRasterQuickInfo.szFileSpec);
            wsprintf (tRasterQuickInfo.szFileSpec, L"%ls://%ls", SCHEMETYPE_HTTP, tmpStr);
            }

        /* Get the save unsupported file value  and return false if this raster is unsupported */
        /* Do this test only if it is a new raster attachment, otherwise treat it as "normal"  */
        if (pImage->isNewObject ()  &&
            (false == IsRasterAttachmentSupportedInDwg(context, tRasterQuickInfo.szSchemeType,
            (0 == tRasterQuickInfo.szFileSpec[0]) ?
            fileName.c_str() : tRasterQuickInfo.szFileSpec, tRasterQuickInfo.fileFormat)))
            {
            return UnsupportedRaster;
            }
        status = RealDwgSuccess;
        }
    // We've failed to retrieve the file information
    else if (pImage->isNewObject ())
        {
        /* It is a new image for which no info is available  */
        /* concerning the image's size in pixels, which can lead to invalid */
        /* georeference                                                     */
        /*try using u and v vector */
        if (bsiDPoint3d_magnitude (&uVector) > 0.0 && bsiDPoint3d_magnitude (&vVector)> 0.0)
            {
            tRasterQuickInfo.extent.x = roundDoubleToLong(width/bsiDPoint3d_magnitude(&uVector));
            tRasterQuickInfo.extent.y = roundDoubleToLong(height/bsiDPoint3d_magnitude(&vVector));
            }
        else
            {
            //V or U vector are invalid, assume 1.
            tRasterQuickInfo.extent.x = width;
            tRasterQuickInfo.extent.y = height;
            }

        status = RealDwgSuccess;
        }
    else
        {
        /* If it is an already existing image, simply set the flag that controls   */
        /* location settings to false and continue                                 */
        imageExtentIsValid = false;
        status = RealDwgSuccess;
        }

    AcDbRasterImageDef*     pImageDef = NULL;
    if (!pImage->isNewObject())
        {
        // Not a new attachment, get the saved file path from the  DWG attachment
        if (Acad::eOk == acdbOpenObject(pImageDef, pImage->imageDefId(), AcDb::kForWrite))
            savedSourceFilename = pImageDef->sourceFileName();
        else
            DIAGNOSTIC_PRINTF ("Failed opening AcDbRasterImageDef id=%I64d.\n", context.ElementIdFromObjectId(pImage->imageDefId()));
        }

    context.ImagePathFromDgnPath (sourceFileName, tRasterQuickInfo.szFileSpec, fileName.c_str(), savedSourceFilename.c_str(), elemHandle.GetModelRef());

    if (pImage->isNewObject ())
        {
        //newly inserted image
        this->CopyOutRasterFileAndRePath (tRasterQuickInfo.szFileSpec, sourceFileName, context);

        NewImageAttach (pImage, sourceFileName.c_str(), tRasterQuickInfo.szFileSpec, logicalName.c_str(), tRasterQuickInfo.extent, false,elemHandle, context);

        if (Acad::eOk != acdbOpenObject(pImageDef, pImage->imageDefId(), AcDb::kForWrite))
            DIAGNOSTIC_PRINTF ("Failed opening AcDbRasterImageDef id=%I64d.\n", context.ElementIdFromObjectId(pImage->imageDefId()));
        }
    else if (imageExtentIsValid &&
        (pImage->imageSize().x != tRasterQuickInfo.extent.x ||
        pImage->imageSize().y != tRasterQuickInfo.extent.y)  )
        {
        //image size was modified (descartes and Irasb can add pixels to the raster file)
        NewImageAttach (pImage, sourceFileName.c_str(), tRasterQuickInfo.szFileSpec, logicalName.c_str(), tRasterQuickInfo.extent, true, elemHandle, context);
        }

    /* Don't set any location information for this attachment if the    */
    /* extent of the image is not valid, because this will lead to bad  */
    /* location for the image                                           */
    if (imageExtentIsValid)
        {
        /* Set U, V vector */
        if (bsiDPoint3d_magnitude (&uVector) > 0.0 && bsiDPoint3d_magnitude (&vVector)> 0.0)
            {
            // valid u and v
            SetUVector (&uVector, tRasterQuickInfo.extent.x);
            SetVVector (&vVector, tRasterQuickInfo.extent.y);
            }
        else
            {
            // invalid u, v.
            SetUVector (&uVector, tRasterQuickInfo.extent.x, width, affAngle, rotAngle);
            SetVVector (&vVector, tRasterQuickInfo.extent.y, height, affAngle, rotAngle);
            }

        /*Set image orientation */
        pImage->setOrientation (RealDwgUtil::GePoint3dFromDPoint3d(pt0),
            RealDwgUtil::GeVector3dFromDPoint3d(uVector),
            RealDwgUtil::GeVector3dFromDPoint3d(vVector));

        SynchClipBoundaryInDWGImage(pImage,numClipVerts,clipVertices,clipping,clipBoundType,&tRasterQuickInfo.extent,isClipMask,context);
        }

    /* Set contrast, brightness and fade */
    pImage->setContrast ((Adesk::Int8)((GetPercentFromSignedRange (contrast) + 100.0) / 2.0));
    pImage->setBrightness ((Adesk::Int8)((GetPercentFromSignedRange (brightness) + 100.0) / 2.0));
    pImage->setFade ((Adesk::Int8)GetPercentFromRange (fade));


    /* Set the display properties */
    bool bTransparent (false);
    if ((displayProps & AD_IMAGE_DISPPROPS_TRANSON) >> AD_IMAGE_DISPPROPS_TRANSON_OFFSET)
        bTransparent = true;
    bool bShow (false);
    if ((displayProps & AD_IMAGE_DISPPROPS_SHOWIMAGE) >> AD_IMAGE_DISPPROPS_SHOWIMAGE_OFFSET)
        bShow = true;
    bool bShowUnAligned (false);
    if ((displayProps & AD_IMAGE_DISPPROPS_SHOWNONALIGNED) >> AD_IMAGE_DISPPROPS_SHOWNONALIGNED_OFFSET)
        bShowUnAligned = true;
    bool bClip (false);
    if ((displayProps & AD_IMAGE_DISPPROPS_USECLIP) >> AD_IMAGE_DISPPROPS_USECLIP_OFFSET)
        bClip = true;

    pImage->setDisplayOpt (AcDbRasterImage::kTransparent, bTransparent);
    pImage->setDisplayOpt (AcDbRasterImage::kShow, bShow);
    pImage->setDisplayOpt (AcDbRasterImage::kShowUnAligned, bShowUnAligned);
    pImage->setDisplayOpt (AcDbRasterImage::kClip, bClip);

    //Update symbology and properties from type 94 raster frame element is it exist
    context.UpdateEntityPropertiesFromElement(pImage, elemHandle);

    LevelHandle         level = context.GetModel()->GetLevelCache().GetLevel (elemHandle.GetElementCP()->ehdr.level);
    AcDbObjectId        layerId;
    if (level.IsValid())
        layerId = context.SavingChanges() ? context.GetFileHolder().GetLayerByLevelHandle(level) : context.GetFileHolder().GetLayerByLevelId(level.GetLevelId());
    if (layerId.isValid())
        pImage->setLayer (layerId);

    // Make sure the SourceFileName member is in synch with the fileName value...
    // Bad synchronization is possible when an existing image attachment path is changed with Raster Manager
    if (NULL != pImageDef)
        {
        if (sourceFileName.CompareToI(WString(pImageDef->sourceFileName())))
            {
            //TR #271568; SetSourceFileName required access to image source file and should not be open by rasterlib
            // we will close and reopen the source file
            RasterSourceUnloadGuard __guardAgainSharingViolation(&elemHandle);
            pImageDef->setSourceFileName (sourceFileName.c_str());
            }
        }

    //Fix TR #182994: This method must be call after we call updateEntityPropertiesFromElement because it will override color in element by
    //foreground color from binary raster.
    if (pIRasterAttachmentQuery->GetColorMode(elemHandle) == ImageColorMode::Monochrome || pIRasterAttachmentQuery->GetColorMode(elemHandle) == ImageColorMode::Palette2)
        SetForegroundColor (&foreground, pImage, context);

    /* Sets the loaded status to true if image was initially unloaded */
    /* in ACad but its display was turned ON during V8 session.        */
    if (NULL != pImageDef)
        {
        if ((bShow) && 0 == pImageDef->isLoaded() && Acad::eOk != pImageDef->load())
            {
            //TR #271568: Image def must be unload otherwise we have a sharing violation as imageDef keeps a ReadOnly (not sharable) handle on file.
            //We will load image only if it is read-only in which case there is no sharing violation
            RasterSourceUnloadGuard __guardAgainSharingViolation(&elemHandle);
            if (__guardAgainSharingViolation.IsReadOnly())
                pImageDef->load ();
            }

        pImageDef->close();
        }

    return status;
    }
};  // ToDwgExtRasterAttachment




/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                  JeanMarcLanglois03/2001
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::CreateDwgImageClipBoundary
(
RasterClipPropertiesCR      pi_ptClip,              /* Input clip properties data */
size_t                      *po_piNumClipVertices,  /* <= number of clip vertices */
DPoint2d                    *po_ptClipVertices,     /* <= clip vertices in pixel coords */
UShort                      *po_pujClipStatus,      /* <= clipping present ?*/
UShort                      *po_pujClipType,        /* <= clip boundary type */
bool*                       isMaskClip,             /* <= is a mask clip */
bool                        allowMaskClip           /* => allow mask clip (for PDF underlay) */
)
    {
    DPoint3dArray           strokePts;

    *po_pujClipStatus = AD_IMAGE_NOT_CLIPPED;
     if (NULL != isMaskClip)
        *isMaskClip = false;

    if (pi_ptClip.GetBoundary().IsValid())
        {
        pi_ptClip.GetBoundary().ComputeStrokePoints (strokePts);
        }
    else if (allowMaskClip)
        {
        RasterClipCollectionCR  masks = pi_ptClip.GetMaskCollection ();
        if (masks.size() > 0 && masks[0].IsValid())
            {
            masks[0]->ComputeStrokePoints (strokePts);
            if (NULL != isMaskClip)
                *isMaskClip = true;
            }
        }

    if (strokePts.size()<2)
        return;

    *po_pujClipStatus = AD_IMAGE_CLIPPED;

    /* Check wether the clip polygon is polygonal or rectangular*/
    //TR #266450: always store our clip as a clip shape,
    //I've tested with AutoCAD 2007 and 2009, and apparently, rectangle are stored as a 5 vertices shapes
    if ( 2 < strokePts.size() )
        {
        *po_piNumClipVertices = strokePts.size();
        *po_pujClipType = AD_IMAGE_CLIPBOUND_POLYGON;
        int i=0;
        for (DPoint3dArray::const_iterator itr=strokePts.begin(); itr!=strokePts.end(); itr++,i++)
            {
            po_ptClipVertices[i].x = itr->x;
            po_ptClipVertices[i].y = itr->y;
            }
        }
    else
        {
        DPoint3d    dVec1, dVec2, dVec3, dVec4, baseVec;

        /* Vector from pt 0 to pt 1 */
        dVec1.x = strokePts[1].x - strokePts[0].x;
        dVec1.y = strokePts[1].y - strokePts[0].y;

        /* Vector from pt 1 to pt 2 */
        dVec2.x = strokePts[2].x - strokePts[1].x;
        dVec2.y = strokePts[2].y - strokePts[1].y;

        /* Vector from pt 2 to pt 3 */
        dVec3.x = strokePts[3].x - strokePts[2].x;
        dVec3.y = strokePts[3].y - strokePts[2].y;

        /* Vector from pt 3 to pt 0 */
        dVec4.x = strokePts[0].x - strokePts[3].x;
        dVec4.y = strokePts[0].y - strokePts[3].y;

        dVec1.z = dVec2.z = dVec3.z = dVec4.z = (double)0;

        baseVec.x = (double)1;
        baseVec.y = baseVec.z = (double)0;

        dVec1.Normalize ();
        dVec2.Normalize ();
        dVec3.Normalize ();
        dVec4.Normalize ();
        baseVec.Normalize ();

        /* If clip polygon is a true rectangle, then only store 2 opposite      */
        /* vertices     */
        if (dVec1.IsPerpendicularTo(dVec2) &&
            dVec2.IsPerpendicularTo(dVec3) &&
            dVec3.IsPerpendicularTo(dVec4) &&
            (dVec1.IsParallelTo(baseVec) || dVec1.IsPerpendicularTo(baseVec)))
            {
            po_ptClipVertices[0].x = strokePts[0].x;
            po_ptClipVertices[0].y = strokePts[0].y;
            po_ptClipVertices[1].x = strokePts[2].x;
            po_ptClipVertices[1].y = strokePts[2].y;
            *po_pujClipType = AD_IMAGE_CLIPBOUND_RECT;
            *po_piNumClipVertices = 2;
            }
        else
            {
            *po_piNumClipVertices = strokePts.size();
            *po_pujClipType = AD_IMAGE_CLIPBOUND_POLYGON;
            int i=0;
            for (DPoint3dArray::const_iterator itr=strokePts.begin(); itr!=strokePts.end(); itr++,i++)
                {
                po_ptClipVertices[i].x = itr->x;
                po_ptClipVertices[i].y = itr->y;
                }
            }
        }
    }


/*---------------------------------------------------------------------------------**//**
* modify clip boundary so its region only intersects the raster extent
* @param        pio_ptClipVertices          <=> clip vertices in pixel coords
* @param        pio_piNumClipVertices       <=> number of clip vertices
* @param        pi_ptExtent                  => image extent in Pixels
* @param        pi_pujClipType               => clip boundary type
* @param        pi_pujClipStatus             => clipping present ?
* @return       convert the raster reference
* @bsimethod                                                    SimonNormand   06/02
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::AdjustDWGImageClipBoundary
(
DPoint2dP       pio_ptClipVertices,     /* <=> clip vertices in pixel coords */
size_t*         pio_piNumClipVertices,  /* <=> number of clip vertices */
DPoint2dCP      pi_ptExtent,            /*  => image extent in Pixels */
UShort          pi_pujClipType,         /*  => clip boundary type */
UShort          pi_pujClipStatus,       /*  => clipping present ? */
bool            startFromUpperLeft      /*  => image starts from upper left corner */
)
    {
    //Check if clip is larger than raster extent
    bool isLarger(false);
    if (AD_IMAGE_NOT_CLIPPED != pi_pujClipStatus)
        {
        for (size_t i = 0; i < *pio_piNumClipVertices && !isLarger; i++)
            {
            if (0 > pio_ptClipVertices[i].x)
                isLarger = true;

            if (0 > pio_ptClipVertices[i].y)
                isLarger = true;

            if (pi_ptExtent->x < pio_ptClipVertices[i].x)
                isLarger = true;

            if (pi_ptExtent->y < pio_ptClipVertices[i].y)
                isLarger = true;
            }
        }


    if (AD_IMAGE_NOT_CLIPPED == pi_pujClipStatus)
        {
        /* In Acad, when an image has no clipping, the clip boundary is set to image extent  */
        pio_ptClipVertices[0].x = 0.0;
        pio_ptClipVertices[0].y = 0.0;

        pio_ptClipVertices[1].x = 0.0;
        pio_ptClipVertices[1].y = pi_ptExtent->y;

        pio_ptClipVertices[2].x = pi_ptExtent->x;
        pio_ptClipVertices[2].y = pi_ptExtent->y;

        pio_ptClipVertices[3].x = pi_ptExtent->x;
        pio_ptClipVertices[3].y = 0.0;

        pio_ptClipVertices[4].x = 0.0;
        pio_ptClipVertices[4].y = 0.0;

        *pio_piNumClipVertices=5;
        }
    else if ((AD_IMAGE_CLIPBOUND_RECT == pi_pujClipType) || (!isLarger))
        {
        for (size_t i = 0; i < *pio_piNumClipVertices; i++)
            {
            if (0 > pio_ptClipVertices[i].x)
                pio_ptClipVertices[i].x = 0;

            if (0 > pio_ptClipVertices[i].y)
                pio_ptClipVertices[i].y = 0;

            if (pi_ptExtent->x < pio_ptClipVertices[i].x)
                pio_ptClipVertices[i].x = pi_ptExtent->x;

            if (pi_ptExtent->y < pio_ptClipVertices[i].y)
                pio_ptClipVertices[i].y = pi_ptExtent->y;

            /* Always adjust clip coords so that they are defined from */
            /* upper left of the image in Acad vs lower left in MS V8  */
            if (startFromUpperLeft)
                pio_ptClipVertices[i].y = pi_ptExtent->y - pio_ptClipVertices[i].y;
            else
                pio_ptClipVertices[i].y = pio_ptClipVertices[i].y;
            }
        }
    else
        {
        // Complex shape, need to create elements and have them intersect
        DPoint3d*        ptClipVertices = NULL;
        DPoint3dArray    ptNewClipVertices;

        ptClipVertices = (DPoint3d*)calloc (*pio_piNumClipVertices , sizeof (DPoint3d));

        for (size_t i = 0; i < *pio_piNumClipVertices; i++)
            {
            ptClipVertices[i].x = pio_ptClipVertices[i].x;
            ptClipVertices[i].y = pio_ptClipVertices[i].y;
            }

        // get clipped region points from the raster extent and the input clipping boundary points
        CurveVectorPtr  rasterRegion = CurveVector::CreateRectangle (0.0, 0.0, pi_ptExtent->x, pi_ptExtent->y, 0.0);
        CurveVectorPtr  clipRegion = CurveVector::CreateLinear (ptClipVertices, *pio_piNumClipVertices, CurveVector::BOUNDARY_TYPE_Outer, true);

        if (rasterRegion.IsValid() && clipRegion.IsValid())
            {
            CurveVectorPtr  newRegion = CurveVector::AreaIntersection (*rasterRegion.get(), *clipRegion.get());
            if (newRegion.IsValid())
                {
                IFacetOptionsPtr    strokeOptions = IFacetOptions::Create ();
                if (strokeOptions.IsValid())
                    newRegion = newRegion->Stroke (*strokeOptions.get());
                if (newRegion.IsValid() && RealDwgSuccess == RealDwgUtil::GetPointArrayFromLinearElement(ptNewClipVertices, *newRegion.get()))
                    *pio_piNumClipVertices = ptNewClipVertices.size ();
                }
            }

        if (!ptNewClipVertices.empty())
            {
            for (size_t i = 0; i < *pio_piNumClipVertices; i++)
                {
                pio_ptClipVertices[i].x = ptNewClipVertices[i].x;
                /* Always adjust clip coords so that they are defined from */
                /* upper left of the image in Acad vs lower left in MS V8  */
                if (startFromUpperLeft)
                    pio_ptClipVertices[i].y = pi_ptExtent->y - ptNewClipVertices[i].y;
                else
                    pio_ptClipVertices[i].y = ptNewClipVertices[i].y;
                }
            }

        if (ptClipVertices)
            free (ptClipVertices);
        }
    for (size_t i = 0; i < *pio_piNumClipVertices; i++)
        {
        //DWG Clip polygon are in pixels but they have their 0,0 coordinate at the center of the pixel
        //Raster attachment uses lower left corner of the pixel for its 0,0 coordinate, we have to shift coord. by -0.5 pixel
        pio_ptClipVertices[i].x -= 0.5;
        pio_ptClipVertices[i].y -= 0.5;
        }
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    MarcBedard  11/2003
+---------------+---------------+---------------+---------------+---------------+------*/
void            ConvertFromDgnContext::ImagePathFromDgnPath
(
WStringR        sourceFileName,
WCharCP         pFileSpecBytes,
WCharCP         pFileNameBytes,
WCharCP         pSavedSourceFilename, // From the imageDef
DgnModelRefP    modelRef
)
    {
    //Fix to TR 110938: From AutoCAD documentation in: "Set Paths to Externally Referenced Drawings"
    //Note  If a drawing that contains xrefs is moved or saved to a different path, to a different local hard drive, or to a different network server,
    //you must edit any relative paths to accommodate the host drawing's new location or you must relocate the referenced files.
    if ((pFileSpecBytes[0] != 0))
        {
        if (NULL != pSavedSourceFilename && wcslen (pSavedSourceFilename) && !wcsicmp (pSavedSourceFilename, pFileNameBytes))
            {
            // Since the savedSourceFilename was used to set the Filenamebytes when initialy opening the attachment
            // and they are identical, we don't need to change the savedSourceFilename, just recopy it
            sourceFileName = WString(pSavedSourceFilename);
            }
        else
            {
            // the filename has been modified
            switch (this->GetSettings().GetSaveReferencePathMode())
                {
                case SaveReferencePath_Never:
                    {
                    WString     fileName, fileExt;

                    // Get image name with no path
                    BeFileName::ParseName (NULL, NULL, &fileName, &fileExt, pFileSpecBytes);
                    BeFileName::BuildName (sourceFileName, NULL, NULL, fileName.GetWCharCP(), fileExt.GetWCharCP());
                    }
                break;
                case SaveReferencePath_WhenSameDirectory:
                case SaveReferencePath_Relative:
                    {
                    //return relative path
                    BeFileName::FindRelativePath (sourceFileName, pFileSpecBytes, this->GetOutputFileName().c_str());
                    }
                break;
                default:
                    BeAssert(false && L"Invalid switch case for context.GetSaveReferencePathMode()!");
                    //use the new filespec as it is a full path
                    sourceFileName = WString(pFileSpecBytes);
                }
            }
        }
    else
        {
        //FileSpec is NULL, return relative path
        sourceFileName = WString(pFileNameBytes);
        }
    }


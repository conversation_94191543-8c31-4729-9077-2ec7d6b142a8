/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/DwgECProperties.cpp $
|
|  $Copyright: (c) 2018 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once
#include    <Dgnplatform/DgnPlatformApi.h>
#include    <DgnPlatform\DgnECFinders.h>
#include    "realDwgExported.h"
#include    <boost/foreach.hpp>
// This is needed while working with VC10.  When we go to newer compiler, use the for : syntax and get rid of all this.
#include    <boost/foreach.hpp>

#define FOR_EACH(VAR,COL) BOOST_FOREACH(VAR,COL)

#define BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(C)  namespace boost {                     template<> struct range_const_iterator<C>   {typedef C::const_iterator type;}; \
                                                                                              template<> struct range_mutable_iterator<C> {typedef C::const_iterator type;}; }
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::ECN::ECCustomAttributeInstanceIterable)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::ECN::ECPropertyIterable)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::ECN::ECClassContainer)
BENTLEY_ENABLE_BOOST_FOREACH_CONST_ITERATOR(Bentley::ECN::ECValuesCollection)

USING_NAMESPACE_BENTLEY_DGNPLATFORM
USING_NAMESPACE_BENTLEY_ECOBJECT

BEGIN_BENTLEY_NAMESPACE
namespace RealDwg {

static const UInt16 DWG_ECPROVIDER_ID = 0xD446;

/*---------------------------------------------------------------------------------**//**
* @bsistruct                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
struct DwgPropInfo
    {
    WCharCP             m_accessString;
    CharCP              m_varName;      // same as access string, passed to realDwg_get/setXXXSystemVariable methods...
    PrimitiveType       m_type;

    bool operator< (DwgPropInfo const& rhs) const { return 0 > wcscmp (m_accessString, rhs.m_accessString); }
    };

// Sorted for binary search. Indexed by (propertyIndex - 1)
static const DwgPropInfo s_propertyTable[] =
    {
        { L"HyperlinkBase", "HyperlinkBase", PRIMITIVETYPE_String  },
        { L"angbase",       "angbase", PRIMITIVETYPE_Double        },
        { L"annoallvisible","annoallvisible", PRIMITIVETYPE_Boolean},
        { L"attmode",       "attmode", PRIMITIVETYPE_Boolean       },
        { L"aunits",        "aunits", PRIMITIVETYPE_Integer        },
        { L"auprec",        "auprec", PRIMITIVETYPE_Integer        },
        { L"cecolor",       "cecolor", PRIMITIVETYPE_Integer       },
        { L"celtscale",     "celtscale", PRIMITIVETYPE_Double      },
        { L"dimaso",        "dimaso", PRIMITIVETYPE_Boolean        },
        { L"dimlfac",       "dimlfac", PRIMITIVETYPE_Double        },
        { L"dimlunit",      "dimlunit", PRIMITIVETYPE_Integer      },
        { L"dimscale",      "dimscale", PRIMITIVETYPE_Double       },
        { L"dispsilh",      "dispsilh", PRIMITIVETYPE_Boolean      },
        { L"dwgcodepage",   "dwgcodepage", PRIMITIVETYPE_Integer   },
        { L"elevation",     "elevation", PRIMITIVETYPE_Double      },
        { L"extmax",        "extmax", PRIMITIVETYPE_Point3D        },
        { L"extmin",        "extmin", PRIMITIVETYPE_Point3D        },
        { L"facetres",      "facetres", PRIMITIVETYPE_Double       },
        { L"fillmode",      "fillmode", PRIMITIVETYPE_Boolean      },
        { L"insbase",       "insbase", PRIMITIVETYPE_Point3D       },
        { L"insunits",      "insunits", PRIMITIVETYPE_Integer      },
        { L"isolines",      "isolines", PRIMITIVETYPE_Integer      },
        { L"limcheck",      "limcheck", PRIMITIVETYPE_Boolean      },
        { L"limmax",        "limmax", PRIMITIVETYPE_Point3D        },
        { L"limmin",        "limmin", PRIMITIVETYPE_Point3D        },
        { L"ltscale",       "ltscale", PRIMITIVETYPE_Double        },
        { L"lunits",        "lunits", PRIMITIVETYPE_Integer        },
        { L"luprec",        "luprec", PRIMITIVETYPE_Integer        },
        { L"measurement",   "measurement", PRIMITIVETYPE_Integer   },
        { L"mirrtext",      "mirrtext", PRIMITIVETYPE_Boolean      },
        { L"orthomode",     "orthomode", PRIMITIVETYPE_Boolean     },
        { L"proxygraphics", "proxygraphics", PRIMITIVETYPE_Integer },
        { L"psltscale",     "psltscale", PRIMITIVETYPE_Boolean     },
        { L"pstylemode",    "pstylemode", PRIMITIVETYPE_Boolean    },
        { L"psvpscale",     "psvpscale", PRIMITIVETYPE_Double      },
        { L"qtextmode",     "qtextmode", PRIMITIVETYPE_Boolean     },
        { L"textsize",      "textsize", PRIMITIVETYPE_Double       },
        { L"thickness",     "thickness", PRIMITIVETYPE_Double      },
        { L"tilemode",      "tilemode", PRIMITIVETYPE_Boolean      },
        { L"treedepth",     "treedepth", PRIMITIVETYPE_Integer     },
        { L"ucsorg",        "ucsorg", PRIMITIVETYPE_Point3D        },
        { L"ucsxdir",       "ucsxdir", PRIMITIVETYPE_Point3D       },
        { L"ucsydir",       "ucsydir", PRIMITIVETYPE_Point3D       }
    };

static UInt32 s_propertyTableSize = (UInt32)_countof(s_propertyTable);

/*---------------------------------------------------------------------------------**//**
* @bsistruct                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
struct DwgFileECInstance: DgnECInstance
    {
private:
    DgnFileR                            m_file;
    AcDbDatabase*                       m_acdb;
    DgnECInstanceEnablerCR              m_enabler;

    virtual DgnECHostType               _GetHostType() const override { return DgnECHostType::File; }
    virtual DgnECInstanceHost           _GetInstanceHost() const override { return DgnECInstanceHost (m_file); }
    virtual DgnElementECInstanceP       _GetAsElementInstance() const override { return NULL; }
    virtual DgnModelRefP                _GetDgnModelRefP() const override { return NULL; }
    virtual DgnFileP                    _GetDgnFileP() const override { return &m_file; }
    virtual ECEnablerCR                 _GetEnabler() const override { return m_enabler; }
    virtual DgnECInstanceEnablerCR      _GetDgnECInstanceEnabler() const override { return m_enabler; }

    virtual WString                     _GetInstanceId() const override
        {
        WString id;
        GenerateInstanceId (id, m_file);
        return id;
        }

    virtual bool                        _IsReadOnly() const override { return m_file.IsReadOnly(); }
    virtual ECObjectsStatus             _GetValue (ECValueR v, UInt32 propertyIndex, bool useArrayIndex, UInt32 arrayIndex) const override;
    virtual ECObjectsStatus             _SetValue (UInt32 propertyIndex, ECValueCR v, bool useArrayIndex, UInt32 arrayIndex) override;
    virtual StatusInt                   _WriteChanges () override;

    // nops
    virtual ECObjectsStatus             _InsertArrayElements (UInt32, UInt32 index, UInt32 size) { BeAssert (false); return ECOBJECTS_STATUS_ECInstanceImplementationNotSupported; }
    virtual ECObjectsStatus             _AddArrayElements (UInt32, UInt32 size) { BeAssert (false); return ECOBJECTS_STATUS_ECInstanceImplementationNotSupported; }
    virtual ECObjectsStatus             _RemoveArrayElement (UInt32, UInt32 index) { BeAssert (false); return ECOBJECTS_STATUS_ECInstanceImplementationNotSupported; }
    virtual ECObjectsStatus             _ClearArray (UInt32) { BeAssert (false); return ECOBJECTS_STATUS_ECInstanceImplementationNotSupported; }
    virtual WString                     _ToString (WCharCP indent) const override { return WString(); }
    virtual size_t                      _GetOffsetToIECInstance () const override { return 0; }

    DwgFileECInstance(DgnFileR dgnfile, AcDbDatabase* acdb, DgnECInstanceEnablerCR enabler, DgnECInstanceCreateContextCR context)
        : m_file(dgnfile), m_acdb(acdb), m_enabler(enabler), DgnECInstance(context) { }
public:
    static RefCountedPtr<DwgFileECInstance> Create (DgnFileR dgnfile, AcDbDatabase* acdb, DgnECInstanceEnablerCR enabler, DgnECInstanceCreateContextCR context)
        {
        BeAssert (NULL != acdb);
        return new DwgFileECInstance (dgnfile, acdb, enabler, context);
        }

    static void                         GenerateInstanceId (WStringR id, DgnFileR dgnfile);
    };

typedef RefCountedPtr<DwgFileECInstance> DwgFileECInstancePtr;

/*---------------------------------------------------------------------------------**//**
* @bsistruct                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
struct DwgFileECEnabler : DgnECInstanceEnabler
    {
private:
    virtual WCharCP             _GetName() const override { return GetClass().GetName().c_str(); }
    virtual UInt16              _GetProviderId() const override { return DWG_ECPROVIDER_ID; }
    virtual UInt32              _GetFirstPropertyIndex (UInt32 parentIndex) const override { return 0 == parentIndex ? 1 : 0; }
    virtual bool                _HasChildProperties (UInt32 parentIndex) const override { return false; }
    virtual UInt32              _GetParentPropertyIndex (UInt32) const override { return 0; }

    virtual ECObjectsStatus     _GetPropertyIndex (UInt32& propIdx, WCharCP accessString) const override;
    virtual ECObjectsStatus     _GetAccessString (WCharCP& accessString, UInt32 propIdx) const override;
    virtual UInt32              _GetNextPropertyIndex (UInt32 parentIndex, UInt32 inputIndex) const override;
    virtual ECObjectsStatus     _GetPropertyIndices (bvector<UInt32>& indices, UInt32 parentIndex) const override;

    virtual bool                _SupportsCreateInstanceOnHost (DgnECHostType) const override { return false; }
    DwgFileECEnabler (ECClassCR ecClass) : DgnECInstanceEnabler (ecClass, NULL) { }
public:
    DwgFileECInstancePtr        CreateInstance (DgnFileR dgnfile, DgnECInstanceCreateContextCR context) const;

    static RefCountedPtr<DwgFileECEnabler>  Create (ECSchemaCR schema);
    };

typedef RefCountedPtr<DwgFileECEnabler> DwgFileECEnablerPtr;
typedef DwgFileECEnabler const& DwgFileECEnablerCR;
typedef DwgFileECEnabler& DwgFileECEnablerR;

/*---------------------------------------------------------------------------------**//**
* Provides DWG-specific properties for DgnFiles.
* @bsistruct                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
struct DwgFileECProvider : IDgnECProvider
    {
private:
    mutable DwgFileECEnablerPtr     m_enabler;  // lazily-initialized
    mutable ECSchemaP               m_schema;   // ""

    DwgFileECEnablerR               ObtainEnabler() const
        {
        if (m_enabler.IsNull())
            m_enabler = DwgFileECEnabler::Create (ObtainSchema());

        return *m_enabler;
        }

    ECSchemaR                       ObtainSchema() const
        {
        if (NULL == m_schema)
            {
            SchemaKey key (L"DgnFileSchema", 1, 0);
            m_schema = &DgnECManager::GetManager().GetDeliveredSchemaLocator().FetchSchema (key);
            }

        return *m_schema;
        }

    virtual UInt16                  _GetProviderId() const override         { return DWG_ECPROVIDER_ID; }
    virtual WCharCP                 _GetProviderName() const override       { return L"DwgFileECProvider"; }
    virtual DgnECHostType           _GetHostType() const override           { return DgnECHostType::File; }

    virtual ECSchemaPtr             _LocateSchemaInDgnFile (SchemaInfoR schemaInfo, SchemaMatchType matchType) override;
    virtual BentleyStatus           _LocateSchemaXmlInDgnFile (WStringR schemaXml, SchemaInfoR schemaInfo, SchemaMatchType matchType) override;
    virtual void                    _GetSchemaInfos (bvector<SchemaInfo>& infos, DgnFileR dgnfile, ECSchemaPersistence persistence) override;

    virtual IDgnECInstanceFinderPtr _CreateFinder (DgnFileR dgnfile, ECQueryCR query, void* providerPerFileCache, DgnECInstanceCreateContextCR context) const override;
    virtual DgnECInstancePtr        _LocateInstance (IDgnECInstanceLocatorCR locator, DgnECInstanceCreateContextCR context) const override;
    virtual DgnECInstanceEnablerP   _ObtainInstanceEnablerByName (WCharCP schemaName, WCharCP className, DgnFileR dgnfile, void* perFileCache) override;
    virtual bool                    _SupportsQueryFlags (ECQueryProcessFlags flags) const override { return IsIntrinsicQuery (flags); }
    virtual bool                    _ExternalizeInstanceId (WStringR externalizedId, WCharCP internalId) const override;
    virtual bool                    _InternalizeInstanceId (WStringR internalizedId, WCharCP externalizedId, DgnFileP dgnfile, DgnModelRefP modelRef, ElementRefP elemRef) const override;
    virtual void                    _GetPotentialInstanceChanges (DgnECInstanceChangeRecordsR changes, DgnECTxnInfoCR txns, DgnECInstanceCreateContextCR context) const override;

    DwgFileECProvider() : m_schema(NULL) { }
public:
    static DwgFileECProvider&       GetProvider()
        {
        DgnECManagerR mgr = DgnECManager::GetManager();
        DwgFileECProvider* provider = static_cast<DwgFileECProvider*> (mgr.GetProviderById (DWG_ECPROVIDER_ID));
        if (NULL == provider)
            {
            provider = new DwgFileECProvider();
            mgr.RegisterProvider (*provider);
            }

        return *provider;
        }
    };

/*---------------------------------------------------------------------------------**//**
* @bsistruct                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
struct DwgFileECInstanceFinder : BaseHostInstanceFinder<DgnFileP>
    {
private:
    DwgFileECEnablerR       m_enabler;

    DwgFileECInstanceFinder (IECPropertyValueFilterP pvf, WhereCriterionP where, DwgFileECEnablerR enabler, DgnECInstanceCreateContextCR context)
        : BaseHostInstanceFinder<DgnFileP> (pvf, where, context), m_enabler (enabler) { }

    virtual DgnECHostType           _GetHostType() const override { return DgnECHostType::File; }
    virtual DgnECInstanceIterable   _GetRelatedInstances(DgnECInstanceCR, QueryRelatedClassSpecifierCR) const override
        {
        return DgnECInstanceIterable();
        
        }
    virtual StatusInt               _FindInstances (DgnECInstanceVector& results, DgnFileP const& dgnfile) const override;
public:
    static IDgnECInstanceFinderPtr  Create (IECPropertyValueFilterP pvf, WhereCriterionP where, DwgFileECEnablerR enabler, DgnECInstanceCreateContextCR context)
        {
        return new DwgFileECInstanceFinder (pvf, where, enabler, context);
        }
    };

// ===================================================================================
//
// ==== DwgFileECEnabler ====
//
// ===================================================================================

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
ECObjectsStatus DwgFileECEnabler::_GetPropertyIndex (UInt32& propIdx, WCharCP accessString) const
    {
    DwgPropInfo target;
    target.m_accessString = accessString;

    DwgPropInfo const* end = s_propertyTable + s_propertyTableSize;
    DwgPropInfo const* begin = s_propertyTable;
    DwgPropInfo const* loBound = std::lower_bound (begin, end, target);
    if (end != loBound && 0 == wcscmp (loBound->m_accessString, accessString))
        {
        propIdx = (UInt32)(loBound - s_propertyTable) + 1;
        return ECOBJECTS_STATUS_Success;
        }
    else
        return ECOBJECTS_STATUS_PropertyNotFound;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
ECObjectsStatus DwgFileECEnabler::_GetAccessString (WCharCP& accessString, UInt32 propIdx) const
    {
    if (0 == propIdx || (propIdx - 1) > s_propertyTableSize)
        return ECOBJECTS_STATUS_PropertyNotFound;

    accessString = s_propertyTable[propIdx-1].m_accessString;
    return ECOBJECTS_STATUS_Success;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32 DwgFileECEnabler::_GetNextPropertyIndex (UInt32 parentIndex, UInt32 inputIndex) const
    {
    if (0 != parentIndex || inputIndex > s_propertyTableSize || 0 == inputIndex)
        return ECOBJECTS_STATUS_PropertyNotFound;

    // s_propertyTableSize is the last valid property index
    return inputIndex == s_propertyTableSize ? 0 : inputIndex + 1;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
ECObjectsStatus DwgFileECEnabler::_GetPropertyIndices (bvector<UInt32>& indices, UInt32 parentIndex) const
    {
    if (0 != parentIndex)
        return ECOBJECTS_STATUS_PropertyNotFound;

    indices.reserve (s_propertyTableSize);
    for (UInt32 i = 0; i < s_propertyTableSize; i++)
        indices.push_back (i+1);

    return ECOBJECTS_STATUS_Success;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
DwgFileECInstancePtr DwgFileECEnabler::CreateInstance (DgnFileR dgnfile, DgnECInstanceCreateContextCR context) const
    {
    AcDbDatabase* acdb = realDwg_getDatabase (&dgnfile);
    return NULL != acdb ? DwgFileECInstance::Create (dgnfile, acdb, *this, context) : NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
DwgFileECEnablerPtr DwgFileECEnabler::Create (ECSchemaCR schema)
    {
    ECClassCP ecClass = schema.GetClassCP (L"DwgFileProperties");
    return new DwgFileECEnabler (*ecClass);
    }

// ===================================================================================
//
// ==== DwgFileECInstance ====
//
// ===================================================================================

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
ECObjectsStatus DwgFileECInstance::_GetValue (ECValueR v, UInt32 propIdx, bool useArrayIndex, UInt32 arrayIndex) const
    {
    if (useArrayIndex || 0 == propIdx || s_propertyTableSize < propIdx)
        return ECOBJECTS_STATUS_OperationNotSupported;

    DwgPropInfo const& propInfo = s_propertyTable[propIdx-1];
    StatusInt status = ERROR;
    switch (propInfo.m_type)
        {
    case PRIMITIVETYPE_String:
        {
        WString s;
        if (SUCCESS == (status = realDwg_getStringSystemVariable (s, m_acdb, propInfo.m_varName)))
            v.SetString (s.c_str());
        }
        break;
    case PRIMITIVETYPE_Integer:
        {
        Int32 i;
        if (SUCCESS == (status = realDwg_getIntegerSystemVariable (i, m_acdb, propInfo.m_varName)))
            v.SetInteger (i);
        }
        break;
    case PRIMITIVETYPE_Boolean:
        {
        bool b;
        if (SUCCESS == (status = realDwg_getBooleanSystemVariable (b, m_acdb, propInfo.m_varName)))
            v.SetBoolean (b);
        }
        break;
    case PRIMITIVETYPE_Double:
        {
        double d;
        if (SUCCESS == (status = realDwg_getDoubleSystemVariable (d, m_acdb, propInfo.m_varName)))
            v.SetDouble (d);
        }
        break;
    case PRIMITIVETYPE_Point3D:
        {
        DPoint3d p;
        if (SUCCESS == (status = realDwg_getPointSystemVariable (p, m_acdb, propInfo.m_varName)))
            v.SetPoint3D (p);
        }
        break;
    default:
        BeAssert (false);
        }

    return SUCCESS == status ? ECOBJECTS_STATUS_Success : ECOBJECTS_STATUS_Error;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
ECObjectsStatus DwgFileECInstance::_SetValue (UInt32 propIdx, ECValueCR v, bool useArrayIndex, UInt32 arrayIndex)
    {
    if (useArrayIndex || 0 == propIdx || s_propertyTableSize < propIdx)
        return ECOBJECTS_STATUS_OperationNotSupported;

    DwgPropInfo const& propInfo = s_propertyTable[propIdx-1];
    if (v.IsNull() || !v.IsPrimitive() || v.GetPrimitiveType() != propInfo.m_type)
        return ECOBJECTS_STATUS_OperationNotSupported;

    StatusInt status = ERROR;
    switch (propInfo.m_type)
        {
    case PRIMITIVETYPE_String:
        status = realDwg_setStringSystemVariable (m_acdb, propInfo.m_varName, v.GetString());
        break;
    case PRIMITIVETYPE_Integer:
        status = realDwg_setIntegerSystemVariable (m_acdb, propInfo.m_varName, v.GetInteger());
        break;
    case PRIMITIVETYPE_Boolean:
        status = realDwg_setBooleanSystemVariable (m_acdb, propInfo.m_varName, v.GetBoolean());
        break;
    case PRIMITIVETYPE_Double:
        status = realDwg_setDoubleSystemVariable (m_acdb, propInfo.m_varName, v.GetDouble());
        break;
    case PRIMITIVETYPE_Point3D:
        status = realDwg_setPointSystemVariable (m_acdb, propInfo.m_varName, v.GetPoint3D());
        break;
    default:
        BeAssert (false);
        }

    return SUCCESS == status ? ECOBJECTS_STATUS_Success : ECOBJECTS_STATUS_Error;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt DwgFileECInstance::_WriteChanges()
    {
    // Note: _SetValue() writes the change directly to the AcDbDatabase...we have nothing further to do.
    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
void DwgFileECInstance::GenerateInstanceId (WStringR id, DgnFileR dgnfile)
    {
    id.clear();
    id.Sprintf (L":%04X:%ls", DWG_ECPROVIDER_ID, dgnfile.GetFileName().c_str());
    }


// ===================================================================================
//
// ==== DwgFileECProvider ====
//
// ===================================================================================

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool DwgFileECProvider::_ExternalizeInstanceId (WStringR externalizedId, WCharCP internalId) const
    {
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/14
+---------------+---------------+---------------+---------------+---------------+------*/
bool DwgFileECProvider::_InternalizeInstanceId (WStringR internalizedId, WCharCP externalizedId, DgnFileP dgnfile, DgnModelRefP modelRef, ElementRefP elemRef) const
    {
    if (nullptr == dgnfile)
        return false;

    DwgFileECInstance::GenerateInstanceId (internalizedId, *dgnfile);
    return true;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
ECSchemaPtr DwgFileECProvider::_LocateSchemaInDgnFile (SchemaInfoR schemaInfo, SchemaMatchType matchType)
    {
    ECSchemaR schema = ObtainSchema();
    return schema.GetSchemaKey().Matches (schemaInfo.GetSchemaKey(), matchType) ? &schema : NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
BentleyStatus DwgFileECProvider::_LocateSchemaXmlInDgnFile (WStringR schemaXml, SchemaInfoR schemaInfo, SchemaMatchType matchType)
    {
    ECSchemaPtr schema = _LocateSchemaInDgnFile (schemaInfo, matchType);
    return schema.IsValid() && SUCCESS == schema->WriteToXmlString (schemaXml) ? SUCCESS : ERROR;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
void DwgFileECProvider::_GetSchemaInfos (bvector<SchemaInfo>& infos, DgnFileR dgnfile, ECSchemaPersistence persistence)
    {
    if (persistence != ECSCHEMAPERSISTENCE_External && persistence != ECSCHEMAPERSISTENCE_All)
        return;
    
    infos.push_back (SchemaInfo (ObtainSchema().GetSchemaKey(), dgnfile, GetProviderName()));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
IDgnECInstanceFinderPtr DwgFileECProvider::_CreateFinder (DgnFileR dgnfile, ECQueryCR query, void* perFileCache, DgnECInstanceCreateContextCR context) const
    {
    //DgnECInstanceCreateContext context(query.GetPropertiesToSelect(), pathBasedId);
    if (query.IncludesIntrinsic())
        return DwgFileECInstanceFinder::Create (query.GetPropertyValuePreFilter().get(), query.GetWhereCriterionPtr().get(), ObtainEnabler(), context);
    else if (query.IsFilteredByClassList())
        {
        DwgFileECEnablerR enabler = ObtainEnabler();
        T_ECClassCPSet const* searchClasses = query.LookupSearchClasses (dgnfile);
        if (NULL != searchClasses)
            {
            FOR_EACH (ECClassCP ecClass, *searchClasses)
                {
                if (ecClass->GetName().Equals (enabler.GetClass().GetName()) || ecClass->GetName().Equals (L"AnyClass"))
                    return DwgFileECInstanceFinder::Create (query.GetPropertyValuePreFilter().get(), query.GetWhereCriterionPtr().get(), enabler, context);
                }
            }
        }

    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
DgnECInstancePtr DwgFileECProvider::_LocateInstance (IDgnECInstanceLocatorCR locator, DgnECInstanceCreateContextCR context) const
    {
    DgnFileP dgnfile = locator.GetDgnFile();
    if (NULL != dgnfile && NULL != locator.GetInstanceId())
        {
        WString id;
        DwgFileECInstance::GenerateInstanceId (id, *dgnfile);
        if (id.Equals (locator.GetInstanceId()))
            return ObtainEnabler().CreateInstance (*dgnfile, context);
        }

    return NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
DgnECInstanceEnablerP DwgFileECProvider::_ObtainInstanceEnablerByName (WCharCP schemaName, WCharCP className, DgnFileR dgnfile, void* perFileCache)
    {
    DwgFileECEnablerR enabler = ObtainEnabler();
    return enabler.GetClass().GetSchema().GetName().Equals (schemaName) && enabler.GetClass().GetName().Equals (className) ? &enabler : NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   09/14
+---------------+---------------+---------------+---------------+---------------+------*/
void DwgFileECProvider::_GetPotentialInstanceChanges (DgnECInstanceChangeRecordsR changes, DgnECTxnInfoCR txns, DgnECInstanceCreateContextCR context) const
    {
    auto file = txns.GetFileTxn().GetDgnFile();
    if (nullptr == file)
        return;

    auto& enabler = ObtainEnabler();
    auto instance = enabler.CreateInstance (*file, context);
    changes.AppendModifiedInstances (DgnECInstanceIterable::Create (*instance));
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
void realDwg_initECProvider()
    {
    if (NULL != DgnECManager::GetManager().GetProviderById (DWG_ECPROVIDER_ID))
        {
        BeAssert (false && "Registering DwgFileECProvider more than once...");
        return;
        }

    DwgFileECProvider::GetProvider();
    }

// ===================================================================================
//
// ==== DwgFileECInstanceFinder ====
//
// ===================================================================================

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    Paul.Connelly   07/13
+---------------+---------------+---------------+---------------+---------------+------*/
StatusInt DwgFileECInstanceFinder::_FindInstances (DgnECInstanceVector& results, DgnFileP const& dgnfile) const
    {
    if (NULL == dgnfile)
        return ERROR;

    DgnECInstancePtr instance = m_enabler.CreateInstance (*dgnfile, m_createContext);
    if (instance.IsValid() && InstanceSupportsValueFilter (*instance) && AcceptInstance (*instance))
        results.push_back (instance);

    return SUCCESS;
    }

}   // namespace RealDwg
END_BENTLEY_NAMESPACE


/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgSymbologyData.cpp $
|
|  $Copyright: (c) 2016 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#include    "rDwgInternal.h"

USING_NAMESPACE_BENTLEY_DGNPLATFORM

BEGIN_BENTLEY_NAMESPACE

namespace RealDwg {


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
BaseSymbologyData::BaseSymbologyData (FileHolderCP  fileHolder)
    {
    m_fileHolder = fileHolder;

    byte            settingsTable[800];

    memset (settingsTable, 0, sizeof(settingsTable));
    IDwgConversionSettings& settings = MstnInterfaceHelper::Instance().GetSettings();
    settings.GetDisplayColorTable (settingsTable);

    // move 255 entry to 0.
    memcpy (&settingsTable[768], settingsTable, 3);
    this->SetColorTable (settingsTable+3);

    m_lastColorIndex    = INVALID_COLOR_INDEX;
    m_pColorTree        = NULL;
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
BaseSymbologyData::~BaseSymbologyData ()
    {
    mdlAvlTree_free (&m_pColorTree, nullptr, NULL);
    }

/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      11/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        BaseSymbologyData::SetColorTable (const byte* pColorTable)
    {
    m_colorTableCheckSum = 0;
    for (int i=0, j=0; i<256; i++)
        {
        m_colorTableCheckSum ^= (pColorTable[j] << 16 | pColorTable[j+1] << 8 | pColorTable[j+2]);
        m_colorTable[i].red   = pColorTable[j++];
        m_colorTable[i].green = pColorTable[j++];
        m_colorTable[i].blue  = pColorTable[j++];
        }
    }

/*---------------------------------------------------------------------------------**//**
* Remap an index from another file to an index in this file
*
* @bsimethod                                                    RayBentley    08/00
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      BaseSymbologyData::RemapColorIndex
(
UInt32                      otherFileColorIndex,
BaseSymbologyData*          otherSymbologyData,
ConvertContext&             context
)
    {
    if (otherSymbologyData == this)             // No mapping required if same fileData
        return otherFileColorIndex;

    if (otherFileColorIndex == otherSymbologyData->GetByLayerColorIndex())
        return this->GetByLayerColorIndex();

    if (otherFileColorIndex == otherSymbologyData->GetByCellColorIndex())
        return this->GetByCellColorIndex();

    if (otherFileColorIndex >= this->GetMinimumColorIndex() &&
        (this->m_colorTableCheckSum == otherSymbologyData->m_colorTableCheckSum ||
         0 == memcmp (this->GetColor (otherFileColorIndex), otherSymbologyData->GetColor (otherFileColorIndex), sizeof(RgbColorDef))))
        return otherFileColorIndex;

    return this->GetColorIndex (otherSymbologyData->GetColor (otherFileColorIndex), context);
    }

/*---------------------------------------------------------------------------------**//**
* Remap an index from another file to an index in this file   (only if changed from current value)
*
* @bsimethod                                                    RayBentley    08/00
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      BaseSymbologyData::RemapColorIndexIfChanged
(
UInt32                      currentColorIndex,
UInt32                      otherFileColorIndex,
BaseSymbologyData*          otherSymbologyData,
ConvertContext&             context
)
    {
    if (otherSymbologyData == this)             // No mapping required if same fileData
        return otherFileColorIndex;

    // Don't go through the mapping if the color is still what it was mapped to originally.
    // Mapping from one table and then back does not necessarily give the original index

    if (otherSymbologyData->RemapColorIndex (currentColorIndex, this, context) != otherFileColorIndex)
        return this->RemapColorIndex (otherFileColorIndex, otherSymbologyData, context);
    else
        return currentColorIndex;
    }

/*---------------------------------------------------------------------------------**//**
* Remap an index from another file to an index in this file
* @bsimethod                                                    RayBentley    08/00
+---------------+---------------+---------------+---------------+---------------+------*/
Int32                       BaseSymbologyData::RemapLineStyleId
(
Int32                       otherFileLineStyleId,
BaseSymbologyData*          otherSymbologyData,
ConvertContext&             context
)
    {
    if (otherSymbologyData == this)             // No mapping required if same fileData
        return otherFileLineStyleId;

    if (otherFileLineStyleId == otherSymbologyData->GetByLayerLineStyleId())
        return this->GetByLayerLineStyleId();

    if (otherFileLineStyleId == otherSymbologyData->GetByCellLineStyleId())
        return this->GetByCellLineStyleId();

    return otherFileLineStyleId;
    }

/*---------------------------------------------------------------------------------**//**
* Remap an index from another file to an index in this file   (only if changed from current value)
*
* @bsimethod                                                    RayBentley    08/00
+---------------+---------------+---------------+---------------+---------------+------*/
Int32                       BaseSymbologyData::RemapLineStyleIdIfChanged
(
Int32                       currentLineStyleId,
Int32                       otherFileLineStyleId,
BaseSymbologyData*          otherSymbologyData,
ConvertContext&             context
)
    {
    if (otherSymbologyData == this)             // No mapping required if same fileData
        return otherFileLineStyleId;

    // Don't go through the mapping if the Weight is still what it was mapped to originally.
    // Mapping from one table and then back does not necessarily give the original index

    if (otherSymbologyData->RemapLineStyleId (currentLineStyleId, this, context) != otherFileLineStyleId)
        return this->RemapLineStyleId (otherFileLineStyleId, otherSymbologyData, context);
    else
        return currentLineStyleId;
    }

struct ColorTreeNode
    {
    byte            rgb[4];       // Extra byte so we can use UInt32 comparators.
    UInt32          index;
    };

/*---------------------------------------------------------------------------------**//**
* Finds the closest matching color in the given HSV table and recording that matching color's offset in the table.
* @bsimethod                                                    BSI             09/90
+---------------+---------------+---------------+---------------+---------------+------*/
static int      RgbToClosestMatchingColor /* <= SUCCESS or ERROR. */
(
int*            cmapIndexP,        /* <= index into the color table */
RgbColorDefCP   colorToMatchP,     /* => rgb triad to find colormap index for. */
int             numColors,         /* => size of rgb table to search. */
HsvColorDef*    hsvTableP          /* => table to search. */
)
    {
    long            mindist, dist, dh, ds, dv;
    HsvColorDef     colorHSV, *nextHsvP, *endHsvP;
    int             darkestGrayValue = 255;
    int             dkGreyCmapIndex = -1, cmapIndex;

    if (cmapIndexP == NULL)
        cmapIndexP = &cmapIndex;

    /* Convert caller's color to HSV coordinate system. */
    ColorUtil::ConvertRgbToHsv (&colorHSV, colorToMatchP);

    mindist = LMAXI4;

    endHsvP  = hsvTableP + numColors;
    nextHsvP = hsvTableP;
    for (; nextHsvP < endHsvP; nextHsvP++)
        {
        if (colorHSV.saturation > 2)
            {
            dh = nextHsvP->hue - colorHSV.hue;

            /* Find minimum arc length along color wheel between hues. */
            if (dh > 180)
                dh = (colorHSV.hue + 360) - nextHsvP->hue;
            else if (dh < -180)
                dh = (nextHsvP->hue + 360) - colorHSV.hue;
            }
        else
            dh = 0;

        ds = nextHsvP->saturation - colorHSV.saturation;
        dv = nextHsvP->value - colorHSV.value;

        dist = ((dh*dh) << 1) + ds*ds + dv*dv;
        if (dist < mindist)
            {
            *cmapIndexP = static_cast<int>(nextHsvP - hsvTableP);
            mindist = dist;
            if (mindist == 0)
                break;
            }
        /* Save darkest gray just in case... */
        if ((nextHsvP->saturation == 0) &&  /* This indicates shade of gray. */
            (nextHsvP->value != 0) &&       /* Ignore black. */
            (nextHsvP->value < darkestGrayValue))
            {
            darkestGrayValue = nextHsvP->value;
            dkGreyCmapIndex  = static_cast<int>(nextHsvP - hsvTableP);
            }
        }

    /* If non-black color matched to black, change to darkest grey found. */
    nextHsvP = &hsvTableP [*cmapIndexP];
    if ((nextHsvP->value == 0) &&
        (colorToMatchP->red || colorToMatchP->green || colorToMatchP->blue) &&
        (dkGreyCmapIndex != -1))
        *cmapIndexP = dkGreyCmapIndex;

    return SUCCESS;
    }

/*---------------------------------------------------------------------------------**//**
* Get Index from color
* @bsimethod                                                    RayBentley    08/00
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      BaseSymbologyData::GetColorIndex
(
const RgbColorDef*          pColor,
ConvertContext&             context
)
    {
    if (NULL == pColor)
        return 0;

    if (m_lastColorIndex != INVALID_COLOR_INDEX &&
        pColor->red == m_lastColor.red &&
        pColor->blue == m_lastColor.blue &&
        pColor->green == m_lastColor.green)
        return m_lastColorIndex;

    if (NULL == m_pColorTree)
        {
        m_pColorTree = mdlAvlTree_init (AVLKEY_ULONG);

        m_nTableColors = this->GetNColors() - this->GetMinimumColorIndex();

        // convert RGB table to HSV table
        RgbColorDef*    rgb = &m_colorTable[this->GetMinimumColorIndex()];

        for (int i = 0; i < m_nTableColors; i++, rgb++)
            ColorUtil::ConvertRgbToHsv (&m_hsvTable[i], rgb);

        this->OverrideHSVTable ();
        }

    unsigned int        colorIndex = 0;
    ColorTreeNode*      pColorNode;
    ColorTreeNode       colorNode;

    colorNode.rgb[0] = pColor->red;
    colorNode.rgb[1] = pColor->green;
    colorNode.rgb[2] = pColor->blue;
    colorNode.rgb[3] = 0;

    if (NULL != (pColorNode = (ColorTreeNode *) mdlAvlTree_search (m_pColorTree, &colorNode)))
        {
        colorIndex = pColorNode->index;
        }
    else
        {
        RgbToClosestMatchingColor ((int *) &colorIndex, (RgbColorDef *) pColor, m_nTableColors, m_hsvTable);

        colorIndex += this->GetMinimumColorIndex();
        colorNode.index = colorIndex;
        mdlAvlTree_insertNode (m_pColorTree, &colorNode, sizeof(colorNode));
        }

    m_lastColor = *pColor;
    m_lastColorIndex = colorIndex;

    return colorIndex;
    }

/*---------------------------------------------------------------------------------**//**
* Get Color from Index.
* @bsimethod                                                    RayBentley    08/00
+---------------+---------------+---------------+---------------+---------------+------*/
const RgbColorDef*          BaseSymbologyData::GetColor (UInt32 colorIndex)
    {
    if (colorIndex < 0 || colorIndex > 255)
        colorIndex = 0;

    return &m_colorTable[colorIndex];
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/02
+---------------+---------------+---------------+---------------+---------------+------*/
DwgSymbologyData::DwgSymbologyData (FileHolderCP fileHolder) : BaseSymbologyData (fileHolder)
    {
    }

/*---------------------------------------------------------------------------------**//**
* Get Index from color
* @bsimethod                                                    RayBentley    08/00
+---------------+---------------+---------------+---------------+---------------+------*/
UInt32                      DwgSymbologyData::GetColorIndex
(
const RgbColorDef*          pColor,
ConvertContext&             context
)
    {
    // Find White only for exact whites.

    if (NULL == pColor ||
        (pColor->red == 255 && pColor->green == 255 && pColor->blue == 255) ||
        (pColor->red == 0   && pColor->green == 0   && pColor->blue == 0))          // Assume that if they are using black, they have a white background.
        {
        return 7;
        }
    else
        {
        return BaseSymbologyData::GetColorIndex (pColor, context);
        }
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley    08/00
+---------------+---------------+---------------+---------------+---------------+------*/
void                        DwgSymbologyData::OverrideHSVTable ()
    {
    // Effectively remove whites from the color table so that only exact whites match to white as below.
    for (int iColor = 0; iColor < 256; iColor++)
        {
        if (m_hsvTable[iColor].hue == 0 &&
            m_hsvTable[iColor].saturation == 0 &&
            m_hsvTable[iColor].value == 100)
            {
            m_hsvTable[iColor].value = -1000;
            }
        }
    }



/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/02
+---------------+---------------+---------------+---------------+---------------+------*/
DgnSymbologyData::DgnSymbologyData (FileHolderCP fileHolder) : BaseSymbologyData (fileHolder)
    {
    }


/*---------------------------------------------------------------------------------**//**
* @bsimethod                                                    RayBentley      09/02
+---------------+---------------+---------------+---------------+---------------+------*/
void                        DgnSymbologyData::ReadColorTable (DgnModelP    modelRef)
    {
    DgnColorMapP    colorMap = DgnColorMap::GetForDisplay (modelRef);
    if (NULL == colorMap)
        return;

    byte            dgnCtbl[800];
    colorMap->GetRgbColors ((RgbColorDef*) dgnCtbl);

    memcpy (&dgnCtbl[768], dgnCtbl, 3);
    this->SetColorTable (dgnCtbl + 3);
    }


} // End RealDwg namespace

END_BENTLEY_NAMESPACE

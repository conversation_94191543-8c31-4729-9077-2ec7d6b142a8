/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rdSatFileProcessing.cpp $
|
|  $Copyright: (c) 2019 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/

#include <PSolid\parasolid_kernel.h>
#include <PSolidAcisInterop\PSolidAcisInterop.h>
#include <DgnPlatform/DgnPlatformLib.h>
#include <DgnPlatform/DgnPlatformApi.h>

#include <Mstn\RealDWG\rSatToPs.h>

#include <Bentley\BeFileListIterator.h>
#include <chrono>
#include <thread>
USING_NAMESPACE_BENTLEY_SQLITE
USING_NAMESPACE_BENTLEY_DGNPLATFORM
using namespace Bentley::RealDwg;

struct PsdFakeFileProcessing final : PsdFileProcessing
    {
    PsdFakeFileProcessing (ILogPtr log, WStringCR guid)
        :PsdFileProcessing (log, guid)
        {}
    bool ProcessPsdFile ()
        {
        BeFileName satFileName = 
            m_psdDir
            .SessionDir()
            .AppendToPath (BeFileName::GetFileNameWithoutExtension (m_fileToProcess.c_str ()).c_str ())
            .AppendExtension (L"sat");

        BeFile satFile;
        satFile.Create (satFileName.c_str ());

        return BeFileNameStatus::Success == BeFileName::BeDeleteFile (m_fileToProcess.c_str ());
        }
    };

struct UniqueLog1 final : ILog
    {
private:
    UniqueLogPsdFileName& m_fileName;

    bool m_init = false;
    std::wofstream m_out;

    void Init ()
        {
        if (m_init)
            return;

        BeFileName logFileName = m_fileName.FileName ();

        BeFile logFile;
        logFile.Create (logFileName.c_str ());
        logFile.Close ();
        
        m_out.open (logFileName.c_str (), std::ios::out);
        m_init = true;
        }

public:
    UniqueLog1 (UniqueLogPsdFileName& fileName)
        :m_fileName (fileName)
        {}
    ~UniqueLog1 ()
        {
        if (m_out.is_open ())
            m_out.close ();
        }

    void Log (WCharCP message) override
        {
        Init ();
        m_out << message << std::endl;
        }
    };

struct MyDgnHost : DgnPlatformLib::Host
    {
public:
    MyDgnHost ()
        {
        DgnPlatformLib::Initialize (*this, true, true);
        setlocale (LC_CTYPE, "");
        }
    };

int wmain (int argc, wchar_t* argv[])
    {
    if (argc < 2)
        return -1;

    MyDgnHost host;
    UniqueLogPsdFileName logFileName (argv[1]);
    ILogPtr log = new UniqueLog1 (logFileName);
    if (argc >= 3)
        {
        if (WString (argv[2]) == L"TESTING")
            return PsdFakeFileProcessing (log, argv[1]).Process () ? 0 : 1;
        if (WString (argv[2]) == L"LOGTESTING")
            {
            log->Log (L"Log testing");
            return 0;
            }
        }

    int ret = PsdFileProcessing (log, argv[1]).Process () ? 0 : 1;
    return ret;
    }

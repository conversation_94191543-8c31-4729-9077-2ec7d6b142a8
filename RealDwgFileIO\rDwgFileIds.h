/*--------------------------------------------------------------------------------------+
|
|     $Source: mstn/mdlapps/RealDwgFileIO/rDwgFileIds.h $
|
|  $Copyright: (c) 2017 Bentley Systems, Incorporated. All rights reserved. $
|
+--------------------------------------------------------------------------------------*/
#pragma once


/*---------------------------------------------------------------------------------------
RealDwgFileIO.ma gets loaded by file handler as a UstnMdl, not a true MDL.  When an MDL
fails to load its own resource string, the resource manager first searches UstnMdl list.
Low message ID's have a good chance of ID collision, hence result in our messages in the
MDL's message output.  While resource manager needs to do a better job searching messages
for an MDL app, setting our message ID's to a high value reduces chances of collision.
---------------------------------------------------------------------------------------*/
#define     REALDWGMESSAGE_IdBase       10001

// Message lists.
typedef enum realDwgMessageStringId
    {
    MSGLISTID_RealDwgFileIOMisc = REALDWGMESSAGE_IdBase,
    MSGLISTID_RealDwgFileIOErrors,
    MSGLISTID_RealDwgMessageCenterBrief,
    MSGLISTID_RealDwgMessageCenterDetaild,
    MSGLISTID_RealDwgPresetLights,
    MSGLISTID_RealDwgObjectTypes,
    } RealDwgMessageStringID;

// Message ID's
typedef enum realDwgMessageId
    {
    REALDWGMESSAGE_FatalErrorShutDown = REALDWGMESSAGE_IdBase,
    REALDWGMESSAGE_CorruptionDetected,
    } RealDwgMessageID;

typedef enum realDwgMessageCenterId
    {
    REALDWGMESSAGECENTER_InsufficientSeedResolution = REALDWGMESSAGE_IdBase,
    REALDWGMESSAGECENTER_ExcessiveSeedResolution,
    REALDWGMESSAGECENTER_EntitiesOffDesignPlaneIgnored,
    REALDWGMESSAGECENTER_EntitiesOffDesignPlaneDiscarded,
    REALDWGMESSAGECENTER_EntitiesOffDesignPlaneFixed,
    REALDWGMESSAGECENTER_PatternCellTruncated,
    REALDWGMESSAGECENTER_ReplacedLinetype,
    REALDWGMESSAGECENTER_LoadingUserDBX,
    REALDWGMESSAGECENTER_FailueLoadingDBX,
    REALDWGMESSAGECENTER_UnmatchingCodepage,
    REALDWGMESSAGECENTER_OldVersionAEC,
    REALDWGMESSAGECENTER_LinetypeIgnored,
    REALDWGMESSAGECENTER_RasterAttachmentDiscarded,
    REALDWGMESSAGECENTER_DwgSettingsNotFound,
    REALDWGMESSAGECENTER_NotSupported,
    REALDWGMESSAGECENTER_DgnLinestyleRscNotFound,
    REALDWGMESSAGECENTER_DwgFileNeedsRecovery,
    } RealDwgMessageCenterID;

typedef enum realDwgMiscMessageId
    {
    REALDWGMESSAGE_FILEPROP_Client = REALDWGMESSAGE_IdBase,
    REALDWGMESSAGE_FILEPROP_Manager,
    REALDWGMESSAGE_STATUS_LoadingDBX,
    REALDWGMESSAGE_USING_VERSION,
    REALDWGMESSAGE_PROXY_ClassName,
    REALDWGMESSAGE_PROXY_DXFName,
    REALDWGMESSAGE_PROXY_ApplicationName,
    REALDWGMESSAGE_PROGRESS_Writing,
    } RealDwgMiscMessageID;

typedef enum realDwgPresetLightId
    {
    REALDWGMESSAGE_PRESETLIGHT_D65White = REALDWGMESSAGE_IdBase,
    REALDWGMESSAGE_PRESETLIGHT_Fluorescent,
    REALDWGMESSAGE_PRESETLIGHT_CoolWhite,
    REALDWGMESSAGE_PRESETLIGHT_WhiteFluorescent,
    REALDWGMESSAGE_PRESETLIGHT_DaylightFluorescent,
    REALDWGMESSAGE_PRESETLIGHT_Incandescent,
    REALDWGMESSAGE_PRESETLIGHT_Xenon,
    REALDWGMESSAGE_PRESETLIGHT_Halogen,
    REALDWGMESSAGE_PRESETLIGHT_Quartz,
    REALDWGMESSAGE_PRESETLIGHT_MetalHalide,
    REALDWGMESSAGE_PRESETLIGHT_Mercury,
    REALDWGMESSAGE_PRESETLIGHT_PhosphorMercury,
    REALDWGMESSAGE_PRESETLIGHT_HighPressureSodium,
    REALDWGMESSAGE_PRESETLIGHT_LowPressureSodium,
    REALDWGMESSAGE_PRESETLIGHT_Custom,
    } RealDwgPresetLightId;

typedef enum realDwgObjectTypeId
    {
    REALDWGMESSAGE_OBJECTTYPE_PdfUnderlay = REALDWGMESSAGE_IdBase,
    REALDWGMESSAGE_OBJECTTYPE_DwfUnderlay,
    REALDWGMESSAGE_OBJECTTYPE_DgnUnderlay,
    } RealDwgObjectTypeId;
